<div align=center>
	<img src="docs/images/favicon.ico"/>
</div>

English | [中文](./README_CN.md)


[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/Autumn-27/ScopeSentry-Scan)
## Introduction
Scope Sentry is a tool with functions such as asset mapping, subdomain enumeration, information leakage detection, vulnerability scanning, directory scanning, subdomain takeover, crawler, and page monitoring. By building multiple nodes, users can freely choose nodes to run scanning tasks. When new vulnerabilities emerge, it can quickly check whether the concerned assets have related components.

Distributed Implementation Reference Articles: [https://mp.weixin.qq.com/s/xfgRxUjljoQ8KzacblktxA](https://mp.weixin.qq.com/s/xfgRxUjljoQ8KzacblktxA)

Server Recommendation: [lightnode](https://www.lightnode.com/?inviteCode=CQ11JU&promoteWay=LINK)

## Discord:

[https://discord.gg/GWVwSBBm48](https://discord.gg/GWVwSBBm48)

## Language
Server：python - FastApi

Scan：go

Front-end：vue - vue-element-plus-admin

## Website

- Official Website: [https://www.scope-sentry.top](https://www.scope-sentry.top/en/)
- Github: [https://github.com/Autumn-27/ScopeSentry](https://github.com/Autumn-27/ScopeSentry)
- Scanner source code: [https://github.com/Autumn-27/ScopeSentry-Scan](https://github.com/Autumn-27/ScopeSentry-Scan)
- UI source code: [https://github.com/Autumn-27/ScopeSentry-UI](https://github.com/Autumn-27/ScopeSentry-UI)
- Plugin Market: [Plugin Market](https://plugin.scope-sentry.top/en)
- Plugin Template：[https://github.com/Autumn-27/ScopeSentry-Plugin-Template](https://github.com/Autumn-27/ScopeSentry-Plugin-Template)

## Plugin Flowchart

<img src="流程图.svg"/>

## Current Features
- Plugin System (Add any tool through extension)
- Subdomain Enumeration
- Subdomain Takeover Detection
- Port Scanning
- Asset Identification
- Directory Scanning
- Vulnerability Scanning
- Sensitive Information Leakage Detection
- URL Extraction
- Crawler
- Page Monitoring
- Custom WEB Fingerprint
- POC Import
- Asset Grouping
- Multi-Node Scanning
- Webhook

## To Do
- Weak Password Cracking
- 
## Installation

For installation instructions, see the [official website](https://www.scope-sentry.top)

## Communication

Discord:

[https://discord.gg/agsYdAyN](https://discord.gg/agsYdAyN)


## Screenshots

### Login

![alt text](docs/images/login.png)

### Homepage Dashboard
![alt text](docs/images/index-en.png)

## Plugin System
![alt text](docs/images/plugin-cn.png)
![alt text](docs/images/plugin-m-en.png)
## Asset Data
### Assets
![alt text](docs/images/asset-en.png)
![alt text](docs/images/asset-s-en.png)
![alt text](docs/images/asset-s2-en.png)

### Quick syntax search：
![alt text](docs/images/search.gif)

## Root Domain
![alt text](docs/images/rootdomain-cn.png)

### Subdomains
![alt text](docs/images/subdomain-en.png)

### Subdomain Takeover
![alt text](docs/images/subt-en.png)

### APP
![alt text](docs/images/app-cn.png)

### 小程序
![alt text](docs/images/mp-cn.png)

### URL
![alt text](docs/images/craw-cn.png)

### Crawler
![alt text](docs/images/craw-en.png)

### Sensitive Information
![alt text](docs/images/sns-cn.png)

### Directory Scanning
![alt text](docs/images/dir-cn.png)

### Vulnerabilities
![alt text](docs/images/vul-en.png)

### Page Monitoring
![alt text](docs/images/page-en.png)
![alt text](docs/images/page-change.png)
## Projects

![](docs/images/project-cn.png)

## Project asset aggregation
### Panel - Overview
![](docs/images/project-dsh.png)
### Subdomains
![](docs/images/project-subdomain.png)
### Port
![](docs/images/project-port.png)
### Service
![](docs/images/project-server.png)

## Tasks

![](docs/images/create-task-en.png)

## Task Progress

![](docs/images/task-pg-en.png)

## Nodes

![](docs/images/node-cn.png)


#License

All branches of this project follow AGPL-3.0, and additional terms need to be followed:
1. The commercial use of this software requires a separate commercial license.
2. Companies, organizations, and for-profit entities must obtain a commercial license before using, distributing, or modifying this software.
Individuals and non-profit organizations are free to use this software in accordance with the terms of AGPL-3.0.
4. If you have any commercial license inquiries, <NAME_EMAIL> .
