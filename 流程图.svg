<svg id="SvgjsSvg1006" width="1826.410540325453" height="899.657791891899" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs"><defs id="SvgjsDefs1007"><pattern patternUnits="userSpaceOnUse" id="pattern_mark_0" width="300" height="300"><text x="150" y="100" fill="rgba(229,229,229,0.8)" font-size="18" transform="rotate(-45, 150, 150)" style="dominant-baseline: middle; text-anchor: middle;"></text></pattern><pattern patternUnits="userSpaceOnUse" id="pattern_mark_1" width="300" height="300"><text x="150" y="200" fill="rgba(229,229,229,0.8)" font-size="18" transform="rotate(-45, 150, 150)" style="dominant-baseline: middle; text-anchor: middle;"></text></pattern><marker id="SvgjsMarker1069" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1070" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1079" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1080" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1089" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1090" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1093" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1094" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1097" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1098" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1107" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1108" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1125" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1126" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1137" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1138" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1155" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1156" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1165" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1166" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1175" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1176" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1179" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1180" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1197" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1198" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1215" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1216" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1265" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1266" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1275" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1276" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1293" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1294" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1311" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1312" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1321" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1322" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1341" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1342" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1359" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1360" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1395" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1396" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1431" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1432" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1441" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1442" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1463" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1464" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1491" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1492" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1501" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1502" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker><marker id="SvgjsMarker1505" markerWidth="13" markerHeight="9" refX="9.5" refY="4.5" viewBox="0 0 13 9" orient="auto" markerUnits="userSpaceOnUse" stroke-dasharray="0,0"><path id="SvgjsPath1506" d="M0,0 L13,4.5 L0,9 L0,0" fill="#323232" stroke="#323232" stroke-width="1"></path></marker></defs><rect id="svgbackgroundid" width="1826.410540325453" height="899.657791891899" fill="transparent"></rect><rect id="SvgjsRect1009" width="1826.410540325453" height="899.657791891899" fill="url(#pattern_mark_0)"></rect><rect id="SvgjsRect1010" width="1826.410540325453" height="899.657791891899" fill="url(#pattern_mark_1)"></rect><g id="SvgjsG1011" transform="translate(683.0432259708515,52.26288844337654)"><path id="SvgjsPath1012" d="M 17.667 0L 99.186 0C 122.741 0 122.741 53 99.186 53L 17.667 53C -5.889 53 -5.889 0 17.667 0Z" stroke="none" fill-opacity="1" fill="#ff9999"></path><g id="SvgjsG1013"><text id="SvgjsText1014" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="15px" width="97px" fill="#ffffff" font-weight="700" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="15px" weight="700" font-style="" opacity="1" y="14.125" transform="rotate(0)"><tspan id="SvgjsTspan1015" dy="18.75" x="58.5"><tspan id="SvgjsTspan1016" style="">START</tspan></tspan></text></g></g><g id="SvgjsG1017" transform="translate(1150.2045672241402,52.26288844337654)"><path id="SvgjsPath1018" d="M 17.667 0L 164.633 0C 188.189 0 188.189 53 164.633 53L 17.667 53C -5.889 53 -5.889 0 17.667 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1019"><text id="SvgjsText1020" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="163px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="7.375" transform="rotate(0)"><tspan id="SvgjsTspan1021" dy="16.25" x="91.5"><tspan id="SvgjsTspan1022" style="">TargetHandler/目标处理模</tspan></tspan><tspan id="SvgjsTspan1023" dy="16.25" x="91.5"><tspan id="SvgjsTspan1024" style="">块</tspan></tspan></text></g></g><g id="SvgjsG1025" transform="translate(799.8954397984356,184.832105674601)"><path id="SvgjsPath1026" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1027"><text id="SvgjsText1028" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="2.375" transform="rotate(0)"><tspan id="SvgjsTspan1029" dy="16.25" x="84"><tspan id="SvgjsTspan1030" style="">SubdomainSecurity/域</tspan></tspan><tspan id="SvgjsTspan1031" dy="16.25" x="84"><tspan id="SvgjsTspan1032" style="">名安全检测模块</tspan></tspan></text></g></g><g id="SvgjsG1033" transform="translate(1257.3454428501936,184.832105674601)"><path id="SvgjsPath1034" d="M 14.333 0L 140.367 0C 159.478 0 159.478 43 140.367 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1035"><text id="SvgjsText1036" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="135px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="2.375" transform="rotate(0)"><tspan id="SvgjsTspan1037" dy="16.25" x="77.5"><tspan id="SvgjsTspan1038" style="">SubdomainScan/子域</tspan></tspan><tspan id="SvgjsTspan1039" dy="16.25" x="77.5"><tspan id="SvgjsTspan1040" style="">名扫描模块</tspan></tspan></text></g></g><g id="SvgjsG1041" transform="translate(1701.8574993848565,781.7878746082736)"><path id="SvgjsPath1042" d="M 14.333 0L 85.22 0C 104.331 0 104.331 43 85.22 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="none" fill-opacity="1" fill="#99ccff"></path><g id="SvgjsG1043"><text id="SvgjsText1044" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="15px" width="80px" fill="#ffffff" font-weight="700" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="15px" weight="700" font-style="" opacity="1" y="9.125" transform="rotate(0)"><tspan id="SvgjsTspan1045" dy="18.75" x="50"><tspan id="SvgjsTspan1046" style="">FINISH</tspan></tspan></text></g></g><g id="SvgjsG1047" transform="translate(607.9616511331707,407.290423646951)"><path id="SvgjsPath1048" d="M 14.333 0L 157.705 0C 176.816 0 176.816 43 157.705 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1049"><text id="SvgjsText1050" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="153px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="2.375" transform="rotate(0)"><tspan id="SvgjsTspan1051" dy="16.25" x="86.5"><tspan id="SvgjsTspan1052" style="">PortScanPreparation/端</tspan></tspan><tspan id="SvgjsTspan1053" dy="16.25" x="86.5"><tspan id="SvgjsTspan1054" style="">口扫描预处理模块</tspan></tspan></text></g></g><g id="SvgjsG1055" transform="translate(904.6120079202167,25)"><path id="SvgjsPath1056" d="M 0 0L 130.1 0L 130.1 43L 0 43Z" stroke="none" fill="none"></path><g id="SvgjsG1057"><text id="SvgjsText1058" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="131px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="10.875" transform="rotate(0)"><tspan id="SvgjsTspan1059" dy="16.25" x="65.5"><tspan id="SvgjsTspan1060" style="">输入目标string</tspan></tspan></text></g></g><g id="SvgjsG1061" transform="translate(1447.9971265280637,27.5)"><path id="SvgjsPath1062" d="M 0 0L 130.1 0L 130.1 38L 0 38Z" stroke="none" fill="none"></path><g id="SvgjsG1063"><text id="SvgjsText1064" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="131px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="8.375" transform="rotate(0)"><tspan id="SvgjsTspan1065" dy="16.25" x="65.5"><tspan id="SvgjsTspan1066" style="">输出string类型</tspan></tspan></text></g></g><g id="SvgjsG1067"><path id="SvgjsPath1068" d="M1241.3545672241403 106.26288844337654L1241.3545672241403 145.04749705898877L1334.6954428501936 145.04749705898877L1334.6954428501936 181.882105674601" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1069)"></path></g><g id="SvgjsG1071" transform="translate(1607.005980123889,129.832105674601)"><path id="SvgjsPath1072" d="M 17.667 0L 164.633 0C 188.189 0 188.189 53 164.633 53L 17.667 53C -5.889 53 -5.889 0 17.667 0Z" stroke="rgba(232,85,164,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1073"><text id="SvgjsText1074" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="163px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="15.875" transform="rotate(0)"><tspan id="SvgjsTspan1075" dy="16.25" x="91.5"><tspan id="SvgjsTspan1076" style="">目标去重</tspan></tspan></text></g></g><g id="SvgjsG1077"><path id="SvgjsPath1078" d="M1333.5045672241404 78.76288844337653L1698.155980123889 78.76288844337653L1698.155980123889 126.88210567460101" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1079)"></path></g><g id="SvgjsG1081" transform="translate(1310.9203147900776,137.332105674601)"><path id="SvgjsPath1082" d="M 0 0L 130.1 0L 130.1 38L 0 38Z" stroke="none" fill="none"></path><g id="SvgjsG1083"><text id="SvgjsText1084" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="131px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="8.375" transform="rotate(0)"><tspan id="SvgjsTspan1085" dy="16.25" x="65.5"><tspan id="SvgjsTspan1086" style="">非string类型</tspan></tspan></text></g></g><g id="SvgjsG1087"><path id="SvgjsPath1088" d="M1698.155980123889 183.832105674601L1698.155980123889 206.33238633246174L1414.9954428501935 206.33238633246174" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1089)"></path></g><g id="SvgjsG1091"><path id="SvgjsPath1092" d="M799.8954397984356 78.76288844337653L975.050003511288 78.76288844337653L975.050003511288 78.76288844337653L1147.2545672241401 78.76288844337653" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1093)"></path></g><g id="SvgjsG1095"><path id="SvgjsPath1096" d="M1256.3454428501936 206.332105674601L1112.361403526516 206.332105674601L1112.361403526516 206.332105674601L970.3273642028385 206.332105674601" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1097)"></path></g><g id="SvgjsG1099" transform="translate(1320.918913224622,259.832105674601)"><path id="SvgjsPath1100" d="M 0 0L 223.2 0L 223.2 43L 0 43Z" stroke="none" fill="none"></path><g id="SvgjsG1101"><text id="SvgjsText1102" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="224px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="10.875" transform="rotate(0)"><tspan id="SvgjsTspan1103" dy="16.25" x="112"><tspan id="SvgjsTspan1104" style="">输出types.SubdomainResult</tspan></tspan></text></g></g><g id="SvgjsG1105"><path id="SvgjsPath1106" d="M1334.5424586048198 227.7398906260189L1334.5424586048198 314.332105674601L1272.8954428501936 314.332105674601" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1107)"></path></g><g id="SvgjsG1109" transform="translate(1041.0954428501934,156.51019435169644)"><path id="SvgjsPath1110" d="M 0 0L 223.2 0L 223.2 53L 0 53Z" stroke="none" fill="none"></path><g id="SvgjsG1111"><text id="SvgjsText1112" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="224px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="7.375" transform="rotate(0)"><tspan id="SvgjsTspan1113" dy="16.25" x="112"><tspan id="SvgjsTspan1114" style="">输入如果是非string类型直接发送到下</tspan></tspan><tspan id="SvgjsTspan1115" dy="16.25" x="112"><tspan id="SvgjsTspan1116" style="">个模块</tspan></tspan></text></g></g><g id="SvgjsG1117" transform="translate(1087.6454428501934,287.832105674601)"><path id="SvgjsPath1118" d="M 17.667 0L 164.633 0C 188.189 0 188.189 53 164.633 53L 17.667 53C -5.889 53 -5.889 0 17.667 0Z" stroke="rgba(232,85,164,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1119"><text id="SvgjsText1120" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="163px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="15.875" transform="rotate(0)"><tspan id="SvgjsTspan1121" dy="16.25" x="91.5"><tspan id="SvgjsTspan1122" style="">子域名去重</tspan></tspan></text></g></g><g id="SvgjsG1123"><path id="SvgjsPath1124" d="M1086.6454428501934 314.332105674601L1055.975711487041 314.332105674601L1055.975711487041 314.332105674601L1027.0059801238888 314.332105674601" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1125)"></path></g><g id="SvgjsG1127" transform="translate(586.276265365021,275.832105674601)"><path id="SvgjsPath1128" d="M 0 0L 223.2 0L 223.2 49L 0 49Z" stroke="none" fill="none"></path><g id="SvgjsG1129"><text id="SvgjsText1130" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="224px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="5.375" transform="rotate(0)"><tspan id="SvgjsTspan1131" dy="16.25" x="112"><tspan id="SvgjsTspan1132" style="">如果输入是非types.SubdomainResult</tspan></tspan><tspan id="SvgjsTspan1133" dy="16.25" x="112"><tspan id="SvgjsTspan1134" style="">类型直接发送到下个模块</tspan></tspan></text></g></g><g id="SvgjsG1135"><path id="SvgjsPath1136" d="M798.8954397984356 206.332105674601L548.8954397984356 206.332105674601L548.8954397984356 206.332105674601L300.59543979843556 206.332105674601" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1137)"></path></g><g id="SvgjsG1139" transform="translate(197.8954397984356,181.332105674601)"><path id="SvgjsPath1140" d="M 16.667 0L 83.333 0C 105.556 0 105.556 50 83.333 50L 16.667 50C -5.556 50 -5.556 0 16.667 0Z" stroke="rgba(70,105,234,1)" stroke-width="1.5" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1141"><text id="SvgjsText1142" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="80px" fill="#4669ea" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="14.375" transform="rotate(0)"><tspan id="SvgjsTspan1143" dy="16.25" x="50"><tspan id="SvgjsTspan1144" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1145" transform="translate(372.6570909316064,163.01019435169644)"><path id="SvgjsPath1146" d="M 0 0L 196 0L 196 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1147"><text id="SvgjsText1148" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="196px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="0.875" transform="rotate(0)"><tspan id="SvgjsTspan1149" dy="16.25" x="98"><tspan id="SvgjsTspan1150" style="">如果结果类型是SubTakeResult则</tspan></tspan><tspan id="SvgjsTspan1151" dy="16.25" x="98"><tspan id="SvgjsTspan1152" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1153"><path id="SvgjsPath1154" d="M798.8954397984356 206.332105674601L577.9616511331707 206.332105674601L577.9616511331707 428.790423646951L605.0116511331706 428.790423646951" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1155)"></path></g><g id="SvgjsG1157" transform="translate(989.5415490217836,407.290423646951)"><path id="SvgjsPath1158" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1159"><text id="SvgjsText1160" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="10.875" transform="rotate(0)"><tspan id="SvgjsTspan1161" dy="16.25" x="84"><tspan id="SvgjsTspan1162" style="">PortScan/端口扫描模块</tspan></tspan></text></g></g><g id="SvgjsG1163"><path id="SvgjsPath1164" d="M780.9999999999999 428.790423646951L884.7707745108917 428.790423646951L884.7707745108917 428.790423646951L986.5915490217835 428.790423646951" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1165)"></path></g><g id="SvgjsG1167" transform="translate(924.3059801238888,289.332105674601)"><path id="SvgjsPath1168" d="M 16.667 0L 83.333 0C 105.556 0 105.556 50 83.333 50L 16.667 50C -5.556 50 -5.556 0 16.667 0Z" stroke="rgba(70,105,234,1)" stroke-width="1.5" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1169"><text id="SvgjsText1170" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="80px" fill="#4669ea" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="14.375" transform="rotate(0)"><tspan id="SvgjsTspan1171" dy="16.25" x="50"><tspan id="SvgjsTspan1172" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1173"><path id="SvgjsPath1174" d="M923.5559801238888 314.332105674601L883.6364020006371 314.332105674601L883.6364020006371 230.78210567460098" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1175)"></path></g><g id="SvgjsG1177"><path id="SvgjsPath1178" d="M883.5509542519002 228.8284450853108C 883.6364397984356 332.2731750400254 693.9806511331707 302.8493542815266 694.2328334954501 404.3512223853571" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1179)"></path></g><g id="SvgjsG1181" transform="translate(385.99042426493963,299.332105674601)"><path id="SvgjsPath1182" d="M 0 0L 196 0L 196 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1183"><text id="SvgjsText1184" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="196px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="0.875" transform="rotate(0)"><tspan id="SvgjsTspan1185" dy="16.25" x="98"><tspan id="SvgjsTspan1186" style="">将SubdomainResult转为</tspan></tspan><tspan id="SvgjsTspan1187" dy="16.25" x="98"><tspan id="SvgjsTspan1188" style="">DomainResolve</tspan></tspan></text></g></g><g id="SvgjsG1189" transform="translate(800.7707745108918,375.99622330258876)"><path id="SvgjsPath1190" d="M 0 0L 168 0L 168 49L 0 49Z" stroke="none" fill="none"></path><g id="SvgjsG1191"><text id="SvgjsText1192" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="168px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="13.875" transform="rotate(0)"><tspan id="SvgjsTspan1193" dy="16.25" x="84"><tspan id="SvgjsTspan1194" style="">输出types.DomainSkip类型</tspan></tspan></text></g></g><g id="SvgjsG1195"><path id="SvgjsPath1196" d="M1158.0235490217838 428.790423646951C 1263.8741306337733 428.7904236469508 1317.2994214397681 428.790423646951 1421.2000030517577 428.790423646951" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1197)"></path></g><g id="SvgjsG1199" transform="translate(1131.0491787865508,380.49622330258876)"><path id="SvgjsPath1200" d="M 0 0L 158.95 0L 158.95 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1201"><text id="SvgjsText1202" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="159px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="9.375" transform="rotate(0)"><tspan id="SvgjsTspan1203" dy="16.25" x="79.5"><tspan id="SvgjsTspan1204" style="">输出types.PortAlive</tspan></tspan></text></g></g><g id="SvgjsG1205" transform="translate(1424.1500030517577,407.290423646951)"><path id="SvgjsPath1206" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1207"><text id="SvgjsText1208" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="2.375" transform="rotate(0)"><tspan id="SvgjsTspan1209" dy="16.25" x="84"><tspan id="SvgjsTspan1210" style="">PortFingerprint/端口指</tspan></tspan><tspan id="SvgjsTspan1211" dy="16.25" x="84"><tspan id="SvgjsTspan1212" style="">纹识别</tspan></tspan></text></g></g><g id="SvgjsG1213"><path id="SvgjsPath1214" d="M1592.6319274561606 428.790423646951L1621.6319274561606 428.790423646951L1621.6319274561606 575.4570903136184L1549.2486697184247 575.4570903136184" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1215)"></path></g><g id="SvgjsG1217" transform="translate(1295.7995402344636,375.99622330258876)"><path id="SvgjsPath1218" d="M 0 0L 148.817 0L 148.817 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1219"><text id="SvgjsText1220" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="149px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="0.875" transform="rotate(0)"><tspan id="SvgjsTspan1221" dy="16.25" x="74.5"><tspan id="SvgjsTspan1222" style="">types.PortAlive转为</tspan></tspan><tspan id="SvgjsTspan1223" dy="16.25" x="74.5"><tspan id="SvgjsTspan1224" style="">types.AssetOther</tspan></tspan></text></g></g><g id="SvgjsG1225" transform="translate(1378.8166697184247,553.9570903136184)"><path id="SvgjsPath1226" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1227"><text id="SvgjsText1228" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="2.375" transform="rotate(0)"><tspan id="SvgjsTspan1229" dy="16.25" x="84"><tspan id="SvgjsTspan1230" style="">AssetMapping/资产测绘</tspan></tspan><tspan id="SvgjsTspan1231" dy="16.25" x="84"><tspan id="SvgjsTspan1232" style="">模块</tspan></tspan></text></g></g><g id="SvgjsG1233" transform="translate(1628.8519755834727,457.8270075972438)"><path id="SvgjsPath1234" d="M 0 0L 148.817 0L 148.817 98L 0 98Z" stroke="none" fill="none"></path><g id="SvgjsG1235"><text id="SvgjsText1236" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="149px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="-2.625" transform="rotate(0)"><tspan id="SvgjsTspan1237" dy="16.25" x="74.5"><tspan id="SvgjsTspan1238" style="">输出为</tspan></tspan><tspan id="SvgjsTspan1239" dy="16.25" x="74.5"><tspan id="SvgjsTspan1240" style="">types.AssetOther，其中</tspan></tspan><tspan id="SvgjsTspan1241" dy="16.25" x="74.5"><tspan id="SvgjsTspan1242" style="">type字段表示是http资产</tspan></tspan><tspan id="SvgjsTspan1243" dy="16.25" x="74.5"><tspan id="SvgjsTspan1244" style="">还是非http资产，server</tspan></tspan><tspan id="SvgjsTspan1245" dy="16.25" x="74.5"><tspan id="SvgjsTspan1246" style="">标识具体服务</tspan></tspan><tspan id="SvgjsTspan1247" dy="16.25" x="74.5"><tspan id="SvgjsTspan1248" style=""> </tspan></tspan></text></g></g><g id="SvgjsG1249" transform="translate(1108.5767894463625,515.8270075972439)"><path id="SvgjsPath1250" d="M 0 0L 265.556 0L 265.556 65L 0 65Z" stroke="none" fill="none"></path><g id="SvgjsG1251"><text id="SvgjsText1252" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="266px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="21.875" transform="rotate(0)"><tspan id="SvgjsTspan1253" dy="16.25" x="133"><tspan id="SvgjsTspan1254" style="">输出types.AssetOther和types.AssetHttp</tspan></tspan></text></g></g><g id="SvgjsG1255" transform="translate(936.4105403254531,553.9570903136184)"><path id="SvgjsPath1256" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1257"><text id="SvgjsText1258" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="2.375" transform="rotate(0)"><tspan id="SvgjsTspan1259" dy="16.25" x="84"><tspan id="SvgjsTspan1260" style="">AssetHandle/资产处理</tspan></tspan><tspan id="SvgjsTspan1261" dy="16.25" x="84"><tspan id="SvgjsTspan1262" style="">模块</tspan></tspan></text></g></g><g id="SvgjsG1263"><path id="SvgjsPath1264" d="M1378.0388919406469 575.4570903136184L1241.46571613305 575.4570903136184L1241.46571613305 575.4570903136184L1106.842540325453 575.4570903136184" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1265)"></path></g><g id="SvgjsG1267" transform="translate(698.6327625476754,503.3270075972437)"><path id="SvgjsPath1268" d="M 0 0L 265.556 0L 265.556 65L 0 65Z" stroke="none" fill="none"></path><g id="SvgjsG1269"><text id="SvgjsText1270" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="266px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="21.875" transform="rotate(0)"><tspan id="SvgjsTspan1271" dy="16.25" x="133"><tspan id="SvgjsTspan1272" style="">输出types.AssetOther和types.AssetHttp</tspan></tspan></text></g></g><g id="SvgjsG1273"><path id="SvgjsPath1274" d="M935.6327625476753 575.4570903136184L819.896938355272 575.4570903136184L819.896938355272 575.4570903136184L706.1111141628688 575.4570903136184" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1275)"></path></g><g id="SvgjsG1277" transform="translate(520.8611141628687,548.9570903136184)"><path id="SvgjsPath1278" d="M 17.667 0L 164.633 0C 188.189 0 188.189 53 164.633 53L 17.667 53C -5.889 53 -5.889 0 17.667 0Z" stroke="rgba(232,85,164,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1279"><text id="SvgjsText1280" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="163px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="7.375" transform="rotate(0)"><tspan id="SvgjsTspan1281" dy="16.25" x="91.5"><tspan id="SvgjsTspan1282" style="">根据端口和host和历史数据</tspan></tspan><tspan id="SvgjsTspan1283" dy="16.25" x="91.5"><tspan id="SvgjsTspan1284" style="">对比生成变更历史</tspan></tspan></text></g></g><g id="SvgjsG1285" transform="translate(301.451029471836,550.4570903136184)"><path id="SvgjsPath1286" d="M 16.667 0L 83.333 0C 105.556 0 105.556 50 83.333 50L 16.667 50C -5.556 50 -5.556 0 16.667 0Z" stroke="rgba(70,105,234,1)" stroke-width="1.5" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1287"><text id="SvgjsText1288" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="80px" fill="#4669ea" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="14.375" transform="rotate(0)"><tspan id="SvgjsTspan1289" dy="16.25" x="50"><tspan id="SvgjsTspan1290" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1291"><path id="SvgjsPath1292" d="M520.083336385091 575.4570903136184L461.2671829284635 575.4570903136184L461.2671829284635 575.4570903136184L404.15102947183607 575.4570903136184" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1293)"></path></g><g id="SvgjsG1295" transform="translate(411.1560718173524,528.3270075972439)"><path id="SvgjsPath1296" d="M 0 0L 100 0L 100 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1297"><text id="SvgjsText1298" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="100px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="0.875" transform="rotate(0)"><tspan id="SvgjsTspan1299" dy="16.25" x="50"><tspan id="SvgjsTspan1300" style="">资产结果和变更</tspan></tspan><tspan id="SvgjsTspan1301" dy="16.25" x="50"><tspan id="SvgjsTspan1302" style="">历史入库</tspan></tspan></text></g></g><g id="SvgjsG1303" transform="translate(42.518075595597196,553.9570903136184)"><path id="SvgjsPath1304" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1305"><text id="SvgjsText1306" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="10.875" transform="rotate(0)"><tspan id="SvgjsTspan1307" dy="16.25" x="84"><tspan id="SvgjsTspan1308" style="">URLScan/URL扫描模块</tspan></tspan></text></g></g><g id="SvgjsG1309"><path id="SvgjsPath1310" d="M300.701029471836 575.4570903136184L255.72555253371664 575.4570903136184L255.72555253371664 575.4570903136184L212.95007559559718 575.4570903136184" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1311)"></path></g><g id="SvgjsG1313" transform="translate(151.50204978839875,515.8270075972439)"><path id="SvgjsPath1314" d="M 0 0L 245.949 0L 245.949 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1315"><text id="SvgjsText1316" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="246px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="9.375" transform="rotate(0)"><tspan id="SvgjsTspan1317" dy="16.25" x="123"><tspan id="SvgjsTspan1318" style="">输入types.AssetHttp和types.AssetOther</tspan></tspan></text></g></g><g id="SvgjsG1319"><path id="SvgjsPath1320" d="M126.25903779779853 597.9570903136184L126.25903779779853 689.372482460946L126.25907559559734 689.372482460946L126.25907559559734 778.8378746082735" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1321)"></path></g><g id="SvgjsG1323" transform="translate(42.51807559559734,781.7878746082736)"><path id="SvgjsPath1324" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1325"><text id="SvgjsText1326" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="10.875" transform="rotate(0)"><tspan id="SvgjsTspan1327" dy="16.25" x="84"><tspan id="SvgjsTspan1328" style="">WebCrawler爬虫模块</tspan></tspan></text></g></g><g id="SvgjsG1329" transform="translate(25,713.657791891899)"><path id="SvgjsPath1330" d="M 0 0L 100 0L 100 49L 0 49Z" stroke="none" fill="none"></path><g id="SvgjsG1331"><text id="SvgjsText1332" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="100px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="-2.625" transform="rotate(0)"><tspan id="SvgjsTspan1333" dy="16.25" x="50"><tspan id="SvgjsTspan1334" style="">将url列表类型为</tspan></tspan><tspan id="SvgjsTspan1335" dy="16.25" x="50"><tspan id="SvgjsTspan1336" style="">[]string发到下个</tspan></tspan><tspan id="SvgjsTspan1337" dy="16.25" x="50"><tspan id="SvgjsTspan1338" style="">模块</tspan></tspan></text></g></g><g id="SvgjsG1339"><path id="SvgjsPath1340" d="M211 803.2878746082736L424.1350759803339 803.2878746082736L424.1350759803339 803.2878746082736L635.3201519606677 803.2878746082736" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1341)"></path></g><g id="SvgjsG1343" transform="translate(638.2701519606677,781.7878746082736)"><path id="SvgjsPath1344" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1345"><text id="SvgjsText1346" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="2.375" transform="rotate(0)"><tspan id="SvgjsTspan1347" dy="16.25" x="84"><tspan id="SvgjsTspan1348" style="">URLSecurity/url安全检</tspan></tspan><tspan id="SvgjsTspan1349" dy="16.25" x="84"><tspan id="SvgjsTspan1350" style="">测模块</tspan></tspan></text></g></g><g id="SvgjsG1351" transform="translate(102.7459871263197,357.290423646951)"><path id="SvgjsPath1352" d="M 16.667 0L 83.333 0C 105.556 0 105.556 50 83.333 50L 16.667 50C -5.556 50 -5.556 0 16.667 0Z" stroke="rgba(70,105,234,1)" stroke-width="1.5" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1353"><text id="SvgjsText1354" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="80px" fill="#4669ea" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="14.375" transform="rotate(0)"><tspan id="SvgjsTspan1355" dy="16.25" x="50"><tspan id="SvgjsTspan1356" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1357"><path id="SvgjsPath1358" d="M126.29432044572128 552.9577129400725C 126.25903779779856 494.3414284844073 152.7459871263197 466.9060854761624 152.65072397692825 409.988742555525" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1359)"></path></g><g id="SvgjsG1361" transform="translate(42.518075595597196,455.95709031361787)"><path id="SvgjsPath1362" d="M 0 0L 100 0L 100 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1363"><text id="SvgjsText1364" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="100px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="9.375" transform="rotate(0)"><tspan id="SvgjsTspan1365" dy="16.25" x="50"><tspan id="SvgjsTspan1366" style="">types.UrlResult</tspan></tspan></text></g></g><g id="SvgjsG1367" transform="translate(128,676.657791891899)"><path id="SvgjsPath1368" d="M 0 0L 100 0L 100 49L 0 49Z" stroke="none" fill="none"></path><g id="SvgjsG1369"><text id="SvgjsText1370" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="100px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="-2.625" transform="rotate(0)"><tspan id="SvgjsTspan1371" dy="16.25" x="50"><tspan id="SvgjsTspan1372" style="">将URLScan所有</tspan></tspan><tspan id="SvgjsTspan1373" dy="16.25" x="50"><tspan id="SvgjsTspan1374" style="">的输入发送到下</tspan></tspan><tspan id="SvgjsTspan1375" dy="16.25" x="50"><tspan id="SvgjsTspan1376" style="">个模块</tspan></tspan></text></g></g><g id="SvgjsG1377" transform="translate(310.0000000000001,809.657791891899)"><path id="SvgjsPath1378" d="M 0 0L 118.4 0L 118.4 41.869L 0 41.869Z" stroke="none" fill="none"></path><g id="SvgjsG1379"><text id="SvgjsText1380" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="119px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="-6.1905" transform="rotate(0)"><tspan id="SvgjsTspan1381" dy="16.25" x="59.5"><tspan id="SvgjsTspan1382" style="">如果输入类型不为</tspan></tspan><tspan id="SvgjsTspan1383" dy="16.25" x="59.5"><tspan id="SvgjsTspan1384" style="">[]string直接发往下</tspan></tspan><tspan id="SvgjsTspan1385" dy="16.25" x="59.5"><tspan id="SvgjsTspan1386" style="">个模块</tspan></tspan></text></g></g><g id="SvgjsG1387" transform="translate(290.74598712631996,692.657791891899)"><path id="SvgjsPath1388" d="M 16.667 0L 83.333 0C 105.556 0 105.556 50 83.333 50L 16.667 50C -5.556 50 -5.556 0 16.667 0Z" stroke="rgba(70,105,234,1)" stroke-width="1.5" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1389"><text id="SvgjsText1390" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="80px" fill="#4669ea" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="14.375" transform="rotate(0)"><tspan id="SvgjsTspan1391" dy="16.25" x="50"><tspan id="SvgjsTspan1392" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1393"><path id="SvgjsPath1394" d="M126.44072494107466 780.8045182222322C 126.2590377977987 711.1693229282937 300.40000915527355 787.5270064528354 290.64377583789235 720.3558565413316" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1395)"></path></g><g id="SvgjsG1397" transform="translate(182.51807559559734,749.157791891899)"><path id="SvgjsPath1398" d="M 0 0L 100 0L 100 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1399"><text id="SvgjsText1400" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="100px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="0.875" transform="rotate(0)"><tspan id="SvgjsTspan1401" dy="16.25" x="50"><tspan id="SvgjsTspan1402" style="">types.CrawlerRe</tspan></tspan><tspan id="SvgjsTspan1403" dy="16.25" x="50"><tspan id="SvgjsTspan1404" style="">sult</tspan></tspan></text></g></g><g id="SvgjsG1405" transform="translate(449.7268035376319,732.157791891899)"><path id="SvgjsPath1406" d="M 0 0L 152.654 0L 152.654 65L 0 65Z" stroke="none" fill="none"></path><g id="SvgjsG1407"><text id="SvgjsText1408" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="153px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="-2.625" transform="rotate(0)"><tspan id="SvgjsTspan1409" dy="16.25" x="76.5"><tspan id="SvgjsTspan1410" style="">types.CrawlerResult</tspan></tspan><tspan id="SvgjsTspan1411" dy="16.25" x="76.5"><tspan id="SvgjsTspan1412" style="">types.UrlResult</tspan></tspan><tspan id="SvgjsTspan1413" dy="16.25" x="76.5"><tspan id="SvgjsTspan1414" style="">types.AssetHttp</tspan></tspan><tspan id="SvgjsTspan1415" dy="16.25" x="76.5"><tspan id="SvgjsTspan1416" style="">types.AssetOther</tspan></tspan></text></g></g><g id="SvgjsG1417" transform="translate(989.5415490217837,781.7878746082736)"><path id="SvgjsPath1418" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1419"><text id="SvgjsText1420" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="10.875" transform="rotate(0)"><tspan id="SvgjsTspan1421" dy="16.25" x="84"><tspan id="SvgjsTspan1422" style="">DirScan/目录扫描模块</tspan></tspan></text></g></g><g id="SvgjsG1423" transform="translate(812.0000000000001,682.157791891899)"><path id="SvgjsPath1424" d="M 16.667 0L 83.333 0C 105.556 0 105.556 50 83.333 50L 16.667 50C -5.556 50 -5.556 0 16.667 0Z" stroke="rgba(70,105,234,1)" stroke-width="1.5" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1425"><text id="SvgjsText1426" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="80px" fill="#4669ea" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="14.375" transform="rotate(0)"><tspan id="SvgjsTspan1427" dy="16.25" x="50"><tspan id="SvgjsTspan1428" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1429"><path id="SvgjsPath1430" d="M722.1066174072306 780.7924454895933C 722.011114162869 735.0243865938646 765.236511985591 707.157791891899 809.3050339224337 707.3225882534068" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1431)"></path></g><g id="SvgjsG1433" transform="matrix(0.7660444431189778,-0.6427876096865396,0.6427876096865396,0.7660444431189778,668.0819782846476,740.3406284005644)"><path id="SvgjsPath1434" d="M 0 0L 129.137 0L 129.137 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1435"><text id="SvgjsText1436" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="130px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="9.375" transform="rotate(0)"><tspan id="SvgjsTspan1437" dy="16.25" x="65"><tspan id="SvgjsTspan1438" style="">types.SensitiveResult</tspan></tspan></text></g></g><g id="SvgjsG1439"><path id="SvgjsPath1440" d="M806.7520763650704 803.2878746082736C 879.2678654277557 803.2878746082736 916.0257599590983 803.2878746082736 986.5915490217836 803.2878746082736" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1441)"></path></g><g id="SvgjsG1443" transform="translate(821.5808255665855,809.657791891899)"><path id="SvgjsPath1444" d="M 0 0L 152.654 0L 152.654 65L 0 65Z" stroke="none" fill="none"></path><g id="SvgjsG1445"><text id="SvgjsText1446" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="153px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="-2.625" transform="rotate(0)"><tspan id="SvgjsTspan1447" dy="16.25" x="76.5"><tspan id="SvgjsTspan1448" style="">types.CrawlerResult</tspan></tspan><tspan id="SvgjsTspan1449" dy="16.25" x="76.5"><tspan id="SvgjsTspan1450" style="">types.UrlResult</tspan></tspan><tspan id="SvgjsTspan1451" dy="16.25" x="76.5"><tspan id="SvgjsTspan1452" style="">types.AssetHttp</tspan></tspan><tspan id="SvgjsTspan1453" dy="16.25" x="76.5"><tspan id="SvgjsTspan1454" style="">types.AssetOther</tspan></tspan></text></g></g><g id="SvgjsG1455" transform="translate(1157.0234734261862,676.157791891899)"><path id="SvgjsPath1456" d="M 16.667 0L 83.333 0C 105.556 0 105.556 50 83.333 50L 16.667 50C -5.556 50 -5.556 0 16.667 0Z" stroke="rgba(70,105,234,1)" stroke-width="1.5" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1457"><text id="SvgjsText1458" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="80px" fill="#4669ea" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="14.375" transform="rotate(0)"><tspan id="SvgjsTspan1459" dy="16.25" x="50"><tspan id="SvgjsTspan1460" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1461"><path id="SvgjsPath1462" d="M1073.365179954625 780.7912975259692C 1073.2825112239848 735.2883967799798 1110.5239955978923 701.157791891899 1154.3312022065747 701.3619381141307" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1463)"></path></g><g id="SvgjsG1465" transform="matrix(0.6427876096865393,-0.7660444431189781,0.7660444431189781,0.6427876096865393,1022.0216642470434,734.6042458411243)"><path id="SvgjsPath1466" d="M 0 0L 100 0L 100 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1467"><text id="SvgjsText1468" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="100px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="9.375" transform="rotate(0)"><tspan id="SvgjsTspan1469" dy="16.25" x="50"><tspan id="SvgjsTspan1470" style="">types.DirResult</tspan></tspan></text></g></g><g id="SvgjsG1471" transform="translate(1356.3694513972328,781.7878746082736)"><path id="SvgjsPath1472" d="M 14.333 0L 153.149 0C 172.26 0 172.26 43 153.149 43L 14.333 43C -4.778 43 -4.778 0 14.333 0Z" stroke="rgba(0,0,0,1)" stroke-width="2" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1473"><text id="SvgjsText1474" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="148px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="10.875" transform="rotate(0)"><tspan id="SvgjsTspan1475" dy="16.25" x="84"><tspan id="SvgjsTspan1476" style="">DirScan/目录扫描模块</tspan></tspan></text></g></g><g id="SvgjsG1477" transform="translate(1180.3694513972328,809.657791891899)"><path id="SvgjsPath1478" d="M 0 0L 152.654 0L 152.654 65L 0 65Z" stroke="none" fill="none"></path><g id="SvgjsG1479"><text id="SvgjsText1480" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="153px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="-2.625" transform="rotate(0)"><tspan id="SvgjsTspan1481" dy="16.25" x="76.5"><tspan id="SvgjsTspan1482" style="">types.CrawlerResult</tspan></tspan><tspan id="SvgjsTspan1483" dy="16.25" x="76.5"><tspan id="SvgjsTspan1484" style="">types.UrlResult</tspan></tspan><tspan id="SvgjsTspan1485" dy="16.25" x="76.5"><tspan id="SvgjsTspan1486" style="">types.AssetHttp</tspan></tspan><tspan id="SvgjsTspan1487" dy="16.25" x="76.5"><tspan id="SvgjsTspan1488" style="">types.AssetOther</tspan></tspan></text></g></g><g id="SvgjsG1489"><path id="SvgjsPath1490" d="M1158.0234734261862 803.2878746082736C 1236.7618646146047 803.2878746082736 1276.6310602088142 803.2878746082736 1353.4194513972327 803.2878746082736" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1491)"></path></g><g id="SvgjsG1493" transform="translate(1491.6319274561606,676.157791891899)"><path id="SvgjsPath1494" d="M 16.667 0L 83.333 0C 105.556 0 105.556 50 83.333 50L 16.667 50C -5.556 50 -5.556 0 16.667 0Z" stroke="rgba(70,105,234,1)" stroke-width="1.5" fill-opacity="1" fill="#ffffff"></path><g id="SvgjsG1495"><text id="SvgjsText1496" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="80px" fill="#4669ea" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="14.375" transform="rotate(0)"><tspan id="SvgjsTspan1497" dy="16.25" x="50"><tspan id="SvgjsTspan1498" style="">存入数据库</tspan></tspan></text></g></g><g id="SvgjsG1499"><path id="SvgjsPath1500" d="M1440.1550760548653 780.7888724736038C 1440.1104135994342 743.513754623222 1453.357807471109 701.157791891899 1488.9594936028016 701.5426260112556" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1501)"></path></g><g id="SvgjsG1503"><path id="SvgjsPath1504" d="M1524.8513758016352 803.2878746082736C 1595.0538252349238 803.2878746082736 1630.655049951568 803.2878746082736 1699.9074993848565 803.2878746082736" stroke="#323232" stroke-width="1.5" fill="none" marker-end="url(#SvgjsMarker1505)"></path></g><g id="SvgjsG1507" transform="matrix(0.6427876096865393,-0.7660444431189781,0.7660444431189781,0.6427876096865393,1379.2846767812962,740.4093780011896)"><path id="SvgjsPath1508" d="M 0 0L 102.102 0L 102.102 40L 0 40Z" stroke="none" fill="none"></path><g id="SvgjsG1509"><text id="SvgjsText1510" font-family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" text-anchor="middle" font-size="13px" width="103px" fill="#323232" font-weight="400" align="middle" lineHeight="125%" anchor="middle" family="&quot;Microsoft YaHei&quot;, 微软雅黑, Arial" size="13px" weight="400" font-style="" opacity="1" y="9.375" transform="rotate(0)"><tspan id="SvgjsTspan1511" dy="16.25" x="51.5"><tspan id="SvgjsTspan1512" style="">types.VulnResult</tspan></tspan></text></g></g><g id="SvgjsG1513"></g></svg>