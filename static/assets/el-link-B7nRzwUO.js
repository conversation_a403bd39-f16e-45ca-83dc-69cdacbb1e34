import{Y as e,bm as a,d as s,a6 as t,a7 as i,o as n,c as l,i as d,w as o,aI as r,a as f,C as c,j as u,n as p,a8 as y,a9 as g,ag as b}from"./index-C6fb_XFi.js";const k=e({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},href:{type:String,default:""},target:{type:String,default:"_self"},icon:{type:a}}),m={click:e=>e instanceof MouseEvent},v=["href","target"],h=s({name:"ElLink"});const $=b(g(s({...h,props:k,emits:m,setup(e,{emit:a}){const s=e,g=t("link"),b=i((()=>[g.b(),g.m(s.type),g.is("disabled",s.disabled),g.is("underline",s.underline&&!s.disabled)]));function k(e){s.disabled||a("click",e)}return(e,a)=>(n(),l("a",{class:p(f(b)),href:e.disabled||!e.href?void 0:e.href,target:e.disabled||!e.href?void 0:e.target,onClick:k},[e.icon?(n(),d(f(c),{key:0},{default:o((()=>[(n(),d(r(e.icon)))])),_:1})):u("v-if",!0),e.$slots.default?(n(),l("span",{key:1,class:p(f(g).e("inner"))},[y(e.$slots,"default")],2)):u("v-if",!0),e.$slots.icon?y(e.$slots,"icon",{key:2}):u("v-if",!0)],10,v))}}),[["__file","link.vue"]]));export{$ as E};
