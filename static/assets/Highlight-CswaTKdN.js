import{_ as t}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as e,l as s,o as i,i as a,w as o,e as l,a as h,z as p,t as r,M as g}from"./index-3XfDPlIS.js";import{_}from"./Highlight.vue_vue_type_script_lang-zuUE9qRJ.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";const m=e({__name:"Highlight",setup(e){const{t:m}=s(),n=t=>{g.info(t)};return(e,s)=>(i(),a(h(t),{title:h(m)("highlightDemo.highlight")},{default:o((()=>[l(h(_),{keys:[h(m)("highlightDemo.keys1"),h(m)("highlightDemo.keys2")],onClick:n},{default:o((()=>[p(r(h(m)("highlightDemo.message")),1)])),_:1},8,["keys"])])),_:1},8,["title"]))}});export{m as default};
