import{d as e,s as t,v as a,S as o,G as i,r as l,o as n,c as m,e as d,w as s,a as c,f as u,t as r,z as p,B as f,A as v,i as b,j as _,F as y,l as h,_ as x}from"./index-DfJTpRkj.js";import{E as N,a as g}from"./el-col-B4Ik8fnS.js";import{E as w,a as V}from"./el-form-DsaI0u2w.js";import{E as j,b as T}from"./el-radio-group-CTAZlJKV.js";import{E as S}from"./el-text-vKNLRkxx.js";import{E as k}from"./el-switch-C5ZBDFmL.js";import{E as U}from"./el-divider-0NmzbuNU.js";import{E as I}from"./el-card-DyZz6u6e.js";import{_ as A}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{_ as E}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as C}from"./useTable-CtyddZqf.js";import{e as M,f as D,h as O,u as P,i as R,j as F}from"./index-D9_Z6cKh.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-CbhrEnto.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";const G={class:"mb-10px"},H={style:{position:"relative",top:"12px"}},L=x(e({__name:"notification",setup(e){const{t:x}=h(),L=t([{field:"selection",type:"selection",width:"55"},{field:"name",label:"Name",minWidth:20},{field:"method",label:"Method",minWidth:20},{field:"url",label:"URL"},{field:"contentType",label:"Content Type",minWidth:25},{field:"data",label:"POST DATA"},{field:"state",label:x("common.state"),minWidth:25,formatter:(e,t,i)=>{let l="",n="";return 1==i?(l="#2eb98a",n=x("common.on")):(l="red",n=x("common.off")),a(g,{gutter:20},[a(N,{span:1},[a(o,{icon:"clarity:circle-solid",color:l,size:10})]),a(N,{span:5},[a(S,{type:"info"},n)])])}},{field:"action",label:x("tableDemo.action"),formatter:(e,t,o)=>a("div",[a(i,{type:"primary",onClick:()=>te(e)},x("common.edit")),a(i,{type:"danger",onClick:()=>oe(e)},x("common.delete"))])}]),{tableState:W,tableMethods:z}=C({fetchDataApi:async()=>({list:(await D()).data.list})}),{dataList:B}=W,{getList:J,getElTableExpose:K}=z,$=t({name:"",url:"",method:"GET",contentType:"raw",data:"",state:!0}),q=t({dirScanNotification:!0,portScanNotification:!0,sensitiveNotification:!0,subdomainNotification:!0,subdomainTakeoverNotification:!0,pageMonNotification:!0,vulNotification:!0});(async()=>{const e=await O();q.dirScanNotification=e.data.dirScanNotification,q.portScanNotification=e.data.portScanNotification,q.sensitiveNotification=e.data.sensitiveNotification,q.subdomainNotification=e.data.subdomainNotification,q.subdomainTakeoverNotification=e.data.subdomainTakeoverNotification,q.pageMonNotification=e.data.pageMonNotification,q.vulNotification=e.data.vulNotification})();const Q=l(!1),X=l(!1),Y=async()=>{Z.value="",$.name="",$.url="",$.method="GET",$.contentType="raw",$.data="",$.state=!0,X.value=!0},Z=l(""),ee=l(!1),te=e=>{Z.value=e.id,$.name=e.name,$.url=e.url,$.method=e.method,$.contentType=e.contentType,$.data=e.data,$.state=e.state,X.value=!0},ae=l(!1),oe=async e=>{ae.value=!0;try{await M([e.id]);ae.value=!1,J()}catch(t){ae.value=!1,J()}},ie=l([]),le=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await ne()},ne=async()=>{const e=await K(),t=(null==e?void 0:e.getSelectionRows())||[];ie.value=t.map((e=>e.id)),ae.value=!0;try{await M(ie.value);ae.value=!1,J()}catch(a){ae.value=!1,J()}};return(e,t)=>(n(),m(y,null,[d(c(I),{shadow:"never",class:"mb-20px"},{header:s((()=>[d(c(g),null,{default:s((()=>[d(c(N),{span:3,style:{height:"100%"}},{default:s((()=>[u("span",null,r(c(x)("configuration.noticeConfig")),1)])),_:1})])),_:1})])),default:s((()=>[d(c(g),null,{default:s((()=>[d(c(N),{style:{position:"relative",top:"16px"}},{default:s((()=>[u("div",G,[d(c(i),{type:"primary",onClick:Y},{default:s((()=>[p(r(c(x)("configuration.newWebhookConfig")),1)])),_:1}),d(c(i),{type:"danger",loading:ae.value,onClick:le},{default:s((()=>[p(r(c(x)("common.delete")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),u("div",H,[d(c(E),{data:c(B),columns:L,stripe:"",border:!0,resizable:!0,maxHeight:"200",style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["data","columns"])]),d(c(U)),d(c(w),{model:q,"label-width":"auto","status-icon":"",ref:"ruleFormRef",style:{position:"relative",top:"1rem"}},{default:s((()=>[d(c(g),null,{default:s((()=>[d(c(N),{span:5},{default:s((()=>[d(c(V),{label:c(x)("subdomain.subdomainName")},{default:s((()=>[d(c(k),{modelValue:q.subdomainNotification,"onUpdate:modelValue":t[0]||(t[0]=e=>q.subdomainNotification=e),"inline-prompt":"","active-text":c(x)("common.switchAction"),"inactive-text":c(x)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),d(c(N),{span:5},{default:s((()=>[d(c(V),{label:c(x)("task.subdomainTakeover")},{default:s((()=>[d(c(k),{modelValue:q.subdomainTakeoverNotification,"onUpdate:modelValue":t[1]||(t[1]=e=>q.subdomainTakeoverNotification=e),"inline-prompt":"","active-text":c(x)("common.switchAction"),"inactive-text":c(x)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),d(c(N),{span:5},{default:s((()=>[d(c(V),{label:c(x)("dirScan.dirScanName")},{default:s((()=>[d(c(k),{modelValue:q.dirScanNotification,"onUpdate:modelValue":t[2]||(t[2]=e=>q.dirScanNotification=e),"inline-prompt":"","active-text":c(x)("common.switchAction"),"inactive-text":c(x)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),d(c(g),null,{default:s((()=>[d(c(N),{span:5},{default:s((()=>[d(c(V),{label:c(x)("task.portScan")},{default:s((()=>[d(c(k),{modelValue:q.portScanNotification,"onUpdate:modelValue":t[3]||(t[3]=e=>q.portScanNotification=e),"inline-prompt":"","active-text":c(x)("common.switchAction"),"inactive-text":c(x)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),d(c(N),{span:5},{default:s((()=>[d(c(V),{label:c(x)("sensitiveInformation.sensitiveInformationName")},{default:s((()=>[d(c(k),{modelValue:q.sensitiveNotification,"onUpdate:modelValue":t[4]||(t[4]=e=>q.sensitiveNotification=e),"inline-prompt":"","active-text":c(x)("common.switchAction"),"inactive-text":c(x)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),d(c(N),{span:5},{default:s((()=>[d(c(V),{label:c(x)("PageMonitoring.pageMonitoringName")},{default:s((()=>[d(c(k),{modelValue:q.pageMonNotification,"onUpdate:modelValue":t[5]||(t[5]=e=>q.pageMonNotification=e),"inline-prompt":"","active-text":c(x)("common.switchAction"),"inactive-text":c(x)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),d(c(g),null,{default:s((()=>[d(c(N),{span:5},{default:s((()=>[d(c(V),{label:c(x)("vulnerability.vulnerabilityName")},{default:s((()=>[d(c(k),{modelValue:q.vulNotification,"onUpdate:modelValue":t[6]||(t[6]=e=>q.vulNotification=e),"inline-prompt":"","active-text":c(x)("common.switchAction"),"inactive-text":c(x)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),d(c(g),null,{default:s((()=>[d(c(N),{span:2,offset:8},{default:s((()=>[d(c(V),null,{default:s((()=>[d(c(f),{type:"primary",onClick:t[7]||(t[7]=e=>(async()=>{Q.value=!0,await P(q.dirScanNotification,q.portScanNotification,q.sensitiveNotification,q.subdomainNotification,q.subdomainTakeoverNotification,q.pageMonNotification,q.vulNotification),Q.value=!1})()),loading:Q.value},{default:s((()=>[p(r(c(x)("common.submit")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),d(c(A),{modelValue:X.value,"onUpdate:modelValue":t[15]||(t[15]=e=>X.value=e),title:c(x)("configuration.newWebhookConfig"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:"100"},{default:s((()=>[d(c(S),{class:"mx-2",type:"danger",size:"small",style:{position:"relative",left:"2rem"}},{default:s((()=>[p(r(c(x)("configuration.noticeHelp")),1)])),_:1}),d(c(w),{model:$,"label-width":"auto","status-icon":"",ref:"ruleFormRef",style:{position:"relative",top:"1rem"}},{default:s((()=>[d(c(V),{label:"Name",prop:"name"},{default:s((()=>[d(c(v),{modelValue:$.name,"onUpdate:modelValue":t[8]||(t[8]=e=>$.name=e),placeholder:"Input name."},null,8,["modelValue"])])),_:1}),d(c(V),{label:"Method",prop:"method"},{default:s((()=>[d(c(j),{modelValue:$.method,"onUpdate:modelValue":t[9]||(t[9]=e=>$.method=e)},{default:s((()=>[d(c(T),{value:"GET"},{default:s((()=>[p("GET")])),_:1}),d(c(T),{value:"POST"},{default:s((()=>[p("POST")])),_:1})])),_:1},8,["modelValue"])])),_:1}),d(c(V),{label:"URL",prop:"url"},{default:s((()=>[d(c(v),{modelValue:$.url,"onUpdate:modelValue":t[10]||(t[10]=e=>$.url=e),placeholder:"Input URL."},null,8,["modelValue"])])),_:1}),"POST"==$.method?(n(),b(c(V),{key:0,label:"Data Type",prop:"contentType"},{default:s((()=>[d(c(j),{modelValue:$.contentType,"onUpdate:modelValue":t[11]||(t[11]=e=>$.contentType=e)},{default:s((()=>[d(c(T),{value:"raw"},{default:s((()=>[p("Raw")])),_:1}),d(c(T),{value:"json"},{default:s((()=>[p("Json")])),_:1})])),_:1},8,["modelValue"])])),_:1})):_("",!0),"POST"==$.method?(n(),b(c(V),{key:1,label:"Data",prop:"Data"},{default:s((()=>[d(c(v),{modelValue:$.data,"onUpdate:modelValue":t[12]||(t[12]=e=>$.data=e),placeholder:"Input POST Data."},null,8,["modelValue"])])),_:1})):_("",!0),d(c(V),{label:c(x)("common.state")},{default:s((()=>[d(c(k),{modelValue:$.state,"onUpdate:modelValue":t[13]||(t[13]=e=>$.state=e),"inline-prompt":"","active-text":c(x)("common.switchAction"),"inactive-text":c(x)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),d(c(g),null,{default:s((()=>[d(c(N),{span:2,offset:8},{default:s((()=>[d(c(V),null,{default:s((()=>[d(c(f),{type:"primary",onClick:t[14]||(t[14]=e=>(async()=>{ee.value=!0,""==Z.value?await R($.name,$.url,$.method,$.contentType,$.data,$.state):await F(Z.value,$.name,$.url,$.method,$.contentType,$.data,$.state),J(),ee.value=!1,X.value=!1})()),loading:ee.value},{default:s((()=>[p(r(c(x)("common.submit")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])],64))}}),[["__scopeId","data-v-00fd9ad3"]]);export{L as default};
