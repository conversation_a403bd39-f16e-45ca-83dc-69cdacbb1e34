import{d as e,s as t,V as o,r as a,o as s,c as i,e as l,w as r,a as m,f as n,t as p,A as d,B as u,z as f,F as j,l as c,_}from"./index-DfJTpRkj.js";import{a as g,E as y}from"./el-col-B4Ik8fnS.js";import{E as x,a as b}from"./el-form-DsaI0u2w.js";import{E as w}from"./el-text-vKNLRkxx.js";import{E as h}from"./el-divider-0NmzbuNU.js";import{E as v}from"./el-card-DyZz6u6e.js";import z from"./notification-CKYNsCEL.js";import C from"./Deduplication-BFELM6hZ.js";import{g as M,s as V}from"./index-D9_Z6cKh.js";import{j as E,o as k,T as A}from"./index-B-gHSwWD.js";import"./castArray-CvwAI87l.js";import"./el-radio-group-CTAZlJKV.js";import"./el-switch-C5ZBDFmL.js";import"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import"./refs-DAMUgizk.js";import"./el-popper-D2BmgSQA.js";import"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-CbhrEnto.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./index-CyH6XROR.js";import"./useTable-CtyddZqf.js";import"./el-input-number-DV6Zl9Iq.js";import"./index-D1ADinPR.js";const D=_(e({__name:"system",setup(e){const _=[E(),k],{t:D}=c(),I=t({timezone:"",ModulesConfig:""});o((async()=>{try{const e=await M();200==e.code&&(I.timezone=e.data.timezone,I.ModulesConfig=e.data.ModulesConfig)}catch(e){}}));const B=async()=>{window.confirm("Do you want to save the data?")&&await F()},F=async()=>{H.value=!0;(await V(I.timezone,I.ModulesConfig)).code,H.value=!1},H=a(!1);return(e,t)=>(s(),i(j,null,[l(m(v),{shadow:"never",class:"mb-20px"},{header:r((()=>[l(m(g),null,{default:r((()=>[l(m(y),{span:3,style:{height:"100%"}},{default:r((()=>[n("span",null,p(m(D)("configuration.system")),1)])),_:1})])),_:1})])),default:r((()=>[l(m(x),{model:I,"label-width":"auto",style:{"max-width":"600px"}},{default:r((()=>[l(m(b),{label:m(D)("configuration.timezone")},{default:r((()=>[l(m(d),{modelValue:I.timezone,"onUpdate:modelValue":t[0]||(t[0]=e=>I.timezone=e)},null,8,["modelValue"])])),_:1},8,["label"]),l(m(b),{label:"Module Config"},{default:r((()=>[l(m(A),{modelValue:I.ModulesConfig,"onUpdate:modelValue":t[1]||(t[1]=e=>I.ModulesConfig=e),extensions:_,autofocus:!0,"indent-with-tab":!0,"tab-size":2,style:{height:"550px",width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),l(m(g),null,{default:r((()=>[l(m(y),{span:12,offset:2},{default:r((()=>[l(m(u),{type:"primary",onClick:B,loading:H.value},{default:r((()=>[f("Save")])),_:1},8,["loading"]),l(m(h),{direction:"vertical"}),l(m(w),{size:"small",type:"danger"},{default:r((()=>[f(p(m(D)("configuration.threadMsg")),1)])),_:1})])),_:1})])),_:1})])),_:1}),l(z),l(C)],64))}}),[["__scopeId","data-v-bc860df1"]]);export{D as default};
