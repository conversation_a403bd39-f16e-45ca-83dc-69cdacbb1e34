import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{_ as o}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{d as t,l,r as i,s as a,y as s,o as r,i as m,w as p,e as n,z as u,t as d,a as c,c as j,F as f,R as _,f as g}from"./index-3XfDPlIS.js";import{u as v,F as b}from"./useForm-CxJHOWP1.js";import{u as y}from"./useValidator-ByHrE6OC.js";import{g as D}from"./index-BlcO9gtB.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./refs-CSSW5x_d.js";import"./el-form-BY8piFS2.js";import"./castArray-uOT054sj.js";import"./el-col-CN1tVfqh.js";import"./el-tag-DcMbxLLg.js";import"./el-checkbox-DjLAvZXr.js";import"./index-tjM0-mlU.js";import"./el-radio-group-evFfsZkP.js";/* empty css                          */import"./el-input-number-CfcpPMpr.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-virtual-list-Drl4IGmp.js";import"./raf-BoCEWvzN.js";import"./el-select-v2-CJw7ZO42.js";import"./el-switch-C-DLgt5X.js";import"./el-autocomplete-DpYoUHkX.js";import"./el-divider-D9UCOo44.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-upload-D4EYBM-E.js";import"./el-progress-DtkkA0nz.js";import"./InputPassword-CsftE_fC.js";import"./style.css_vue_type_style_index_0_src_true_lang-C2CruVLZ.js";import"./JsonEditor.vue_vue_type_script_setup_true_lang-C3k54pLm.js";import"./IconPicker-CGJrUvM2.js";import"./el-tab-pane-xcqYouKU.js";import"./el-pagination-DwzzZyu4.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./tsxHelper-C7SpLWNA.js";/* empty css                        */import"./index-Dz8ZrwBc.js";const x=t({__name:"Dialog",setup(t){const{required:x}=y(),{t:k}=l(),h=i(!1),P=i(!1),{formRegister:C,formMethods:V}=v(),{getElFormExpose:w}=V,F=a([{field:"field1",label:k("formDemo.input"),component:"Input",formItemProps:{rules:[x()]}},{field:"field2",label:k("formDemo.select"),component:"Select",optionApi:async()=>(await D()).data},{field:"field3",label:k("formDemo.radio"),component:"RadioGroup",componentProps:{options:[{label:"option-1",value:"1"},{label:"option-2",value:"2"}]}},{field:"field4",label:k("formDemo.checkbox"),component:"CheckboxGroup",value:[],componentProps:{options:[{label:"option-1",value:"1"},{label:"option-2",value:"2"}]}},{field:"field5",component:"DatePicker",label:k("formDemo.datePicker"),componentProps:{type:"date"}},{field:"field6",component:"TimeSelect",label:k("formDemo.timeSelect")}]),I=async()=>{const e=await w();null==e||e.validate((e=>{}))};return(t,l)=>{const i=s("BaseButton");return r(),m(c(e),{title:c(k)("dialogDemo.dialog"),message:c(k)("dialogDemo.dialogDes")},{default:p((()=>[n(i,{type:"primary",onClick:l[0]||(l[0]=e=>h.value=!h.value)},{default:p((()=>[u(d(c(k)("dialogDemo.open")),1)])),_:1}),n(i,{type:"primary",onClick:l[1]||(l[1]=e=>P.value=!P.value)},{default:p((()=>[u(d(c(k)("dialogDemo.combineWithForm")),1)])),_:1}),n(c(o),{modelValue:h.value,"onUpdate:modelValue":l[3]||(l[3]=e=>h.value=e),title:c(k)("dialogDemo.dialog")},{footer:p((()=>[n(i,{onClick:l[2]||(l[2]=e=>h.value=!1)},{default:p((()=>[u(d(c(k)("dialogDemo.close")),1)])),_:1})])),default:p((()=>[(r(),j(f,null,_(1e4,(e=>g("div",{key:e},d(e),1))),64))])),_:1},8,["modelValue","title"]),n(c(o),{modelValue:P.value,"onUpdate:modelValue":l[5]||(l[5]=e=>P.value=e),title:c(k)("dialogDemo.dialog")},{footer:p((()=>[n(i,{type:"primary",onClick:I},{default:p((()=>[u(d(c(k)("dialogDemo.submit")),1)])),_:1}),n(i,{onClick:l[4]||(l[4]=e=>P.value=!1)},{default:p((()=>[u(d(c(k)("dialogDemo.close")),1)])),_:1})])),default:p((()=>[n(c(b),{schema:F,onRegister:c(C)},null,8,["schema","onRegister"])])),_:1},8,["modelValue","title"])])),_:1},8,["title","message"])}}});export{x as default};
