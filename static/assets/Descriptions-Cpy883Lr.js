import{d as e,aq as l,a8 as r,a as t,r as o,e as a,S as s,Q as i,ag as d,aJ as c,bx as n,J as p,b as f,k as x,_ as u}from"./index-DfJTpRkj.js";import"./el-tooltip-l0sNRNKZ.js";import{E as b}from"./el-popper-D2BmgSQA.js";import{a as v,E as m}from"./el-col-B4Ik8fnS.js";import{E as g}from"./index-Co3LqSsp.js";const h=f(),y=r((()=>h.getMobile)),{getPrefixCls:j}=x(),z=j("descriptions"),O=u(e({name:"Descriptions",props:{title:l.string.def(""),message:l.string.def(""),collapse:l.bool.def(!0),border:l.bool.def(!0),column:l.number.def(2),size:l.oneOf(["large","default","small"]).def("default"),direction:l.oneOf(["horizontal","vertical"]).def("horizontal"),extra:l.string.def(""),schema:{type:Array,default:()=>[]},data:{type:Object,default:()=>({})}},setup(e,{attrs:l}){const f=r((()=>{const r=["title","message","collapse","schema","data","class"],o={...l,...e};for(const e in o)-1!==r.indexOf(e)&&delete o[e];return t(y)&&(o.direction="vertical"),o})),x=e=>{const l=["field"],r={...e};for(const t in r)-1!==l.indexOf(t)&&delete r[t];return{labelClassName:`${z}-label`,...r}},u=o(!0),h=()=>{e.collapse&&(u.value=!t(u))};return()=>{let l;return a("div",{class:[z,"bg-[var(--el-color-white)] dark:bg-[var(--el-bg-color)] dark:border-[var(--el-border-color)] dark:border-1px"]},[e.title?a("div",{class:[`${z}-header`,"relative h-50px flex justify-between items-center layout-border__bottom px-10px cursor-pointer"],onClick:h},[a("div",{class:[`${z}-header__title`,"relative font-18px font-bold ml-10px"]},[a("div",{class:"flex items-center"},[e.title,e.message?a(b,{content:e.message,placement:"right"},{default:()=>[a(s,{icon:"bi:question-circle-fill",class:"ml-5px",size:14},null)]}):null])]),e.collapse?a(s,{icon:u.value?"ep:arrow-down":"ep:arrow-up"},null):null]):null,a(g,null,{default:()=>{return[i(a("div",{class:[`${z}-content`,"p-20px"]},[a(v,c({gutter:0},t(f),{class:"outline-1px outline-[var(--el-border-color-lighter)] outline-solid"}),(r=l=e.schema.map((l=>a(m,{key:l.field,span:l.span||24/e.column,class:"flex items-stretch"},{default:()=>{var r,t,o,s;return["horizontal"===e.direction?a("div",{class:"flex items-stretch bg-[var(--el-fill-color-light)] outline-1px outline-[var(--el-border-color-lighter)] outline-solid flex-1"},[a("div",c(x(l),{class:"w-120px text-left px-8px py-11px font-700 color-[var(--el-text-color-regular)] border-r-1px border-r-[var(--el-border-color-lighter)] border-r-solid "}),[l.label]),a("div",{class:"flex-1 px-8px py-11px bg-[var(--el-bg-color)] color-[var(--el-text-color-primary)] text-size-14px"},[(null==(r=l.slots)?void 0:r.default)?null==(t=l.slots)?void 0:t.default(e.data):n(e.data,l.field)??"-"])]):a("div",{class:"bg-[var(--el-fill-color-light)] outline-1px outline-[var(--el-border-color-lighter)] outline-solid flex-1"},[a("div",c(x(l),{class:"text-left px-8px py-11px font-700 color-[var(--el-text-color-regular)] border-b-1px border-b-[var(--el-border-color-lighter)] border-b-solid"}),[l.label]),a("div",{class:"flex-1 px-8px py-11px bg-[var(--el-bg-color)] color-[var(--el-text-color-primary)] text-size-14px"},[(null==(o=l.slots)?void 0:o.default)?null==(s=l.slots)?void 0:s.default(e.data):n(e.data,l.field)??"-"])])]}}))),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!p(r)?l:{default:()=>[l]}))]),[[d,t(u)]])];var r}})])}}}),[["__scopeId","data-v-19103a92"]]);export{O as D};
