const e={common:{selectCategory:e=>{const{normalize:n}=e;return n(["Select Category"])},about:e=>{const{normalize:n}=e;return n(["About"])},unprocessed:e=>{const{normalize:n}=e;return n(["Unprocessed"])},processing:e=>{const{normalize:n}=e;return n(["Processing"])},ignored:e=>{const{normalize:n}=e;return n(["Ignored"])},suspected:e=>{const{normalize:n}=e;return n(["Suspected"])},confirmed:e=>{const{normalize:n}=e;return n(["Confirmed"])},cleanLog:e=>{const{normalize:n}=e;return n(["Clear Logs"])},log:e=>{const{normalize:n}=e;return n(["Log"])},filesize:e=>{const{normalize:n}=e;return n(["Size"])},quantity:e=>{const{normalize:n}=e;return n(["Quantity"])},import:e=>{const{normalize:n}=e;return n(["Import"])},category:e=>{const{normalize:n}=e;return n(["Category"])},uploadMsg:e=>{const{normalize:n}=e;return n(["Batch import only supports nuclei. Put the yaml file into a zip and upload it. You can directly download and upload the nuclei official poc library, and the poc will be automatically extracted."])},upload:e=>{const{normalize:n}=e;return n(["Upload"])},cover:e=>{const{normalize:n}=e;return n(["Cover"])},download:e=>{const{normalize:n}=e;return n(["Download"])},operation:e=>{const{normalize:n}=e;return n(["Operation"])},selectAll:e=>{const{normalize:n}=e;return n(["Select All"])},multipleSelection:e=>{const{normalize:n}=e;return n(["Multiple Selection"])},name:e=>{const{normalize:n}=e;return n(["Name"])},cversion:e=>{const{normalize:n}=e;return n(["Current Version"])},lversion:e=>{const{normalize:n}=e;return n(["Latest Version"])},updateButtonMsg:e=>{const{normalize:n}=e;return n(["Currently, only server upgrades are supported"])},switchAction:e=>{const{normalize:n}=e;return n(["On"])},switchInactive:e=>{const{normalize:n}=e;return n(["Off"])},inputText:e=>{const{normalize:n}=e;return n(["Please input"])},selectText:e=>{const{normalize:n}=e;return n(["Please select"])},startTimeText:e=>{const{normalize:n}=e;return n(["Start time"])},endTimeText:e=>{const{normalize:n}=e;return n(["End time"])},changePassword:e=>{const{normalize:n}=e;return n(["Change Password"])},submit:e=>{const{normalize:n}=e;return n(["Submit"])},update:e=>{const{normalize:n}=e;return n(["One click update"])},updatemsg:e=>{const{normalize:n}=e;return n(["Discovering new versions"])},querysyntax:e=>{const{normalize:n}=e;return n(["Query Syntax"])},newPassword:e=>{const{normalize:n}=e;return n(["New Password"])},statusStop:e=>{const{normalize:n}=e;return n(["Stop"])},login:e=>{const{normalize:n}=e;return n(["Login"])},edit:e=>{const{normalize:n}=e;return n(["Edit"])},version:e=>{const{normalize:n}=e;return n(["System version information"])},delete:e=>{const{normalize:n}=e;return n(["Delete"])},new:e=>{const{normalize:n}=e;return n(["New"])},view:e=>{const{normalize:n}=e;return n(["View"])},state:e=>{const{normalize:n}=e;return n(["State"])},config:e=>{const{normalize:n}=e;return n(["Config"])},on:e=>{const{normalize:n}=e;return n(["On"])},off:e=>{const{normalize:n}=e;return n(["Off"])},save:e=>{const{normalize:n}=e;return n(["Save"])},true:e=>{const{normalize:n}=e;return n(["true"])},false:e=>{const{normalize:n}=e;return n(["false"])},required:e=>{const{normalize:n}=e;return n(["This is required"])},loginOut:e=>{const{normalize:n}=e;return n(["Login out"])},document:e=>{const{normalize:n}=e;return n(["Document"])},reminder:e=>{const{normalize:n}=e;return n(["Reminder"])},loginOutMessage:e=>{const{normalize:n}=e;return n(["Exit the system?"])},back:e=>{const{normalize:n}=e;return n(["Back"])},ok:e=>{const{normalize:n}=e;return n(["OK"])},cancel:e=>{const{normalize:n}=e;return n(["Cancel"])},reload:e=>{const{normalize:n}=e;return n(["Reload current"])},closeTab:e=>{const{normalize:n}=e;return n(["Close current"])},closeTheLeftTab:e=>{const{normalize:n}=e;return n(["Close left"])},closeTheRightTab:e=>{const{normalize:n}=e;return n(["Close right"])},closeOther:e=>{const{normalize:n}=e;return n(["Close other"])},closeAll:e=>{const{normalize:n}=e;return n(["Close all"])},prevLabel:e=>{const{normalize:n}=e;return n(["Prev"])},nextLabel:e=>{const{normalize:n}=e;return n(["Next"])},skipLabel:e=>{const{normalize:n}=e;return n(["Jump"])},doneLabel:e=>{const{normalize:n}=e;return n(["End"])},menu:e=>{const{normalize:n}=e;return n(["Menu"])},menuDes:e=>{const{normalize:n}=e;return n(["Menu bar rendered in routed structure"])},collapse:e=>{const{normalize:n}=e;return n(["Collapse"])},collapseDes:e=>{const{normalize:n}=e;return n(["Expand and zoom the menu bar"])},tagsView:e=>{const{normalize:n}=e;return n(["Tags view"])},tagsViewDes:e=>{const{normalize:n}=e;return n(["Used to record routing history"])},tool:e=>{const{normalize:n}=e;return n(["Tool"])},toolDes:e=>{const{normalize:n}=e;return n(["Used to set up custom systems"])},query:e=>{const{normalize:n}=e;return n(["Query"])},reset:e=>{const{normalize:n}=e;return n(["Reset"])},shrink:e=>{const{normalize:n}=e;return n(["Put away"])},expand:e=>{const{normalize:n}=e;return n(["Expand"])},delMessage:e=>{const{normalize:n}=e;return n(["Delete the selected data?"])},delWarning:e=>{const{normalize:n}=e;return n(["Warning"])},delOk:e=>{const{normalize:n}=e;return n(["OK"])},delCancel:e=>{const{normalize:n}=e;return n(["Cancel"])},delNoData:e=>{const{normalize:n}=e;return n(["Please select the data to delete"])},delSuccess:e=>{const{normalize:n}=e;return n(["Deleted successfully"])},refresh:e=>{const{normalize:n}=e;return n(["Refresh"])},fullscreen:e=>{const{normalize:n}=e;return n(["Fullscreen"])},size:e=>{const{normalize:n}=e;return n(["Size"])},columnSetting:e=>{const{normalize:n}=e;return n(["Column setting"])},lengthRange:e=>{const{normalize:n,interpolate:r,named:t}=e;return n(["The length should be between ",r(t("min"))," and ",r(t("max"))])},notSpace:e=>{const{normalize:n}=e;return n(["Spaces are not allowed"])},notSpecialCharacters:e=>{const{normalize:n}=e;return n(["Special characters are not allowed"])},isEqual:e=>{const{normalize:n}=e;return n(["The two are not equal"])}},export:{exportType:e=>{const{normalize:n}=e;return n(["Export Type"])},exportTypeAll:e=>{const{normalize:n}=e;return n(["All data"])},exportTypeSearch:e=>{const{normalize:n}=e;return n(["Current search criteria"])},exportQuantity:e=>{const{normalize:n}=e;return n(["Export data volume"])},exportRecords:e=>{const{normalize:n}=e;return n(["Export Records"])},fileName:e=>{const{normalize:n}=e;return n(["File Name"])},createTime:e=>{const{normalize:n}=e;return n(["Create Time"])},endTime:e=>{const{normalize:n}=e;return n(["Complete Time"])},fileSize:e=>{const{normalize:n}=e;return n(["File Size"])},state:e=>{const{normalize:n}=e;return n(["State"])},run:e=>{const{normalize:n}=e;return n(["running"])},success:e=>{const{normalize:n}=e;return n(["Success"])},fail:e=>{const{normalize:n}=e;return n(["Fail"])},type:e=>{const{normalize:n}=e;return n(["Type"])},download:e=>{const{normalize:n}=e;return n(["Download"])},field:e=>{const{normalize:n}=e;return n(["Field"])},fileType:e=>{const{normalize:n}=e;return n(["File Type"])}},searchHelp:{notice:e=>{const{normalize:n}=e;return n(["Keywords need to be wrapped in double quotes"])},operator:e=>{const{normalize:n}=e;return n(["Operator"])},meaning:e=>{const{normalize:n}=e;return n(["Meaning"])},equal:e=>{const{normalize:n}=e;return n(["Exact matching means querying only keyword assets."])},notIn:e=>{const{normalize:n}=e;return n(["Eliminate means to eliminate assets containing keywords."])},like:e=>{const{normalize:n}=e;return n(["Match, indicating that the query contains keyword assets(Supports regular input)."])},brackets:e=>{const{normalize:n}=e;return n(["The content in brackets has the highest priority"])},and:e=>{const{normalize:n}=e;return n(["and conditions"])},or:e=>{const{normalize:n}=e;return n(["or condition"])},keywords:e=>{const{normalize:n}=e;return n(["Keywords"])},explain:e=>{const{normalize:n}=e;return n(["Explain"])},example:e=>{const{normalize:n}=e;return n(["Example"])},app:e=>{const{normalize:n}=e;return n(["Retrieve specified component"])},body:e=>{const{normalize:n}=e;return n(["Retrieve HTTP response body"])},header:e=>{const{normalize:n}=e;return n(["Retrieve HTTP request header"])},project:e=>{const{normalize:n}=e;return n(["Retrieve by project name"])},title:e=>{const{normalize:n}=e;return n(["Detect website title"])},statuscode:e=>{const{normalize:n}=e;return n(["Retrieve response code, fuzzy search is not supported"])},icon:e=>{const{normalize:n}=e;return n(["Retrieve by website icon hash"])},ip:e=>{const{normalize:n}=e;return n(["Retrieve IP"])},length:e=>{const{normalize:n}=e;return n(["Retrieve Respone Length"])},port:e=>{const{normalize:n}=e;return n(["Retrieve port"])},domain:e=>{const{normalize:n}=e;return n(["Retrieve domain"])},protocol:e=>{const{normalize:n}=e;return n(["Retrieve by service"])},banner:e=>{const{normalize:n}=e;return n(["Retrieve banner of non-HTTP assets"])},subdomainType:e=>{const{normalize:n}=e;return n(["Retrieve record type"])},subdomainValue:e=>{const{normalize:n}=e;return n(["Retrieve record value"])},url:e=>{const{normalize:n}=e;return n(["Retrieve URL"])},input:e=>{const{normalize:n}=e;return n(["Retrieve input source"])},source:e=>{const{normalize:n}=e;return n(["Retrieve URL source"])},urlType:e=>{const{normalize:n}=e;return n(["Retrieve URL type"])},method:e=>{const{normalize:n}=e;return n(["Retrieve Method"])},crawlerBody:e=>{const{normalize:n}=e;return n(["Retrieve POST data"])},sname:e=>{const{normalize:n}=e;return n(["Retrieve sensitive information name"])},sinfo:e=>{const{normalize:n}=e;return n(["Retrieve sensitive information"])},redirect:e=>{const{normalize:n}=e;return n(["Retrieve redirect link"])},vulname:e=>{const{normalize:n}=e;return n(["Retrieve vulnerability name"])},matched:e=>{const{normalize:n}=e;return n(["Retrieve matched content"])},vulRequest:e=>{const{normalize:n}=e;return n(["Retrieve request content"])},response:e=>{const{normalize:n}=e;return n(["Retrieve response content"])},hash:e=>{const{normalize:n}=e;return n(["Retrieve response hash"])},diff:e=>{const{normalize:n}=e;return n(["Retrieve diff content"])},level:e=>{const{normalize:n}=e;return n(["Retrieve vuln level(info、high、medium、critical、low、unknown)"])},sensMd5:e=>{const{normalize:n}=e;return n(["Retrieve based on response body MD5"])},sensLevel:e=>{const{normalize:n}=e;return n(["Search by sensitivity level (red, green, cyan, yellow, orange, gray, pink)"])},taskName:e=>{const{normalize:n}=e;return n(["Search by task name, only supports exact search."])},icp:e=>{const{normalize:n}=e;return n(["Search by ICP"])},company:e=>{const{normalize:n}=e;return n(["Search by company name"])},name:e=>{const{normalize:n}=e;return n(["Search by name"])}},lock:{lockScreen:e=>{const{normalize:n}=e;return n(["Lock screen"])},lock:e=>{const{normalize:n}=e;return n(["Lock"])},lockPassword:e=>{const{normalize:n}=e;return n(["Lock screen password"])},unlock:e=>{const{normalize:n}=e;return n(["Click to unlock"])},backToLogin:e=>{const{normalize:n}=e;return n(["Back to login"])},entrySystem:e=>{const{normalize:n}=e;return n(["Entry the system"])},placeholder:e=>{const{normalize:n}=e;return n(["Please enter the lock screen password"])},message:e=>{const{normalize:n}=e;return n(["Lock screen password error"])}},error:{noPermission:e=>{const{normalize:n}=e;return n(["Sorry, you don't have permission to access this page."])},pageError:e=>{const{normalize:n}=e;return n(["Sorry, the page you visited does not exist."])},networkError:e=>{const{normalize:n}=e;return n(["Sorry, the server reported an error."])},returnToHome:e=>{const{normalize:n}=e;return n(["Return to home"])}},setting:{projectSetting:e=>{const{normalize:n}=e;return n(["Project setting"])},theme:e=>{const{normalize:n}=e;return n(["Theme"])},layout:e=>{const{normalize:n}=e;return n(["Layout"])},systemTheme:e=>{const{normalize:n}=e;return n(["System theme"])},menuTheme:e=>{const{normalize:n}=e;return n(["Menu theme"])},interfaceDisplay:e=>{const{normalize:n}=e;return n(["Interface display"])},breadcrumb:e=>{const{normalize:n}=e;return n(["Breadcrumb"])},breadcrumbIcon:e=>{const{normalize:n}=e;return n(["Breadcrumb icon"])},collapseMenu:e=>{const{normalize:n}=e;return n(["Collapse menu"])},hamburgerIcon:e=>{const{normalize:n}=e;return n(["Hamburger icon"])},screenfullIcon:e=>{const{normalize:n}=e;return n(["Screenfull icon"])},sizeIcon:e=>{const{normalize:n}=e;return n(["Size icon"])},localeIcon:e=>{const{normalize:n}=e;return n(["Locale icon"])},tagsView:e=>{const{normalize:n}=e;return n(["Tags view"])},logo:e=>{const{normalize:n}=e;return n(["Logo"])},greyMode:e=>{const{normalize:n}=e;return n(["Grey mode"])},fixedHeader:e=>{const{normalize:n}=e;return n(["Fixed header"])},headerTheme:e=>{const{normalize:n}=e;return n(["Header theme"])},cutMenu:e=>{const{normalize:n}=e;return n(["Cut Menu"])},copy:e=>{const{normalize:n}=e;return n(["Copy"])},clearAndReset:e=>{const{normalize:n}=e;return n(["Clear cache and reset"])},copySuccess:e=>{const{normalize:n}=e;return n(["Copy success"])},copyFailed:e=>{const{normalize:n}=e;return n(["Copy failed"])},footer:e=>{const{normalize:n}=e;return n(["Footer"])},uniqueOpened:e=>{const{normalize:n}=e;return n(["Unique opened"])},tagsViewIcon:e=>{const{normalize:n}=e;return n(["Tags view icon"])},dynamicRouter:e=>{const{normalize:n}=e;return n(["Enable dynamic router"])},serverDynamicRouter:e=>{const{normalize:n}=e;return n(["Server dynamic router"])},reExperienced:e=>{const{normalize:n}=e;return n(["Please exit the login experience again"])},fixedMenu:e=>{const{normalize:n}=e;return n(["Fixed menu"])}},size:{default:e=>{const{normalize:n}=e;return n(["Default"])},large:e=>{const{normalize:n}=e;return n(["Large"])},small:e=>{const{normalize:n}=e;return n(["Small"])}},login:{welcome:e=>{const{normalize:n}=e;return n(["Welcome to the system"])},message:e=>{const{normalize:n}=e;return n(["Backstage management system"])},username:e=>{const{normalize:n}=e;return n(["Username"])},password:e=>{const{normalize:n}=e;return n(["Password"])},register:e=>{const{normalize:n}=e;return n(["Register"])},checkPassword:e=>{const{normalize:n}=e;return n(["Confirm password"])},login:e=>{const{normalize:n}=e;return n(["Sign in"])},otherLogin:e=>{const{normalize:n}=e;return n(["Sign in with"])},remember:e=>{const{normalize:n}=e;return n(["Remember me"])},hasUser:e=>{const{normalize:n}=e;return n(["Existing account? Go to login"])},forgetPassword:e=>{const{normalize:n}=e;return n(["Forget password"])},usernamePlaceholder:e=>{const{normalize:n}=e;return n(["Please input username"])},passwordPlaceholder:e=>{const{normalize:n}=e;return n(["Please input password"])},code:e=>{const{normalize:n}=e;return n(["Verification code"])},codePlaceholder:e=>{const{normalize:n}=e;return n(["Please input verification code"])}},router:{assetinfo:e=>{const{normalize:n}=e;return n(["Asset Information"])},taskManagement:e=>{const{normalize:n}=e;return n(["Task"])},scanTask:e=>{const{normalize:n}=e;return n(["Scan Tasks"])},scheduledTask:e=>{const{normalize:n}=e;return n(["Scheduled Tasks"])},scanTemplate:e=>{const{normalize:n}=e;return n(["Scan Template"])},nodeManagement:e=>{const{normalize:n}=e;return n(["Node"])},projectDetail:e=>{const{normalize:n}=e;return n(["Project Detail"])},vulFingerprint:e=>{const{normalize:n}=e;return n(["Vul-Fingerprint"])},projectManagement:e=>{const{normalize:n}=e;return n(["Project"])},pocManagement:e=>{const{normalize:n}=e;return n(["POC"])},fingerprintManagement:e=>{const{normalize:n}=e;return n(["Fingerprint"])},sensitiveInformationRules:e=>{const{normalize:n}=e;return n(["Sensitive Information"])},dictionaryManagement:e=>{const{normalize:n}=e;return n(["Dictionary"])},subdomainDictionary:e=>{const{normalize:n}=e;return n(["Subdomain"])},subfinder:e=>{const{normalize:n}=e;return n(["Subfinder"])},rad:e=>{const{normalize:n}=e;return n(["Rad"])},system:e=>{const{normalize:n}=e;return n(["System"])},dirDictionary:e=>{const{normalize:n}=e;return n(["Dir Scan"])},portDictionary:e=>{const{normalize:n}=e;return n(["Port"])},configuration:e=>{const{normalize:n}=e;return n(["Configuration"])},login:e=>{const{normalize:n}=e;return n(["Login"])},pluginsManager:e=>{const{normalize:n}=e;return n(["Plugins Manager"])},dashboard:e=>{const{normalize:n}=e;return n(["Dashboard"])},document:e=>{const{normalize:n}=e;return n(["Document"])}},permission:{hasPermission:e=>{const{normalize:n}=e;return n(["Please set the operation permission value"])}},dashboard:{totalAssets:e=>{const{normalize:n}=e;return n(["Total assets"])},subDomain:e=>{const{normalize:n}=e;return n(["Subdomain"])},informationLeakage:e=>{const{normalize:n}=e;return n(["Information leakage"])},URL:e=>{const{normalize:n}=e;return n(["URL"])},nodeInfo:e=>{const{normalize:n}=e;return n(["Node Status Information"])},taskInfo:e=>{const{normalize:n}=e;return n(["Task Information"])}},node:{nodeName:e=>{const{normalize:n}=e;return n(["Node Name"])},nodeStatus:e=>{const{normalize:n}=e;return n(["Node Status"])},taskCount:e=>{const{normalize:n}=e;return n(["Task Count"])},finished:e=>{const{normalize:n}=e;return n(["finished Count"])},statusRun:e=>{const{normalize:n}=e;return n(["Running"])},statusStop:e=>{const{normalize:n}=e;return n(["Stop"])},statusError:e=>{const{normalize:n}=e;return n(["Not connect"])},nodeUsageStatus:e=>{const{normalize:n}=e;return n(["Node usage status"])},nodeUsageCpu:e=>{const{normalize:n}=e;return n(["CPU"])},nodeUsageMemory:e=>{const{normalize:n}=e;return n(["Memory"])},nodeUsageLoad:e=>{const{normalize:n}=e;return n(["Load"])},createTime:e=>{const{normalize:n}=e;return n(["Create Time"])},updateTime:e=>{const{normalize:n}=e;return n(["Update Time"])},log:e=>{const{normalize:n}=e;return n(["Log"])},onlineNodeMsg:e=>{const{normalize:n}=e;return n(["No scanning node is connected"])},plugin:e=>{const{normalize:n}=e;return n(["Plugin"])},restart:e=>{const{normalize:n}=e;return n(["Restart"])},restartMsg:e=>{const{normalize:n}=e;return n(["Only supports Docker"])}},task:{taskName:e=>{const{normalize:n}=e;return n(["Task Name"])},taskCount:e=>{const{normalize:n}=e;return n(["Task Count"])},taskProgress:e=>{const{normalize:n}=e;return n(["Task Progress"])},createTime:e=>{const{normalize:n}=e;return n(["Create Time"])},typeTask:e=>{const{normalize:n}=e;return n(["Task Type"])},endTime:e=>{const{normalize:n}=e;return n(["End Time"])},addTask:e=>{const{normalize:n}=e;return n(["Add Task"])},ignore:e=>{const{normalize:n}=e;return n(["Ignore target"])},addURL:e=>{const{normalize:n}=e;return n(["Add URL"])},delURL:e=>{const{normalize:n}=e;return n(["Delete URL"])},retest:e=>{const{normalize:n}=e;return n(["Retest"])},delTask:e=>{const{normalize:n}=e;return n(["Delete Task"])},delAsset:e=>{const{normalize:n}=e;return n(["Delete assets at the same time:"])},lastTime:e=>{const{normalize:n}=e;return n(["Last Run"])},nextTime:e=>{const{normalize:n}=e;return n(["Next Run"])},taskCycle:e=>{const{normalize:n}=e;return n(["Task Cycle"])},msgTaskName:e=>{const{normalize:n}=e;return n(["Please enter the task name"])},taskTarget:e=>{const{normalize:n}=e;return n(["Target"])},subdomainTakeover:e=>{const{normalize:n}=e;return n(["Subdomain Takeover"])},assetMapping:e=>{const{normalize:n}=e;return n(["Asset Mapping"])},msgTarget:e=>{const{normalize:n}=e;return n(["Please enter the target, one per line. \n192.168.1.1-192.168.1.253\n192.168.1.1/24\nexample.com\nCIDR:192.168.0.0/18 (This method will scan the network segment in one node, and the fast scan will survive.)\nCMP:xxx Company (Company Name)\nICP: Beijing ICP Certificate xxxx No. (ICP Registration No.)\nAPP:xxx (APP Name)\nAPP-ID:com.xx.xx (app package name)\n"])},ignoreMsg:e=>{const{normalize:n}=e;return n(["Ignore the target, one per line. \n192.168.1.1-192.168.1.253\n192.168.1.1/24\n*.example.com\nThe domain name format needs to include a wildcard, otherwise it will be judged as identical."])},subdomainScan:e=>{const{normalize:n}=e;return n(["Subdomain Scan"])},nodeMsg:e=>{const{normalize:n}=e;return n(["Please select a scanning node"])},nodeSelect:e=>{const{normalize:n}=e;return n(["Node Selection"])},config:e=>{const{normalize:n}=e;return n(["Config"])},url:e=>{const{normalize:n}=e;return n(["URL"])},msgUrl:e=>{const{normalize:n}=e;return n(["Get more page entries"])},sensitiveInfoScan:e=>{const{normalize:n}=e;return n(["SensitiveInfo Scan"])},msgPageMonitoringAll:e=>{const{normalize:n}=e;return n(["Monitoring all pages"])},msgPageMonitoringJs:e=>{const{normalize:n}=e;return n(["Monitoring JS pages only"])},msgCrawler:e=>{const{normalize:n}=e;return n(["The crawler obtains get and post parameters. It is recommended to enable URL scanning to obtain more website entries."])},vulScan:e=>{const{normalize:n}=e;return n(["Vulnerability Scan"])},vulList:e=>{const{normalize:n}=e;return n(["Vul List"])},save:e=>{const{normalize:n}=e;return n(["Save"])},data:e=>{const{normalize:n}=e;return n(["Data"])},duplicatesSubdomain:e=>{const{normalize:n}=e;return n(["Subdomain Duplicates"])},duplicates:e=>{const{normalize:n}=e;return n(["Duplicates"])},portScan:e=>{const{normalize:n}=e;return n(["Port Scan"])},portSelect:e=>{const{normalize:n}=e;return n(["Select Port"])},selectNodeMsg:e=>{const{normalize:n}=e;return n(["When a new node is registered, tasks will be automatically added to the new node"])},duplicatesMsg:e=>{const{normalize:n}=e;return n(["Subdomain names that have been queried in history will be skipped."])},waybackUrlMsg:e=>{const{normalize:n}=e;return n(["Get URL from Wayback Machine."])},addPageMonitTask:e=>{const{normalize:n}=e;return n(["New Page Monitoring"])},duplicatesPort:e=>{const{normalize:n}=e;return n(["Port Duplicates"])},duplicatesPortMsg:e=>{const{normalize:n}=e;return n(["Scan only undiscovered ports"])},runNow:e=>{const{normalize:n}=e;return n(["Run Now"])},templateName:e=>{const{normalize:n}=e;return n(["Template Name"])},addTemplate:e=>{const{normalize:n}=e;return n(["Add Template"])},editTemplate:e=>{const{normalize:n}=e;return n(["Edit Template"])},deleteTemplate:e=>{const{normalize:n}=e;return n(["Del Template"])},autoNode:e=>{const{normalize:n}=e;return n(["Auto Join"])},running:e=>{const{normalize:n}=e;return n(["Running"])},stop:e=>{const{normalize:n}=e;return n(["Stop"])},start:e=>{const{normalize:n}=e;return n(["Start"])},finish:e=>{const{normalize:n}=e;return n(["Finish"])},result:e=>{const{normalize:n}=e;return n(["Result"])},select:e=>{const{normalize:n}=e;return n(["Select data"])},targetNumber:e=>{const{normalize:n}=e;return n(["Target number"])},targetSource:e=>{const{normalize:n}=e;return n(["Target Source"])},general:e=>{const{normalize:n}=e;return n(["General"])},fromAsset:e=>{const{normalize:n}=e;return n(["From Asset"])},fromSubdomain:e=>{const{normalize:n}=e;return n(["From Subdomain"])},fromRootDomain:e=>{const{normalize:n}=e;return n(["From Root Domain"])},fromProject:e=>{const{normalize:n}=e;return n(["From Project"])},targetProject:e=>{const{normalize:n}=e;return n(["Select Project"])},search:e=>{const{normalize:n}=e;return n(["Search Statement"])},daily:e=>{const{normalize:n}=e;return n(["Daily"])},ndays:e=>{const{normalize:n}=e;return n(["N Days"])},hourly:e=>{const{normalize:n}=e;return n(["Hourly"])},nhours:e=>{const{normalize:n}=e;return n(["N Hours"])},nminutes:e=>{const{normalize:n}=e;return n(["N Minutes"])},weekly:e=>{const{normalize:n}=e;return n(["Weekly"])},monthly:e=>{const{normalize:n}=e;return n(["Monthly"])},day:e=>{const{normalize:n}=e;return n(["Day"])},hour:e=>{const{normalize:n}=e;return n(["Hour"])},minute:e=>{const{normalize:n}=e;return n(["Minute"])},monday:e=>{const{normalize:n}=e;return n(["monday"])},tuesday:e=>{const{normalize:n}=e;return n(["tuesday"])},wednesday:e=>{const{normalize:n}=e;return n(["wednesday"])},thursday:e=>{const{normalize:n}=e;return n(["thursday"])},friday:e=>{const{normalize:n}=e;return n(["friday"])},saturday:e=>{const{normalize:n}=e;return n(["saturday"])},sunday:e=>{const{normalize:n}=e;return n(["sunday"])},bindProject:e=>{const{normalize:n}=e;return n(["Bind Project"])},addScheduled:e=>{const{normalize:n}=e;return n(["Create scheduled task"])},syncToProject:e=>{const{normalize:n}=e;return n(["Sync To Project"])},syncToExisting:e=>{const{normalize:n}=e;return n(["Existing projects"])},createNewProject:e=>{const{normalize:n}=e;return n(["Create New Project"])},selectProject:e=>{const{normalize:n}=e;return n(["Select Project"])}},scanTemplate:{TargetHandler:e=>{const{normalize:n}=e;return n(["Target Processing"])},SubdomainScan:e=>{const{normalize:n}=e;return n(["Subdomain Scan"])},SubdomainSecurity:e=>{const{normalize:n}=e;return n(["Domain Security Detection"])},PortScanPreparation:e=>{const{normalize:n}=e;return n(["Port Scan Preprocessing"])},PortScan:e=>{const{normalize:n}=e;return n(["Port Scan"])},AssetMapping:e=>{const{normalize:n}=e;return n(["Asset Mapping"])},URLScan:e=>{const{normalize:n}=e;return n(["URL Scan"])},WebCrawler:e=>{const{normalize:n}=e;return n(["Crawler"])},DirScan:e=>{const{normalize:n}=e;return n(["Catalog Scan"])},VulnerabilityScan:e=>{const{normalize:n}=e;return n(["Vulnerability Scan"])},AssetHandle:e=>{const{normalize:n}=e;return n(["Asset Processing"])},PortFingerprint:e=>{const{normalize:n}=e;return n(["Port Fingerprint Recognition"])},URLSecurity:e=>{const{normalize:n}=e;return n(["URL Security Check"])},PassiveScan:e=>{const{normalize:n}=e;return n(["PassiveScan"])}},asset:{assetName:e=>{const{normalize:n}=e;return n(["Asset"])},banner:e=>{const{normalize:n}=e;return n(["Banner"])},products:e=>{const{normalize:n}=e;return n(["Products"])},IP:e=>{const{normalize:n}=e;return n(["IP"])},domain:e=>{const{normalize:n}=e;return n(["Domain"])},port:e=>{const{normalize:n}=e;return n(["Port"])},service:e=>{const{normalize:n}=e;return n(["Service"])},title:e=>{const{normalize:n}=e;return n(["Title"])},status:e=>{const{normalize:n}=e;return n(["Status"])},time:e=>{const{normalize:n}=e;return n(["time"])},total:e=>{const{normalize:n}=e;return n(["Total"])},p:e=>{const{normalize:n}=e;return n([])},result:e=>{const{normalize:n}=e;return n(["results"])},detail:e=>{const{normalize:n}=e;return n(["Detail"])},assetDetail:e=>{const{normalize:n}=e;return n(["Asset Detail"])},assetTotalNum:e=>{const{normalize:n}=e;return n(["Total Assets"])},responseHeader:e=>{const{normalize:n}=e;return n(["Response Header"])},responseBody:e=>{const{normalize:n}=e;return n(["Response Body"])},historyDiff:e=>{const{normalize:n}=e;return n(["Historical changes"])},export:e=>{const{normalize:n}=e;return n(["Export"])},screenshot:e=>{const{normalize:n}=e;return n(["Screenshot"])},Chart:e=>{const{normalize:n}=e;return n(["Chart"])}},subdomain:{subdomainName:e=>{const{normalize:n}=e;return n(["Subdomain"])},recordType:e=>{const{normalize:n}=e;return n(["Record Type"])},recordValue:e=>{const{normalize:n}=e;return n(["Record Value"])}},rootDomain:{rootDomainName:e=>{const{normalize:n}=e;return n(["Root Domain"])},company:e=>{const{normalize:n}=e;return n(["Company"])}},app:{appName:e=>{const{normalize:n}=e;return n(["APP"])},name:e=>{const{normalize:n}=e;return n(["Name"])},category:e=>{const{normalize:n}=e;return n(["Category"])},description:e=>{const{normalize:n}=e;return n(["Description"])}},miniProgram:{miniProgramName:e=>{const{normalize:n}=e;return n(["Mini Program"])}},URL:{URLName:e=>{const{normalize:n}=e;return n(["URL"])},source:e=>{const{normalize:n}=e;return n(["Source"])},type:e=>{const{normalize:n}=e;return n(["Type"])},input:e=>{const{normalize:n}=e;return n(["Input"])}},crawler:{crawlerName:e=>{const{normalize:n}=e;return n(["Crawler"])},getParameter:e=>{const{normalize:n}=e;return n(["Get Parameter"])},postParameter:e=>{const{normalize:n}=e;return n(["Post Parameter"])}},sensitiveInformation:{sensitiveInformationName:e=>{const{normalize:n}=e;return n(["Sensitive Information"])},sensitiveName:e=>{const{normalize:n}=e;return n(["Name"])},sensitiveColor:e=>{const{normalize:n}=e;return n(["Color"])},sensitiveRegular:e=>{const{normalize:n}=e;return n(["Regular"])},sensitiveNameMsg:e=>{const{normalize:n}=e;return n(["Please enter a rule name"])},sensitiveRegularMsg:e=>{const{normalize:n}=e;return n(["Please enter a regular expression"])},sensAggre:e=>{const{normalize:n}=e;return n(["Aggregation of sensitive information names"])}},dirScan:{dirScanName:e=>{const{normalize:n}=e;return n(["DirScan"])},title:e=>{const{normalize:n}=e;return n(["Title"])},status:e=>{const{normalize:n}=e;return n(["Status"])},length:e=>{const{normalize:n}=e;return n(["Length"])}},vulnerability:{vulnerabilityName:e=>{const{normalize:n}=e;return n(["Vulnerability"])}},PageMonitoring:{pageMonitoringName:e=>{const{normalize:n}=e;return n(["Page Monitoring"])},oldResponseBody:e=>{const{normalize:n}=e;return n(["Old ResponseBody"])},currentResponseBody:e=>{const{normalize:n}=e;return n(["Current ResponseBody"])},statusCode:e=>{const{normalize:n}=e;return n(["Response Code"])},hash:e=>{const{normalize:n}=e;return n(["Body Hash"])},similarity:e=>{const{normalize:n}=e;return n(["Similarity"])}},project:{project:e=>{const{normalize:n}=e;return n(["Project"])},addProject:e=>{const{normalize:n}=e;return n(["Add Project"])},totalAssets:e=>{const{normalize:n}=e;return n(["Total Assets"])},projectName:e=>{const{normalize:n}=e;return n(["Project Name"])},msgProject:e=>{const{normalize:n}=e;return n(["Please enter the project name"])},projectScope:e=>{const{normalize:n}=e;return n(["Project Scope"])},msgProjectScope:e=>{const{normalize:n}=e;return n(["Enter the root domain name of the project, one per line."])},msgScheduledTasks:e=>{const{normalize:n}=e;return n(["Scheduled scanning"])},scheduledTasks:e=>{const{normalize:n}=e;return n(["Scheduled Tasks"])},cycle:e=>{const{normalize:n}=e;return n(["Monitoring Cycle"])},projectDetail:e=>{const{normalize:n}=e;return n(["Project Detail"])},CreatTime:e=>{const{normalize:n}=e;return n(["Creat Time"])},msgProjectTag:e=>{const{normalize:n}=e;return n(["Please enter the project TAG"])},aggregation:e=>{const{normalize:n}=e;return n(["Aggregation"])},overview:e=>{const{normalize:n}=e;return n(["Overview"])}},poc:{pocName:e=>{const{normalize:n}=e;return n(["POC Name"])},content:e=>{const{normalize:n}=e;return n(["POC Content"])},level:e=>{const{normalize:n}=e;return n(["Risk Level"])},critical:e=>{const{normalize:n}=e;return n(["Critical"])},high:e=>{const{normalize:n}=e;return n(["High"])},medium:e=>{const{normalize:n}=e;return n(["Medium"])},low:e=>{const{normalize:n}=e;return n(["Low"])},info:e=>{const{normalize:n}=e;return n(["Info"])},unknown:e=>{const{normalize:n}=e;return n(["Unknown"])},nameMsg:e=>{const{normalize:n}=e;return n(["Please enter the POC name. The POC name and the name in the POC content should be consistent."])},contentMsg:e=>{const{normalize:n}=e;return n(["Please enter poc content"])}},configuration:{subfinder:e=>{const{normalize:n}=e;return n(["subfinder configuration"])},rad:e=>{const{normalize:n}=e;return n(["rad configuration"])},system:e=>{const{normalize:n}=e;return n(["system configuration"])},timezone:e=>{const{normalize:n}=e;return n(["Timezone"])},maxTaskNum:e=>{const{normalize:n}=e;return n(["Max Task Num"])},dirScanThread:e=>{const{normalize:n}=e;return n(["DirScan Thread Num"])},portScanThread:e=>{const{normalize:n}=e;return n(["PortScan Thread Num"])},crawlerThread:e=>{const{normalize:n}=e;return n(["Crawlers Thread Num"])},urlThread:e=>{const{normalize:n}=e;return n(["URL Scan Thread Num"])},maxUrlNum:e=>{const{normalize:n}=e;return n(["Max URL Num"])},noticeConfig:e=>{const{normalize:n}=e;return n(["Notification Configuration"])},newWebhookConfig:e=>{const{normalize:n}=e;return n(["New configuration"])},threadMsg:e=>{const{normalize:n}=e;return n(["Please configure the number of concurrencies according to the system memory"])},noticeHelp:e=>{const{normalize:n}=e;return n(['*msg* is the message parameter position. eg\\:http://example.com?msg=*mes* or POST "msg":"*msg*"'])},duplicationconfiguration:e=>{const{normalize:n}=e;return n(["Deduplication Configuration"])},deduplicationHour:e=>{const{normalize:n}=e;return n(["Deduplication cycle"])},deduplicationFlag:e=>{const{normalize:n}=e;return n(["Deduplication switch"])},runNowOne:e=>{const{normalize:n}=e;return n(["Run once immediately"])}},form:{input:e=>{const{normalize:n}=e;return n(["Search:"])}},portDict:{name:e=>{const{normalize:n}=e;return n(["Name"])},value:e=>{const{normalize:n}=e;return n(["Value"])},nameMsg:e=>{const{normalize:n}=e;return n(["Please enter name"])},valueMsg:e=>{const{normalize:n}=e;return n(["Please enter value"])}},fingerprint:{name:e=>{const{normalize:n}=e;return n(["Name"])},rule:e=>{const{normalize:n}=e;return n(["Rule"])},category:e=>{const{normalize:n}=e;return n(["Category"])},nameMsg:e=>{const{normalize:n}=e;return n(["Please enter fingerprint name"])},ruleMsg:e=>{const{normalize:n}=e;return n(["Please enter rule content"])},parentCategory:e=>{const{normalize:n}=e;return n(["Parent Category"])},amount:e=>{const{normalize:n}=e;return n(["Asset Quantity"])}},plugin:{name:e=>{const{normalize:n}=e;return n(["Name"])},version:e=>{const{normalize:n}=e;return n(["Version"])},parameter:e=>{const{normalize:n}=e;return n(["Parameter"])},introduction:e=>{const{normalize:n}=e;return n(["Introduction"])},new:e=>{const{normalize:n}=e;return n(["New Plugin"])},delete:e=>{const{normalize:n}=e;return n(["Delete Plugin"])},module:e=>{const{normalize:n}=e;return n(["Module"])},help:e=>{const{normalize:n}=e;return n(["Parameter Help"])},source:e=>{const{normalize:n}=e;return n(["Source"])},isSystem:e=>{const{normalize:n}=e;return n(["isSystem"])},market:e=>{const{normalize:n}=e;return n(["Plugin Market"])},import:e=>{const{normalize:n}=e;return n(["Import"])},key:e=>{const{normalize:n}=e;return n(["Plugin Key"])},keyMsg:e=>{const{normalize:n}=e;return n(["Enter the plugin key, view the server running log, or check the PLUGINKEYfile in the project running root directory."])},reInstall:e=>{const{normalize:n}=e;return n(["Reinstall"])},reCheck:e=>{const{normalize:n}=e;return n(["Recheck"])},uninstall:e=>{const{normalize:n}=e;return n(["Uninstall"])}},workplace:{goodMorning:e=>{const{normalize:n}=e;return n(["Good morning"])},happyDay:e=>{const{normalize:n}=e;return n(["Wish you happy every day!"])},toady:e=>{const{normalize:n}=e;return n(["It's sunny today"])},project:e=>{const{normalize:n}=e;return n(["Project"])},access:e=>{const{normalize:n}=e;return n(["Project access"])},toDo:e=>{const{normalize:n}=e;return n(["To do"])},introduction:e=>{const{normalize:n}=e;return n(["A serious introduction"])},more:e=>{const{normalize:n}=e;return n(["More"])},shortcutOperation:e=>{const{normalize:n}=e;return n(["Shortcut operation"])},operation:e=>{const{normalize:n}=e;return n(["Operation"])},index:e=>{const{normalize:n}=e;return n(["Index"])},personal:e=>{const{normalize:n}=e;return n(["Personal"])},team:e=>{const{normalize:n}=e;return n(["Team"])},quote:e=>{const{normalize:n}=e;return n(["Quote"])},contribution:e=>{const{normalize:n}=e;return n(["Contribution"])},hot:e=>{const{normalize:n}=e;return n(["Hot"])},yield:e=>{const{normalize:n}=e;return n(["Yield"])},dynamic:e=>{const{normalize:n}=e;return n(["Dynamic"])},push:e=>{const{normalize:n}=e;return n(["push"])},pushCode:e=>{const{normalize:n}=e;return n(["Archer push code to Github"])},follow:e=>{const{normalize:n}=e;return n(["Follow"])}},formDemo:{input:e=>{const{normalize:n}=e;return n(["Input"])},inputNumber:e=>{const{normalize:n}=e;return n(["InputNumber"])},default:e=>{const{normalize:n}=e;return n(["Default"])},icon:e=>{const{normalize:n}=e;return n(["Icon"])},mixed:e=>{const{normalize:n}=e;return n(["Mixed"])},password:e=>{const{normalize:n}=e;return n(["Password"])},textarea:e=>{const{normalize:n}=e;return n(["Textarea"])},remoteSearch:e=>{const{normalize:n}=e;return n(["Remote search"])},slot:e=>{const{normalize:n}=e;return n(["Slot"])},position:e=>{const{normalize:n}=e;return n(["Position"])},autocomplete:e=>{const{normalize:n}=e;return n(["Autocomplete"])},select:e=>{const{normalize:n}=e;return n(["Select"])},optionSlot:e=>{const{normalize:n}=e;return n(["Option Slot"])},selectGroup:e=>{const{normalize:n}=e;return n(["Select Group"])},selectV2:e=>{const{normalize:n}=e;return n(["SelectV2"])},cascader:e=>{const{normalize:n}=e;return n(["Cascader"])},switch:e=>{const{normalize:n}=e;return n(["Switch"])},rate:e=>{const{normalize:n}=e;return n(["Rate"])},colorPicker:e=>{const{normalize:n}=e;return n(["Color Picker"])},transfer:e=>{const{normalize:n}=e;return n(["Transfer"])},render:e=>{const{normalize:n}=e;return n(["Render"])},radio:e=>{const{normalize:n}=e;return n(["Radio"])},radioGroup:e=>{const{normalize:n}=e;return n(["Radio Group"])},button:e=>{const{normalize:n}=e;return n(["Button"])},checkbox:e=>{const{normalize:n}=e;return n(["Checkbox"])},checkboxButton:e=>{const{normalize:n}=e;return n(["Checkbox Button"])},checkboxGroup:e=>{const{normalize:n}=e;return n(["Checkbox Group"])},slider:e=>{const{normalize:n}=e;return n(["Slider"])},datePicker:e=>{const{normalize:n}=e;return n(["Date Picker"])},shortcuts:e=>{const{normalize:n}=e;return n(["Shortcuts"])},today:e=>{const{normalize:n}=e;return n(["Today"])},yesterday:e=>{const{normalize:n}=e;return n(["Yesterday"])},aWeekAgo:e=>{const{normalize:n}=e;return n(["A week ago"])},week:e=>{const{normalize:n}=e;return n(["Week"])},year:e=>{const{normalize:n}=e;return n(["Year"])},month:e=>{const{normalize:n}=e;return n(["Month"])},dates:e=>{const{normalize:n}=e;return n(["Dates"])},daterange:e=>{const{normalize:n}=e;return n(["Date Range"])},monthrange:e=>{const{normalize:n}=e;return n(["Month Range"])},dateTimePicker:e=>{const{normalize:n}=e;return n(["DateTimePicker"])},dateTimerange:e=>{const{normalize:n}=e;return n(["Datetime Range"])},timePicker:e=>{const{normalize:n}=e;return n(["Time Picker"])},timeSelect:e=>{const{normalize:n}=e;return n(["Time Select"])},inputPassword:e=>{const{normalize:n}=e;return n(["input Password"])},passwordStrength:e=>{const{normalize:n}=e;return n(["Password Strength"])},defaultForm:e=>{const{normalize:n}=e;return n(["All examples"])},formDes:e=>{const{normalize:n}=e;return n(["The secondary encapsulation of form components based on ElementPlus realizes data-driven and supports all Form parameters"])},example:e=>{const{normalize:n}=e;return n(["example"])},operate:e=>{const{normalize:n}=e;return n(["operate"])},change:e=>{const{normalize:n}=e;return n(["Change"])},restore:e=>{const{normalize:n}=e;return n(["Restore"])},disabled:e=>{const{normalize:n}=e;return n(["Disabled"])},disablement:e=>{const{normalize:n}=e;return n(["Disablement"])},delete:e=>{const{normalize:n}=e;return n(["Delete"])},add:e=>{const{normalize:n}=e;return n(["Add"])},setValue:e=>{const{normalize:n}=e;return n(["Set value"])},resetValue:e=>{const{normalize:n}=e;return n(["Reset value"])},set:e=>{const{normalize:n}=e;return n(["Set"])},subitem:e=>{const{normalize:n}=e;return n(["Subitem"])},formValidation:e=>{const{normalize:n}=e;return n(["Form validation"])},verifyReset:e=>{const{normalize:n}=e;return n(["Verify reset"])},richText:e=>{const{normalize:n}=e;return n(["Rich text"])},jsonEditor:e=>{const{normalize:n}=e;return n(["JSON Editor"])},form:e=>{const{normalize:n}=e;return n(["Form"])},remoteLoading:e=>{const{normalize:n}=e;return n(["Remote loading"])},focus:e=>{const{normalize:n}=e;return n(["Focus"])},treeSelect:e=>{const{normalize:n}=e;return n(["Tree Select"])},showCheckbox:e=>{const{normalize:n}=e;return n(["Show Checkbox"])},selectAnyLevel:e=>{const{normalize:n}=e;return n(["Select Any Level"])},multiple:e=>{const{normalize:n}=e;return n(["Multiple"])},filterable:e=>{const{normalize:n}=e;return n(["Filterable"])},customContent:e=>{const{normalize:n}=e;return n(["Custom content"])},lazyLoad:e=>{const{normalize:n}=e;return n(["Lazy load"])},upload:e=>{const{normalize:n}=e;return n(["Upload"])},userAvatar:e=>{const{normalize:n}=e;return n(["User avatar"])},iconPicker:e=>{const{normalize:n}=e;return n(["Icon picker"])}},guideDemo:{guide:e=>{const{normalize:n}=e;return n(["Guide"])},start:e=>{const{normalize:n}=e;return n(["Start"])},message:e=>{const{normalize:n}=e;return n(["The guide page is very useful for some people who enter the project for the first time. You can briefly introduce the functions of the project. The boot page is based on driver.js"])}},iconDemo:{icon:e=>{const{normalize:n}=e;return n(["Icon"])},localIcon:e=>{const{normalize:n}=e;return n(["Local Icon"])},iconify:e=>{const{normalize:n}=e;return n(["Iconify component"])},recommendedUse:e=>{const{normalize:n}=e;return n(["Recommended use"])},recommendeDes:e=>{const{normalize:n}=e;return n(["Iconify component basically contains all icons. You can query any icon you want. And packaging will only package the icons used."])},accessAddress:e=>{const{normalize:n}=e;return n(["Access address"])}},echartDemo:{echart:e=>{const{normalize:n}=e;return n(["Echart"])},echartDes:e=>{const{normalize:n}=e;return n(["Based on the secondary packaging components of eckarts, the width is adaptive. The corresponding chart can be displayed by passing in the options and height attributes."])}},countToDemo:{countTo:e=>{const{normalize:n}=e;return n(["CountTo"])},countToDes:e=>{const{normalize:n}=e;return n(["The transformation is based on vue-count-to and supports all vue-count-to parameters."])},suffix:e=>{const{normalize:n}=e;return n(["Suffix"])},prefix:e=>{const{normalize:n}=e;return n(["Prefix"])},separator:e=>{const{normalize:n}=e;return n(["Separator"])},duration:e=>{const{normalize:n}=e;return n(["Duration"])},endVal:e=>{const{normalize:n}=e;return n(["End val"])},startVal:e=>{const{normalize:n}=e;return n(["Start val"])},start:e=>{const{normalize:n}=e;return n(["Start"])},pause:e=>{const{normalize:n}=e;return n(["Pause"])},resume:e=>{const{normalize:n}=e;return n(["Resume"])}},watermarkDemo:{watermark:e=>{const{normalize:n}=e;return n(["Watermark"])},createdWatermark:e=>{const{normalize:n}=e;return n(["Created watermark"])},clearWatermark:e=>{const{normalize:n}=e;return n(["Clear watermark"])},resetWatermark:e=>{const{normalize:n}=e;return n(["Reset watermark"])}},qrcodeDemo:{qrcode:e=>{const{normalize:n}=e;return n(["Qrcode"])},qrcodeDes:e=>{const{normalize:n}=e;return n(["Secondary packaging based on qrcode"])},basicUsage:e=>{const{normalize:n}=e;return n(["Basic usage"])},imgTag:e=>{const{normalize:n}=e;return n(["Img tag"])},style:e=>{const{normalize:n}=e;return n(["Style config"])},click:e=>{const{normalize:n}=e;return n(["Click event"])},asynchronousContent:e=>{const{normalize:n}=e;return n(["Asynchronous content"])},invalid:e=>{const{normalize:n}=e;return n(["Invalid"])},logoConfig:e=>{const{normalize:n}=e;return n(["Logo config"])},logoStyle:e=>{const{normalize:n}=e;return n(["Logo style"])},size:e=>{const{normalize:n}=e;return n(["size config"])}},highlightDemo:{highlight:e=>{const{normalize:n}=e;return n(["Highlight"])},message:e=>{const{normalize:n}=e;return n(["The best time to plant a tree is ten years ago, followed by now."])},keys1:e=>{const{normalize:n}=e;return n(["ten years ago"])},keys2:e=>{const{normalize:n}=e;return n(["now"])}},infotipDemo:{infotip:e=>{const{normalize:n}=e;return n(["Infotip"])},infotipDes:e=>{const{normalize:n}=e;return n(["Secondary packaging of components based on Highlight"])},title:e=>{const{normalize:n}=e;return n(["matters needing attention"])}},levelDemo:{menu:e=>{const{normalize:n}=e;return n(["Multi level menu cache"])}},searchDemo:{search:e=>{const{normalize:n}=e;return n(["Search"])},searchDes:e=>{const{normalize:n}=e;return n(["Based on the secondary encapsulation of form components, the functions of query and reset are realized"])},operate:e=>{const{normalize:n}=e;return n(["operate"])},change:e=>{const{normalize:n}=e;return n(["Change"])},grid:e=>{const{normalize:n}=e;return n(["grid"])},button:e=>{const{normalize:n}=e;return n(["Button"])},restore:e=>{const{normalize:n}=e;return n(["Restore"])},inline:e=>{const{normalize:n}=e;return n(["inline"])},bottom:e=>{const{normalize:n}=e;return n(["Bottom"])},position:e=>{const{normalize:n}=e;return n(["position"])},left:e=>{const{normalize:n}=e;return n(["left"])},center:e=>{const{normalize:n}=e;return n(["center"])},right:e=>{const{normalize:n}=e;return n(["right"])},dynamicOptions:e=>{const{normalize:n}=e;return n(["Dynamic options"])},deleteRadio:e=>{const{normalize:n}=e;return n(["Delete radio"])},restoreRadio:e=>{const{normalize:n}=e;return n(["Restore radio"])},loading:e=>{const{normalize:n}=e;return n(["Loading"])},reset:e=>{const{normalize:n}=e;return n(["Reset"])}},stickyDemo:{sticky:e=>{const{normalize:n}=e;return n(["Sticky"])}},tableDemo:{table:e=>{const{normalize:n}=e;return n(["Table"])},tableDes:e=>{const{normalize:n}=e;return n(["Secondary packaging of Table components based on ElementPlus"])},index:e=>{const{normalize:n}=e;return n(["Index"])},title:e=>{const{normalize:n}=e;return n(["Title"])},author:e=>{const{normalize:n}=e;return n(["Author"])},displayTime:e=>{const{normalize:n}=e;return n(["Display time"])},importance:e=>{const{normalize:n}=e;return n(["Importance"])},pageviews:e=>{const{normalize:n}=e;return n(["Pageviews"])},action:e=>{const{normalize:n}=e;return n(["Action"])},important:e=>{const{normalize:n}=e;return n(["Important"])},good:e=>{const{normalize:n}=e;return n(["Good"])},commonly:e=>{const{normalize:n}=e;return n(["Commonly"])},operate:e=>{const{normalize:n}=e;return n(["operate"])},example:e=>{const{normalize:n}=e;return n(["example"])},show:e=>{const{normalize:n}=e;return n(["Show"])},hidden:e=>{const{normalize:n}=e;return n(["Hidden"])},pagination:e=>{const{normalize:n}=e;return n(["pagination"])},reserveIndex:e=>{const{normalize:n}=e;return n(["Reserve index"])},restoreIndex:e=>{const{normalize:n}=e;return n(["Restore index"])},showSelections:e=>{const{normalize:n}=e;return n(["Show selections"])},hiddenSelections:e=>{const{normalize:n}=e;return n(["Restore selections"])},showExpandedRows:e=>{const{normalize:n}=e;return n(["Show expanded rows"])},hiddenExpandedRows:e=>{const{normalize:n}=e;return n(["Hidden expanded rows"])},changeTitle:e=>{const{normalize:n}=e;return n(["Change title"])},header:e=>{const{normalize:n}=e;return n(["Header"])},selectAllNone:e=>{const{normalize:n}=e;return n(["Select all / none"])},delOrAddAction:e=>{const{normalize:n}=e;return n(["Delete or add action"])},showOrHiddenStripe:e=>{const{normalize:n}=e;return n(["Show or hidden stripe"])},showOrHiddenBorder:e=>{const{normalize:n}=e;return n(["Show or hidden border"])},fixedHeaderOrAuto:e=>{const{normalize:n}=e;return n(["Fixed header or auto"])},getSelections:e=>{const{normalize:n}=e;return n(["Get selections"])},preview:e=>{const{normalize:n}=e;return n(["Preview"])},showOrHiddenSortable:e=>{const{normalize:n}=e;return n(["Show or hidden sortable"])},videoPreview:e=>{const{normalize:n}=e;return n(["Video preview"])},cardTable:e=>{const{normalize:n}=e;return n(["Card table"])}}};export{e as default};
