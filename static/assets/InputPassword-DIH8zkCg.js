import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as a,l,r as s,o as t,i as o,w as u,e as p,a as m}from"./index-C6fb_XFi.js";import{I as r}from"./InputPassword-ywconkEY.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";const d=a({__name:"InputPassword",setup(a){const{t:d}=l(),n=s("");return(a,l)=>(t(),o(m(e),{title:m(d)("inputPasswordDemo.title"),message:m(d)("inputPasswordDemo.inputPasswordDes")},{default:u((()=>[p(m(r),{modelValue:n.value,"onUpdate:modelValue":l[0]||(l[0]=e=>n.value=e),class:"mb-20px"},null,8,["modelValue"]),p(m(r),{modelValue:n.value,"onUpdate:modelValue":l[1]||(l[1]=e=>n.value=e),strength:""},null,8,["modelValue"]),p(m(r),{modelValue:n.value,"onUpdate:modelValue":l[2]||(l[2]=e=>n.value=e),strength:"",disabled:"",class:"mt-20px"},null,8,["modelValue"])])),_:1},8,["title","message"]))}});export{d as default};
