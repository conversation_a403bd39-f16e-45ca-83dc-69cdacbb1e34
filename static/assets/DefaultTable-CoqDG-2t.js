import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,l as a,v as o,e as l,G as i,r as s,o as r,i as m,w as p,a as n,J as d}from"./index-C6fb_XFi.js";import{_ as c}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{a as b}from"./index-Bh6CT4kq.js";import{E as j}from"./el-tag-C_oEQYGz.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";const u=t({__name:"DefaultTable",setup(t){const{t:u}=a(),f=[{field:"title",label:u("tableDemo.title")},{field:"author",label:u("tableDemo.author")},{field:"display_time",label:u("tableDemo.displayTime"),sortable:!0},{field:"importance",label:u("tableDemo.importance"),formatter:(e,t,a)=>o(j,{type:1===a?"success":2===a?"warning":"danger"},(()=>u(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")))},{field:"pageviews",label:u("tableDemo.pageviews")},{field:"action",label:u("tableDemo.action"),slots:{default:e=>{let t;return l(i,{type:"primary",onClick:()=>y(e)},"function"==typeof(a=t=u("tableDemo.action"))||"[object Object]"===Object.prototype.toString.call(a)&&!d(a)?t:{default:()=>[t]});var a}}}],g=s(!0);let _=s([]);(async e=>{const t=await b(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{g.value=!1}));t&&(_.value=t.data.list)})();const y=e=>{};return(t,a)=>(r(),m(n(e),{title:n(u)("tableDemo.table"),message:n(u)("tableDemo.tableDes")},{default:p((()=>[l(n(c),{columns:f,data:n(_),loading:g.value,defaultSort:{prop:"display_time",order:"descending"}},null,8,["data","loading"])])),_:1},8,["title","message"]))}});export{u as default};
