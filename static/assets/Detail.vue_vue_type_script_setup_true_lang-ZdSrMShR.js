import{d as e,dE as a,r as l,s as t,o as s,i,w as o,e as r,a as u,A as n,B as v,z as d,t as m,l as p}from"./index-C6fb_XFi.js";import{a as c,E as g}from"./el-form-C2Y6uNCj.js";import{E as f,a as b}from"./el-col-Dl4_4Pn5.js";import{E as _}from"./el-divider-Bw95UAdD.js";import"./el-tag-C_oEQYGz.js";import"./el-popper-CeVwVUf9.js";import"./el-virtual-list-D7NvYvyu.js";import{E as y}from"./el-select-v2-CaMVABoW.js";import{E as V}from"./el-switch-Bh7JeorW.js";import{r as w}from"./index-CnCQNuY4.js";const h=(e,a,l)=>w.post({url:"/api/sensitive/data",data:{search:e,pageIndex:a,pageSize:l}}),j=e=>w.post({url:"/api/sensitive/delete",data:{ids:e}}),I=(e,a)=>w.post({url:"/api/sensitive/update/state",data:{ids:e,state:a}}),x=e({__name:"Detail",props:{closeDialog:{type:Function},getList:{type:Function},sensitiveForm:{}},setup(e){const{t:h}=p(),j=e,{sensitiveForm:I}=a(j),x=l({...I.value}),E=t({name:[{required:!0,message:h("sensitiveInformation.sensitiveNameMsg"),trigger:"blur"}],regular:[{required:!0,message:h("sensitiveInformation.sensitiveRegularMsg"),trigger:"blur"}]}),k=[{value:"null",label:"null"},{value:"green",label:"green"},{value:"red",label:"red"},{value:"cyan",label:"cyan"},{value:"yellow",label:"yellow"},{value:"orange",label:"orange"},{value:"gray",label:"gray"},{value:"pink",label:"pink"}],F=l(!1),M=l(),N=async e=>{F.value=!0,e&&await e.validate((async(e,a)=>{if(e){let e;e=""!=x.value.id?await(l=x.value.id,t=x.value.name,s=x.value.regular,i=x.value.color,o=x.value.state,w.post({url:"/api/sensitive/update",data:{id:l,name:t,regular:s,color:i,state:o}})):await((e,a,l,t)=>w.post({url:"/api/sensitive/add",data:{name:e,regular:a,color:l,state:t}}))(x.value.name,x.value.regular,x.value.color,x.value.state),200===e.code&&(j.getList(),j.closeDialog()),F.value=!1}else F.value=!1;var l,t,s,i,o}))};return(e,a)=>(s(),i(u(g),{model:x.value,"label-width":"auto",rules:E,"status-icon":"",ref_key:"ruleFormRef",ref:M},{default:o((()=>[r(u(c),{label:u(h)("sensitiveInformation.sensitiveName"),prop:"name"},{default:o((()=>[r(u(n),{modelValue:x.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>x.value.name=e),placeholder:u(h)("sensitiveInformation.sensitiveNameMsg")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),r(u(c),{label:u(h)("sensitiveInformation.sensitiveRegular"),prop:"regular"},{default:o((()=>[r(u(n),{modelValue:x.value.regular,"onUpdate:modelValue":a[1]||(a[1]=e=>x.value.regular=e),placeholder:u(h)("sensitiveInformation.sensitiveRegularMsg")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),r(u(c),{label:u(h)("sensitiveInformation.sensitiveColor")},{default:o((()=>[r(u(y),{modelValue:x.value.color,"onUpdate:modelValue":a[2]||(a[2]=e=>x.value.color=e),placeholder:"Please select color",options:k},null,8,["modelValue"])])),_:1},8,["label"]),r(u(c),{label:u(h)("common.state")},{default:o((()=>[r(u(V),{modelValue:x.value.state,"onUpdate:modelValue":a[3]||(a[3]=e=>x.value.state=e),"inline-prompt":"","active-text":u(h)("common.switchAction"),"inactive-text":u(h)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),r(u(_)),r(u(b),null,{default:o((()=>[r(u(f),{span:2,offset:8},{default:o((()=>[r(u(c),null,{default:o((()=>[r(u(v),{type:"primary",onClick:a[4]||(a[4]=e=>N(M.value)),loading:F.value},{default:o((()=>[d(m(u(h)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"]))}});export{x as _,j as d,h as g,I as u};
