import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,s as l,r as a,y as s,P as o,o as i,c as r,e as p,w as u,a as n,z as m,t as d,A as c,B as j,f as v,i as _,j as f,Q as g,F as y,R as b,l as x,K as w,v as h}from"./index-DfJTpRkj.js";import{E as k,a as A}from"./el-tab-pane-BijWf7kq.js";import{a as D,E as V}from"./el-col-B4Ik8fnS.js";import{E as P}from"./el-switch-C5ZBDFmL.js";import{E}from"./el-text-vKNLRkxx.js";import{E as L,a as R}from"./el-form-DsaI0u2w.js";import{b as I,a as U}from"./el-dropdown-item-nnpzYk3y.js";import"./el-popper-D2BmgSQA.js";import{_ as O}from"./ProjectList.vue_vue_type_style_index_0_lang-CsAdl8VA.js";import{_ as S}from"./AddProject.vue_vue_type_script_setup_true_lang-Blr9b8Xr.js";import{_ as T}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{a as C,d as $}from"./index-jyMftxhc.js";import{u as z}from"./useIcon-CNpM61rT.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./strings-CUyZ1T6U.js";import"./castArray-CvwAI87l.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./el-tag-CbhrEnto.js";import"./index-DE7jtbbk.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./refs-DAMUgizk.js";import"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import"./el-table-column-7FjdLFwR.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./index-CyH6XROR.js";/* empty css                          */import"./el-divider-0NmzbuNU.js";import"./index-B0JD2UFG.js";import"./index-D1ADinPR.js";import"./DetailTemplate-DU3RXrBH.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";/* empty css                */import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";const B={class:"mb-10px"},F=t({__name:"Project",setup(t){const{t:F}=x();let H=l({}),K=a([]),M=l({});const N=a(!1),Q=async(e,t)=>{0===e?(e=q.value,t=X.value):(q.value=e,X.value=t);try{const l=await C(Y.value,e,t);Object.assign(H,l.data.result),K.value=Object.keys(l.data.tag),Object.assign(M,l.data.tag);const a=K.value.indexOf("All");-1!==a&&K.value.splice(a,1)}catch(l){}},G=a(!1),J=async()=>{G.value=!0},W=()=>{G.value=!1},Y=a(""),Z=z({icon:"iconoir:search"}),q=a(1),X=a(50),ee=a(!1),te=async()=>{ee.value=!0,N.value=!0,await Q(q.value,X.value),ee.value=!1,N.value=!1};te();const le=a(!1),ae=z({icon:"openmoji:delete"}),se=async()=>{const e=a(!1);w({title:"Delete",draggable:!0,message:()=>h("div",{style:{display:"flex",alignItems:"center"}},[h("p",{style:{margin:"0 10px 0 0"}},F("task.delAsset")),h(P,{modelValue:e.value,"onUpdate:modelValue":t=>{e.value=t}})])}).then((async()=>{await $(ie.value,e.value),Q(q.value,X.value)}))},oe=z({icon:"ri:arrow-drop-down-line"}),ie=a([]);return(t,l)=>{const a=s("ElIcon"),x=s("ElDropdownMenu"),w=o("loading");return i(),r(y,null,[p(n(e),null,{default:u((()=>[p(n(D),{style:{"margin-bottom":"20px"},gutter:20},{default:u((()=>[p(n(V),{span:.5},{default:u((()=>[p(n(E),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:u((()=>[m(d(n(F)("form.input"))+":",1)])),_:1})])),_:1}),p(n(V),{span:5},{default:u((()=>[p(n(c),{modelValue:Y.value,"onUpdate:modelValue":l[0]||(l[0]=e=>Y.value=e),placeholder:n(F)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),p(n(V),{span:5,style:{position:"relative",left:"16px"}},{default:u((()=>[p(n(j),{loading:ee.value,type:"primary",icon:n(Z),size:"large",style:{height:"100%"},onClick:te},null,8,["loading","icon"])])),_:1})])),_:1}),p(n(D),{style:{"margin-bottom":"0%"}},{default:u((()=>[p(n(V),{span:2},{default:u((()=>[v("div",B,[p(n(j),{type:"primary",onClick:J},{default:u((()=>[m(d(n(F)("project.addProject")),1)])),_:1})])])),_:1}),p(n(V),{span:2},{default:u((()=>[p(n(L),null,{default:u((()=>[p(n(R),{label:n(F)("common.multipleSelection")},{default:u((()=>[p(n(P),{modelValue:le.value,"onUpdate:modelValue":l[1]||(l[1]=e=>le.value=e),class:"mb-2","inline-prompt":"","active-text":"Yes","inactive-text":"No"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),le.value?(i(),_(n(V),{key:0,span:1},{default:u((()=>[p(n(I),{trigger:"click"},{dropdown:u((()=>[p(x,null,{default:u((()=>[p(n(U),{icon:n(ae),onClick:se},{default:u((()=>[m(d(n(F)("common.delete")),1)])),_:1},8,["icon"])])),_:1})])),default:u((()=>[p(n(j),{plain:"",class:"custom-button align-bottom"},{default:u((()=>[m(d(n(F)("common.operation"))+" ",1),p(a,{class:"el-icon--right"},{default:u((()=>[p(n(oe))])),_:1})])),_:1})])),_:1})])),_:1})):f("",!0)])),_:1}),g((i(),_(n(A),{class:"demo-tabs"},{default:u((()=>[p(n(k),{label:`All (${n(M).All})`},{default:u((()=>[p(O,{tableDataList:n(H).All,getProjectTag:Q,total:n(M).All,multipleSelection:le.value,selectedRows:ie.value,"onUpdate:selectedRows":l[2]||(l[2]=e=>ie.value=e)},null,8,["tableDataList","total","multipleSelection","selectedRows"])])),_:1},8,["label"]),(i(!0),r(y,null,b(n(K),(e=>(i(),_(n(k),{label:`${e} (${n(M)[e]})`,key:e},{default:u((()=>[p(O,{tableDataList:n(H)[e],getProjectTag:Q,total:n(M)[e],multipleSelection:le.value,selectedRows:ie.value,"onUpdate:selectedRows":l[3]||(l[3]=e=>ie.value=e)},null,8,["tableDataList","total","multipleSelection","selectedRows"])])),_:2},1032,["label"])))),128))])),_:1})),[[w,N.value]])])),_:1}),p(n(T),{modelValue:G.value,"onUpdate:modelValue":l[4]||(l[4]=e=>G.value=e),title:n(F)("project.addProject"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:u((()=>[p(S,{closeDialog:W,projectid:"",getProjectData:Q,schedule:!1})])),_:1},8,["modelValue","title"])],64)}}});export{F as default};
