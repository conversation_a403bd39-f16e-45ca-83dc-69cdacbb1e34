import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as a,r as l,s as t,e as o,v as i,B as n,C as s,D as r,G as u,F as p,K as c,H as d,N as m,y as g,o as f,c as v,w as y,a as h,z as b,t as _,A as x,f as j,I as F,E as S,j as k,J as w,l as C,M as A}from"./index-3XfDPlIS.js";import{a as E,E as V}from"./el-col-CN1tVfqh.js";import{E as U}from"./el-text-CLWE0mUm.js";import{E as B}from"./el-tag-DcMbxLLg.js";import"./el-tooltip-l0sNRNKZ.js";import{E as z}from"./el-popper-DVoWBu_3.js";import{E as D}from"./el-upload-D4EYBM-E.js";import"./el-progress-DtkkA0nz.js";import{E as I,a as H,b as L}from"./el-dropdown-item-BMccEdtX.js";import{_ as P}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as R}from"./useTable-BezX3TfM.js";import{u as T}from"./useIcon-k-uSyz6l.js";import{_ as W}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{d as M,g as N,u as O,r as $,a as G,b as K,e as J,c as q}from"./index-DcjWAoK6.js";import Q from"./detail-BsKDcOei.js";import"./el-card-CuEws33_.js";import"./index-tjM0-mlU.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./el-table-column-B5hg3WH6.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./index-DkclijAA.js";import"./index-Dz8ZrwBc.js";import"./el-form-BY8piFS2.js";import"./index-C7jW5IRr.js";const X={class:"flex flex-wrap gap-3 items-center"},Y={href:"https://plugin.scope-sentry.top/",target:"_blank"},Z={style:{position:"relative",top:"12px"}},ee={key:0},ae={class:"flex flex-col gap-2"};function le(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!w(e)}const te=a({__name:"plugin",setup(a){const w=T({icon:"iconoir:search"}),{t:te}=C(),oe=l(""),ie=()=>{ve()},ne=t([{field:"index",label:te("tableDemo.index"),type:"index",minWidth:"15"},{field:"selection",type:"selection",minWidth:55},{field:"name",label:te("plugin.name"),formatter:(e,a,l)=>o("a",{href:`https://plugin.scope-sentry.top/plugin/${e.hash}`,style:"color: #409EFF; text-decoration: none;",target:"_blank"},[l])},{field:"module",label:te("plugin.module"),formatter:(e,a,l)=>{const t=se[l]||"#FFFFFF";return o(B,{style:{backgroundColor:t,color:"#000"}},le(l)?l:{default:()=>[l]})}},{field:"isSystem",label:te("plugin.isSystem"),formatter:(e,a,l)=>o(B,{type:l?"success":"warning"},{default:()=>[l?"true":"false"]})},{field:"version",label:te("plugin.version"),minWidth:100},{field:"parameter",label:te("plugin.parameter"),formatter:(e,a,l)=>o(z,{content:e.help,placement:"top",effect:"light"},{default:()=>[o("span",{style:"cursor: pointer;"},[l])]})},{field:"introduction",label:te("plugin.introduction"),minWidth:200},{field:"action",label:te("tableDemo.action"),minWidth:"300",fixed:"right",formatter:(e,a,l)=>{let t,c,d;const m=i(L,{onCommand:a=>{switch(a){case"reinstall":G("all",e.hash,e.module);break;case"recheck":$("all",e.hash,e.module);break;case"uninstall":O("all",e.hash,e.module)}}},{default:()=>i(n,{style:{outline:"none",boxShadow:"none"}},(()=>[te("common.operation"),i(s,{},(()=>i(r)))])),dropdown:()=>i(I,null,(()=>[i(H,{command:"reinstall"},(()=>te("plugin.reInstall"))),i(H,{command:"recheck"},(()=>te("plugin.reCheck"))),i(H,{command:"uninstall"},(()=>te("plugin.uninstall")))]))});return o(p,null,[m,o(u,{type:"warning",style:{marginLeft:"10px"},onClick:()=>Pe(e)},le(t=te("common.log"))?t:{default:()=>[t]}),o(u,{type:"success",onClick:()=>Ee(e.id)},le(c=te("common.edit"))?c:{default:()=>[c]}),o(u,{type:"danger",onClick:()=>Fe(e.hash,e.module),disabled:e.isSystem},le(d=te("common.delete"))?d:{default:()=>[d]})])}}]),se={TargetHandler:"#2243dda6",SubdomainScan:"#FF9B85",SubdomainSecurity:"#FFFFBA",PortScanPreparation:"#BAFFB3",PortScan:"#BAE1FF",AssetMapping:"#e3ffba",URLScan:"#D1BAFF",WebCrawler:"#FFABAB",DirScan:"#3ccde6",VulnerabilityScan:"#FF677D",AssetHandle:"#B2E1FF",PortFingerprint:"#ffb5e4",URLSecurity:"#FFE4BA",PassiveScan:"#A2DFF7"},{tableRegister:re,tableState:ue,tableMethods:pe}=R({fetchDataApi:async()=>{const{currentPage:e,pageSize:a}=ue,l=await K(oe.value,e.value,a.value);return{list:l.data.list,total:l.data.total}},immediate:!0}),{loading:ce,dataList:de,total:me,currentPage:ge,pageSize:fe}=ue;fe.value=20;const{getList:ve,getElTableExpose:ye}=pe;function he(){return{background:"var(--el-fill-color-light)"}}const be=l(!1);let _e=te("plugin.new");const xe=()=>{be.value=!1},je=async()=>{c({title:"Delete",draggable:!0}).then((async()=>{await we()}))},Fe=async(e,a)=>{c({title:"Delete",draggable:!0}).then((async()=>{await ke(e,a)}))},Se=l(!1),ke=async(e,a)=>{Se.value=!0;try{await M([{hash:e,module:a}]);Se.value=!1,ve()}catch(l){Se.value=!1,ve()}},we=async()=>{const e=await ye(),a=((null==e?void 0:e.getSelectionRows())||[]).map((e=>({hash:e.hash,module:e.module})));Se.value=!0;try{await M(a);Se.value=!1,ve()}catch(l){Se.value=!1,ve()}},Ce=async()=>{Ae.value="",be.value=!0},Ae=l(""),Ee=async e=>{Ae.value=e,_e=te("common.edit"),be.value=!0};d((()=>{Ue(),window.addEventListener("resize",Ue)}));const Ve=l(0),Ue=()=>{const e=window.innerHeight||document.documentElement.clientHeight;Ve.value=.7*e},Be=l(!1),ze=()=>{Be.value=!1},De=l(""),Ie=l(),He=l(""),Le=l(""),Pe=async e=>{He.value=e.module,Le.value=e.hash;const a=await N(e.module,e.hash);De.value=a.logs,Be.value=!0},Re=async()=>{await J(He.value,Le.value),De.value=""},Te={Authorization:`${m().getToken}`},We=l(),Me=e=>{We.value.clearFiles();const a=e[0];We.value.handleStart(a)},Ne=e=>{var a;200===e.code?A.success("Upload succes"):A.error(e.message),505==e.code&&localStorage.removeItem("plugin_key"),ve(),null==(a=We.value)||a.clearFiles()},Oe=(e,a)=>{a.length>0&&We.value.submit()},$e=l(!1),Ge=l(""),Ke=async()=>{if(Ge.value){200==(await q(Ge.value)).code&&(localStorage.setItem("plugin_key",Ge.value),$e.value=!1)}};return(()=>{const e=localStorage.getItem("plugin_key");e||($e.value=!0),Ge.value=e})(),(a,l)=>{const t=g("Icon");return f(),v(p,null,[o(h(e),null,{default:y((()=>[o(h(E),null,{default:y((()=>[o(h(V),{span:1},{default:y((()=>[o(h(U),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:y((()=>[b(_(h(te)("plugin.name"))+":",1)])),_:1})])),_:1}),o(h(V),{span:5},{default:y((()=>[o(h(x),{modelValue:oe.value,"onUpdate:modelValue":l[0]||(l[0]=e=>oe.value=e),placeholder:h(te)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(h(V),{span:5,style:{position:"relative",left:"16px"}},{default:y((()=>[o(h(n),{type:"primary",icon:h(w),style:{height:"100%"},onClick:ie},{default:y((()=>[b("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(h(E),{gutter:16,class:"mt-4"},{default:y((()=>[o(h(V),{xs:24,sm:24,md:24,lg:24,xl:24},{default:y((()=>[j("div",X,[o(h(u),{type:"primary",onClick:Ce},{default:y((()=>[b(_(h(te)("plugin.new")),1)])),_:1}),o(h(u),{type:"danger",loading:Se.value,onClick:je},{default:y((()=>[b(_(h(te)("plugin.delete")),1)])),_:1},8,["loading"]),j("a",Y,[o(h(u),{type:"info"},{default:y((()=>[b(_(h(te)("plugin.market")),1)])),_:1})]),o(h(D),{ref_key:"upload",ref:We,class:"flex items-center",action:"/api/plugin/import?key="+Ge.value,headers:Te,"on-success":Ne,limit:1,"on-exceed":Me,"auto-upload":!1,onChange:Oe},{trigger:y((()=>[o(h(u),null,{icon:y((()=>[o(t,{icon:"iconoir:upload"})])),default:y((()=>[b(" "+_(h(te)("plugin.import")),1)])),_:1})])),_:1},8,["action"])])])),_:1})])),_:1}),j("div",Z,[o(h(P),{pageSize:h(fe),"onUpdate:pageSize":l[1]||(l[1]=e=>F(fe)?fe.value=e:null),currentPage:h(ge),"onUpdate:currentPage":l[2]||(l[2]=e=>F(ge)?ge.value=e:null),columns:ne,data:h(de),stripe:"",border:!0,loading:h(ce),resizable:!0,pagination:{total:h(me),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:h(re),headerCellStyle:he,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister"])])])),_:1}),o(h(W),{modelValue:be.value,"onUpdate:modelValue":l[3]||(l[3]=e=>be.value=e),title:h(_e),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:y((()=>[o(Q,{closeDialog:xe,getList:h(ve),id:Ae.value},null,8,["getList","id"])])),_:1},8,["modelValue","title"]),o(h(W),{modelValue:Be.value,"onUpdate:modelValue":l[4]||(l[4]=e=>Be.value=e),title:h(te)("node.log"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:Ve.value},{footer:y((()=>[o(h(u),{onClick:Re,type:"danger"},{default:y((()=>[b(_(h(te)("common.cleanLog")),1)])),_:1}),o(h(u),{onClick:ze},{default:y((()=>[b(_(h(te)("common.off")),1)])),_:1})])),default:y((()=>[o(h(S),{ref_key:"scrollbarRef",ref:Ie},{default:y((()=>[De.value?(f(),v("pre",ee,_(De.value),1)):k("",!0)])),_:1},512)])),_:1},8,["modelValue","title","maxHeight"]),o(h(W),{modelValue:$e.value,"onUpdate:modelValue":l[6]||(l[6]=e=>$e.value=e),title:h(te)("plugin.key"),center:"",width:"30%",style:{"max-width":"400px",height:"200px"}},{default:y((()=>[j("div",ae,[o(h(z),{class:"item",effect:"dark",content:h(te)("plugin.keyMsg"),placement:"top"},{default:y((()=>[o(h(x),{modelValue:Ge.value,"onUpdate:modelValue":l[5]||(l[5]=e=>Ge.value=e)},null,8,["modelValue"])])),_:1},8,["content"]),o(h(u),{onClick:Ke,type:"primary",class:"w-full"},{default:y((()=>[b("确定")])),_:1})])])),_:1},8,["modelValue","title"])],64)}}});export{te as default};
