import{a8 as e,z as t,e as n,d as o,s as l,r,O as a,b9 as c,aq as i,o as d,i as u,a as s}from"./index-DfJTpRkj.js";var h={d:(e,t)=>{for(var n in t)h.o(t,n)&&!h.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},p={};function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function y(e,t){if(e){if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function b(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}h.d(p,{Z:()=>E});const v=(C={computed:()=>e,createTextVNode:()=>t,createVNode:()=>n,defineComponent:()=>o,reactive:()=>l,ref:()=>r,watch:()=>a,watchEffect:()=>c},N={},h.d(N,C),N),m=(0,v.defineComponent)({props:{data:{required:!0,type:String},onClick:Function},render:function(){var e=this.data,t=this.onClick;return(0,v.createVNode)("span",{class:"vjs-tree-brackets",onClick:t},[e])}}),k=(0,v.defineComponent)({emits:["change","update:modelValue"],props:{checked:{type:Boolean,default:!1},isMultiple:Boolean,onChange:Function},setup:function(e,t){var n=t.emit;return{uiType:(0,v.computed)((function(){return e.isMultiple?"checkbox":"radio"})),model:(0,v.computed)({get:function(){return e.checked},set:function(e){return n("update:modelValue",e)}})}},render:function(){var e=this.uiType,t=this.model,n=this.$emit;return(0,v.createVNode)("label",{class:["vjs-check-controller",t?"is-checked":""],onClick:function(e){return e.stopPropagation()}},[(0,v.createVNode)("span",{class:"vjs-check-controller-inner is-".concat(e)},null),(0,v.createVNode)("input",{checked:t,class:"vjs-check-controller-original is-".concat(e),type:e,onChange:function(){return n("change",t)}},null)])}}),w=(0,v.defineComponent)({props:{nodeType:{required:!0,type:String},onClick:Function},render:function(){var e=this.nodeType,t=this.onClick,n="objectStart"===e||"arrayStart"===e;return n||"objectCollapsed"===e||"arrayCollapsed"===e?(0,v.createVNode)("span",{class:"vjs-carets vjs-carets-".concat(n?"open":"close"),onClick:t},[(0,v.createVNode)("svg",{viewBox:"0 0 1024 1024",focusable:"false","data-icon":"caret-down",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},[(0,v.createVNode)("path",{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"},null)])]):null}});var C,N;function S(e){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function j(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function O(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"root",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=(arguments.length>3?arguments[3]:void 0)||{},l=o.key,r=o.index,a=o.type,c=void 0===a?"content":a,i=o.showComma,d=void 0!==i&&i,u=o.length,s=void 0===u?1:u,h=j(e);if("array"===h){var p=V(e.map((function(e,o,l){return O(e,"".concat(t,"[").concat(o,"]"),n+1,{index:o,showComma:o!==l.length-1,length:s,type:c})})));return[O("[",t,n,{showComma:!1,key:l,length:e.length,type:"arrayStart"})[0]].concat(p,O("]",t,n,{showComma:d,length:e.length,type:"arrayEnd"})[0])}if("object"===h){var f=Object.keys(e),y=V(f.map((function(o,l,r){return O(e[o],/^[a-zA-Z_]\w*$/.test(o)?"".concat(t,".").concat(o):"".concat(t,'["').concat(o,'"]'),n+1,{key:o,showComma:l!==r.length-1,length:s,type:c})})));return[O("{",t,n,{showComma:!1,key:l,index:r,length:f.length,type:"objectStart"})[0]].concat(y,O("}",t,n,{showComma:d,length:f.length,type:"objectEnd"})[0])}return[{content:e,level:n,key:l,index:r,path:t,showComma:d,length:s,type:c}]}function V(e){if("function"==typeof Array.prototype.flat)return e.flat();for(var t=b(e),n=[];t.length;){var o=t.shift();Array.isArray(o)?t.unshift.apply(t,b(o)):n.push(o)}return n}function P(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new WeakMap;if(null==e)return e;if(e instanceof Date)return new Date(e);if(e instanceof RegExp)return new RegExp(e);if("object"!==S(e))return e;if(t.get(e))return t.get(e);if(Array.isArray(e)){var n=e.map((function(e){return P(e,t)}));return t.set(e,n),n}var o={};for(var l in e)o[l]=P(e[l],t);return t.set(e,o),o}function T(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function L(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?T(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):T(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var x={showLength:{type:Boolean,default:!1},showDoubleQuotes:{type:Boolean,default:!0},renderNodeKey:Function,renderNodeValue:Function,selectableType:String,showSelectController:{type:Boolean,default:!1},showLine:{type:Boolean,default:!0},showLineNumber:{type:Boolean,default:!1},selectOnClickNode:{type:Boolean,default:!0},nodeSelectable:{type:Function,default:function(){return!0}},highlightSelectedNode:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!1},theme:{type:String,default:"light"},showKeyValueSpace:{type:Boolean,default:!0},editable:{type:Boolean,default:!1},editableTrigger:{type:String,default:"click"},onNodeClick:{type:Function},onBracketsClick:{type:Function},onIconClick:{type:Function},onValueChange:{type:Function}};const B=(0,v.defineComponent)({name:"TreeNode",props:L(L({},x),{},{node:{type:Object,required:!0},collapsed:Boolean,checked:Boolean,style:Object,onSelectedChange:{type:Function}}),emits:["nodeClick","bracketsClick","iconClick","selectedChange","valueChange"],setup:function(e,t){var n=t.emit,o=(0,v.computed)((function(){return j(e.node.content)})),l=(0,v.computed)((function(){return"vjs-value vjs-value-".concat(o.value)})),r=(0,v.computed)((function(){return e.showDoubleQuotes?'"'.concat(e.node.key,'"'):e.node.key})),a=(0,v.computed)((function(){return"multiple"===e.selectableType})),c=(0,v.computed)((function(){return"single"===e.selectableType})),i=(0,v.computed)((function(){return e.nodeSelectable(e.node)&&(a.value||c.value)})),d=(0,v.reactive)({editing:!1}),u=function(t){var o,l,r="null"===(l=null===(o=t.target)||void 0===o?void 0:o.value)?null:"undefined"===l?void 0:"true"===l||"false"!==l&&(l[0]+l[l.length-1]==='""'||l[0]+l[l.length-1]==="''"?l.slice(1,-1):"number"==typeof Number(l)&&!isNaN(Number(l))||"NaN"===l?Number(l):l);n("valueChange",r,e.node.path)},s=(0,v.computed)((function(){var t,n=null===(t=e.node)||void 0===t?void 0:t.content;return null===n?n="null":void 0===n&&(n="undefined"),"string"===o.value?'"'.concat(n,'"'):n+""})),h=function(){var t=e.renderNodeValue;return t?t({node:e.node,defaultValue:s.value}):s.value},p=function(){n("bracketsClick",!e.collapsed,e.node)},f=function(){n("iconClick",!e.collapsed,e.node)},y=function(){n("selectedChange",e.node)},b=function(){n("nodeClick",e.node),i.value&&e.selectOnClickNode&&n("selectedChange",e.node)},g=function(t){if(e.editable&&!d.editing){d.editing=!0;var n=function e(n){var o;n.target!==t.target&&(null===(o=n.target)||void 0===o?void 0:o.parentElement)!==t.target&&(d.editing=!1,document.removeEventListener("click",e))};document.removeEventListener("click",n),document.addEventListener("click",n)}};return function(){var t,n=e.node;return(0,v.createVNode)("div",{class:{"vjs-tree-node":!0,"has-selector":e.showSelectController,"has-carets":e.showIcon,"is-highlight":e.highlightSelectedNode&&e.checked,dark:"dark"===e.theme},onClick:b,style:e.style},[e.showLineNumber&&(0,v.createVNode)("span",{class:"vjs-node-index"},[n.id+1]),e.showSelectController&&i.value&&"objectEnd"!==n.type&&"arrayEnd"!==n.type&&(0,v.createVNode)(k,{isMultiple:a.value,checked:e.checked,onChange:y},null),(0,v.createVNode)("div",{class:"vjs-indent"},[Array.from(Array(n.level)).map((function(t,n){return(0,v.createVNode)("div",{key:n,class:{"vjs-indent-unit":!0,"has-line":e.showLine}},null)})),e.showIcon&&(0,v.createVNode)(w,{nodeType:n.type,onClick:f},null)]),n.key&&(0,v.createVNode)("span",{class:"vjs-key"},[(t=e.renderNodeKey,t?t({node:e.node,defaultKey:r.value||""}):r.value),(0,v.createVNode)("span",{class:"vjs-colon"},[":".concat(e.showKeyValueSpace?" ":"")])]),(0,v.createVNode)("span",null,["content"!==n.type&&n.content?(0,v.createVNode)(m,{data:n.content.toString(),onClick:p},null):(0,v.createVNode)("span",{class:l.value,onClick:!e.editable||e.editableTrigger&&"click"!==e.editableTrigger?void 0:g,onDblclick:e.editable&&"dblclick"===e.editableTrigger?g:void 0},[e.editable&&d.editing?(0,v.createVNode)("input",{value:s.value,onChange:u,style:{padding:"3px 8px",border:"1px solid #eee",boxShadow:"none",boxSizing:"border-box",borderRadius:5,fontFamily:"inherit"}},null):h()]),n.showComma&&(0,v.createVNode)("span",null,[","]),e.showLength&&e.collapsed&&(0,v.createVNode)("span",{class:"vjs-comment"},[(0,v.createTextVNode)(" // "),n.length,(0,v.createTextVNode)(" items ")])])])}}});function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const E=(0,v.defineComponent)({name:"Tree",props:A(A({},x),{},{data:{type:[String,Number,Boolean,Array,Object],default:null},collapsedNodeLength:{type:Number,default:1/0},deep:{type:Number,default:1/0},pathCollapsible:{type:Function,default:function(){return!1}},rootPath:{type:String,default:"root"},virtual:{type:Boolean,default:!1},height:{type:Number,default:400},itemHeight:{type:Number,default:20},selectedValue:{type:[String,Array],default:function(){return""}},collapsedOnClickBrackets:{type:Boolean,default:!0},style:Object,onSelectedChange:{type:Function},theme:{type:String,default:"light"}}),slots:["renderNodeKey","renderNodeValue"],emits:["nodeClick","bracketsClick","iconClick","selectedChange","update:selectedValue","update:data"],setup:function(e,t){var n=t.emit,o=t.slots,l=(0,v.ref)(),r=(0,v.computed)((function(){return O(e.data,e.rootPath)})),a=function(t,n){return r.value.reduce((function(o,l){var r,a=l.level>=t||l.length>=n,c=null===(r=e.pathCollapsible)||void 0===r?void 0:r.call(e,l);return"objectStart"!==l.type&&"arrayStart"!==l.type||!a&&!c?o:A(A({},o),{},g({},l.path,1))}),{})},c=(0,v.reactive)({translateY:0,visibleData:null,hiddenPaths:a(e.deep,e.collapsedNodeLength)}),i=(0,v.computed)((function(){for(var e=null,t=[],n=r.value.length,o=0;o<n;o++){var l=A(A({},r.value[o]),{},{id:o}),a=c.hiddenPaths[l.path];if(e&&e.path===l.path){var i="objectStart"===e.type,d=A(A(A({},l),e),{},{showComma:l.showComma,content:i?"{...}":"[...]",type:i?"objectCollapsed":"arrayCollapsed"});e=null,t.push(d)}else{if(a&&!e){e=l;continue}if(e)continue;t.push(l)}}return t})),d=(0,v.computed)((function(){var t=e.selectedValue;return t&&"multiple"===e.selectableType&&Array.isArray(t)?t:[t]})),u=(0,v.computed)((function(){return!e.selectableType||e.selectOnClickNode||e.showSelectController?"":"When selectableType is not null, selectOnClickNode and showSelectController cannot be false at the same time, because this will cause the selection to fail."})),s=function(){var t=i.value;if(e.virtual){var n,o=e.height/e.itemHeight,r=(null===(n=l.value)||void 0===n?void 0:n.scrollTop)||0,a=Math.floor(r/e.itemHeight),d=a<0?0:a+o>t.length?t.length-o:a;d<0&&(d=0);var u=d+o;c.translateY=d*e.itemHeight,c.visibleData=t.filter((function(e,t){return t>=d&&t<u}))}else c.visibleData=t},h=function(){s()},p=function(t){var o,l=t.path,r=e.selectableType;if("multiple"===r){var a=d.value.findIndex((function(e){return e===l})),c=b(d.value);-1!==a?c.splice(a,1):c.push(l),n("update:selectedValue",c),n("selectedChange",c,b(d.value))}else if("single"===r&&d.value[0]!==l){var i=(o=d.value,1,function(e){if(Array.isArray(e))return e}(o)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,l,r=[],a=!0,c=!1;try{for(n=n.call(e);!(a=(o=n.next()).done)&&(r.push(o.value),1!==r.length);a=!0);}catch(i){c=!0,l=i}finally{try{a||null==n.return||n.return()}finally{if(c)throw l}}return r}}(o)||y(o,1)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0],u=l;n("update:selectedValue",u),n("selectedChange",u,i)}},f=function(e){n("nodeClick",e)},m=function(e,t){if(e)c.hiddenPaths=A(A({},c.hiddenPaths),{},g({},t,1));else{var n=A({},c.hiddenPaths);delete n[t],c.hiddenPaths=n}},k=function(t,o){e.collapsedOnClickBrackets&&m(t,o.path),n("bracketsClick",t,o)},w=function(e,t){m(e,t.path),n("iconClick",e,t)},C=function(t,o){var l=P(e.data),r=e.rootPath;new Function("data","val","data".concat(o.slice(r.length),"=val"))(l,t),n("update:data",l)};return(0,v.watchEffect)((function(){u.value&&function(e){throw new Error("[VueJSONPretty] ".concat(e))}(u.value)})),(0,v.watchEffect)((function(){i.value&&s()})),(0,v.watch)((function(){return e.deep}),(function(t){t&&(c.hiddenPaths=a(t,e.collapsedNodeLength))})),(0,v.watch)((function(){return e.collapsedNodeLength}),(function(t){t&&(c.hiddenPaths=a(e.deep,t))})),function(){var t,n,a=null!==(t=e.renderNodeKey)&&void 0!==t?t:o.renderNodeKey,u=null!==(n=e.renderNodeValue)&&void 0!==n?n:o.renderNodeValue,s=c.visibleData&&c.visibleData.map((function(t){return(0,v.createVNode)(B,{key:t.id,node:t,collapsed:!!c.hiddenPaths[t.path],theme:e.theme,showDoubleQuotes:e.showDoubleQuotes,showLength:e.showLength,checked:d.value.includes(t.path),selectableType:e.selectableType,showLine:e.showLine,showLineNumber:e.showLineNumber,showSelectController:e.showSelectController,selectOnClickNode:e.selectOnClickNode,nodeSelectable:e.nodeSelectable,highlightSelectedNode:e.highlightSelectedNode,editable:e.editable,editableTrigger:e.editableTrigger,showIcon:e.showIcon,showKeyValueSpace:e.showKeyValueSpace,renderNodeKey:a,renderNodeValue:u,onNodeClick:f,onBracketsClick:k,onIconClick:w,onSelectedChange:p,onValueChange:C,style:e.itemHeight&&20!==e.itemHeight?{lineHeight:"".concat(e.itemHeight,"px")}:{}},null)}));return(0,v.createVNode)("div",{ref:l,class:{"vjs-tree":!0,"is-virtual":e.virtual,dark:"dark"===e.theme},onScroll:e.virtual?h:void 0,style:e.showLineNumber?A({paddingLeft:"".concat(12*Number(r.value.length.toString().length),"px")},e.style):e.style},[e.virtual?(0,v.createVNode)("div",{class:"vjs-tree-list",style:{height:"".concat(e.height,"px")}},[(0,v.createVNode)("div",{class:"vjs-tree-list-holder",style:{height:"".concat(i.value.length*e.itemHeight,"px")}},[(0,v.createVNode)("div",{class:"vjs-tree-list-holder-inner",style:{transform:"translateY(".concat(c.translateY,"px)")}},[s])])]):s])}}});var I=p.Z;const F=o({__name:"JsonEditor",props:{modelValue:{type:Object,default:()=>({})},deep:i.number.def(5),showLength:i.bool.def(!0),showLineNumbers:i.bool.def(!0),showLineNumber:i.bool.def(!0),showIcon:i.bool.def(!0),showDoubleQuotes:i.bool.def(!0),virtual:i.bool.def(!1),height:i.number.def(400),itemHeight:i.number.def(20),rootPath:i.string.def("root"),nodeSelectable:i.func.def(),selectableType:i.oneOf(["multiple","single"]).def(),showSelectController:i.bool.def(!1),selectOnClickNode:i.bool.def(!0),highlightSelectedNode:i.bool.def(!0),collapsedOnClickBrackets:i.bool.def(!0),renderNodeKey:i.func.def(),renderNodeValue:i.func.def(),editable:i.bool.def(!0),editableTrigger:i.oneOf(["click","dblclick"]).def("click")},emits:["update:modelValue","node-click","brackets-click","icon-click","selected-value"],setup(t,{emit:n}){const o=n,l=t,r=e((()=>l.modelValue)),a=e({get:()=>r.value,set:e=>{o("update:modelValue",e)}}),c=e=>{o("node-click",e)},i=e=>{o("brackets-click",e)},h=e=>{o("icon-click",e)},p=(e,t)=>{o("selected-value",e,t)};return(e,n)=>(d(),u(s(I),{data:a.value,"onUpdate:data":n[0]||(n[0]=e=>a.value=e),deep:t.deep,"show-length":t.showLength,"show-line-numbers":t.showLineNumbers,"show-line-number":t.showLineNumber,"show-icon":t.showIcon,"show-double-quotes":t.showDoubleQuotes,virtual:t.virtual,height:t.height,"item-height":t.itemHeight,"root-path":t.rootPath,"node-selectable":t.nodeSelectable,"selectable-type":t.selectableType,"show-select-controller":t.showSelectController,"select-on-click-node":t.selectOnClickNode,"highlight-selected-node":t.highlightSelectedNode,"collapsed-on-click-brackets":t.collapsedOnClickBrackets,"render-node-key":t.renderNodeKey,"render-node-value":t.renderNodeValue,editable:t.editable,"editable-trigger":t.editableTrigger,onNodeClick:c,onBracketsClick:i,onIconClick:h,onSelectedChange:p},null,8,["data","deep","show-length","show-line-numbers","show-line-number","show-icon","show-double-quotes","virtual","height","item-height","root-path","node-selectable","selectable-type","show-select-controller","select-on-click-node","highlight-selected-node","collapsed-on-click-brackets","render-node-key","render-node-value","editable","editable-trigger"]))}});export{F as _};
