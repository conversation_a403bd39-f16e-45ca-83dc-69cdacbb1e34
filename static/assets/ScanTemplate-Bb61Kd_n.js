import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,r as a,s as l,e as o,G as i,F as s,K as r,H as n,o as p,c as m,w as u,a as d,z as c,t as g,A as f,B as j,f as v,I as y,J as _,l as b}from"./index-DfJTpRkj.js";import{a as h,E as x}from"./el-col-B4Ik8fnS.js";import{E as w}from"./el-text-vKNLRkxx.js";import{_ as k}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as S}from"./useTable-CtyddZqf.js";import{u as T}from"./useIcon-CNpM61rT.js";import{f as z,h as A}from"./index-B0JD2UFG.js";import{_ as C}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import E from"./DetailTemplate-DU3RXrBH.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tag-CbhrEnto.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";import"./el-switch-C5ZBDFmL.js";import"./el-form-DsaI0u2w.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";/* empty css                */import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";const D={class:"mb-10px"},I={style:{position:"relative",top:"12px"}};function L(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!_(e)}const U=t({__name:"ScanTemplate",setup(t){const _=T({icon:"iconoir:search"}),{t:U}=b(),H=a(""),N=()=>{K()},V=l([{field:"selection",type:"selection",minWidth:55},{field:"name",label:U("task.templateName")},{field:"action",label:U("tableDemo.action"),formatter:(e,t,a)=>{let l,r;return o(s,null,[o(i,{type:"success",onClick:()=>ne(e.id)},L(l=U("common.edit"))?l:{default:()=>[l]}),o(i,{type:"danger",onClick:()=>Z(e)},L(r=U("common.delete"))?r:{default:()=>[r]})])}}]),{tableRegister:P,tableState:R,tableMethods:F}=S({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=R,a=await A(H.value,e.value,t.value);return{list:a.data.list,total:a.data.total}},immediate:!0}),{loading:O,dataList:B,total:M,currentPage:G,pageSize:J}=R;J.value=20;const{getList:K,getElTableExpose:W}=F;function Q(){return{background:"var(--el-fill-color-light)"}}const $=a(!1);let q=U("task.addTemplate");const X=()=>{$.value=!1},Y=async()=>{r({title:"Delete",draggable:!0}).then((async()=>{await le()}))},Z=async e=>{r({title:"Delete",draggable:!0}).then((async()=>{await te(e)}))},ee=a(!1),te=async e=>{ee.value=!0;try{await z([e.id]);ee.value=!1,K()}catch(t){ee.value=!1,K()}},ae=a([]),le=async()=>{const e=await W(),t=(null==e?void 0:e.getSelectionRows())||[];ae.value=t.map((e=>e.id)),ee.value=!0;try{await z(ae.value);ee.value=!1,K()}catch(a){ee.value=!1,K()}};n((()=>{ie(),window.addEventListener("resize",ie)}));const oe=a(0),ie=()=>{const e=window.innerHeight||document.documentElement.clientHeight;oe.value=.8*e},se=async()=>{re.value="",q=U("task.addTemplate"),$.value=!0},re=a(""),ne=async e=>{re.value=e,q=U("task.editTemplate"),$.value=!0};return(t,a)=>(p(),m(s,null,[o(d(e),null,{default:u((()=>[o(d(h),null,{default:u((()=>[o(d(x),{span:1},{default:u((()=>[o(d(w),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:u((()=>[c(g(d(U)("task.templateName"))+": ",1)])),_:1})])),_:1}),o(d(x),{span:5},{default:u((()=>[o(d(f),{modelValue:H.value,"onUpdate:modelValue":a[0]||(a[0]=e=>H.value=e),placeholder:d(U)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(d(x),{span:5,style:{position:"relative",left:"16px"}},{default:u((()=>[o(d(j),{type:"primary",icon:d(_),style:{height:"100%"},onClick:N},{default:u((()=>[c("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(d(h),null,{default:u((()=>[o(d(x),{style:{position:"relative",top:"16px"}},{default:u((()=>[v("div",D,[o(d(i),{type:"primary",onClick:se},{default:u((()=>[c(g(d(U)("task.addTemplate")),1)])),_:1}),o(d(i),{type:"danger",loading:ee.value,onClick:Y},{default:u((()=>[c(g(d(U)("task.deleteTemplate")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),v("div",I,[o(d(k),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:d(J),"onUpdate:pageSize":a[1]||(a[1]=e=>y(J)?J.value=e:null),currentPage:d(G),"onUpdate:currentPage":a[2]||(a[2]=e=>y(G)?G.value=e:null),columns:V,data:d(B),stripe:"",border:!0,loading:d(O),"max-height":oe.value,resizable:!0,pagination:{total:d(M),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:d(P),headerCellStyle:Q,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])])])),_:1}),o(d(C),{modelValue:$.value,"onUpdate:modelValue":a[3]||(a[3]=e=>$.value=e),title:d(q),center:"",fullscreen:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:u((()=>[o(E,{closeDialog:X,getList:d(K),id:re.value},null,8,["getList","id"])])),_:1},8,["modelValue","title"])],64))}});export{U as default};
