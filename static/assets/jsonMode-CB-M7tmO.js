import{m as e}from"./MonacoDiffEditor-CT_9o8pr.js";import"./index-3XfDPlIS.js";
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.52.0(f6dc0eb8fce67e57f6036f4769d92c1666cdf546)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var t,n,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,s=(e,t,n,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let c of o(t))a.call(e,c)||c===n||r(e,c,{get:()=>t[c],enumerable:!(s=i(t,c))||s.enumerable});return e},c={};s(c,t=e,"default"),n&&s(n,t,"default");var u,d,l,g,f,h,m,p,v,b,k,C,_,w,y,x,E,I,S,A,T,L,R,M,j,F,N,P,O,U,D,V,B,K,W,H,X,z,q,$,Q,J,G,Y,Z,ee,te,ne,re,ie,oe,ae,se,ce,ue,de,le,ge,fe,he,me,pe,ve,be,ke,Ce,_e,we,ye,xe,Ee,Ie,Se,Ae,Te,Le,Re,Me,je,Fe,Ne,Pe,Oe,Ue,De,Ve,Be,Ke,We,He,Xe,ze,qe,$e,Qe,Je,Ge,Ye,Ze,et,tt,nt,rt,it,ot,at,st,ct,ut,dt,lt,gt,ft,ht,mt,pt,vt,bt,kt,Ct,_t,wt,yt,xt,Et,It,St,At,Tt,Lt,Rt,Mt,jt,Ft=class{constructor(e){this._defaults=e,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval((()=>this._checkIfIdle()),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange((()=>this._stopWorker()))}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){if(!this._worker)return;Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=c.editor.createWebWorker({moduleId:"vs/language/json/jsonWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...e){let t;return this._getClient().then((e=>{t=e})).then((t=>{if(this._worker)return this._worker.withSyncedResources(e)})).then((e=>t))}};(u||(u={})).is=function(e){return"string"==typeof e},(d||(d={})).is=function(e){return"string"==typeof e},(g=l||(l={})).MIN_VALUE=-2147483648,g.MAX_VALUE=2147483647,g.is=function(e){return"number"==typeof e&&g.MIN_VALUE<=e&&e<=g.MAX_VALUE},(h=f||(f={})).MIN_VALUE=0,h.MAX_VALUE=2147483647,h.is=function(e){return"number"==typeof e&&h.MIN_VALUE<=e&&e<=h.MAX_VALUE},(p=m||(m={})).create=function(e,t){return e===Number.MAX_VALUE&&(e=f.MAX_VALUE),t===Number.MAX_VALUE&&(t=f.MAX_VALUE),{line:e,character:t}},p.is=function(e){let t=e;return Nt.objectLiteral(t)&&Nt.uinteger(t.line)&&Nt.uinteger(t.character)},(b=v||(v={})).create=function(e,t,n,r){if(Nt.uinteger(e)&&Nt.uinteger(t)&&Nt.uinteger(n)&&Nt.uinteger(r))return{start:m.create(e,t),end:m.create(n,r)};if(m.is(e)&&m.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)},b.is=function(e){let t=e;return Nt.objectLiteral(t)&&m.is(t.start)&&m.is(t.end)},(C=k||(k={})).create=function(e,t){return{uri:e,range:t}},C.is=function(e){let t=e;return Nt.objectLiteral(t)&&v.is(t.range)&&(Nt.string(t.uri)||Nt.undefined(t.uri))},(w=_||(_={})).create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},w.is=function(e){let t=e;return Nt.objectLiteral(t)&&v.is(t.targetRange)&&Nt.string(t.targetUri)&&v.is(t.targetSelectionRange)&&(v.is(t.originSelectionRange)||Nt.undefined(t.originSelectionRange))},(x=y||(y={})).create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},x.is=function(e){const t=e;return Nt.objectLiteral(t)&&Nt.numberRange(t.red,0,1)&&Nt.numberRange(t.green,0,1)&&Nt.numberRange(t.blue,0,1)&&Nt.numberRange(t.alpha,0,1)},(I=E||(E={})).create=function(e,t){return{range:e,color:t}},I.is=function(e){const t=e;return Nt.objectLiteral(t)&&v.is(t.range)&&y.is(t.color)},(A=S||(S={})).create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},A.is=function(e){const t=e;return Nt.objectLiteral(t)&&Nt.string(t.label)&&(Nt.undefined(t.textEdit)||H.is(t))&&(Nt.undefined(t.additionalTextEdits)||Nt.typedArray(t.additionalTextEdits,H.is))},(L=T||(T={})).Comment="comment",L.Imports="imports",L.Region="region",(M=R||(R={})).create=function(e,t,n,r,i,o){const a={startLine:e,endLine:t};return Nt.defined(n)&&(a.startCharacter=n),Nt.defined(r)&&(a.endCharacter=r),Nt.defined(i)&&(a.kind=i),Nt.defined(o)&&(a.collapsedText=o),a},M.is=function(e){const t=e;return Nt.objectLiteral(t)&&Nt.uinteger(t.startLine)&&Nt.uinteger(t.startLine)&&(Nt.undefined(t.startCharacter)||Nt.uinteger(t.startCharacter))&&(Nt.undefined(t.endCharacter)||Nt.uinteger(t.endCharacter))&&(Nt.undefined(t.kind)||Nt.string(t.kind))},(F=j||(j={})).create=function(e,t){return{location:e,message:t}},F.is=function(e){let t=e;return Nt.defined(t)&&k.is(t.location)&&Nt.string(t.message)},(P=N||(N={})).Error=1,P.Warning=2,P.Information=3,P.Hint=4,(U=O||(O={})).Unnecessary=1,U.Deprecated=2,(D||(D={})).is=function(e){const t=e;return Nt.objectLiteral(t)&&Nt.string(t.href)},(B=V||(V={})).create=function(e,t,n,r,i,o){let a={range:e,message:t};return Nt.defined(n)&&(a.severity=n),Nt.defined(r)&&(a.code=r),Nt.defined(i)&&(a.source=i),Nt.defined(o)&&(a.relatedInformation=o),a},B.is=function(e){var t;let n=e;return Nt.defined(n)&&v.is(n.range)&&Nt.string(n.message)&&(Nt.number(n.severity)||Nt.undefined(n.severity))&&(Nt.integer(n.code)||Nt.string(n.code)||Nt.undefined(n.code))&&(Nt.undefined(n.codeDescription)||Nt.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(Nt.string(n.source)||Nt.undefined(n.source))&&(Nt.undefined(n.relatedInformation)||Nt.typedArray(n.relatedInformation,j.is))},(W=K||(K={})).create=function(e,t,...n){let r={title:e,command:t};return Nt.defined(n)&&n.length>0&&(r.arguments=n),r},W.is=function(e){let t=e;return Nt.defined(t)&&Nt.string(t.title)&&Nt.string(t.command)},(X=H||(H={})).replace=function(e,t){return{range:e,newText:t}},X.insert=function(e,t){return{range:{start:e,end:e},newText:t}},X.del=function(e){return{range:e,newText:""}},X.is=function(e){const t=e;return Nt.objectLiteral(t)&&Nt.string(t.newText)&&v.is(t.range)},(q=z||(z={})).create=function(e,t,n){const r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},q.is=function(e){const t=e;return Nt.objectLiteral(t)&&Nt.string(t.label)&&(Nt.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(Nt.string(t.description)||void 0===t.description)},($||($={})).is=function(e){const t=e;return Nt.string(t)},(J=Q||(Q={})).replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},J.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},J.del=function(e,t){return{range:e,newText:"",annotationId:t}},J.is=function(e){const t=e;return H.is(t)&&(z.is(t.annotationId)||$.is(t.annotationId))},(Y=G||(G={})).create=function(e,t){return{textDocument:e,edits:t}},Y.is=function(e){let t=e;return Nt.defined(t)&&de.is(t.textDocument)&&Array.isArray(t.edits)},(ee=Z||(Z={})).create=function(e,t,n){let r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},ee.is=function(e){let t=e;return t&&"create"===t.kind&&Nt.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Nt.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Nt.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||$.is(t.annotationId))},(ne=te||(te={})).create=function(e,t,n,r){let i={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),void 0!==r&&(i.annotationId=r),i},ne.is=function(e){let t=e;return t&&"rename"===t.kind&&Nt.string(t.oldUri)&&Nt.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Nt.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Nt.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||$.is(t.annotationId))},(ie=re||(re={})).create=function(e,t,n){let r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},ie.is=function(e){let t=e;return t&&"delete"===t.kind&&Nt.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Nt.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Nt.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||$.is(t.annotationId))},(oe||(oe={})).is=function(e){let t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((e=>Nt.string(e.kind)?Z.is(e)||te.is(e)||re.is(e):G.is(e))))},(se=ae||(ae={})).create=function(e){return{uri:e}},se.is=function(e){let t=e;return Nt.defined(t)&&Nt.string(t.uri)},(ue=ce||(ce={})).create=function(e,t){return{uri:e,version:t}},ue.is=function(e){let t=e;return Nt.defined(t)&&Nt.string(t.uri)&&Nt.integer(t.version)},(le=de||(de={})).create=function(e,t){return{uri:e,version:t}},le.is=function(e){let t=e;return Nt.defined(t)&&Nt.string(t.uri)&&(null===t.version||Nt.integer(t.version))},(fe=ge||(ge={})).create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},fe.is=function(e){let t=e;return Nt.defined(t)&&Nt.string(t.uri)&&Nt.string(t.languageId)&&Nt.integer(t.version)&&Nt.string(t.text)},(me=he||(he={})).PlainText="plaintext",me.Markdown="markdown",me.is=function(e){const t=e;return t===me.PlainText||t===me.Markdown},(pe||(pe={})).is=function(e){const t=e;return Nt.objectLiteral(e)&&he.is(t.kind)&&Nt.string(t.value)},(be=ve||(ve={})).Text=1,be.Method=2,be.Function=3,be.Constructor=4,be.Field=5,be.Variable=6,be.Class=7,be.Interface=8,be.Module=9,be.Property=10,be.Unit=11,be.Value=12,be.Enum=13,be.Keyword=14,be.Snippet=15,be.Color=16,be.File=17,be.Reference=18,be.Folder=19,be.EnumMember=20,be.Constant=21,be.Struct=22,be.Event=23,be.Operator=24,be.TypeParameter=25,(Ce=ke||(ke={})).PlainText=1,Ce.Snippet=2,(_e||(_e={})).Deprecated=1,(ye=we||(we={})).create=function(e,t,n){return{newText:e,insert:t,replace:n}},ye.is=function(e){const t=e;return t&&Nt.string(t.newText)&&v.is(t.insert)&&v.is(t.replace)},(Ee=xe||(xe={})).asIs=1,Ee.adjustIndentation=2,(Ie||(Ie={})).is=function(e){const t=e;return t&&(Nt.string(t.detail)||void 0===t.detail)&&(Nt.string(t.description)||void 0===t.description)},(Se||(Se={})).create=function(e){return{label:e}},(Ae||(Ae={})).create=function(e,t){return{items:e||[],isIncomplete:!!t}},(Le=Te||(Te={})).fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},Le.is=function(e){const t=e;return Nt.string(t)||Nt.objectLiteral(t)&&Nt.string(t.language)&&Nt.string(t.value)},(Re||(Re={})).is=function(e){let t=e;return!!t&&Nt.objectLiteral(t)&&(pe.is(t.contents)||Te.is(t.contents)||Nt.typedArray(t.contents,Te.is))&&(void 0===e.range||v.is(e.range))},(Me||(Me={})).create=function(e,t){return t?{label:e,documentation:t}:{label:e}},(je||(je={})).create=function(e,t,...n){let r={label:e};return Nt.defined(t)&&(r.documentation=t),Nt.defined(n)?r.parameters=n:r.parameters=[],r},(Ne=Fe||(Fe={})).Text=1,Ne.Read=2,Ne.Write=3,(Pe||(Pe={})).create=function(e,t){let n={range:e};return Nt.number(t)&&(n.kind=t),n},(Ue=Oe||(Oe={})).File=1,Ue.Module=2,Ue.Namespace=3,Ue.Package=4,Ue.Class=5,Ue.Method=6,Ue.Property=7,Ue.Field=8,Ue.Constructor=9,Ue.Enum=10,Ue.Interface=11,Ue.Function=12,Ue.Variable=13,Ue.Constant=14,Ue.String=15,Ue.Number=16,Ue.Boolean=17,Ue.Array=18,Ue.Object=19,Ue.Key=20,Ue.Null=21,Ue.EnumMember=22,Ue.Struct=23,Ue.Event=24,Ue.Operator=25,Ue.TypeParameter=26,(De||(De={})).Deprecated=1,(Ve||(Ve={})).create=function(e,t,n,r,i){let o={name:e,kind:t,location:{uri:r,range:n}};return i&&(o.containerName=i),o},(Be||(Be={})).create=function(e,t,n,r){return void 0!==r?{name:e,kind:t,location:{uri:n,range:r}}:{name:e,kind:t,location:{uri:n}}},(We=Ke||(Ke={})).create=function(e,t,n,r,i,o){let a={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==o&&(a.children=o),a},We.is=function(e){let t=e;return t&&Nt.string(t.name)&&Nt.number(t.kind)&&v.is(t.range)&&v.is(t.selectionRange)&&(void 0===t.detail||Nt.string(t.detail))&&(void 0===t.deprecated||Nt.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))},(Xe=He||(He={})).Empty="",Xe.QuickFix="quickfix",Xe.Refactor="refactor",Xe.RefactorExtract="refactor.extract",Xe.RefactorInline="refactor.inline",Xe.RefactorRewrite="refactor.rewrite",Xe.Source="source",Xe.SourceOrganizeImports="source.organizeImports",Xe.SourceFixAll="source.fixAll",(qe=ze||(ze={})).Invoked=1,qe.Automatic=2,(Qe=$e||($e={})).create=function(e,t,n){let r={diagnostics:e};return null!=t&&(r.only=t),null!=n&&(r.triggerKind=n),r},Qe.is=function(e){let t=e;return Nt.defined(t)&&Nt.typedArray(t.diagnostics,V.is)&&(void 0===t.only||Nt.typedArray(t.only,Nt.string))&&(void 0===t.triggerKind||t.triggerKind===ze.Invoked||t.triggerKind===ze.Automatic)},(Ge=Je||(Je={})).create=function(e,t,n){let r={title:e},i=!0;return"string"==typeof t?(i=!1,r.kind=t):K.is(t)?r.command=t:r.edit=t,i&&void 0!==n&&(r.kind=n),r},Ge.is=function(e){let t=e;return t&&Nt.string(t.title)&&(void 0===t.diagnostics||Nt.typedArray(t.diagnostics,V.is))&&(void 0===t.kind||Nt.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||K.is(t.command))&&(void 0===t.isPreferred||Nt.boolean(t.isPreferred))&&(void 0===t.edit||oe.is(t.edit))},(Ze=Ye||(Ye={})).create=function(e,t){let n={range:e};return Nt.defined(t)&&(n.data=t),n},Ze.is=function(e){let t=e;return Nt.defined(t)&&v.is(t.range)&&(Nt.undefined(t.command)||K.is(t.command))},(tt=et||(et={})).create=function(e,t){return{tabSize:e,insertSpaces:t}},tt.is=function(e){let t=e;return Nt.defined(t)&&Nt.uinteger(t.tabSize)&&Nt.boolean(t.insertSpaces)},(rt=nt||(nt={})).create=function(e,t,n){return{range:e,target:t,data:n}},rt.is=function(e){let t=e;return Nt.defined(t)&&v.is(t.range)&&(Nt.undefined(t.target)||Nt.string(t.target))},(ot=it||(it={})).create=function(e,t){return{range:e,parent:t}},ot.is=function(e){let t=e;return Nt.objectLiteral(t)&&v.is(t.range)&&(void 0===t.parent||ot.is(t.parent))},(st=at||(at={})).namespace="namespace",st.type="type",st.class="class",st.enum="enum",st.interface="interface",st.struct="struct",st.typeParameter="typeParameter",st.parameter="parameter",st.variable="variable",st.property="property",st.enumMember="enumMember",st.event="event",st.function="function",st.method="method",st.macro="macro",st.keyword="keyword",st.modifier="modifier",st.comment="comment",st.string="string",st.number="number",st.regexp="regexp",st.operator="operator",st.decorator="decorator",(ut=ct||(ct={})).declaration="declaration",ut.definition="definition",ut.readonly="readonly",ut.static="static",ut.deprecated="deprecated",ut.abstract="abstract",ut.async="async",ut.modification="modification",ut.documentation="documentation",ut.defaultLibrary="defaultLibrary",(dt||(dt={})).is=function(e){const t=e;return Nt.objectLiteral(t)&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])},(gt=lt||(lt={})).create=function(e,t){return{range:e,text:t}},gt.is=function(e){const t=e;return null!=t&&v.is(t.range)&&Nt.string(t.text)},(ht=ft||(ft={})).create=function(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}},ht.is=function(e){const t=e;return null!=t&&v.is(t.range)&&Nt.boolean(t.caseSensitiveLookup)&&(Nt.string(t.variableName)||void 0===t.variableName)},(pt=mt||(mt={})).create=function(e,t){return{range:e,expression:t}},pt.is=function(e){const t=e;return null!=t&&v.is(t.range)&&(Nt.string(t.expression)||void 0===t.expression)},(bt=vt||(vt={})).create=function(e,t){return{frameId:e,stoppedLocation:t}},bt.is=function(e){const t=e;return Nt.defined(t)&&v.is(e.stoppedLocation)},(Ct=kt||(kt={})).Type=1,Ct.Parameter=2,Ct.is=function(e){return 1===e||2===e},(wt=_t||(_t={})).create=function(e){return{value:e}},wt.is=function(e){const t=e;return Nt.objectLiteral(t)&&(void 0===t.tooltip||Nt.string(t.tooltip)||pe.is(t.tooltip))&&(void 0===t.location||k.is(t.location))&&(void 0===t.command||K.is(t.command))},(xt=yt||(yt={})).create=function(e,t,n){const r={position:e,label:t};return void 0!==n&&(r.kind=n),r},xt.is=function(e){const t=e;return Nt.objectLiteral(t)&&m.is(t.position)&&(Nt.string(t.label)||Nt.typedArray(t.label,_t.is))&&(void 0===t.kind||kt.is(t.kind))&&void 0===t.textEdits||Nt.typedArray(t.textEdits,H.is)&&(void 0===t.tooltip||Nt.string(t.tooltip)||pe.is(t.tooltip))&&(void 0===t.paddingLeft||Nt.boolean(t.paddingLeft))&&(void 0===t.paddingRight||Nt.boolean(t.paddingRight))},(Et||(Et={})).createSnippet=function(e){return{kind:"snippet",value:e}},(It||(It={})).create=function(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}},(St||(St={})).create=function(e){return{items:e}},(Tt=At||(At={})).Invoked=0,Tt.Automatic=1,(Lt||(Lt={})).create=function(e,t){return{range:e,text:t}},(Rt||(Rt={})).create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}},(Mt||(Mt={})).is=function(e){const t=e;return Nt.objectLiteral(t)&&d.is(t.uri)&&Nt.string(t.name)},function(e){function t(e,n){if(e.length<=1)return e;const r=e.length/2|0,i=e.slice(0,r),o=e.slice(r);t(i,n),t(o,n);let a=0,s=0,c=0;for(;a<i.length&&s<o.length;){let t=n(i[a],o[s]);e[c++]=t<=0?i[a++]:o[s++]}for(;a<i.length;)e[c++]=i[a++];for(;s<o.length;)e[c++]=o[s++];return e}e.create=function(e,t,n,r){return new Pt(e,t,n,r)},e.is=function(e){let t=e;return!!(Nt.defined(t)&&Nt.string(t.uri)&&(Nt.undefined(t.languageId)||Nt.string(t.languageId))&&Nt.uinteger(t.lineCount)&&Nt.func(t.getText)&&Nt.func(t.positionAt)&&Nt.func(t.offsetAt))},e.applyEdits=function(e,n){let r=e.getText(),i=t(n,((e,t)=>{let n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),o=r.length;for(let t=i.length-1;t>=0;t--){let n=i[t],a=e.offsetAt(n.range.start),s=e.offsetAt(n.range.end);if(!(s<=o))throw new Error("Overlapping edit");r=r.substring(0,a)+n.newText+r.substring(s,r.length),o=a}return r}}(jt||(jt={}));var Nt,Pt=class{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&(e.push(r),n=!1);let i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return m.create(0,e);for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return m.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}};!function(e){const t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(Nt||(Nt={}));var Ot=class{constructor(e,t,n){this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);const r=e=>{let t,n=e.getLanguageId();n===this._languageId&&(this._listener[e.uri.toString()]=e.onDidChangeContent((()=>{window.clearTimeout(t),t=window.setTimeout((()=>this._doValidate(e.uri,n)),500)})),this._doValidate(e.uri,n))},i=e=>{c.editor.setModelMarkers(e,this._languageId,[]);let t=e.uri.toString(),n=this._listener[t];n&&(n.dispose(),delete this._listener[t])};this._disposables.push(c.editor.onDidCreateModel(r)),this._disposables.push(c.editor.onWillDisposeModel(i)),this._disposables.push(c.editor.onDidChangeModelLanguage((e=>{i(e.model),r(e.model)}))),this._disposables.push(n((e=>{c.editor.getModels().forEach((e=>{e.getLanguageId()===this._languageId&&(i(e),r(e))}))}))),this._disposables.push({dispose:()=>{c.editor.getModels().forEach(i);for(let e in this._listener)this._listener[e].dispose()}}),c.editor.getModels().forEach(r)}dispose(){this._disposables.forEach((e=>e&&e.dispose())),this._disposables.length=0}_doValidate(e,t){this._worker(e).then((t=>t.doValidation(e.toString()))).then((n=>{const r=n.map((e=>function(e,t){let n="number"==typeof t.code?String(t.code):t.code;return{severity:Ut(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:n,source:t.source}}(0,e)));let i=c.editor.getModel(e);i&&i.getLanguageId()===t&&c.editor.setModelMarkers(i,t,r)})).then(void 0,(e=>{}))}};function Ut(e){switch(e){case N.Error:return c.MarkerSeverity.Error;case N.Warning:return c.MarkerSeverity.Warning;case N.Information:return c.MarkerSeverity.Info;case N.Hint:return c.MarkerSeverity.Hint;default:return c.MarkerSeverity.Info}}var Dt=class{constructor(e,t){this._worker=e,this._triggerCharacters=t}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(e,t,n,r){const i=e.uri;return this._worker(i).then((e=>e.doComplete(i.toString(),Vt(t)))).then((n=>{if(!n)return;const r=e.getWordUntilPosition(t),i=new c.Range(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn),o=n.items.map((e=>{const t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,command:(n=e.command,n&&"editor.action.triggerSuggest"===n.command?{id:n.command,title:n.title,arguments:n.arguments}:void 0),range:i,kind:Wt(e.kind)};var n,r;return e.textEdit&&(void 0!==(r=e.textEdit).insert&&void 0!==r.replace?t.range={insert:Kt(e.textEdit.insert),replace:Kt(e.textEdit.replace)}:t.range=Kt(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(Ht)),e.insertTextFormat===ke.Snippet&&(t.insertTextRules=c.languages.CompletionItemInsertTextRule.InsertAsSnippet),t}));return{isIncomplete:n.isIncomplete,suggestions:o}}))}};function Vt(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function Bt(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function Kt(e){if(e)return new c.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function Wt(e){const t=c.languages.CompletionItemKind;switch(e){case ve.Text:return t.Text;case ve.Method:return t.Method;case ve.Function:return t.Function;case ve.Constructor:return t.Constructor;case ve.Field:return t.Field;case ve.Variable:return t.Variable;case ve.Class:return t.Class;case ve.Interface:return t.Interface;case ve.Module:return t.Module;case ve.Property:return t.Property;case ve.Unit:return t.Unit;case ve.Value:return t.Value;case ve.Enum:return t.Enum;case ve.Keyword:return t.Keyword;case ve.Snippet:return t.Snippet;case ve.Color:return t.Color;case ve.File:return t.File;case ve.Reference:return t.Reference}return t.Property}function Ht(e){if(e)return{range:Kt(e.range),text:e.newText}}var Xt=class{constructor(e){this._worker=e}provideHover(e,t,n){let r=e.uri;return this._worker(r).then((e=>e.doHover(r.toString(),Vt(t)))).then((e=>{if(e)return{range:Kt(e.range),contents:qt(e.contents)}}))}};function zt(e){return"string"==typeof e?{value:e}:(t=e)&&"object"==typeof t&&"string"==typeof t.kind?"plaintext"===e.kind?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+"\n"+e.value+"\n```\n"};var t}function qt(e){if(e)return Array.isArray(e)?e.map(zt):[zt(e)]}var $t=class{constructor(e){this._worker=e}provideDocumentHighlights(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.findDocumentHighlights(r.toString(),Vt(t)))).then((e=>{if(e)return e.map((e=>({range:Kt(e.range),kind:Qt(e.kind)})))}))}};function Qt(e){switch(e){case Fe.Read:return c.languages.DocumentHighlightKind.Read;case Fe.Write:return c.languages.DocumentHighlightKind.Write;case Fe.Text:return c.languages.DocumentHighlightKind.Text}return c.languages.DocumentHighlightKind.Text}var Jt=class{constructor(e){this._worker=e}provideDefinition(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.findDefinition(r.toString(),Vt(t)))).then((e=>{if(e)return[Gt(e)]}))}};function Gt(e){return{uri:c.Uri.parse(e.uri),range:Kt(e.range)}}var Yt=class{constructor(e){this._worker=e}provideReferences(e,t,n,r){const i=e.uri;return this._worker(i).then((e=>e.findReferences(i.toString(),Vt(t)))).then((e=>{if(e)return e.map(Gt)}))}},Zt=class{constructor(e){this._worker=e}provideRenameEdits(e,t,n,r){const i=e.uri;return this._worker(i).then((e=>e.doRename(i.toString(),Vt(t),n))).then((e=>function(e){if(!e||!e.changes)return;let t=[];for(let n in e.changes){const r=c.Uri.parse(n);for(let i of e.changes[n])t.push({resource:r,versionId:void 0,textEdit:{range:Kt(i.range),text:i.newText}})}return{edits:t}}(e)))}};var en=class{constructor(e){this._worker=e}provideDocumentSymbols(e,t){const n=e.uri;return this._worker(n).then((e=>e.findDocumentSymbols(n.toString()))).then((e=>{if(e)return e.map((e=>"children"in e?tn(e):{name:e.name,detail:"",containerName:e.containerName,kind:nn(e.kind),range:Kt(e.location.range),selectionRange:Kt(e.location.range),tags:[]}))}))}};function tn(e){return{name:e.name,detail:e.detail??"",kind:nn(e.kind),range:Kt(e.range),selectionRange:Kt(e.selectionRange),tags:e.tags??[],children:(e.children??[]).map((e=>tn(e)))}}function nn(e){let t=c.languages.SymbolKind;switch(e){case Oe.File:return t.File;case Oe.Module:return t.Module;case Oe.Namespace:return t.Namespace;case Oe.Package:return t.Package;case Oe.Class:return t.Class;case Oe.Method:return t.Method;case Oe.Property:return t.Property;case Oe.Field:return t.Field;case Oe.Constructor:return t.Constructor;case Oe.Enum:return t.Enum;case Oe.Interface:return t.Interface;case Oe.Function:return t.Function;case Oe.Variable:return t.Variable;case Oe.Constant:return t.Constant;case Oe.String:return t.String;case Oe.Number:return t.Number;case Oe.Boolean:return t.Boolean;case Oe.Array:return t.Array}return t.Function}var rn=class{constructor(e){this._worker=e}provideLinks(e,t){const n=e.uri;return this._worker(n).then((e=>e.findDocumentLinks(n.toString()))).then((e=>{if(e)return{links:e.map((e=>({range:Kt(e.range),url:e.target})))}}))}},on=class{constructor(e){this._worker=e}provideDocumentFormattingEdits(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.format(r.toString(),null,sn(t)).then((e=>{if(e&&0!==e.length)return e.map(Ht)}))))}},an=class{constructor(e){this._worker=e,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,t,n,r){const i=e.uri;return this._worker(i).then((e=>e.format(i.toString(),Bt(t),sn(n)).then((e=>{if(e&&0!==e.length)return e.map(Ht)}))))}};function sn(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var cn=class{constructor(e){this._worker=e}provideDocumentColors(e,t){const n=e.uri;return this._worker(n).then((e=>e.findDocumentColors(n.toString()))).then((e=>{if(e)return e.map((e=>({color:e.color,range:Kt(e.range)})))}))}provideColorPresentations(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.getColorPresentations(r.toString(),t.color,Bt(t.range)))).then((e=>{if(e)return e.map((e=>{let t={label:e.label};return e.textEdit&&(t.textEdit=Ht(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(Ht)),t}))}))}},un=class{constructor(e){this._worker=e}provideFoldingRanges(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.getFoldingRanges(r.toString(),t))).then((e=>{if(e)return e.map((e=>{const t={start:e.startLine+1,end:e.endLine+1};return void 0!==e.kind&&(t.kind=function(e){switch(e){case T.Comment:return c.languages.FoldingRangeKind.Comment;case T.Imports:return c.languages.FoldingRangeKind.Imports;case T.Region:return c.languages.FoldingRangeKind.Region}return}(e.kind)),t}))}))}};var dn,ln,gn=class{constructor(e){this._worker=e}provideSelectionRanges(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.getSelectionRanges(r.toString(),t.map(Vt)))).then((e=>{if(e)return e.map((e=>{const t=[];for(;e;)t.push({range:Kt(e.range)}),e=e.parent;return t}))}))}};function fn(e){return 32===e||9===e}function hn(e){return 10===e||13===e}function mn(e){return e>=48&&e<=57}(ln=dn||(dn={}))[ln.lineFeed=10]="lineFeed",ln[ln.carriageReturn=13]="carriageReturn",ln[ln.space=32]="space",ln[ln._0=48]="_0",ln[ln._1=49]="_1",ln[ln._2=50]="_2",ln[ln._3=51]="_3",ln[ln._4=52]="_4",ln[ln._5=53]="_5",ln[ln._6=54]="_6",ln[ln._7=55]="_7",ln[ln._8=56]="_8",ln[ln._9=57]="_9",ln[ln.a=97]="a",ln[ln.b=98]="b",ln[ln.c=99]="c",ln[ln.d=100]="d",ln[ln.e=101]="e",ln[ln.f=102]="f",ln[ln.g=103]="g",ln[ln.h=104]="h",ln[ln.i=105]="i",ln[ln.j=106]="j",ln[ln.k=107]="k",ln[ln.l=108]="l",ln[ln.m=109]="m",ln[ln.n=110]="n",ln[ln.o=111]="o",ln[ln.p=112]="p",ln[ln.q=113]="q",ln[ln.r=114]="r",ln[ln.s=115]="s",ln[ln.t=116]="t",ln[ln.u=117]="u",ln[ln.v=118]="v",ln[ln.w=119]="w",ln[ln.x=120]="x",ln[ln.y=121]="y",ln[ln.z=122]="z",ln[ln.A=65]="A",ln[ln.B=66]="B",ln[ln.C=67]="C",ln[ln.D=68]="D",ln[ln.E=69]="E",ln[ln.F=70]="F",ln[ln.G=71]="G",ln[ln.H=72]="H",ln[ln.I=73]="I",ln[ln.J=74]="J",ln[ln.K=75]="K",ln[ln.L=76]="L",ln[ln.M=77]="M",ln[ln.N=78]="N",ln[ln.O=79]="O",ln[ln.P=80]="P",ln[ln.Q=81]="Q",ln[ln.R=82]="R",ln[ln.S=83]="S",ln[ln.T=84]="T",ln[ln.U=85]="U",ln[ln.V=86]="V",ln[ln.W=87]="W",ln[ln.X=88]="X",ln[ln.Y=89]="Y",ln[ln.Z=90]="Z",ln[ln.asterisk=42]="asterisk",ln[ln.backslash=92]="backslash",ln[ln.closeBrace=125]="closeBrace",ln[ln.closeBracket=93]="closeBracket",ln[ln.colon=58]="colon",ln[ln.comma=44]="comma",ln[ln.dot=46]="dot",ln[ln.doubleQuote=34]="doubleQuote",ln[ln.minus=45]="minus",ln[ln.openBrace=123]="openBrace",ln[ln.openBracket=91]="openBracket",ln[ln.plus=43]="plus",ln[ln.slash=47]="slash",ln[ln.formFeed=12]="formFeed",ln[ln.tab=9]="tab",new Array(20).fill(0).map(((e,t)=>" ".repeat(t)));var pn,vn=200;new Array(vn).fill(0).map(((e,t)=>"\n"+" ".repeat(t))),new Array(vn).fill(0).map(((e,t)=>"\r"+" ".repeat(t))),new Array(vn).fill(0).map(((e,t)=>"\r\n"+" ".repeat(t))),new Array(vn).fill(0).map(((e,t)=>"\n"+"\t".repeat(t))),new Array(vn).fill(0).map(((e,t)=>"\r"+"\t".repeat(t))),new Array(vn).fill(0).map(((e,t)=>"\r\n"+"\t".repeat(t))),(pn||(pn={})).DEFAULT={allowTrailingComma:!1};var bn,kn,Cn,_n,wn,yn,xn=function(e,t=!1){const n=e.length;let r=0,i="",o=0,a=16,s=0,c=0,u=0,d=0,l=0;function g(t,n){let i=0,o=0;for(;i<t||!n;){let t=e.charCodeAt(r);if(t>=48&&t<=57)o=16*o+t-48;else if(t>=65&&t<=70)o=16*o+t-65+10;else{if(!(t>=97&&t<=102))break;o=16*o+t-97+10}r++,i++}return i<t&&(o=-1),o}function f(){if(i="",l=0,o=r,c=s,d=u,r>=n)return o=n,a=17;let t=e.charCodeAt(r);if(fn(t)){do{r++,i+=String.fromCharCode(t),t=e.charCodeAt(r)}while(fn(t));return a=15}if(hn(t))return r++,i+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,i+="\n"),s++,u=r,a=14;switch(t){case 123:return r++,a=1;case 125:return r++,a=2;case 91:return r++,a=3;case 93:return r++,a=4;case 58:return r++,a=6;case 44:return r++,a=5;case 34:return r++,i=function(){let t="",i=r;for(;;){if(r>=n){t+=e.substring(i,r),l=2;break}const o=e.charCodeAt(r);if(34===o){t+=e.substring(i,r),r++;break}if(92!==o){if(o>=0&&o<=31){if(hn(o)){t+=e.substring(i,r),l=2;break}l=6}r++}else{if(t+=e.substring(i,r),r++,r>=n){l=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:const e=g(4,!0);e>=0?t+=String.fromCharCode(e):l=4;break;default:l=5}i=r}}return t}(),a=10;case 47:const c=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!hn(e.charCodeAt(r));)r++;return i=e.substring(c,r),a=12}if(42===e.charCodeAt(r+1)){r+=2;const t=n-1;let o=!1;for(;r<t;){const t=e.charCodeAt(r);if(42===t&&47===e.charCodeAt(r+1)){r+=2,o=!0;break}r++,hn(t)&&(13===t&&10===e.charCodeAt(r)&&r++,s++,u=r)}return o||(r++,l=1),i=e.substring(c,r),a=13}return i+=String.fromCharCode(t),r++,a=16;case 45:if(i+=String.fromCharCode(t),r++,r===n||!mn(e.charCodeAt(r)))return a=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=function(){let t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&mn(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(r++,!(r<e.length&&mn(e.charCodeAt(r))))return l=3,e.substring(t,r);for(r++;r<e.length&&mn(e.charCodeAt(r));)r++}let n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if(r++,(r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&mn(e.charCodeAt(r))){for(r++;r<e.length&&mn(e.charCodeAt(r));)r++;n=r}else l=3;return e.substring(t,n)}(),a=11;default:for(;r<n&&h(t);)r++,t=e.charCodeAt(r);if(o!==r){switch(i=e.substring(o,r),i){case"true":return a=8;case"false":return a=9;case"null":return a=7}return a=16}return i+=String.fromCharCode(t),r++,a=16}}function h(e){if(fn(e)||hn(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,i="",o=0,a=16,l=0},getPosition:()=>r,scan:t?function(){let e;do{e=f()}while(e>=12&&e<=15);return e}:f,getToken:()=>a,getTokenValue:()=>i,getTokenOffset:()=>o,getTokenLength:()=>r-o,getTokenStartLine:()=>c,getTokenStartCharacter:()=>o-d,getTokenError:()=>l}};function En(e){return{getInitialState:()=>new Dn(null,null,!1,null),tokenize:(t,n)=>function(e,t,n,r=0){let i=0,o=!1;switch(n.scanError){case 2:t='"'+t,i=1;break;case 1:t="/*"+t,i=2}const a=xn(t);let s=n.lastWasColon,c=n.parents;const u={tokens:[],endState:n.clone()};for(;;){let d=r+a.getPosition(),l="";const g=a.scan();if(17===g)break;if(d===r+a.getPosition())throw new Error("Scanner did not advance, next 3 characters are: "+t.substr(a.getPosition(),3));switch(o&&(d-=i),o=i>0,g){case 1:c=Un.push(c,0),l=Sn,s=!1;break;case 2:c=Un.pop(c),l=Sn,s=!1;break;case 3:c=Un.push(c,1),l=An,s=!1;break;case 4:c=Un.pop(c),l=An,s=!1;break;case 6:l=Tn,s=!0;break;case 5:l=Ln,s=!1;break;case 8:case 9:l=Rn,s=!1;break;case 7:l=Mn,s=!1;break;case 10:const e=c?c.type:0;l=s||1===e?jn:Nn,s=!1;break;case 11:l=Fn,s=!1}if(e)switch(g){case 12:l=On;break;case 13:l=Pn}u.endState=new Dn(n.getStateData(),a.getTokenError(),s,c),u.tokens.push({startIndex:d,scopes:l})}return u}(e,t,n)}}(kn=bn||(bn={}))[kn.None=0]="None",kn[kn.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",kn[kn.UnexpectedEndOfString=2]="UnexpectedEndOfString",kn[kn.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",kn[kn.InvalidUnicode=4]="InvalidUnicode",kn[kn.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",kn[kn.InvalidCharacter=6]="InvalidCharacter",(_n=Cn||(Cn={}))[_n.OpenBraceToken=1]="OpenBraceToken",_n[_n.CloseBraceToken=2]="CloseBraceToken",_n[_n.OpenBracketToken=3]="OpenBracketToken",_n[_n.CloseBracketToken=4]="CloseBracketToken",_n[_n.CommaToken=5]="CommaToken",_n[_n.ColonToken=6]="ColonToken",_n[_n.NullKeyword=7]="NullKeyword",_n[_n.TrueKeyword=8]="TrueKeyword",_n[_n.FalseKeyword=9]="FalseKeyword",_n[_n.StringLiteral=10]="StringLiteral",_n[_n.NumericLiteral=11]="NumericLiteral",_n[_n.LineCommentTrivia=12]="LineCommentTrivia",_n[_n.BlockCommentTrivia=13]="BlockCommentTrivia",_n[_n.LineBreakTrivia=14]="LineBreakTrivia",_n[_n.Trivia=15]="Trivia",_n[_n.Unknown=16]="Unknown",_n[_n.EOF=17]="EOF",(yn=wn||(wn={}))[yn.InvalidSymbol=1]="InvalidSymbol",yn[yn.InvalidNumberFormat=2]="InvalidNumberFormat",yn[yn.PropertyNameExpected=3]="PropertyNameExpected",yn[yn.ValueExpected=4]="ValueExpected",yn[yn.ColonExpected=5]="ColonExpected",yn[yn.CommaExpected=6]="CommaExpected",yn[yn.CloseBraceExpected=7]="CloseBraceExpected",yn[yn.CloseBracketExpected=8]="CloseBracketExpected",yn[yn.EndOfFileExpected=9]="EndOfFileExpected",yn[yn.InvalidCommentToken=10]="InvalidCommentToken",yn[yn.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",yn[yn.UnexpectedEndOfString=12]="UnexpectedEndOfString",yn[yn.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",yn[yn.InvalidUnicode=14]="InvalidUnicode",yn[yn.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",yn[yn.InvalidCharacter=16]="InvalidCharacter";var In,Sn="delimiter.bracket.json",An="delimiter.array.json",Tn="delimiter.colon.json",Ln="delimiter.comma.json",Rn="keyword.json",Mn="keyword.json",jn="string.value.json",Fn="number.json",Nn="string.key.json",Pn="comment.block.json",On="comment.line.json",Un=class e{constructor(e,t){this.parent=e,this.type=t}static pop(e){return e?e.parent:null}static push(t,n){return new e(t,n)}static equals(e,t){if(!e&&!t)return!0;if(!e||!t)return!1;for(;e&&t;){if(e===t)return!0;if(e.type!==t.type)return!1;e=e.parent,t=t.parent}return!0}},Dn=class e{constructor(e,t,n,r){this._state=e,this.scanError=t,this.lastWasColon=n,this.parents=r}clone(){return new e(this._state,this.scanError,this.lastWasColon,this.parents)}equals(t){return t===this||!!(t&&t instanceof e)&&(this.scanError===t.scanError&&this.lastWasColon===t.lastWasColon&&Un.equals(this.parents,t.parents))}getStateData(){return this._state}setStateData(e){this._state=e}};function Vn(){return new Promise(((e,t)=>{if(!In)return t("JSON not registered!");e(In)}))}var Bn=class extends Ot{constructor(e,t,n){super(e,t,n.onDidChange),this._disposables.push(c.editor.onWillDisposeModel((e=>{this._resetSchema(e.uri)}))),this._disposables.push(c.editor.onDidChangeModelLanguage((e=>{this._resetSchema(e.model.uri)})))}_resetSchema(e){this._worker().then((t=>{t.resetSchema(e.toString())}))}};function Kn(e){const t=[],n=[],r=new Ft(e);function i(){const{languageId:t,modeConfiguration:r}=e;Hn(n),r.documentFormattingEdits&&n.push(c.languages.registerDocumentFormattingEditProvider(t,new on(In))),r.documentRangeFormattingEdits&&n.push(c.languages.registerDocumentRangeFormattingEditProvider(t,new an(In))),r.completionItems&&n.push(c.languages.registerCompletionItemProvider(t,new Dt(In,[" ",":",'"']))),r.hovers&&n.push(c.languages.registerHoverProvider(t,new Xt(In))),r.documentSymbols&&n.push(c.languages.registerDocumentSymbolProvider(t,new en(In))),r.tokens&&n.push(c.languages.setTokensProvider(t,En(!0))),r.colors&&n.push(c.languages.registerColorProvider(t,new cn(In))),r.foldingRanges&&n.push(c.languages.registerFoldingRangeProvider(t,new un(In))),r.diagnostics&&n.push(new Bn(t,In,e)),r.selectionRanges&&n.push(c.languages.registerSelectionRangeProvider(t,new gn(In)))}t.push(r),In=(...e)=>r.getLanguageServiceWorker(...e),i(),t.push(c.languages.setLanguageConfiguration(e.languageId,Xn));let o=e.modeConfiguration;return e.onDidChange((e=>{e.modeConfiguration!==o&&(o=e.modeConfiguration,i())})),t.push(Wn(n)),Wn(t)}function Wn(e){return{dispose:()=>Hn(e)}}function Hn(e){for(;e.length;)e.pop().dispose()}var Xn={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"]],autoClosingPairs:[{open:"{",close:"}",notIn:["string"]},{open:"[",close:"]",notIn:["string"]},{open:'"',close:'"',notIn:["string"]}]};export{Dt as CompletionAdapter,Jt as DefinitionAdapter,Ot as DiagnosticsAdapter,cn as DocumentColorAdapter,on as DocumentFormattingEditProvider,$t as DocumentHighlightAdapter,rn as DocumentLinkAdapter,an as DocumentRangeFormattingEditProvider,en as DocumentSymbolAdapter,un as FoldingRangeAdapter,Xt as HoverAdapter,Yt as ReferenceAdapter,Zt as RenameAdapter,gn as SelectionRangeAdapter,Ft as WorkerManager,Vt as fromPosition,Bt as fromRange,Vn as getWorker,Kn as setupMode,Kt as toRange,Ht as toTextEdit};
