import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as o,l,s as t,r as i,y as a,o as r,c as s,e as n,w as m,z as p,t as c,a as u,F as d,_ as f}from"./index-3XfDPlIS.js";import{u as h,_ as v}from"./useSearch-C9auOKSx.js";import{g as b}from"./index-BlcO9gtB.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./useForm-CxJHOWP1.js";import"./el-form-BY8piFS2.js";import"./castArray-uOT054sj.js";import"./el-col-CN1tVfqh.js";import"./el-tag-DcMbxLLg.js";import"./el-checkbox-DjLAvZXr.js";import"./index-tjM0-mlU.js";import"./el-radio-group-evFfsZkP.js";/* empty css                          */import"./el-input-number-CfcpPMpr.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-virtual-list-Drl4IGmp.js";import"./raf-BoCEWvzN.js";import"./el-select-v2-CJw7ZO42.js";import"./el-switch-C-DLgt5X.js";import"./el-autocomplete-DpYoUHkX.js";import"./el-divider-D9UCOo44.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-upload-D4EYBM-E.js";import"./el-progress-DtkkA0nz.js";import"./InputPassword-CsftE_fC.js";import"./style.css_vue_type_style_index_0_src_true_lang-C2CruVLZ.js";import"./JsonEditor.vue_vue_type_script_setup_true_lang-C3k54pLm.js";import"./IconPicker-CGJrUvM2.js";import"./el-tab-pane-xcqYouKU.js";import"./el-pagination-DwzzZyu4.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./tsxHelper-C7SpLWNA.js";/* empty css                        */import"./useIcon-k-uSyz6l.js";import"./index-Dz8ZrwBc.js";const D=f(o({__name:"Search",setup(o){const{t:f}=l(),{searchRegister:D,searchMethods:j}=h(),{setSchema:_,setProps:g,setValues:k}=j,I=[{value:"1",label:"Level one 1",children:[{value:"1-1",label:"Level two 1-1",children:[{value:"1-1-1",label:"Level three 1-1-1"}]}]},{value:"2",label:"Level one 2",children:[{value:"2-1",label:"Level two 2-1",children:[{value:"2-1-1",label:"Level three 2-1-1"}]},{value:"2-2",label:"Level two 2-2",children:[{value:"2-2-1",label:"Level three 2-2-1"}]}]},{value:"3",label:"Level one 3",children:[{value:"3-1",label:"Level two 3-1",children:[{value:"3-1-1",label:"Level three 3-1-1"}]},{value:"3-2",label:"Level two 3-2",children:[{value:"3-2-1",label:"Level three 3-2-1"}]}]}],y=t([{field:"field1",label:f("formDemo.input"),component:"Input"},{field:"field2",label:f("formDemo.select"),component:"Select",componentProps:{options:[{label:"option1",value:"1"},{label:"option2",value:"2"}],on:{change:e=>{}}}},{field:"field3",label:f("formDemo.radio"),component:"RadioGroup",componentProps:{options:[{label:"option-1",value:"1"},{label:"option-2",value:"2"}]}},{field:"field5",component:"DatePicker",label:f("formDemo.datePicker"),componentProps:{type:"date"}},{field:"field6",component:"TimeSelect",label:f("formDemo.timeSelect")},{field:"field8",label:f("formDemo.input"),component:"Input"},{field:"field9",label:f("formDemo.input"),component:"Input"},{field:"field10",label:f("formDemo.input"),component:"Input"},{field:"field11",label:f("formDemo.input"),component:"Input"},{field:"field12",label:f("formDemo.input"),component:"Input"},{field:"field13",label:f("formDemo.input"),component:"Input"},{field:"field14",label:f("formDemo.input"),component:"Input"},{field:"field15",label:f("formDemo.input"),component:"Input"},{field:"field16",label:f("formDemo.input"),component:"Input"},{field:"field17",label:f("formDemo.input"),component:"Input"},{field:"field18",label:f("formDemo.input"),component:"Input"},{field:"field19",label:`${f("formDemo.treeSelect")}`,component:"TreeSelect",optionApi:async()=>await new Promise((e=>{setTimeout((()=>{e(I)}),3e3)}))}]),C=i(!1),L=e=>{g({isCol:e})},x=i("inline"),w=()=>{x.value="inline"===u(x)?"bottom":"inline"},P=i("left"),S=e=>{x.value="bottom",P.value=e},R=async()=>{const e=await b();e&&_([{field:"field2",path:"componentProps.options",value:e.data}])},T=e=>{},A=()=>{_([{field:"field3",path:"remove",value:!0}])},$=()=>{_([{field:"field3",path:"remove",value:!1}])},B=()=>{k({field1:"Joy"})},F=i(!1),J=()=>{F.value=!0,setTimeout((()=>{F.value=!1}),2e3)},O=i(!1),V=()=>{O.value=!0,setTimeout((()=>{O.value=!1}),2e3)};return(o,l)=>{const t=a("BaseButton");return r(),s(d,null,[n(u(e),{title:`${u(f)("searchDemo.search")} ${u(f)("searchDemo.operate")}`,style:{"margin-bottom":"20px"}},{default:m((()=>[n(t,{onClick:l[0]||(l[0]=e=>L(!0))},{default:m((()=>[p(c(u(f)("searchDemo.grid")),1)])),_:1}),n(t,{onClick:l[1]||(l[1]=e=>L(!1))},{default:m((()=>[p(c(u(f)("searchDemo.restore"))+" "+c(u(f)("searchDemo.grid")),1)])),_:1}),n(t,{onClick:w},{default:m((()=>[p(c(u(f)("searchDemo.button"))+" "+c(u(f)("searchDemo.position")),1)])),_:1}),n(t,{onClick:l[2]||(l[2]=e=>S("left"))},{default:m((()=>[p(c(u(f)("searchDemo.bottom"))+" "+c(u(f)("searchDemo.position"))+"-"+c(u(f)("searchDemo.left")),1)])),_:1}),n(t,{onClick:l[3]||(l[3]=e=>S("center"))},{default:m((()=>[p(c(u(f)("searchDemo.bottom"))+" "+c(u(f)("searchDemo.position"))+"-"+c(u(f)("searchDemo.center")),1)])),_:1}),n(t,{onClick:l[4]||(l[4]=e=>S("right"))},{default:m((()=>[p(c(u(f)("searchDemo.bottom"))+" "+c(u(f)("searchDemo.position"))+"-"+c(u(f)("searchDemo.right")),1)])),_:1}),n(t,{onClick:R},{default:m((()=>[p(c(u(f)("formDemo.select"))+" "+c(u(f)("searchDemo.dynamicOptions")),1)])),_:1}),n(t,{onClick:A},{default:m((()=>[p(c(u(f)("searchDemo.deleteRadio")),1)])),_:1}),n(t,{onClick:$},{default:m((()=>[p(c(u(f)("searchDemo.restoreRadio")),1)])),_:1}),n(t,{onClick:B},{default:m((()=>[p(c(u(f)("formDemo.setValue")),1)])),_:1}),n(t,{onClick:J},{default:m((()=>[p(c(u(f)("searchDemo.search"))+" "+c(u(f)("searchDemo.loading")),1)])),_:1}),n(t,{onClick:V},{default:m((()=>[p(c(u(f)("searchDemo.reset"))+" "+c(u(f)("searchDemo.loading")),1)])),_:1})])),_:1},8,["title"]),n(u(e),{title:u(f)("searchDemo.search"),message:u(f)("searchDemo.searchDes")},{default:m((()=>[n(u(v),{schema:y,"is-col":C.value,layout:x.value,"button-position":P.value,"search-loading":F.value,"reset-loading":O.value,"show-expand":"","expand-field":"field6",onSearch:T,onReset:T,onRegister:u(D)},null,8,["schema","is-col","layout","button-position","search-loading","reset-loading","onRegister"])])),_:1},8,["title","message"])],64)}}}),[["__scopeId","data-v-f1ee1ca7"]]);export{D as default};
