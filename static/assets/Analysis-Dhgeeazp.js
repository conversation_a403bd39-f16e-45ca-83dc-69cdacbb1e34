import{P as e,g as a,U as t}from"./PanelGroup-koq1ky0O.js";import{d as l,r as s,s as o,v as r,x as n,y as d,o as m,c as u,e as i,w as p,a as c,f,t as v,i as g,z as _,j as h,A as y,B as b,F as x,l as j,_ as k}from"./index-DfJTpRkj.js";import{E as w,a as S}from"./el-col-B4Ik8fnS.js";import{E as U}from"./el-card-DyZz6u6e.js";import{E as V}from"./el-progress-Wzr98AjO.js";import{E as C}from"./el-text-vKNLRkxx.js";import"./el-tooltip-l0sNRNKZ.js";import{E as N}from"./el-popper-D2BmgSQA.js";import{E as A,a as I}from"./el-form-DsaI0u2w.js";import{_ as E}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{E as F}from"./el-tag-CbhrEnto.js";import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{g as M}from"./index-KH6atv8j.js";import{g as T}from"./index-B0JD2UFG.js";import{c as B}from"./index-Dt8htmKv.js";import"./el-skeleton-item-BCvv4WDW.js";import"./CountTo.vue_vue_type_script_setup_true_lang-F2X0Lxsw.js";import"./index-D1ADinPR.js";import"./castArray-CvwAI87l.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";const P={class:"flex flex-col gap-2"},H=k(l({__name:"Analysis",setup(l){const{t:k}=j(),H=s(!0),O=o([{field:"nodeName",label:k("node.nodeName")},{field:"taskCount",label:k("node.taskCount"),formatter:(e,a,t)=>r(F,{round:!0,effect:"dark"},(()=>t))},{field:"nodeStatus",label:k("node.nodeStatus"),formatter:(e,a,t)=>r(F,{type:"1"===t?"success":"2"===t?"warning":"danger",effect:"dark"},(()=>k("1"==t?"node.statusRun":"2"==t?"node.statusStop":"node.statusError")))}]),G=o([{field:"name",label:k("task.taskName")},{field:"taskNum",label:k("task.taskCount"),formatter:(e,a,t)=>r(F,{round:!0,effect:"dark"},(()=>t))},{field:"progress",label:k("task.taskProgress"),formatter:(e,a,t)=>r(V,{percentage:t,type:"line",striped:!0,status:t<100?"":"success",stripedFlow:t<100})},{field:"creatTime",label:k("task.createTime")}]),R=o([{field:"nodeName",label:k("node.nodeName")},{field:"nodeUsageCpu",label:k("node.nodeUsageCpu"),formatter:(e,a,t)=>{let l=parseFloat(t);return l=parseFloat(l.toFixed(2)),r(V,{percentage:l,type:"dashboard",color:l<50?"#26a33f":l<=80?"#fe9900":"#df2800"})}},{field:"nodeUsageMemory",label:k("node.nodeUsageMemory"),formatter:(e,a,t)=>{let l=parseFloat(t);return l=parseFloat(l.toFixed(2)),r(V,{percentage:l,type:"dashboard",color:l<50?"#26a33f":l<80?"#fe9900":"#df2800"})}}]),$=o([{field:"name",label:k("common.name")},{field:"cversion",label:k("common.cversion")},{field:"lversion",label:k("common.lversion"),formatter:(e,a,t)=>{if(e.cversion!=e.lversion){ee.value=!0;const a=e.msg.split("\\n");let l="";return a.forEach((e=>{l+=`<div>${e}</div>`})),r(N,{placement:"top",content:l,rawContent:!0},[r(C,{type:"danger"},t)])}return r(C,t)}}]);let D=s([]);const K=s([]),L=async()=>{try{const e=await M();e&&e.data&&Array.isArray(e.data.list)&&(K.value=e.data.list.map((e=>({nodeName:e.name,taskCount:e.running,nodeStatus:e.state,nodeUsageCpu:e.cpuNum,nodeUsageMemory:e.memNum}))),D.value=o(e.data.list.map((e=>({nodeName:e.name,nodeUsageCpu:e.cpuNum,nodeUsageMemory:e.memNum})))))}catch(e){}finally{H.value=!1}},q=s([]),J=async()=>{const e=await T("",1,10);q.value=o(e.data.list.map((e=>({name:e.name,taskNum:e.taskNum,progress:e.progress,creatTime:e.creatTime}))))},Q=s([]),W=async()=>{await Promise.all([L(),J()]),H.value=!1};(async()=>{const e=await a();Q.value=o(e.data.list.map((e=>({name:e.name,cversion:e.cversion,lversion:e.lversion,msg:e.msg}))))})(),W();const X=setInterval(W,1e4);n((()=>{clearInterval(X)}));const Y=s(""),Z=async()=>{if(Y.value){200==(await B(Y.value)).code&&(localStorage.setItem("plugin_key",Y.value),le.value=!1,te.value=!0)}},ee=s(!1),ae=s({server:"https://github.com/Autumn-27/ScopeSentry/archive/refs/heads/main.zip",scan:""}),te=s(!1),le=s(!1);async function se(){const e=localStorage.getItem("plugin_key");505==(await t(ae.value.server,ae.value.scan,e)).code&&localStorage.removeItem("plugin_key")}return(a,t)=>{const l=d("BaseButton");return m(),u(x,null,[i(e),i(c(S),{gutter:20,justify:"space-between"},{default:p((()=>[i(c(w),{xl:12,lg:12,md:24,sm:24,xs:24},{default:p((()=>[i(c(U),{shadow:"hover",class:"mb-20px"},{header:p((()=>[f("span",null,v(c(k)("dashboard.nodeInfo")),1)])),default:p((()=>[i(c(E),{columns:O,data:K.value,stripe:"",border:!1,height:250},null,8,["columns","data"])])),_:1})])),_:1}),i(c(w),{xl:12,lg:12,md:24,sm:24,xs:24},{default:p((()=>[i(c(U),{shadow:"hover",class:"mb-20px"},{header:p((()=>[f("span",null,v(c(k)("dashboard.taskInfo")),1)])),default:p((()=>[i(c(E),{columns:G,data:q.value,stripe:"",border:!1,height:250,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0}},null,8,["columns","data"])])),_:1})])),_:1}),i(c(w),{span:12},{default:p((()=>[i(c(U),{shadow:"hover",class:"mb-25px"},{header:p((()=>[f("div",null,[f("span",null,v(c(k)("node.nodeUsageStatus")),1)])])),default:p((()=>[i(c(E),{columns:R,data:c(D),highlightCurrentRow:!1,stripe:"",border:!1,height:600},null,8,["columns","data"])])),_:1})])),_:1}),i(c(w),{span:12},{default:p((()=>[i(c(U),{shadow:"hover",class:"mb-25px"},{header:p((()=>[i(c(S),null,{default:p((()=>[i(c(w),{span:12},{default:p((()=>[f("div",null,[f("span",null,v(c(k)("common.version")),1),ee.value?(m(),g(c(C),{key:0,type:"danger",size:"small",style:{position:"relative",left:"1rem"}},{default:p((()=>[_("*"+v(c(k)("common.updatemsg")),1)])),_:1})):h("",!0)])])),_:1})])),_:1})])),default:p((()=>[i(c(E),{columns:$,data:Q.value,stripe:"",border:!1,height:600},null,8,["columns","data"])])),_:1})])),_:1})])),_:1}),i(c(z),{modelValue:te.value,"onUpdate:modelValue":t[2]||(t[2]=e=>te.value=e),title:"Update",center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:430},{default:p((()=>[i(c(C),{type:"danger",size:"small",style:{position:"relative",left:"1rem"}},{default:p((()=>[_("*更新目前只支持docker容器搭建的程序，输入的url地址确保docker内可访问，节点最新版在github中releases的linux版本")])),_:1}),i(c(A),{model:ae.value,"label-width":"120px",class:"upload-form"},{default:p((()=>[i(c(I),{label:"server url"},{default:p((()=>[i(c(y),{modelValue:ae.value.server,"onUpdate:modelValue":t[0]||(t[0]=e=>ae.value.server=e),placeholder:"server url"},null,8,["modelValue"])])),_:1}),i(c(I),{label:"scan url"},{default:p((()=>[i(c(y),{modelValue:ae.value.scan,"onUpdate:modelValue":t[1]||(t[1]=e=>ae.value.scan=e),placeholder:"scan url(https://github.com/Autumn-27/ScopeSentry-Scan/releases/download/vx.x.x/ScopeSentry-Scan_linux_amd64_vx.x.x.zip)"},null,8,["modelValue"])])),_:1}),i(c(I),null,{default:p((()=>[i(c(b),{type:"primary",onClick:se},{default:p((()=>[_("Submit")])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),i(c(z),{modelValue:le.value,"onUpdate:modelValue":t[4]||(t[4]=e=>le.value=e),title:c(k)("plugin.key"),center:"",width:"30%",style:{"max-width":"400px",height:"200px"}},{default:p((()=>[f("div",P,[i(c(N),{class:"item",effect:"dark",content:c(k)("plugin.keyMsg"),placement:"top"},{default:p((()=>[i(c(y),{modelValue:Y.value,"onUpdate:modelValue":t[3]||(t[3]=e=>Y.value=e)},null,8,["modelValue"])])),_:1},8,["content"]),i(l,{onClick:Z,type:"primary",class:"w-full"},{default:p((()=>[_("确定")])),_:1})])])),_:1},8,["modelValue","title"])],64)}}}),[["__scopeId","data-v-e580b627"]]);export{H as default};
