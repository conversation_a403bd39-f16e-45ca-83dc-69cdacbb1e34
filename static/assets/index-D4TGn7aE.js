import{r as a}from"./index-Dz8ZrwBc.js";const e=(e,t,o)=>a.post({url:"/api/project/data",data:{search:e,pageIndex:t,pageSize:o}}),t=async()=>a.get({url:"/api/project/all"}),o=(e,t,o,d,p,r,s,l,i,u,c,n)=>a.post({url:"/api/project/add",data:{runNow:e,name:t,tag:o,target:d,logo:p,scheduledTasks:r,hour:s,allNode:l,node:i,duplicates:u,ignore:c,template:n}}),d=(e,t,o,d,p,r,s,l,i,u,c,n,g)=>a.post({url:"/api/project/update",data:{runNow:e,id:t,name:o,tag:d,target:p,logo:r,scheduledTasks:s,hour:l,allNode:i,node:u,duplicates:c,ignore:n,template:g}}),p=e=>a.post({url:"/api/project/content",data:{id:e}}),r=(e,t)=>a.post({url:"/api/project/delete",data:{ids:e,delA:t}});export{e as a,o as b,p as c,r as d,t as g,d as u};
