import{d as t,aq as r,a7 as e,v as s,a}from"./index-C6fb_XFi.js";const l=t({name:"Highlight",props:{tag:r.string.def("span"),keys:{type:Array,default:()=>[]},color:r.string.def("var(--el-color-primary)")},emits:["click"],setup(t,{emit:r,slots:l}){const o=e((()=>t.keys.map((e=>s("span",{onClick:()=>{r("click",e)},style:{color:t.color,cursor:"pointer"}},e))))),n=()=>{if(!(null==l?void 0:l.default))return null;const r=null==l?void 0:l.default()[0].children;if(!r)return null==l?void 0:l.default()[0];const e=(n=r,t.keys.forEach(((t,r)=>{const e=new RegExp(t,"g");n=n.replace(e,`{{${r}}}`)})),n.split(/{{|}}/));var n;const i=/^[0-9]*$/,c=e.map((t=>i.test(t)&&a(o)[t]||t));return s(t.tag,c)};return()=>n()}});export{l as _};
