import{_ as t}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as e,r as i,H as a,O as n,Y as o,a as r,x as h,o as s,c,f as l,n as d,k as p,y as u,i as m,w as g,e as f,z as v,A as w}from"./index-3XfDPlIS.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";
/*!
 * Cropper.js v1.6.1
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2023-09-17T03:44:19.860Z
 */function b(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function y(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?b(Object(i),!0).forEach((function(e){C(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):b(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function x(t){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function M(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,k(a.key),a)}}function C(t,e,i){return(e=k(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function D(t){return function(t){if(Array.isArray(t))return B(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return B(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return B(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,a=new Array(e);i<e;i++)a[i]=t[i];return a}function k(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}var O="undefined"!=typeof window&&void 0!==window.document,E=O?window:{},T=!(!O||!E.document.documentElement)&&"ontouchstart"in E.document.documentElement,W=!!O&&"PointerEvent"in E,H="cropper",N="all",L="crop",z="move",R="zoom",Y="e",X="w",j="s",S="n",A="ne",P="nw",I="se",U="sw",_="".concat(H,"-crop"),q="".concat(H,"-disabled"),$="".concat(H,"-hidden"),Q="".concat(H,"-hide"),V="".concat(H,"-invisible"),K="".concat(H,"-modal"),Z="".concat(H,"-move"),G="".concat(H,"Action"),F="".concat(H,"Preview"),J="crop",tt="move",et="none",it="crop",at="cropend",nt="cropmove",ot="cropstart",rt="dblclick",ht=W?"pointerdown":T?"touchstart":"mousedown",st=W?"pointermove":T?"touchmove":"mousemove",ct=W?"pointerup pointercancel":T?"touchend touchcancel":"mouseup",lt="ready",dt="resize",pt="wheel",ut="zoom",mt="image/jpeg",gt=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,ft=/^data:/,vt=/^data:image\/jpeg;base64,/,wt=/^img|canvas$/i,bt={viewMode:0,dragMode:J,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},yt=Number.isNaN||E.isNaN;function xt(t){return"number"==typeof t&&!yt(t)}var Mt=function(t){return t>0&&t<1/0};function Ct(t){return void 0===t}function Dt(t){return"object"===x(t)&&null!==t}var Bt=Object.prototype.hasOwnProperty;function kt(t){if(!Dt(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&Bt.call(i,"isPrototypeOf")}catch(a){return!1}}function Ot(t){return"function"==typeof t}var Et=Array.prototype.slice;function Tt(t){return Array.from?Array.from(t):Et.call(t)}function Wt(t,e){return t&&Ot(e)&&(Array.isArray(t)||xt(t.length)?Tt(t).forEach((function(i,a){e.call(t,i,a,t)})):Dt(t)&&Object.keys(t).forEach((function(i){e.call(t,t[i],i,t)}))),t}var Ht=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];return Dt(t)&&i.length>0&&i.forEach((function(e){Dt(e)&&Object.keys(e).forEach((function(i){t[i]=e[i]}))})),t},Nt=/\.\d*(?:0|9){12}\d*$/;function Lt(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return Nt.test(t)?Math.round(t*e)/e:t}var zt=/^width|height|left|top|marginLeft|marginTop$/;function Rt(t,e){var i=t.style;Wt(e,(function(t,e){zt.test(e)&&xt(t)&&(t="".concat(t,"px")),i[e]=t}))}function Yt(t,e){if(e)if(xt(t.length))Wt(t,(function(t){Yt(t,e)}));else if(t.classList)t.classList.add(e);else{var i=t.className.trim();i?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e}}function Xt(t,e){e&&(xt(t.length)?Wt(t,(function(t){Xt(t,e)})):t.classList?t.classList.remove(e):t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,"")))}function jt(t,e,i){e&&(xt(t.length)?Wt(t,(function(t){jt(t,e,i)})):i?Yt(t,e):Xt(t,e))}var St=/([a-z\d])([A-Z])/g;function At(t){return t.replace(St,"$1-$2").toLowerCase()}function Pt(t,e){return Dt(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(At(e)))}function It(t,e,i){Dt(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(At(e)),i)}var Ut=/\s\s*/,_t=function(){var t=!1;if(O){var e=!1,i=function(){},a=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});E.addEventListener("test",i,a),E.removeEventListener("test",i,a)}return t}();function qt(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(Ut).forEach((function(e){if(!_t){var o=t.listeners;o&&o[e]&&o[e][i]&&(n=o[e][i],delete o[e][i],0===Object.keys(o[e]).length&&delete o[e],0===Object.keys(o).length&&delete t.listeners)}t.removeEventListener(e,n,a)}))}function $t(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(Ut).forEach((function(e){if(a.once&&!_t){var o=t.listeners,r=void 0===o?{}:o;n=function(){delete r[e][i],t.removeEventListener(e,n,a);for(var o=arguments.length,h=new Array(o),s=0;s<o;s++)h[s]=arguments[s];i.apply(t,h)},r[e]||(r[e]={}),r[e][i]&&t.removeEventListener(e,r[e][i],a),r[e][i]=n,t.listeners=r}t.addEventListener(e,n,a)}))}function Qt(t,e,i){var a;return Ot(Event)&&Ot(CustomEvent)?a=new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(a)}function Vt(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var Kt=E.location,Zt=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Gt(t){var e=t.match(Zt);return null!==e&&(e[1]!==Kt.protocol||e[2]!==Kt.hostname||e[3]!==Kt.port)}function Ft(t){var e="timestamp=".concat((new Date).getTime());return t+(-1===t.indexOf("?")?"?":"&")+e}function Jt(t){var e=t.rotate,i=t.scaleX,a=t.scaleY,n=t.translateX,o=t.translateY,r=[];xt(n)&&0!==n&&r.push("translateX(".concat(n,"px)")),xt(o)&&0!==o&&r.push("translateY(".concat(o,"px)")),xt(e)&&0!==e&&r.push("rotate(".concat(e,"deg)")),xt(i)&&1!==i&&r.push("scaleX(".concat(i,")")),xt(a)&&1!==a&&r.push("scaleY(".concat(a,")"));var h=r.length?r.join(" "):"none";return{WebkitTransform:h,msTransform:h,transform:h}}function te(t,e){var i=t.pageX,a=t.pageY,n={endX:i,endY:a};return e?n:y({startX:i,startY:a},n)}function ee(t){var e=t.aspectRatio,i=t.height,a=t.width,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",o=Mt(a),r=Mt(i);if(o&&r){var h=i*e;"contain"===n&&h>a||"cover"===n&&h<a?i=a/e:a=i*e}else o?i=a/e:r&&(a=i*e);return{width:a,height:i}}var ie=String.fromCharCode;var ae=/^data:.*,/;function ne(t){var e,i=new DataView(t);try{var a,n,o;if(255===i.getUint8(0)&&216===i.getUint8(1))for(var r=i.byteLength,h=2;h+1<r;){if(255===i.getUint8(h)&&225===i.getUint8(h+1)){n=h;break}h+=1}if(n){var s=n+10;if("Exif"===function(t,e,i){var a="";i+=e;for(var n=e;n<i;n+=1)a+=ie(t.getUint8(n));return a}(i,n+4,4)){var c=i.getUint16(s);if(((a=18761===c)||19789===c)&&42===i.getUint16(s+2,a)){var l=i.getUint32(s+4,a);l>=8&&(o=s+l)}}}if(o){var d,p,u=i.getUint16(o,a);for(p=0;p<u;p+=1)if(d=o+12*p+2,274===i.getUint16(d,a)){d+=8,e=i.getUint16(d,a),i.setUint16(d,1,a);break}}}catch(m){e=1}return e}var oe={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,a=this.cropper,n=Number(e.minContainerWidth),o=Number(e.minContainerHeight);Yt(a,$),Xt(t,$);var r={width:Math.max(i.offsetWidth,n>=0?n:200),height:Math.max(i.offsetHeight,o>=0?o:100)};this.containerData=r,Rt(a,{width:r.width,height:r.height}),Yt(t,$),Xt(a,$)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,a=Math.abs(e.rotate)%180==90,n=a?e.naturalHeight:e.naturalWidth,o=a?e.naturalWidth:e.naturalHeight,r=n/o,h=t.width,s=t.height;t.height*r>t.width?3===i?h=t.height*r:s=t.width/r:3===i?s=t.width/r:h=t.height*r;var c={aspectRatio:r,naturalWidth:n,naturalHeight:o,width:h,height:s};this.canvasData=c,this.limited=1===i||2===i,this.limitCanvas(!0,!0),c.width=Math.min(Math.max(c.width,c.minWidth),c.maxWidth),c.height=Math.min(Math.max(c.height,c.minHeight),c.maxHeight),c.left=(t.width-c.width)/2,c.top=(t.height-c.height)/2,c.oldLeft=c.left,c.oldTop=c.top,this.initialCanvasData=Ht({},c)},limitCanvas:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,o=this.cropBoxData,r=i.viewMode,h=n.aspectRatio,s=this.cropped&&o;if(t){var c=Number(i.minCanvasWidth)||0,l=Number(i.minCanvasHeight)||0;r>1?(c=Math.max(c,a.width),l=Math.max(l,a.height),3===r&&(l*h>c?c=l*h:l=c/h)):r>0&&(c?c=Math.max(c,s?o.width:0):l?l=Math.max(l,s?o.height:0):s&&(c=o.width,(l=o.height)*h>c?c=l*h:l=c/h));var d=ee({aspectRatio:h,width:c,height:l});c=d.width,l=d.height,n.minWidth=c,n.minHeight=l,n.maxWidth=1/0,n.maxHeight=1/0}if(e)if(r>(s?0:1)){var p=a.width-n.width,u=a.height-n.height;n.minLeft=Math.min(0,p),n.minTop=Math.min(0,u),n.maxLeft=Math.max(0,p),n.maxTop=Math.max(0,u),s&&this.limited&&(n.minLeft=Math.min(o.left,o.left+(o.width-n.width)),n.minTop=Math.min(o.top,o.top+(o.height-n.height)),n.maxLeft=o.left,n.maxTop=o.top,2===r&&(n.width>=a.width&&(n.minLeft=Math.min(0,p),n.maxLeft=Math.max(0,p)),n.height>=a.height&&(n.minTop=Math.min(0,u),n.maxTop=Math.max(0,u))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=a.width,n.maxTop=a.height},renderCanvas:function(t,e){var i=this.canvasData,a=this.imageData;if(e){var n=function(t){var e=t.width,i=t.height,a=t.degree;if(90==(a=Math.abs(a)%180))return{width:i,height:e};var n=a%90*Math.PI/180,o=Math.sin(n),r=Math.cos(n),h=e*r+i*o,s=e*o+i*r;return a>90?{width:s,height:h}:{width:h,height:s}}({width:a.naturalWidth*Math.abs(a.scaleX||1),height:a.naturalHeight*Math.abs(a.scaleY||1),degree:a.rotate||0}),o=n.width,r=n.height,h=i.width*(o/i.naturalWidth),s=i.height*(r/i.naturalHeight);i.left-=(h-i.width)/2,i.top-=(s-i.height)/2,i.width=h,i.height=s,i.aspectRatio=o/r,i.naturalWidth=o,i.naturalHeight=r,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,Rt(this.canvas,Ht({width:i.width,height:i.height},Jt({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,a=i.naturalWidth*(e.width/e.naturalWidth),n=i.naturalHeight*(e.height/e.naturalHeight);Ht(i,{width:a,height:n,left:(e.width-a)/2,top:(e.height-n)/2}),Rt(this.image,Ht({width:i.width,height:i.height},Jt(Ht({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8,n={width:e.width,height:e.height};i&&(e.height*i>e.width?n.height=n.width/i:n.width=n.height*i),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*a),n.height=Math.max(n.minHeight,n.height*a),n.left=e.left+(e.width-n.width)/2,n.top=e.top+(e.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=Ht({},n)},limitCropBox:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,o=this.cropBoxData,r=this.limited,h=i.aspectRatio;if(t){var s=Number(i.minCropBoxWidth)||0,c=Number(i.minCropBoxHeight)||0,l=r?Math.min(a.width,n.width,n.width+n.left,a.width-n.left):a.width,d=r?Math.min(a.height,n.height,n.height+n.top,a.height-n.top):a.height;s=Math.min(s,a.width),c=Math.min(c,a.height),h&&(s&&c?c*h>s?c=s/h:s=c*h:s?c=s/h:c&&(s=c*h),d*h>l?d=l/h:l=d*h),o.minWidth=Math.min(s,l),o.minHeight=Math.min(c,d),o.maxWidth=l,o.maxHeight=d}e&&(r?(o.minLeft=Math.max(0,n.left),o.minTop=Math.max(0,n.top),o.maxLeft=Math.min(a.width,n.left+n.width)-o.width,o.maxTop=Math.min(a.height,n.top+n.height)-o.height):(o.minLeft=0,o.minTop=0,o.maxLeft=a.width-o.width,o.maxTop=a.height-o.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&It(this.face,G,i.width>=e.width&&i.height>=e.height?z:N),Rt(this.cropBox,Ht({width:i.width,height:i.height},Jt({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),Qt(this.element,it,this.getData())}},re={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,a=e?this.crossOriginUrl:this.url,n=t.alt||"The image to preview",o=document.createElement("img");if(e&&(o.crossOrigin=e),o.src=a,o.alt=n,this.viewBox.appendChild(o),this.viewBoxImage=o,i){var r=i;"string"==typeof i?r=t.ownerDocument.querySelectorAll(i):i.querySelector&&(r=[i]),this.previews=r,Wt(r,(function(t){var i=document.createElement("img");It(t,F,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(i.crossOrigin=e),i.src=a,i.alt=n,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(i)}))}},resetPreview:function(){Wt(this.previews,(function(t){var e=Pt(t,F);Rt(t,{width:e.width,height:e.height}),t.innerHTML=e.html,function(t,e){if(Dt(t[e]))try{delete t[e]}catch(i){t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch(i){t.dataset[e]=void 0}else t.removeAttribute("data-".concat(At(e)))}(t,F)}))},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,a=i.width,n=i.height,o=t.width,r=t.height,h=i.left-e.left-t.left,s=i.top-e.top-t.top;this.cropped&&!this.disabled&&(Rt(this.viewBoxImage,Ht({width:o,height:r},Jt(Ht({translateX:-h,translateY:-s},t)))),Wt(this.previews,(function(e){var i=Pt(e,F),c=i.width,l=i.height,d=c,p=l,u=1;a&&(p=n*(u=c/a)),n&&p>l&&(d=a*(u=l/n),p=l),Rt(e,{width:d,height:p}),Rt(e.getElementsByTagName("img")[0],Ht({width:o*u,height:r*u},Jt(Ht({translateX:-h*u,translateY:-s*u},t))))})))}},he={bind:function(){var t=this.element,e=this.options,i=this.cropper;Ot(e.cropstart)&&$t(t,ot,e.cropstart),Ot(e.cropmove)&&$t(t,nt,e.cropmove),Ot(e.cropend)&&$t(t,at,e.cropend),Ot(e.crop)&&$t(t,it,e.crop),Ot(e.zoom)&&$t(t,ut,e.zoom),$t(i,ht,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&$t(i,pt,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&$t(i,rt,this.onDblclick=this.dblclick.bind(this)),$t(t.ownerDocument,st,this.onCropMove=this.cropMove.bind(this)),$t(t.ownerDocument,ct,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&$t(window,dt,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;Ot(e.cropstart)&&qt(t,ot,e.cropstart),Ot(e.cropmove)&&qt(t,nt,e.cropmove),Ot(e.cropend)&&qt(t,at,e.cropend),Ot(e.crop)&&qt(t,it,e.crop),Ot(e.zoom)&&qt(t,ut,e.zoom),qt(i,ht,this.onCropStart),e.zoomable&&e.zoomOnWheel&&qt(i,pt,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&qt(i,rt,this.onDblclick),qt(t.ownerDocument,st,this.onCropMove),qt(t.ownerDocument,ct,this.onCropEnd),e.responsive&&qt(window,dt,this.onResize)}},se={resize:function(){if(!this.disabled){var t,e,i=this.options,a=this.container,n=this.containerData,o=a.offsetWidth/n.width,r=a.offsetHeight/n.height,h=Math.abs(o-1)>Math.abs(r-1)?o:r;if(1!==h)i.restore&&(t=this.getCanvasData(),e=this.getCropBoxData()),this.render(),i.restore&&(this.setCanvasData(Wt(t,(function(e,i){t[i]=e*h}))),this.setCropBoxData(Wt(e,(function(t,i){e[i]=t*h}))))}},dblclick:function(){var t,e;this.disabled||this.options.dragMode===et||this.setDragMode((t=this.dragBox,e=_,(t.classList?t.classList.contains(e):t.className.indexOf(e)>-1)?tt:J))},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,a=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50),t.deltaY?a=t.deltaY>0?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=t.detail>0?1:-1),this.zoom(-a*i,t)))},cropStart:function(t){var e=t.buttons,i=t.button;if(!(this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(xt(e)&&1!==e||xt(i)&&0!==i||t.ctrlKey))){var a,n=this.options,o=this.pointers;t.changedTouches?Wt(t.changedTouches,(function(t){o[t.identifier]=te(t)})):o[t.pointerId||0]=te(t),a=Object.keys(o).length>1&&n.zoomable&&n.zoomOnTouch?R:Pt(t.target,G),gt.test(a)&&!1!==Qt(this.element,ot,{originalEvent:t,action:a})&&(t.preventDefault(),this.action=a,this.cropping=!1,a===L&&(this.cropping=!0,Yt(this.dragBox,K)))}},cropMove:function(t){var e=this.action;if(!this.disabled&&e){var i=this.pointers;t.preventDefault(),!1!==Qt(this.element,nt,{originalEvent:t,action:e})&&(t.changedTouches?Wt(t.changedTouches,(function(t){Ht(i[t.identifier]||{},te(t,!0))})):Ht(i[t.pointerId||0]||{},te(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,i=this.pointers;t.changedTouches?Wt(t.changedTouches,(function(t){delete i[t.identifier]})):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,jt(this.dragBox,K,this.cropped&&this.options.modal)),Qt(this.element,at,{originalEvent:t,action:e}))}}},ce={change:function(t){var e,i=this.options,a=this.canvasData,n=this.containerData,o=this.cropBoxData,r=this.pointers,h=this.action,s=i.aspectRatio,c=o.left,l=o.top,d=o.width,p=o.height,u=c+d,m=l+p,g=0,f=0,v=n.width,w=n.height,b=!0;!s&&t.shiftKey&&(s=d&&p?d/p:1),this.limited&&(g=o.minLeft,f=o.minTop,v=g+Math.min(n.width,a.width,a.left+a.width),w=f+Math.min(n.height,a.height,a.top+a.height));var x=r[Object.keys(r)[0]],M={x:x.endX-x.startX,y:x.endY-x.startY},C=function(t){switch(t){case Y:u+M.x>v&&(M.x=v-u);break;case X:c+M.x<g&&(M.x=g-c);break;case S:l+M.y<f&&(M.y=f-l);break;case j:m+M.y>w&&(M.y=w-m)}};switch(h){case N:c+=M.x,l+=M.y;break;case Y:if(M.x>=0&&(u>=v||s&&(l<=f||m>=w))){b=!1;break}C(Y),(d+=M.x)<0&&(h=X,c-=d=-d),s&&(p=d/s,l+=(o.height-p)/2);break;case S:if(M.y<=0&&(l<=f||s&&(c<=g||u>=v))){b=!1;break}C(S),p-=M.y,l+=M.y,p<0&&(h=j,l-=p=-p),s&&(d=p*s,c+=(o.width-d)/2);break;case X:if(M.x<=0&&(c<=g||s&&(l<=f||m>=w))){b=!1;break}C(X),d-=M.x,c+=M.x,d<0&&(h=Y,c-=d=-d),s&&(p=d/s,l+=(o.height-p)/2);break;case j:if(M.y>=0&&(m>=w||s&&(c<=g||u>=v))){b=!1;break}C(j),(p+=M.y)<0&&(h=S,l-=p=-p),s&&(d=p*s,c+=(o.width-d)/2);break;case A:if(s){if(M.y<=0&&(l<=f||u>=v)){b=!1;break}C(S),p-=M.y,l+=M.y,d=p*s}else C(S),C(Y),M.x>=0?u<v?d+=M.x:M.y<=0&&l<=f&&(b=!1):d+=M.x,M.y<=0?l>f&&(p-=M.y,l+=M.y):(p-=M.y,l+=M.y);d<0&&p<0?(h=U,l-=p=-p,c-=d=-d):d<0?(h=P,c-=d=-d):p<0&&(h=I,l-=p=-p);break;case P:if(s){if(M.y<=0&&(l<=f||c<=g)){b=!1;break}C(S),p-=M.y,l+=M.y,d=p*s,c+=o.width-d}else C(S),C(X),M.x<=0?c>g?(d-=M.x,c+=M.x):M.y<=0&&l<=f&&(b=!1):(d-=M.x,c+=M.x),M.y<=0?l>f&&(p-=M.y,l+=M.y):(p-=M.y,l+=M.y);d<0&&p<0?(h=I,l-=p=-p,c-=d=-d):d<0?(h=A,c-=d=-d):p<0&&(h=U,l-=p=-p);break;case U:if(s){if(M.x<=0&&(c<=g||m>=w)){b=!1;break}C(X),d-=M.x,c+=M.x,p=d/s}else C(j),C(X),M.x<=0?c>g?(d-=M.x,c+=M.x):M.y>=0&&m>=w&&(b=!1):(d-=M.x,c+=M.x),M.y>=0?m<w&&(p+=M.y):p+=M.y;d<0&&p<0?(h=A,l-=p=-p,c-=d=-d):d<0?(h=I,c-=d=-d):p<0&&(h=P,l-=p=-p);break;case I:if(s){if(M.x>=0&&(u>=v||m>=w)){b=!1;break}C(Y),p=(d+=M.x)/s}else C(j),C(Y),M.x>=0?u<v?d+=M.x:M.y>=0&&m>=w&&(b=!1):d+=M.x,M.y>=0?m<w&&(p+=M.y):p+=M.y;d<0&&p<0?(h=P,l-=p=-p,c-=d=-d):d<0?(h=U,c-=d=-d):p<0&&(h=A,l-=p=-p);break;case z:this.move(M.x,M.y),b=!1;break;case R:this.zoom(function(t){var e=y({},t),i=0;return Wt(t,(function(t,a){delete e[a],Wt(e,(function(e){var a=Math.abs(t.startX-e.startX),n=Math.abs(t.startY-e.startY),o=Math.abs(t.endX-e.endX),r=Math.abs(t.endY-e.endY),h=Math.sqrt(a*a+n*n),s=(Math.sqrt(o*o+r*r)-h)/h;Math.abs(s)>Math.abs(i)&&(i=s)}))})),i}(r),t),b=!1;break;case L:if(!M.x||!M.y){b=!1;break}e=Vt(this.cropper),c=x.startX-e.left,l=x.startY-e.top,d=o.minWidth,p=o.minHeight,M.x>0?h=M.y>0?I:A:M.x<0&&(c-=d,h=M.y>0?U:P),M.y<0&&(l-=p),this.cropped||(Xt(this.cropBox,$),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}b&&(o.width=d,o.height=p,o.left=c,o.top=l,this.action=h,this.renderCropBox()),Wt(r,(function(t){t.startX=t.endX,t.startY=t.endY}))}},le={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&Yt(this.dragBox,K),Xt(this.cropBox,$),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=Ht({},this.initialImageData),this.canvasData=Ht({},this.initialCanvasData),this.cropBoxData=Ht({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(Ht(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),Xt(this.dragBox,K),Yt(this.cropBox,$)),this},replace:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,Wt(this.previews,(function(e){e.getElementsByTagName("img")[0].src=t})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,Xt(this.cropper,q)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,Yt(this.cropper,q)),this},destroy:function(){var t=this.element;return t[H]?(t[H]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=i.left,n=i.top;return this.moveTo(Ct(t)?t:a+Number(t),Ct(e)?e:n+Number(e))},moveTo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(xt(t)&&(i.left=t,a=!0),xt(e)&&(i.top=e,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var a=this.options,n=this.canvasData,o=n.width,r=n.height,h=n.naturalWidth,s=n.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&a.zoomable){var c=h*t,l=s*t;if(!1===Qt(this.element,ut,{ratio:t,oldRatio:o/h,originalEvent:i}))return this;if(i){var d=this.pointers,p=Vt(this.cropper),u=d&&Object.keys(d).length?function(t){var e=0,i=0,a=0;return Wt(t,(function(t){var n=t.startX,o=t.startY;e+=n,i+=o,a+=1})),{pageX:e/=a,pageY:i/=a}}(d):{pageX:i.pageX,pageY:i.pageY};n.left-=(c-o)*((u.pageX-p.left-n.left)/o),n.top-=(l-r)*((u.pageY-p.top-n.top)/r)}else kt(e)&&xt(e.x)&&xt(e.y)?(n.left-=(c-o)*((e.x-n.left)/o),n.top-=(l-r)*((e.y-n.top)/r)):(n.left-=(c-o)/2,n.top-=(l-r)/2);n.width=c,n.height=l,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return xt(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,xt(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(xt(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(xt(t)&&(i.scaleX=t,a=!0),xt(e)&&(i.scaleY=e,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.options,a=this.imageData,n=this.canvasData,o=this.cropBoxData;if(this.ready&&this.cropped){t={x:o.left-n.left,y:o.top-n.top,width:o.width,height:o.height};var r=a.width/a.naturalWidth;if(Wt(t,(function(e,i){t[i]=e/r})),e){var h=Math.round(t.y+t.height),s=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=s-t.x,t.height=h-t.y}}else t={x:0,y:0,width:0,height:0};return i.rotatable&&(t.rotate=a.rotate||0),i.scalable&&(t.scaleX=a.scaleX||1,t.scaleY=a.scaleY||1),t},setData:function(t){var e=this.options,i=this.imageData,a=this.canvasData,n={};if(this.ready&&!this.disabled&&kt(t)){var o=!1;e.rotatable&&xt(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,o=!0),e.scalable&&(xt(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,o=!0),xt(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,o=!0)),o&&this.renderCanvas(!0,!0);var r=i.width/i.naturalWidth;xt(t.x)&&(n.left=t.x*r+a.left),xt(t.y)&&(n.top=t.y*r+a.top),xt(t.width)&&(n.width=t.width*r),xt(t.height)&&(n.height=t.height*r),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?Ht({},this.containerData):{}},getImageData:function(){return this.sized?Ht({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&Wt(["left","top","width","height","naturalWidth","naturalHeight"],(function(i){e[i]=t[i]})),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&kt(t)&&(xt(t.left)&&(e.left=t.left),xt(t.top)&&(e.top=t.top),xt(t.width)?(e.width=t.width,e.height=t.width/i):xt(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,e=this.cropBoxData;return this.ready&&this.cropped&&(t={left:e.left,top:e.top,width:e.width,height:e.height}),t||{}},setCropBoxData:function(t){var e,i,a=this.cropBoxData,n=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&kt(t)&&(xt(t.left)&&(a.left=t.left),xt(t.top)&&(a.top=t.top),xt(t.width)&&t.width!==a.width&&(e=!0,a.width=t.width),xt(t.height)&&t.height!==a.height&&(i=!0,a.height=t.height),n&&(e?a.height=a.width/n:i&&(a.width=a.height*n)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,i=function(t,e,i,a){var n=e.aspectRatio,o=e.naturalWidth,r=e.naturalHeight,h=e.rotate,s=void 0===h?0:h,c=e.scaleX,l=void 0===c?1:c,d=e.scaleY,p=void 0===d?1:d,u=i.aspectRatio,m=i.naturalWidth,g=i.naturalHeight,f=a.fillColor,v=void 0===f?"transparent":f,w=a.imageSmoothingEnabled,b=void 0===w||w,y=a.imageSmoothingQuality,x=void 0===y?"low":y,M=a.maxWidth,C=void 0===M?1/0:M,B=a.maxHeight,k=void 0===B?1/0:B,O=a.minWidth,E=void 0===O?0:O,T=a.minHeight,W=void 0===T?0:T,H=document.createElement("canvas"),N=H.getContext("2d"),L=ee({aspectRatio:u,width:C,height:k}),z=ee({aspectRatio:u,width:E,height:W},"cover"),R=Math.min(L.width,Math.max(z.width,m)),Y=Math.min(L.height,Math.max(z.height,g)),X=ee({aspectRatio:n,width:C,height:k}),j=ee({aspectRatio:n,width:E,height:W},"cover"),S=Math.min(X.width,Math.max(j.width,o)),A=Math.min(X.height,Math.max(j.height,r)),P=[-S/2,-A/2,S,A];return H.width=Lt(R),H.height=Lt(Y),N.fillStyle=v,N.fillRect(0,0,R,Y),N.save(),N.translate(R/2,Y/2),N.rotate(s*Math.PI/180),N.scale(l,p),N.imageSmoothingEnabled=b,N.imageSmoothingQuality=x,N.drawImage.apply(N,[t].concat(D(P.map((function(t){return Math.floor(Lt(t))}))))),N.restore(),H}(this.image,this.imageData,e,t);if(!this.cropped)return i;var a=this.getData(t.rounded),n=a.x,o=a.y,r=a.width,h=a.height,s=i.width/Math.floor(e.naturalWidth);1!==s&&(n*=s,o*=s,r*=s,h*=s);var c=r/h,l=ee({aspectRatio:c,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),d=ee({aspectRatio:c,width:t.minWidth||0,height:t.minHeight||0},"cover"),p=ee({aspectRatio:c,width:t.width||(1!==s?i.width:r),height:t.height||(1!==s?i.height:h)}),u=p.width,m=p.height;u=Math.min(l.width,Math.max(d.width,u)),m=Math.min(l.height,Math.max(d.height,m));var g=document.createElement("canvas"),f=g.getContext("2d");g.width=Lt(u),g.height=Lt(m),f.fillStyle=t.fillColor||"transparent",f.fillRect(0,0,u,m);var v=t.imageSmoothingEnabled,w=void 0===v||v,b=t.imageSmoothingQuality;f.imageSmoothingEnabled=w,b&&(f.imageSmoothingQuality=b);var y,x,M,C,B,k,O=i.width,E=i.height,T=n,W=o;T<=-r||T>O?(T=0,y=0,M=0,B=0):T<=0?(M=-T,T=0,B=y=Math.min(O,r+T)):T<=O&&(M=0,B=y=Math.min(r,O-T)),y<=0||W<=-h||W>E?(W=0,x=0,C=0,k=0):W<=0?(C=-W,W=0,k=x=Math.min(E,h+W)):W<=E&&(C=0,k=x=Math.min(h,E-W));var H=[T,W,y,x];if(B>0&&k>0){var N=u/r;H.push(M*N,C*N,B*N,k*N)}return f.drawImage.apply(f,[i].concat(D(H.map((function(t){return Math.floor(Lt(t))}))))),g},setAspectRatio:function(t){var e=this.options;return this.disabled||Ct(t)||(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,a=this.face;if(this.ready&&!this.disabled){var n=t===J,o=e.movable&&t===tt;t=n||o?t:et,e.dragMode=t,It(i,G,t),jt(i,_,n),jt(i,Z,o),e.cropBoxMovable||(It(a,G,t),jt(a,_,n),jt(a,Z,o))}return this}},de=E.Cropper,pe=function(){function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!e||!wt.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=Ht({},bt,kt(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}var e,i,a;return e=t,a=[{key:"noConflict",value:function(){return window.Cropper=de,t}},{key:"setDefaults",value:function(t){Ht(bt,kt(t)&&t)}}],(i=[{key:"init",value:function(){var t,e=this.element,i=e.tagName.toLowerCase();if(!e[H]){if(e[H]=this,"img"===i){if(this.isImg=!0,t=e.getAttribute("src")||"",this.originalUrl=t,!t)return;t=e.src}else"canvas"===i&&window.HTMLCanvasElement&&(t=e.toDataURL());this.load(t)}}},{key:"load",value:function(t){var e=this;if(t){this.url=t,this.imageData={};var i=this.element,a=this.options;if(a.rotatable||a.scalable||(a.checkOrientation=!1),a.checkOrientation&&window.ArrayBuffer)if(ft.test(t))vt.test(t)?this.read((n=t.replace(ae,""),o=atob(n),r=new ArrayBuffer(o.length),Wt(h=new Uint8Array(r),(function(t,e){h[e]=o.charCodeAt(e)})),r)):this.clone();else{var n,o,r,h,s=new XMLHttpRequest,c=this.clone.bind(this);this.reloading=!0,this.xhr=s,s.onabort=c,s.onerror=c,s.ontimeout=c,s.onprogress=function(){s.getResponseHeader("content-type")!==mt&&s.abort()},s.onload=function(){e.read(s.response)},s.onloadend=function(){e.reloading=!1,e.xhr=null},a.checkCrossOrigin&&Gt(t)&&i.crossOrigin&&(t=Ft(t)),s.open("GET",t,!0),s.responseType="arraybuffer",s.withCredentials="use-credentials"===i.crossOrigin,s.send()}else this.clone()}}},{key:"read",value:function(t){var e=this.options,i=this.imageData,a=ne(t),n=0,o=1,r=1;if(a>1){this.url=function(t,e){for(var i=[],a=new Uint8Array(t);a.length>0;)i.push(ie.apply(null,Tt(a.subarray(0,8192)))),a=a.subarray(8192);return"data:".concat(e,";base64,").concat(btoa(i.join("")))}(t,mt);var h=function(t){var e=0,i=1,a=1;switch(t){case 2:i=-1;break;case 3:e=-180;break;case 4:a=-1;break;case 5:e=90,a=-1;break;case 6:e=90;break;case 7:e=90,i=-1;break;case 8:e=-90}return{rotate:e,scaleX:i,scaleY:a}}(a);n=h.rotate,o=h.scaleX,r=h.scaleY}e.rotatable&&(i.rotate=n),e.scalable&&(i.scaleX=o,i.scaleY=r),this.clone()}},{key:"clone",value:function(){var t=this.element,e=this.url,i=t.crossOrigin,a=e;this.options.checkCrossOrigin&&Gt(e)&&(i||(i="anonymous"),a=Ft(e)),this.crossOrigin=i,this.crossOriginUrl=a;var n=document.createElement("img");i&&(n.crossOrigin=i),n.src=a||e,n.alt=t.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),Yt(n,Q),t.parentNode.insertBefore(n,t.nextSibling)}},{key:"start",value:function(){var t=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var i=E.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(E.navigator.userAgent),a=function(e,i){Ht(t.imageData,{naturalWidth:e,naturalHeight:i,aspectRatio:e/i}),t.initialImageData=Ht({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(!e.naturalWidth||i){var n=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){a(n.width,n.height),i||o.removeChild(n)},n.src=e.src,i||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n))}else a(e.naturalWidth,e.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var t=this.element,e=this.options,i=this.image,a=t.parentNode,n=document.createElement("div");n.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=n.querySelector(".".concat(H,"-container")),r=o.querySelector(".".concat(H,"-canvas")),h=o.querySelector(".".concat(H,"-drag-box")),s=o.querySelector(".".concat(H,"-crop-box")),c=s.querySelector(".".concat(H,"-face"));this.container=a,this.cropper=o,this.canvas=r,this.dragBox=h,this.cropBox=s,this.viewBox=o.querySelector(".".concat(H,"-view-box")),this.face=c,r.appendChild(i),Yt(t,$),a.insertBefore(o,t.nextSibling),Xt(i,Q),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,Yt(s,$),e.guides||Yt(s.getElementsByClassName("".concat(H,"-dashed")),$),e.center||Yt(s.getElementsByClassName("".concat(H,"-center")),$),e.background&&Yt(o,"".concat(H,"-bg")),e.highlight||Yt(c,V),e.cropBoxMovable&&(Yt(c,Z),It(c,G,N)),e.cropBoxResizable||(Yt(s.getElementsByClassName("".concat(H,"-line")),$),Yt(s.getElementsByClassName("".concat(H,"-point")),$)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),Ot(e.ready)&&$t(t,lt,e.ready,{once:!0}),Qt(t,lt)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var t=this.cropper.parentNode;t&&t.removeChild(this.cropper),Xt(this.element,$)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}])&&M(e.prototype,i),a&&M(e,a),Object.defineProperty(e,"prototype",{writable:!1}),t}();Ht(pe.prototype,oe,re,he,se,ce,le);const ue=["src"],me=e({__name:"ImageCropping",props:{imageUrl:{type:String,default:"",required:!0},cropBoxWidth:{type:Number,default:200},cropBoxHeight:{type:Number,default:200}},setup(t,{expose:e}){const{getPrefixCls:u}=p(),m=u("image-cropping"),g=t,f=i(),v=i();return a((()=>{(()=>{if(!r(f))return;const t=r(f);v.value=new pe(t,{aspectRatio:1,viewMode:1,dragMode:"move",cropBoxResizable:!1,cropBoxMovable:!1,toggleDragModeOnDblclick:!1,checkCrossOrigin:!1,ready(){var t,e;const i=null==(t=r(v))?void 0:t.getContainerData();null==(e=r(v))||e.setCropBoxData({width:g.cropBoxWidth,height:g.cropBoxHeight,left:((null==i?void 0:i.width)||0)/2-100,top:((null==i?void 0:i.height)||0)/2-100})}})})()})),n((()=>g.imageUrl),(async t=>{var e;await o(),t&&(null==(e=r(v))||e.replace(t))})),h((()=>{var t;null==(t=r(v))||t.destroy()})),e({cropperExpose:()=>r(v)}),(e,i)=>(s(),c("div",{class:d([r(m),"flex justify-center items-center"])},[l("img",{ref_key:"imgRef",ref:f,src:t.imageUrl,class:"block max-w-full",crossorigin:"anonymous",alt:"",srcset:""},null,8,ue)],2))}}),ge=e({__name:"ImageCropping",setup(e){const a=i(),n=i(""),o=()=>{var t,e,i;n.value=(null==(i=null==(e=null==(t=r(a))?void 0:t.cropperExpose())?void 0:e.getCroppedCanvas())?void 0:i.toDataURL())??""};return(e,i)=>{const h=u("BaseButton");return s(),m(r(t),{title:"图片裁剪"},{default:g((()=>[f(h,{type:"primary",class:"mb-20px",onClick:o},{default:g((()=>[v("裁剪")])),_:1}),f(r(w),{modelValue:n.value,"onUpdate:modelValue":i[0]||(i[0]=t=>n.value=t),class:"mb-20px",type:"textarea"},null,8,["modelValue"]),f(r(me),{ref_key:"cropperExpose",ref:a,"image-url":"https://hips.hearstapps.com/hmg-prod/images/%E5%AE%8B%E6%99%BA%E5%AD%9D-1597774015.jpg?crop=0.500xw:1.00xh;0.500xw,0&resize=640:*"},null,512)])),_:1})}}});export{ge as default};
