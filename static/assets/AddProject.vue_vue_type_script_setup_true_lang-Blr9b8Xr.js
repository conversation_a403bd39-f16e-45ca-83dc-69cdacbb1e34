import{d as e,s as a,a8 as l,r as t,H as o,o as r,c as s,e as d,w as u,a as n,A as g,B as p,z as i,t as c,F as m,l as j}from"./index-DfJTpRkj.js";import{E as h}from"./el-divider-0NmzbuNU.js";import{a as f,E as v}from"./el-form-DsaI0u2w.js";import{a as _,E as x}from"./el-col-B4Ik8fnS.js";import{b,u as V,c as y}from"./index-jyMftxhc.js";import{_ as k}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{h as w}from"./index-B0JD2UFG.js";import T from"./DetailTemplate-DU3RXrBH.js";const P=e({__name:"AddProject",props:{closeDialog:{type:Function},projectid:{},getProjectData:{type:Function},schedule:{type:Boolean}},setup(e){const{t:P}=j();let D=a({name:"",tag:"",logo:"",target:"",ignore:"",scheduledTasks:!1,hour:24,node:[],allNode:!1,duplicates:"None",template:""});const N=e,q=l((()=>{const e={name:[{required:!0,message:P("project.msgProject"),trigger:"blur"}],tag:[{required:!0,message:P("project.msgProjectTag"),trigger:"blur"}],target:[{required:!0,message:P("project.msgProjectScope"),trigger:"blur"}],node:[{required:!1,message:P("task.nodeMsg"),trigger:"blur"}],template:[{required:!0,message:"Please select template",trigger:"blur"}]};return D.scheduledTasks&&(e.node=[{required:!0,message:P("task.nodeMsg"),trigger:"blur"}]),e})),U=t(!1),E=t(),F=t(!1),A=t(!1);t(!1),t(!1),a([]);const M=a([]),R=async()=>{M.length=0;const e=await w("",1,1e3);e.data.list.length>0&&e.data.list.forEach((e=>{M.push({value:e.id,label:e.name})}))};o((()=>{(async()=>{if(""!=N.projectid){A.value=!0;const e=await y(N.projectid);D.name=e.data.name,D.tag=e.data.tag,D.target=e.data.target,D.logo=e.data.logo,D.scheduledTasks=e.data.scheduledTasks,D.hour=e.data.hour,D.allNode=e.data.allNode,D.node=e.data.node,D.duplicates=e.data.duplicates,D.ignore=e.data.ignore,D.template=e.data.template,A.value=!1}})(),(async()=>{})(),R()}));const z=t("");const B=t(!1),L=()=>{B.value=!1};return(e,a)=>(r(),s(m,null,[d(n(v),{model:n(D),"label-width":"auto",rules:q.value,"status-icon":"",ref_key:"ruleFormRef",ref:E,loading:A.value},{default:u((()=>[d(n(f),{label:n(P)("project.projectName"),prop:"name"},{default:u((()=>[d(n(g),{modelValue:n(D).name,"onUpdate:modelValue":a[0]||(a[0]=e=>n(D).name=e),placeholder:n(P)("project.msgProject")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),d(n(f),{label:"TAG",prop:"tag"},{default:u((()=>[d(n(g),{modelValue:n(D).tag,"onUpdate:modelValue":a[1]||(a[1]=e=>n(D).tag=e),placeholder:n(P)("project.msgProjectTag")},null,8,["modelValue","placeholder"])])),_:1}),d(n(f),{label:n(P)("project.projectScope"),prop:"target"},{default:u((()=>[d(n(g),{modelValue:n(D).target,"onUpdate:modelValue":a[2]||(a[2]=e=>n(D).target=e),placeholder:n(P)("task.msgTarget"),type:"textarea",autosize:{minRows:6,maxRows:15}},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),d(n(f),{label:n(P)("task.ignore"),prop:"ignore"},{default:u((()=>[d(n(g),{modelValue:n(D).ignore,"onUpdate:modelValue":a[3]||(a[3]=e=>n(D).ignore=e),placeholder:n(P)("task.ignoreMsg"),type:"textarea",rows:"10"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),d(n(f),{label:"Logo",prop:"logo"},{default:u((()=>[d(n(g),{modelValue:n(D).logo,"onUpdate:modelValue":a[4]||(a[4]=e=>n(D).logo=e),placeholder:"http(s)://xxxxx.xx"},null,8,["modelValue"])])),_:1}),d(n(h)),d(n(_),null,{default:u((()=>[d(n(x),{span:2,offset:12},{default:u((()=>[d(n(f),null,{default:u((()=>[d(n(p),{type:"primary",onClick:a[5]||(a[5]=e=>(async e=>{U.value=!0,e&&(await e.validate((async(e,a)=>{e?""==N.projectid?(200===(await b(F.value,D.name,D.tag,D.target,D.logo,D.scheduledTasks,D.hour,D.allNode,D.node,D.duplicates,D.ignore,D.template)).code&&N.closeDialog(),U.value=!1):(200===(await V(F.value,N.projectid,D.name,D.tag,D.target,D.logo,D.scheduledTasks,D.hour,D.allNode,D.node,D.duplicates,D.ignore,D.template)).code&&N.closeDialog(),U.value=!1):U.value=!1})),N.getProjectData(1,50))})(E.value)),loading:U.value},{default:u((()=>[i(c(n(P)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules","loading"]),d(n(k),{modelValue:B.value,"onUpdate:modelValue":a[6]||(a[6]=e=>B.value=e),title:n(""),center:"",fullscreen:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:u((()=>[d(T,{closeDialog:L,getList:R,id:z.value},null,8,["id"])])),_:1},8,["modelValue","title"])],64))}});export{P as _};
