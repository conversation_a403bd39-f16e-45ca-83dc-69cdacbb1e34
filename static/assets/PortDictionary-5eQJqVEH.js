import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,r as a,s as l,e as o,G as i,F as s,y as r,o as n,c as p,w as u,a as m,z as d,A as c,B as g,f,t as v,I as y,J as _,l as j}from"./index-C6fb_XFi.js";import{a as b,E as x}from"./el-col-Dl4_4Pn5.js";import{_ as h}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{_ as S}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{u as w}from"./useIcon-BxqaCND-.js";import{u as D}from"./useTable-CijeIiBB.js";import{b as C,e as k}from"./index-DvCU-6BP.js";import{_ as z}from"./PortDetail.vue_vue_type_script_setup_true_lang-C6Jfv_FD.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tag-C_oEQYGz.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";import"./el-form-C2Y6uNCj.js";import"./el-divider-Bw95UAdD.js";const E={class:"mb-10px"},A={class:"mb-10px"};function I(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!_(e)}const P=t({__name:"PortDictionary",setup(t){const{t:_}=j(),P=w({icon:"iconoir:search"}),U=a(""),F=()=>{J()},V=l([{field:"selection",type:"selection",width:"55"},{field:"id",hidden:!0},{field:"name",label:_("portDict.name"),minWidth:40},{field:"value",label:_("portDict.value")},{field:"action",label:_("tableDemo.action"),minWidth:40,formatter:(e,t,a)=>{let l,r;return o(s,null,[o(i,{type:"primary",onClick:()=>Q(e)},I(l=_("common.edit"))?l:{default:()=>[l]}),o(i,{type:"danger",onClick:()=>$(e)},I(r=_("common.delete"))?r:{default:()=>[r]})])}}]),{tableRegister:H,tableState:L,tableMethods:R}=D({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=L,a=await k(U.value,e.value,t.value);return{list:a.data.list,total:a.data.total}}}),{loading:T,dataList:N,total:W,currentPage:O,pageSize:B}=L,{getList:J,getElTableExpose:M}=R;function G(){return{background:"var(--el-fill-color-light)"}}const X=a(!1),Y=async()=>{K.id="",K.value="",K.name="",X.value=!0},q=()=>{X.value=!1};let K=l({id:"",name:"",value:""});const Q=e=>{K.id=e.id,K.value=e.value,K.name=e.name,X.value=!0},Z=a(!1),$=async e=>{Z.value=!0;try{await C([e.id]);Z.value=!1,J()}catch(t){Z.value=!1,J()}},ee=a([]),te=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{const e=await M(),t=(null==e?void 0:e.getSelectionRows())||[];ee.value=t.map((e=>e.id)),Z.value=!0;try{await C(ee.value),Z.value=!1,J()}catch(a){Z.value=!1,J()}})()};return(t,a)=>{const l=r("ElText");return n(),p(s,null,[o(m(e),null,{default:u((()=>[o(m(b),{gutter:20,style:{"margin-bottom":"15px"}},{default:u((()=>[o(m(x),{span:1.5},{default:u((()=>[o(l,{class:"mx-1",style:{position:"relative",top:"8px"}},{default:u((()=>[d("Search :")])),_:1})])),_:1}),o(m(x),{span:5},{default:u((()=>[o(m(c),{modelValue:U.value,"onUpdate:modelValue":a[0]||(a[0]=e=>U.value=e),placeholder:m(_)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(m(x),{span:5},{default:u((()=>[o(m(g),{type:"primary",icon:m(P),style:{height:"38px"},onClick:F},{default:u((()=>[d("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(m(b),{gutter:60},{default:u((()=>[o(m(x),{span:1},{default:u((()=>[f("div",E,[o(m(g),{type:"primary",onClick:Y},{default:u((()=>[d(v(m(_)("common.new")),1)])),_:1})])])),_:1}),o(m(x),{span:1},{default:u((()=>[f("div",A,[o(m(i),{type:"danger",loading:Z.value,onClick:te},{default:u((()=>[d(v(m(_)("common.delete")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),o(m(h),{pageSize:m(B),"onUpdate:pageSize":a[1]||(a[1]=e=>y(B)?B.value=e:null),currentPage:m(O),"onUpdate:currentPage":a[2]||(a[2]=e=>y(O)?O.value=e:null),columns:V,data:m(N),stripe:"",border:!0,loading:m(T),resizable:!0,pagination:{total:m(W),pageSizes:[10,20,50,100,200,500,1e3]},onRegister:m(H),headerCellStyle:G,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister"])])),_:1}),o(m(S),{modelValue:X.value,"onUpdate:modelValue":a[3]||(a[3]=e=>X.value=e),title:m(_)("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:400},{default:u((()=>[o(z,{closeDialog:q,portDictForm:m(K),getList:m(J)},null,8,["portDictForm","getList"])])),_:1},8,["modelValue","title"])],64)}}});export{P as default};
