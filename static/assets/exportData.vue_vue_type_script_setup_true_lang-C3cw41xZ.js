import{d as e,s as a,e as t,G as l,F as o,r as i,H as r,o as s,i as n,w as d,a as u,z as p,t as m,A as c,c as f,R as y,B as v,J as x,l as b}from"./index-C6fb_XFi.js";import{E as _,a as g}from"./el-tab-pane-DDoZFwPS.js";import{E as h,a as w}from"./el-form-C2Y6uNCj.js";import{a as j,E as V}from"./el-radio-group-hI5DSxSU.js";import{E}from"./el-tag-C_oEQYGz.js";import{E as S}from"./el-space-CdSK6Ce1.js";import{E as C,a as T}from"./el-checkbox-CvJzNe2E.js";/* empty css                          */import{r as k}from"./index-CnCQNuY4.js";import{_ as A}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as R}from"./useTable-CijeIiBB.js";const U=e=>k.post({url:"/api/export/delete",data:{ids:e}});function F(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!x(e)}const q=e({__name:"exportData",props:{index:{},searchParams:{},getFilter:{type:Function}},setup(e){const{t:x}=b(),q=e,z=a({type:"all",quantity:0}),N=async()=>{J.value=!0;const e=q.getFilter();await((e,a,t,l,o,i,r)=>k.post({url:"/api/export",data:{index:e,quantity:a,type:t,search:l,filter:o,field:i,filetype:r}}))(q.index,z.quantity,z.type,q.searchParams,e,oe.value,se.value),J.value=!1},B=a([{field:"selection",type:"selection"},{field:"file_name",label:x("export.fileName"),width:"160"},{field:"state",label:x("export.state"),width:"160",formatter:(e,a,l)=>{if(0==l){let e;return t(E,{type:"info"},F(e=x("export.run"))?e:{default:()=>[e]})}if(1==l){let e;return t(E,{type:"success"},F(e=x("export.success"))?e:{default:()=>[e]})}{let e;return t(E,{type:"danger"},F(e=x("export.fail"))?e:{default:()=>[e]})}}},{field:"create_time",label:x("export.createTime"),width:"160"},{field:"end_time",label:x("export.endTime"),width:"160",formatter:(e,a,t)=>""==t?"-":t},{field:"data_type",label:x("export.type"),width:"120"},{field:"file_size",label:x("export.fileSize"),width:"100",formatter:(e,a,t)=>""==t?"-":t+" MB"},{field:"action",label:x("tableDemo.action"),width:"200",formatter:(e,a,i)=>{let r,s;return t(o,null,[t(l,{type:"success",onClick:()=>G(e.file_name)},F(r=x("export.download"))?r:{default:()=>[r]}),t(l,{type:"danger",onClick:()=>W(e)},F(s=x("common.delete"))?s:{default:()=>[s]})])}}]),{tableRegister:D,tableState:I,tableMethods:M}=R({fetchDataApi:async()=>({list:(await k.get({url:"/api/export/record"})).data.list}),immediate:!1}),{dataList:P,loading:H}=I,{getList:L,getElTableExpose:O}=M,G=async e=>{const a=document.createElement("a");a.href="/api/export/download?file_name="+e,a.click()},J=i(!1),Q=e=>{"exportRecords"==e&&L()},K=i(!1),W=async e=>{window.confirm("Are you sure you want to delete the selected data?")&&await X(e)},X=async e=>{K.value=!0;try{await U([e.file_name]);K.value=!1,L()}catch(a){K.value=!1,L()}},Y=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await $()},Z=i([]),$=async()=>{const e=await O(),a=(null==e?void 0:e.getSelectionRows())||[];Z.value=a.map((e=>e.file_name)),K.value=!0;try{await U(Z.value);K.value=!1,L()}catch(t){K.value=!1,L()}},ee=i([]),ae=async()=>{const e=await(a=q.index,k.post({url:"/api/getfield",data:{index:a}}));var a;ee.value=e.data.field};r((()=>{ae()}));const te=i(!0),le=i(!1),oe=i([]),ie=e=>{oe.value=e?ee.value:[],te.value=!1},re=e=>{const a=e.length;le.value=a===ee.value.length,te.value=a>0&&a<ee.value.length},se=i("csv");return(e,a)=>(s(),n(u(g),{tabPosition:"left",onTabChange:Q,"model-value":"export"},{default:d((()=>[t(u(_),{label:u(x)("asset.export"),name:"export"},{default:d((()=>[t(u(h),{model:z,"label-width":"auto",style:{position:"relative"}},{default:d((()=>[t(u(w),{label:u(x)("export.exportType")},{default:d((()=>[t(u(j),{modelValue:z.type,"onUpdate:modelValue":a[0]||(a[0]=e=>z.type=e)},{default:d((()=>[t(u(V),{value:"all"},{default:d((()=>[p(m(u(x)("export.exportTypeAll")),1)])),_:1}),t(u(V),{value:"search"},{default:d((()=>[p(m(u(x)("export.exportTypeSearch")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),t(u(w),{label:u(x)("export.exportQuantity")},{default:d((()=>[t(u(c),{modelValue:z.quantity,"onUpdate:modelValue":a[1]||(a[1]=e=>z.quantity=e)},null,8,["modelValue"])])),_:1},8,["label"]),t(u(w),{label:u(x)("export.field")},{default:d((()=>[t(u(C),{modelValue:le.value,"onUpdate:modelValue":a[2]||(a[2]=e=>le.value=e),indeterminate:te.value,onChange:ie},{default:d((()=>[p(" All ")])),_:1},8,["modelValue","indeterminate"])])),_:1},8,["label"]),t(u(w),{label:" "},{default:d((()=>[t(u(T),{modelValue:oe.value,"onUpdate:modelValue":a[3]||(a[3]=e=>oe.value=e),onChange:re},{default:d((()=>[(s(!0),f(o,null,y(ee.value,(e=>(s(),n(u(C),{key:e,label:e,value:e},{default:d((()=>[p(m(e),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),t(u(w),{label:u(x)("export.fileType")},{default:d((()=>[t(u(j),{modelValue:se.value,"onUpdate:modelValue":a[4]||(a[4]=e=>se.value=e)},{default:d((()=>[t(u(V),{value:"csv"},{default:d((()=>[p("csv")])),_:1}),t(u(V),{value:"json"},{default:d((()=>[p("json")])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),t(u(w),null,{default:d((()=>[t(u(v),{type:"primary",onClick:N,style:{left:"40%",position:"relative"},loading:J.value},{default:d((()=>[p(" Create ")])),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])])),_:1},8,["label"]),t(u(_),{label:u(x)("export.exportRecords"),name:"exportRecords"},{default:d((()=>[t(u(S),{direction:"vertical",alignment:"flex-start"},{default:d((()=>[t(u(l),{type:"danger",loading:K.value,onClick:Y},{default:d((()=>[p(m(u(x)("common.delete")),1)])),_:1},8,["loading"]),t(u(A),{onRegister:u(D),columns:B,data:u(P),loading:u(H),"max-height":"500",style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["onRegister","columns","data","loading"])])),_:1})])),_:1},8,["label"])])),_:1}))}});export{q as _};
