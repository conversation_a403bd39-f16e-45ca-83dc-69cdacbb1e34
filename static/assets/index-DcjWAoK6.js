import{r as a}from"./index-Dz8ZrwBc.js";const l=(l,p,s)=>a.post({url:"/api/plugin/list",data:{search:l,pageIndex:p,pageSize:s}}),p=l=>a.post({url:"/api/plugin/detail",data:{id:l}}),s=(l,p,s,e,i,t,u,o,d)=>a.post({url:"/api/plugin/save",data:{id:l,name:p,version:s,module:e,parameter:i,help:t,introduction:u,source:o,key:d}}),e=l=>a.post({url:"/api/plugin/delete",data:{data:l}}),i=l=>a.post({url:"/api/plugin/key/check",data:{key:l}}),t=(l,p)=>a.post({url:"/api/plugin/log",data:{module:l,hash:p}}),u=(l,p)=>a.post({url:"/api/plugin/log/clean",data:{module:l,hash:p}}),o=l=>a.post({url:"/api/plugin/list/bymodule",data:{module:l}}),d=(l,p,s)=>a.post({url:"/api/plugin/reinstall",data:{node:l,hash:p,module:s}}),n=(l,p,s)=>a.post({url:"/api/plugin/recheck",data:{node:l,hash:p,module:s}}),r=(l,p,s)=>a.post({url:"/api/plugin/uninstall",data:{node:l,hash:p,module:s}});export{d as a,l as b,i as c,e as d,u as e,o as f,t as g,p as h,n as r,s,r as u};
