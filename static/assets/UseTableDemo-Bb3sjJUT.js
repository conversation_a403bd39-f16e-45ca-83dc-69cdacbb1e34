import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as t,a,l,s as o,e as i,z as n,G as s,r,o as d,c as m,w as p,t as u,I as c,F as b,J as f,_ as g}from"./index-3XfDPlIS.js";import{_ as D}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{a as _}from"./index-DkbkkiFT.js";import{E as j}from"./el-tag-DcMbxLLg.js";import{u as h}from"./useTable-BezX3TfM.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./el-table-column-B5hg3WH6.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./index-Dz8ZrwBc.js";function v(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!f(e)}const x=g(t({__name:"UseTableDemo",setup(t){const{tableRegister:f,tableMethods:g,tableState:x}=h({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=x,l=await _({pageIndex:a(e),pageSize:a(t)});return{list:l.data.list,total:l.data.total}}}),{loading:y,dataList:C,total:k,currentPage:w,pageSize:S}=x,{setProps:A,setColumn:T,getElTableExpose:z,addColumn:I,delColumn:R,refresh:O}=g,{t:P}=l(),E=o([{field:"expand",type:"expand",slots:{default:e=>{const{row:t}=e;return i("div",{class:"ml-30px"},[i("div",null,[P("tableDemo.title"),n("："),t.title]),i("div",null,[P("tableDemo.author"),n("："),t.author]),i("div",null,[P("tableDemo.displayTime"),n("："),t.display_time])])}}},{field:"selection",type:"selection"},{field:"index",label:P("tableDemo.index"),type:"index"},{field:"content",label:P("tableDemo.header"),children:[{field:"title",label:P("tableDemo.title")},{field:"author",label:P("tableDemo.author")},{field:"display_time",label:P("tableDemo.displayTime")},{field:"importance",label:P("tableDemo.importance"),formatter:(e,t,a)=>i(j,{type:1===a?"success":2===a?"warning":"danger"},{default:()=>[P(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")]})},{field:"pageviews",label:P("tableDemo.pageviews")}]},{field:"action",label:P("tableDemo.action"),slots:{default:e=>{let t;return i(s,{type:"primary",onClick:()=>U(e)},v(t=P("tableDemo.action"))?t:{default:()=>[t]})}}}]),U=e=>{},$=r(!0),H=e=>{$.value=e},F=e=>{A({reserveIndex:e})},G=e=>{T([{field:"selection",path:"hidden",value:!e}])},L=r(1),M=()=>{T([{field:"title",path:"label",value:`${P("tableDemo.title")}${a(L)}`}]),L.value++},B=e=>{T([{field:"expand",path:"hidden",value:!e}])},J=async()=>{const e=await z();null==e||e.toggleAllSelection()},K=r(!0),N=()=>{a(K)?(R("action"),K.value=!1):(I({field:"action",label:P("tableDemo.action"),slots:{default:e=>{let t;return i(s,{type:"primary",onClick:()=>U(e)},v(t=P("tableDemo.action"))?t:{default:()=>[t]})}}}),K.value=!0)},W=r(!1),q=()=>{A({stripe:!a(W)}),W.value=!a(W)},Q=r("auto"),V=()=>{"auto"===a(Q)?(A({height:300}),Q.value=300):(A({height:"auto"}),Q.value="auto")},X=async()=>{const e=await z();null==e||e.getSelectionRows()};return(t,l)=>(d(),m(b,null,[i(a(e),{title:`UseTable ${a(P)("tableDemo.operate")}`,style:{"margin-bottom":"20px"}},{default:p((()=>[i(a(s),{onClick:l[0]||(l[0]=e=>H(!0))},{default:p((()=>[n(u(a(P)("tableDemo.show"))+" "+u(a(P)("tableDemo.pagination")),1)])),_:1}),i(a(s),{onClick:l[1]||(l[1]=e=>H(!1))},{default:p((()=>[n(u(a(P)("tableDemo.hidden"))+" "+u(a(P)("tableDemo.pagination")),1)])),_:1}),i(a(s),{onClick:l[2]||(l[2]=e=>F(!0))},{default:p((()=>[n(u(a(P)("tableDemo.reserveIndex")),1)])),_:1}),i(a(s),{onClick:l[3]||(l[3]=e=>F(!1))},{default:p((()=>[n(u(a(P)("tableDemo.restoreIndex")),1)])),_:1}),i(a(s),{onClick:l[4]||(l[4]=e=>G(!0))},{default:p((()=>[n(u(a(P)("tableDemo.showSelections")),1)])),_:1}),i(a(s),{onClick:l[5]||(l[5]=e=>G(!1))},{default:p((()=>[n(u(a(P)("tableDemo.hiddenSelections")),1)])),_:1}),i(a(s),{onClick:M},{default:p((()=>[n(u(a(P)("tableDemo.changeTitle")),1)])),_:1}),i(a(s),{onClick:l[6]||(l[6]=e=>B(!0))},{default:p((()=>[n(u(a(P)("tableDemo.showExpandedRows")),1)])),_:1}),i(a(s),{onClick:l[7]||(l[7]=e=>B(!1))},{default:p((()=>[n(u(a(P)("tableDemo.hiddenExpandedRows")),1)])),_:1}),i(a(s),{onClick:J},{default:p((()=>[n(u(a(P)("tableDemo.selectAllNone")),1)])),_:1}),i(a(s),{onClick:N},{default:p((()=>[n(u(a(P)("tableDemo.delOrAddAction")),1)])),_:1}),i(a(s),{onClick:q},{default:p((()=>[n(u(a(P)("tableDemo.showOrHiddenStripe")),1)])),_:1}),i(a(s),{onClick:V},{default:p((()=>[n(u(a(P)("tableDemo.fixedHeaderOrAuto")),1)])),_:1}),i(a(s),{onClick:X},{default:p((()=>[n(u(a(P)("tableDemo.getSelections")),1)])),_:1})])),_:1},8,["title"]),i(a(e),{title:`UseTable ${a(P)("tableDemo.example")}`},{default:p((()=>[i(a(D),{pageSize:a(S),"onUpdate:pageSize":l[8]||(l[8]=e=>c(S)?S.value=e:null),currentPage:a(w),"onUpdate:currentPage":l[9]||(l[9]=e=>c(w)?w.value=e:null),showAction:"",columns:E,data:a(C),loading:a(y),pagination:$.value?{total:a(k)}:void 0,onRegister:a(f),onRefresh:a(O)},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister","onRefresh"])])),_:1},8,["title"])],64))}}),[["__scopeId","data-v-daa5600d"]]);export{x as default};
