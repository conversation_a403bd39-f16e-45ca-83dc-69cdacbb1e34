import{d as e,s as t,V as o,r as a,o as s,c as i,e as l,w as r,a as m,f as n,t as p,A as d,B as u,z as f,F as j,l as c,_ as g}from"./index-C6fb_XFi.js";import{a as _,E as y}from"./el-col-Dl4_4Pn5.js";import{E as x,a as w}from"./el-form-C2Y6uNCj.js";import{E as b}from"./el-text-BnUG9HvL.js";import{E as h}from"./el-divider-Bw95UAdD.js";import{E as v}from"./el-card-B37ahJ8o.js";import z from"./notification-CeqtEgNJ.js";import C from"./Deduplication-SjqWskOK.js";import{g as M,s as V}from"./index-Bkh3VFwV.js";import{j as E,o as D,T as k}from"./index-CZoUTVkP.js";import"./castArray-DRqY4cIf.js";import"./el-radio-group-hI5DSxSU.js";import"./el-switch-Bh7JeorW.js";import"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import"./refs-3HtnmaOD.js";import"./el-popper-CeVwVUf9.js";import"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-C_oEQYGz.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./index-ghAu5K8t.js";import"./useTable-CijeIiBB.js";import"./el-input-number-DVs4I2j5.js";import"./index-CnCQNuY4.js";const A=g(e({__name:"system",setup(e){const g=[E(),D],{t:A}=c(),H=t({timezone:"",ModulesConfig:""});o((async()=>{try{const e=await M();200==e.code&&(H.timezone=e.data.timezone,H.ModulesConfig=e.data.ModulesConfig)}catch(e){}}));const B=async()=>{window.confirm("Do you want to save the data?")&&await F()},F=async()=>{I.value=!0;(await V(H.timezone,H.ModulesConfig)).code,I.value=!1},I=a(!1);return(e,t)=>(s(),i(j,null,[l(m(v),{shadow:"never",class:"mb-20px"},{header:r((()=>[l(m(_),null,{default:r((()=>[l(m(y),{span:3,style:{height:"100%"}},{default:r((()=>[n("span",null,p(m(A)("configuration.system")),1)])),_:1})])),_:1})])),default:r((()=>[l(m(x),{model:H,"label-width":"auto",style:{"max-width":"600px"}},{default:r((()=>[l(m(w),{label:m(A)("configuration.timezone")},{default:r((()=>[l(m(d),{modelValue:H.timezone,"onUpdate:modelValue":t[0]||(t[0]=e=>H.timezone=e)},null,8,["modelValue"])])),_:1},8,["label"]),l(m(w),{label:"Module Config"},{default:r((()=>[l(m(k),{modelValue:H.ModulesConfig,"onUpdate:modelValue":t[1]||(t[1]=e=>H.ModulesConfig=e),extensions:g,autofocus:!0,"indent-with-tab":!0,"tab-size":2,style:{height:"550px",width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),l(m(_),null,{default:r((()=>[l(m(y),{span:12,offset:2},{default:r((()=>[l(m(u),{type:"primary",onClick:B,loading:I.value},{default:r((()=>[f("Save")])),_:1},8,["loading"]),l(m(h),{direction:"vertical"}),l(m(b),{size:"small",type:"danger"},{default:r((()=>[f(p(m(A)("configuration.threadMsg")),1)])),_:1})])),_:1})])),_:1})])),_:1}),l(z),l(C)],64))}}),[["__scopeId","data-v-bc860df1"]]);export{A as default};
