import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,l as s,r as i,o as r,i as a,w as o,e as l,a as p,f as n,t as m,z as c}from"./index-C6fb_XFi.js";import{_ as d}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{g as j}from"./index-Bh6CT4kq.js";import{E as u}from"./el-link-B7nRzwUO.js";import{E as x}from"./el-divider-Bw95UAdD.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tag-C_oEQYGz.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";const f={class:"flex cursor-pointer"},v={class:"pr-16px"},_=["src"],g={class:"mb-12px font-700 font-size-16px"},y={class:"line-clamp-3 font-size-12px"},b={class:"flex justify-center items-center"},w=["onClick"],k=["onClick"],C=t({__name:"CardTable",setup(t){const{t:C}=s(),h=i(!0);let z=i([]);(async e=>{const t=await j(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{h.value=!1}));t&&(z.value=t.data.list)})();return(t,s)=>(r(),a(p(e),{title:p(C)("tableDemo.cardTable")},{default:o((()=>[l(p(d),{columns:[],data:p(z),loading:h.value,"custom-content":"","card-wrap-style":{width:"200px",marginBottom:"20px",marginRight:"20px"}},{content:o((e=>[n("div",f,[n("div",v,[n("img",{src:e.logo,class:"w-48px h-48px rounded-[50%]",alt:""},null,8,_)]),n("div",null,[n("div",g,m(e.name),1),n("div",y,m(e.desc),1)])])])),"content-footer":o((e=>[n("div",b,[n("div",{class:"flex-1 text-center",onClick:()=>{}},[l(p(u),{underline:!1},{default:o((()=>[c("操作一")])),_:1})],8,w),l(p(x),{direction:"vertical"}),n("div",{class:"flex-1 text-center",onClick:()=>{}},[l(p(u),{underline:!1},{default:o((()=>[c("操作二")])),_:1})],8,k)])])),_:1},8,["data","loading"])])),_:1},8,["title"]))}});export{C as default};
