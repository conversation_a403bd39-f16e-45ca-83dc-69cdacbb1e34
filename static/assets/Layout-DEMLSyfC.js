import{b5 as e,r as t,bt as l,H as a,bu as o,d6 as n,d as s,a7 as r,a8 as i,o as u,i as c,w as d,a as p,c as v,aH as m,n as h,af as f,a9 as g,e as x,C as b,dP as y,j as w,h as _,aa as C,ah as k,Z as M,bm as I,aA as T,a6 as $,$ as V,aQ as P,ab as S,f as j,aI as L,t as B,ai as z,aK as O,dQ as A,aJ as E,cq as R,cp as H,bf as N,dR as U,D as F,ad as q,s as D,O as W,x as G,v as J,a4 as Q,Q as Z,ag as K,F as Y,dd as X,a0 as ee,dS as te,b9 as le,b1 as ae,cV as oe,bq as ne,b7 as se,Y as re,bh as ie,y as ue,z as ce,k as de,aq as pe,R as ve,_ as me,b as he,l as fe,p as ge,m as xe,dT as be,dU as ye,dV as we,dW as _e,dX as Ce,dY as ke,dZ as Me,M as Ie,d_ as Te,d$ as $e,S as Ve,e0 as Pe,e1 as Se,u as je,q as Le,E as Be,J as ze,aW as Oe,P as Ae,e2 as Ee,e3 as Re,e4 as He,e5 as Ne,e6 as Ue,N as Fe,e7 as qe,e8 as De,bv as We,A as Ge,e9 as Je,c1 as Qe,ea as Ze,T as Ke}from"./index-3XfDPlIS.js";import{E as Ye}from"./el-drawer-COw0uCzP.js";import{E as Xe}from"./el-divider-D9UCOo44.js";import{T as et,_ as tt}from"./LocaleDropdown.vue_vue_type_script_setup_true_lang-CF7Rxe0t.js";import{E as lt}from"./el-switch-C-DLgt5X.js";import"./el-tooltip-l0sNRNKZ.js";import{E as at}from"./el-popper-DVoWBu_3.js";import{a as ot,f as nt,b as st}from"./tree-BfZhwLPs.js";import{_ as rt}from"./index-6fa0VYPg.js";import{C as it}from"./index-tjM0-mlU.js";import{E as ut,a as ct,b as dt}from"./el-dropdown-item-BMccEdtX.js";import{_ as pt}from"./logo-BM2ksA2B.js";import{_ as vt}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{u as mt,F as ht,d as ft}from"./useForm-CxJHOWP1.js";import{u as gt}from"./useValidator-ByHrE6OC.js";import{c as xt,b as bt}from"./index-96QZJe_-.js";import"./useIcon-k-uSyz6l.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./el-tag-DcMbxLLg.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./el-form-BY8piFS2.js";import"./el-col-CN1tVfqh.js";import"./el-checkbox-DjLAvZXr.js";import"./el-radio-group-evFfsZkP.js";/* empty css                          */import"./el-input-number-CfcpPMpr.js";import"./el-virtual-list-Drl4IGmp.js";import"./raf-BoCEWvzN.js";import"./el-select-v2-CJw7ZO42.js";import"./el-autocomplete-DpYoUHkX.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./el-upload-D4EYBM-E.js";import"./el-progress-DtkkA0nz.js";import"./InputPassword-CsftE_fC.js";import"./style.css_vue_type_style_index_0_src_true_lang-C2CruVLZ.js";import"./JsonEditor.vue_vue_type_script_setup_true_lang-C3k54pLm.js";import"./IconPicker-CGJrUvM2.js";import"./el-tab-pane-xcqYouKU.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./tsxHelper-C7SpLWNA.js";/* empty css                        */import"./index-Dz8ZrwBc.js";const yt={visibilityHeight:{type:Number,default:200},target:{type:String,default:""},right:{type:Number,default:40},bottom:{type:Number,default:40}},wt={click:e=>e instanceof MouseEvent},_t="ElBacktop",Ct=s({name:_t});const kt=k(C(s({...Ct,props:yt,emits:wt,setup(s,{emit:C}){const k=s,M=r("backtop"),{handleClick:I,visible:T}=((s,r,i)=>{const u=e(),c=e(),d=t(!1),p=()=>{u.value&&(d.value=u.value.scrollTop>=s.visibilityHeight)},v=n(p,300,!0);return l(c,"scroll",v),a((()=>{var e;c.value=document,u.value=document.documentElement,s.target&&(u.value=null!=(e=document.querySelector(s.target))?e:void 0,u.value||o(i,`target does not exist: ${s.target}`),c.value=u.value),p()})),{visible:d,handleClick:e=>{var t;null==(t=u.value)||t.scrollTo({top:0,behavior:"smooth"}),r("click",e)}}})(k,C,_t),$=i((()=>({right:`${k.right}px`,bottom:`${k.bottom}px`})));return(e,t)=>(u(),c(_,{name:`${p(M).namespace.value}-fade-in`},{default:d((()=>[p(T)?(u(),v("div",{key:0,style:m(p($)),class:h(p(M).b()),onClick:t[0]||(t[0]=f(((...e)=>p(I)&&p(I)(...e)),["stop"]))},[g(e.$slots,"default",{},(()=>[x(p(b),{class:h(p(M).e("icon"))},{default:d((()=>[x(p(y))])),_:1},8,["class"])]))],6)):w("v-if",!0)])),_:3},8,["name"]))}}),[["__file","backtop.vue"]])),Mt=Symbol("breadcrumbKey"),It=M({separator:{type:String,default:"/"},separatorIcon:{type:I}}),Tt=["aria-label"],$t=s({name:"ElBreadcrumb"});var Vt=C(s({...$t,props:It,setup(e){const l=e,{t:o}=T(),n=r("breadcrumb"),s=t();return $(Mt,l),a((()=>{const e=s.value.querySelectorAll(`.${n.e("item")}`);e.length&&e[e.length-1].setAttribute("aria-current","page")})),(e,t)=>(u(),v("div",{ref_key:"breadcrumb",ref:s,class:h(p(n).b()),"aria-label":p(o)("el.breadcrumb.label"),role:"navigation"},[g(e.$slots,"default")],10,Tt))}}),[["__file","breadcrumb.vue"]]);const Pt=M({to:{type:V([String,Object]),default:""},replace:{type:Boolean,default:!1}}),St=s({name:"ElBreadcrumbItem"});var jt=C(s({...St,props:Pt,setup(e){const l=e,a=P(),o=S(Mt,void 0),n=r("breadcrumb"),s=a.appContext.config.globalProperties.$router,i=t(),m=()=>{l.to&&s&&(l.replace?s.replace(l.to):s.push(l.to))};return(e,t)=>{var l,a;return u(),v("span",{class:h(p(n).e("item"))},[j("span",{ref_key:"link",ref:i,class:h([p(n).e("inner"),p(n).is("link",!!e.to)]),role:"link",onClick:m},[g(e.$slots,"default")],2),(null==(l=p(o))?void 0:l.separatorIcon)?(u(),c(p(b),{key:0,class:h(p(n).e("separator"))},{default:d((()=>[(u(),c(L(p(o).separatorIcon)))])),_:1},8,["class"])):(u(),v("span",{key:1,class:h(p(n).e("separator")),role:"presentation"},B(null==(a=p(o))?void 0:a.separator),3))],2)}}}),[["__file","breadcrumb-item.vue"]]);const Lt=k(Vt,{BreadcrumbItem:jt}),Bt=z(jt);let zt=class{constructor(e,t){this.parent=e,this.domNode=t,this.subIndex=0,this.subIndex=0,this.init()}init(){this.subMenuItems=this.domNode.querySelectorAll("li"),this.addListeners()}gotoSubIndex(e){e===this.subMenuItems.length?e=0:e<0&&(e=this.subMenuItems.length-1),this.subMenuItems[e].focus(),this.subIndex=e}addListeners(){const e=this.parent.domNode;Array.prototype.forEach.call(this.subMenuItems,(t=>{t.addEventListener("keydown",(t=>{let l=!1;switch(t.code){case O.down:this.gotoSubIndex(this.subIndex+1),l=!0;break;case O.up:this.gotoSubIndex(this.subIndex-1),l=!0;break;case O.tab:A(e,"mouseleave");break;case O.enter:case O.space:l=!0,t.currentTarget.click()}return l&&(t.preventDefault(),t.stopPropagation()),!1}))}))}},Ot=class{constructor(e,t){this.domNode=e,this.submenu=null,this.submenu=null,this.init(t)}init(e){this.domNode.setAttribute("tabindex","0");const t=this.domNode.querySelector(`.${e}-menu`);t&&(this.submenu=new zt(this,t)),this.addListeners()}addListeners(){this.domNode.addEventListener("keydown",(e=>{let t=!1;switch(e.code){case O.down:A(e.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(0),t=!0;break;case O.up:A(e.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(this.submenu.subMenuItems.length-1),t=!0;break;case O.tab:A(e.currentTarget,"mouseleave");break;case O.enter:case O.space:t=!0,e.currentTarget.click()}t&&e.preventDefault()}))}},At=class{constructor(e,t){this.domNode=e,this.init(t)}init(e){const t=this.domNode.childNodes;Array.from(t).forEach((t=>{1===t.nodeType&&new Ot(t,e)}))}};var Et=C(s({name:"ElMenuCollapseTransition",setup(){const e=r("menu");return{listeners:{onBeforeEnter:e=>e.style.opacity="0.2",onEnter(t,l){R(t,`${e.namespace.value}-opacity-transition`),t.style.opacity="1",l()},onAfterEnter(t){H(t,`${e.namespace.value}-opacity-transition`),t.style.opacity=""},onBeforeLeave(t){t.dataset||(t.dataset={}),N(t,e.m("collapse"))?(H(t,e.m("collapse")),t.dataset.oldOverflow=t.style.overflow,t.dataset.scrollWidth=t.clientWidth.toString(),R(t,e.m("collapse"))):(R(t,e.m("collapse")),t.dataset.oldOverflow=t.style.overflow,t.dataset.scrollWidth=t.clientWidth.toString(),H(t,e.m("collapse"))),t.style.width=`${t.scrollWidth}px`,t.style.overflow="hidden"},onLeave(e){R(e,"horizontal-collapse-transition"),e.style.width=`${e.dataset.scrollWidth}px`}}}}}),[["render",function(e,t,l,a,o,n){return u(),c(_,E({mode:"out-in"},e.listeners),{default:d((()=>[g(e.$slots,"default")])),_:3},16)}],["__file","menu-collapse-transition.vue"]]);function Rt(e,t){const l=i((()=>{let l=e.parent;const a=[t.value];for(;"ElMenu"!==l.type.name;)l.props.index&&a.unshift(l.props.index),l=l.parent;return a}));return{parentMenu:i((()=>{let t=e.parent;for(;t&&!["ElMenu","ElSubMenu"].includes(t.type.name);)t=t.parent;return t})),indexPath:l}}function Ht(e){return i((()=>{const t=e.backgroundColor;return t?new U(t).shade(20).toString():""}))}const Nt=(e,t)=>{const l=r("menu");return i((()=>l.cssVarBlock({"text-color":e.textColor||"","hover-text-color":e.textColor||"","bg-color":e.backgroundColor||"","hover-bg-color":Ht(e).value||"","active-color":e.activeTextColor||"",level:`${t}`})))},Ut=M({index:{type:String,required:!0},showTimeout:Number,hideTimeout:Number,popperClass:String,disabled:Boolean,teleported:{type:Boolean,default:void 0},popperOffset:Number,expandCloseIcon:{type:I},expandOpenIcon:{type:I},collapseCloseIcon:{type:I},collapseOpenIcon:{type:I}}),Ft="ElSubMenu";var qt=s({name:Ft,props:Ut,setup(e,{slots:l,expose:n}){const s=P(),{indexPath:u,parentMenu:c}=Rt(s,i((()=>e.index))),d=r("menu"),p=r("sub-menu"),v=S("rootMenu");v||o(Ft,"can not inject root menu");const m=S(`subMenu:${c.value.uid}`);m||o(Ft,"can not inject sub menu");const h=t({}),f=t({});let g;const x=t(!1),y=t(),w=t(null),_=i((()=>"horizontal"===L.value&&k.value?"bottom-start":"right-start")),C=i((()=>"horizontal"===L.value&&k.value||"vertical"===L.value&&!v.props.collapse?e.expandCloseIcon&&e.expandOpenIcon?V.value?e.expandOpenIcon:e.expandCloseIcon:F:e.collapseCloseIcon&&e.collapseOpenIcon?V.value?e.collapseOpenIcon:e.collapseCloseIcon:q)),k=i((()=>0===m.level)),M=i((()=>{const t=e.teleported;return void 0===t?k.value:t})),I=i((()=>v.props.collapse?`${d.namespace.value}-zoom-in-left`:`${d.namespace.value}-zoom-in-top`)),T=i((()=>"horizontal"===L.value&&k.value?["bottom-start","bottom-end","top-start","top-end","right-start","left-start"]:["right-start","right","right-end","left-start","bottom-start","bottom-end","top-start","top-end"])),V=i((()=>v.openedMenus.includes(e.index))),j=i((()=>{let e=!1;return Object.values(h.value).forEach((t=>{t.active&&(e=!0)})),Object.values(f.value).forEach((t=>{t.active&&(e=!0)})),e})),L=i((()=>v.props.mode)),B=D({index:e.index,indexPath:u,active:j}),z=Nt(v.props,m.level+1),O=i((()=>{var t;return null!=(t=e.popperOffset)?t:v.props.popperOffset})),A=i((()=>{var t;return null!=(t=e.popperClass)?t:v.props.popperClass})),E=i((()=>{var t;return null!=(t=e.showTimeout)?t:v.props.showTimeout})),R=i((()=>{var t;return null!=(t=e.hideTimeout)?t:v.props.hideTimeout})),H=e=>{var t,l,a;e||null==(a=null==(l=null==(t=w.value)?void 0:t.popperRef)?void 0:l.popperInstanceRef)||a.destroy()},N=()=>{"hover"===v.props.menuTrigger&&"horizontal"===v.props.mode||v.props.collapse&&"vertical"===v.props.mode||e.disabled||v.handleSubMenuClick({index:e.index,indexPath:u.value,active:j.value})},U=(t,l=E.value)=>{var a;"focus"!==t.type&&("click"===v.props.menuTrigger&&"horizontal"===v.props.mode||!v.props.collapse&&"vertical"===v.props.mode||e.disabled?m.mouseInChild.value=!0:(m.mouseInChild.value=!0,null==g||g(),({stop:g}=X((()=>{v.openMenu(e.index,u.value)}),l)),M.value&&(null==(a=c.value.vnode.el)||a.dispatchEvent(new MouseEvent("mouseenter")))))},ee=(t=!1)=>{var l;"click"===v.props.menuTrigger&&"horizontal"===v.props.mode||!v.props.collapse&&"vertical"===v.props.mode?m.mouseInChild.value=!1:(null==g||g(),m.mouseInChild.value=!1,({stop:g}=X((()=>!x.value&&v.closeMenu(e.index,u.value)),R.value)),M.value&&t&&(null==(l=m.handleMouseleave)||l.call(m,!0)))};W((()=>v.props.collapse),(e=>H(Boolean(e))));{const e=e=>{f.value[e.index]=e},t=e=>{delete f.value[e.index]};$(`subMenu:${s.uid}`,{addSubMenu:e,removeSubMenu:t,handleMouseleave:ee,mouseInChild:x,level:m.level+1})}return n({opened:V}),a((()=>{v.addSubMenu(B),m.addSubMenu(B)})),G((()=>{m.removeSubMenu(B),v.removeSubMenu(B)})),()=>{var t;const a=[null==(t=l.title)?void 0:t.call(l),J(b,{class:p.e("icon-arrow"),style:{transform:V.value?e.expandCloseIcon&&e.expandOpenIcon||e.collapseCloseIcon&&e.collapseOpenIcon&&v.props.collapse?"none":"rotateZ(180deg)":"none"}},{default:()=>Q(C.value)?J(s.appContext.components[C.value]):J(C.value)})],o=v.isMenuPopup?J(at,{ref:w,visible:V.value,effect:"light",pure:!0,offset:O.value,showArrow:!1,persistent:!0,popperClass:A.value,placement:_.value,teleported:M.value,fallbackPlacements:T.value,transition:I.value,gpuAcceleration:!1},{content:()=>{var e;return J("div",{class:[d.m(L.value),d.m("popup-container"),A.value],onMouseenter:e=>U(e,100),onMouseleave:()=>ee(!0),onFocus:e=>U(e,100)},[J("ul",{class:[d.b(),d.m("popup"),d.m(`popup-${_.value}`)],style:z.value},[null==(e=l.default)?void 0:e.call(l)])])},default:()=>J("div",{class:p.e("title"),onClick:N},a)}):J(Y,{},[J("div",{class:p.e("title"),ref:y,onClick:N},a),J(rt,{},{default:()=>{var e;return Z(J("ul",{role:"menu",class:[d.b(),d.m("inline")],style:z.value},[null==(e=l.default)?void 0:e.call(l)]),[[K,V.value]])}})]);return J("li",{class:[p.b(),p.is("active",j.value),p.is("opened",V.value),p.is("disabled",e.disabled)],role:"menuitem",ariaHaspopup:!0,ariaExpanded:V.value,onMouseenter:U,onMouseleave:()=>ee(),onFocus:U},[o])}}});const Dt=M({mode:{type:String,values:["horizontal","vertical"],default:"vertical"},defaultActive:{type:String,default:""},defaultOpeneds:{type:V(Array),default:()=>ee([])},uniqueOpened:Boolean,router:Boolean,menuTrigger:{type:String,values:["hover","click"],default:"hover"},collapse:Boolean,backgroundColor:String,textColor:String,activeTextColor:String,closeOnClickOutside:Boolean,collapseTransition:{type:Boolean,default:!0},ellipsis:{type:Boolean,default:!0},popperOffset:{type:Number,default:6},ellipsisIcon:{type:I,default:()=>te},popperEffect:{type:String,values:["dark","light"],default:"dark"},popperClass:String,showTimeout:{type:Number,default:300},hideTimeout:{type:Number,default:300}}),Wt=e=>Array.isArray(e)&&e.every((e=>Q(e)));var Gt=s({name:"ElMenu",props:Dt,emits:{close:(e,t)=>Q(e)&&Wt(t),open:(e,t)=>Q(e)&&Wt(t),select:(e,t,l,a)=>Q(e)&&Wt(t)&&ne(l)&&(void 0===a||a instanceof Promise)},setup(e,{emit:l,slots:o,expose:n}){const s=P(),u=s.appContext.config.globalProperties.$router,c=t(),d=r("menu"),p=r("sub-menu"),v=t(-1),m=t(e.defaultOpeneds&&!e.collapse?e.defaultOpeneds.slice(0):[]),h=t(e.defaultActive),f=t({}),g=t({}),x=i((()=>"horizontal"===e.mode||"vertical"===e.mode&&e.collapse)),y=(t,a)=>{m.value.includes(t)||(e.uniqueOpened&&(m.value=m.value.filter((e=>a.includes(e)))),m.value.push(t),l("open",t,a))},w=e=>{const t=m.value.indexOf(e);-1!==t&&m.value.splice(t,1)},_=(e,t)=>{w(e),l("close",e,t)},C=({index:e,indexPath:t})=>{m.value.includes(e)?_(e,t):y(e,t)},k=t=>{("horizontal"===e.mode||e.collapse)&&(m.value=[]);const{index:a,indexPath:o}=t;if(!se(a)&&!se(o))if(e.router&&u){const e=t.route||a,n=u.push(e).then((e=>(e||(h.value=a),e)));l("select",a,o,{index:a,indexPath:o,route:e},n)}else h.value=a,l("select",a,o,{index:a,indexPath:o})},M=()=>{var e,t;if(!c.value)return-1;const l=Array.from(null!=(t=null==(e=c.value)?void 0:e.childNodes)?t:[]).filter((e=>"#comment"!==e.nodeName&&("#text"!==e.nodeName||e.nodeValue))),a=getComputedStyle(c.value),o=Number.parseInt(a.paddingLeft,10),n=Number.parseInt(a.paddingRight,10),s=c.value.clientWidth-o-n;let r=0,i=0;return l.forEach(((e,t)=>{r+=(e=>{const t=getComputedStyle(e),l=Number.parseInt(t.marginLeft,10),a=Number.parseInt(t.marginRight,10);return e.offsetWidth+l+a||0})(e),r<=s-64&&(i=t+1)})),i===l.length?-1:i};let I=!0;const T=()=>{if(v.value===M())return;const e=()=>{v.value=-1,re((()=>{v.value=M()}))};I?e():((e,t=33.34)=>{let l;return()=>{l&&clearTimeout(l),l=setTimeout((()=>{e()}),t)}})(e)(),I=!1};let V;W((()=>e.defaultActive),(t=>{f.value[t]||(h.value=""),(t=>{const l=f.value,a=l[t]||h.value&&l[h.value]||l[e.defaultActive];h.value=a?a.index:t})(t)})),W((()=>e.collapse),(e=>{e&&(m.value=[])})),W(f.value,(()=>{const t=h.value&&f.value[h.value];if(!t||"horizontal"===e.mode||e.collapse)return;t.indexPath.forEach((e=>{const t=g.value[e];t&&y(e,t.indexPath)}))})),le((()=>{"horizontal"===e.mode&&e.ellipsis?V=ae(c,T).stop:null==V||V()}));const S=t(!1);{const t=e=>{g.value[e.index]=e},l=e=>{delete g.value[e.index]},a=e=>{f.value[e.index]=e},o=e=>{delete f.value[e.index]};$("rootMenu",D({props:e,openedMenus:m,items:f,subMenus:g,activeIndex:h,isMenuPopup:x,addMenuItem:a,removeMenuItem:o,addSubMenu:t,removeSubMenu:l,openMenu:y,closeMenu:_,handleMenuItemClick:k,handleSubMenuClick:C})),$(`subMenu:${s.uid}`,{addSubMenu:t,removeSubMenu:l,mouseInChild:S,level:0})}a((()=>{"horizontal"===e.mode&&new At(s.vnode.el,d.namespace.value)}));n({open:e=>{const{indexPath:t}=g.value[e];t.forEach((e=>y(e,t)))},close:w,handleResize:T});return()=>{var t,a;let n=null!=(a=null==(t=o.default)?void 0:t.call(o))?a:[];const s=[];if("horizontal"===e.mode&&c.value){const t=oe(n),l=-1===v.value?t:t.slice(0,v.value),a=-1===v.value?[]:t.slice(v.value);(null==a?void 0:a.length)&&e.ellipsis&&(n=l,s.push(J(qt,{index:"sub-menu-more",class:p.e("hide-arrow"),popperOffset:e.popperOffset},{title:()=>J(b,{class:p.e("icon-more")},{default:()=>J(e.ellipsisIcon)}),default:()=>a})))}const r=Nt(e,0),i=e.closeOnClickOutside?[[it,()=>{m.value.length&&(S.value||(m.value.forEach((e=>{return l("close",e,(t=e,g.value[t].indexPath));var t})),m.value=[]))}]]:[],u=Z(J("ul",{key:String(e.collapse),role:"menubar",ref:c,style:r.value,class:{[d.b()]:!0,[d.m(e.mode)]:!0,[d.m("collapse")]:e.collapse}},[...n,...s]),i);return e.collapseTransition&&"vertical"===e.mode?J(Et,(()=>u)):u}}});const Jt=M({index:{type:V([String,null]),default:null},route:{type:V([String,Object])},disabled:Boolean}),Qt="ElMenuItem";var Zt=C(s({name:Qt,components:{ElTooltip:at},props:Jt,emits:{click:e=>Q(e.index)&&Array.isArray(e.indexPath)},setup(e,{emit:t}){const l=P(),n=S("rootMenu"),s=r("menu"),u=r("menu-item");n||o(Qt,"can not inject root menu");const{parentMenu:c,indexPath:d}=Rt(l,ie(e,"index")),p=S(`subMenu:${c.value.uid}`);p||o(Qt,"can not inject sub menu");const v=i((()=>e.index===n.activeIndex)),m=D({index:e.index,indexPath:d,active:v});return a((()=>{p.addSubMenu(m),n.addMenuItem(m)})),G((()=>{p.removeSubMenu(m),n.removeMenuItem(m)})),{parentMenu:c,rootMenu:n,active:v,nsMenu:s,nsMenuItem:u,handleClick:()=>{e.disabled||(n.handleMenuItemClick({index:e.index,indexPath:d.value,route:e.route}),t("click",m))}}}}),[["render",function(e,t,l,a,o,n){const s=ue("el-tooltip");return u(),v("li",{class:h([e.nsMenuItem.b(),e.nsMenuItem.is("active",e.active),e.nsMenuItem.is("disabled",e.disabled)]),role:"menuitem",tabindex:"-1",onClick:t[0]||(t[0]=(...t)=>e.handleClick&&e.handleClick(...t))},["ElMenu"===e.parentMenu.type.name&&e.rootMenu.props.collapse&&e.$slots.title?(u(),c(s,{key:0,effect:e.rootMenu.props.popperEffect,placement:"right","fallback-placements":["left"],persistent:""},{content:d((()=>[g(e.$slots,"title")])),default:d((()=>[j("div",{class:h(e.nsMenu.be("tooltip","trigger"))},[g(e.$slots,"default")],2)])),_:3},8,["effect"])):(u(),v(Y,{key:1},[g(e.$slots,"default"),g(e.$slots,"title")],64))],2)}],["__file","menu-item.vue"]]);var Kt=C(s({name:"ElMenuItemGroup",props:{title:String},setup:()=>({ns:r("menu-item-group")})}),[["render",function(e,t,l,a,o,n){return u(),v("li",{class:h(e.ns.b())},[j("div",{class:h(e.ns.e("title"))},[e.$slots.title?g(e.$slots,"title",{key:1}):(u(),v(Y,{key:0},[ce(B(e.title),1)],64))],2),j("ul",null,[g(e.$slots,"default")])],2)}],["__file","menu-item-group.vue"]]);const Yt=k(Gt,{MenuItem:Zt,MenuItemGroup:Kt,SubMenu:qt}),Xt=z(Zt);z(Kt);const el=z(qt),tl=s({__name:"Backtop",setup(e){const{getPrefixCls:t,variables:l}=de(),a=t("backtop");return(e,t)=>(u(),c(p(kt),{class:h(`${p(a)}-backtop`),target:`.${p(l).namespace}-layout-content-scrollbar .${p(l).elNamespace}-scrollbar__wrap`},null,8,["class","target"]))}}),ll=["onClick"],al=me(s({__name:"ColorRadioPicker",props:{schema:{type:Array,default:()=>[]},modelValue:pe.string.def("")},emits:["update:modelValue","change"],setup(e,{emit:l}){const{getPrefixCls:a}=de(),o=a("color-radio-picker"),n=e,s=l,r=t(n.modelValue);return W((()=>n.modelValue),(e=>{e!==p(r)&&(r.value=e)})),W((()=>r.value),(e=>{s("update:modelValue",e),s("change",e)})),(t,l)=>{const a=ue("Icon");return u(),v("div",{class:h([p(o),"flex flex-wrap space-x-14px"])},[(u(!0),v(Y,null,ve(e.schema,((e,t)=>(u(),v("span",{key:`radio-${t}`,class:h(["w-20px h-20px cursor-pointer rounded-2px border-solid border-gray-300 border-2px text-center leading-20px mb-5px",{"is-active":r.value===e}]),style:m({background:e}),onClick:t=>r.value=e},[r.value===e?(u(),c(a,{key:0,color:"#fff",icon:"ep:check",size:16})):w("",!0)],14,ll)))),128))],2)}}}),[["__scopeId","data-v-2165faf4"]]),ol={class:"flex justify-between items-center"},nl={class:"text-14px"},sl={class:"flex justify-between items-center"},rl={class:"text-14px"},il={class:"flex justify-between items-center"},ul={class:"text-14px"},cl={class:"flex justify-between items-center"},dl={class:"text-14px"},pl={class:"flex justify-between items-center"},vl={class:"text-14px"},ml={class:"flex justify-between items-center"},hl={class:"text-14px"},fl={class:"flex justify-between items-center"},gl={class:"text-14px"},xl={class:"flex justify-between items-center"},bl={class:"text-14px"},yl={class:"flex justify-between items-center"},wl={class:"text-14px"},_l={class:"flex justify-between items-center"},Cl={class:"text-14px"},kl={class:"flex justify-between items-center"},Ml={class:"text-14px"},Il={class:"flex justify-between items-center"},Tl={class:"text-14px"},$l=s({__name:"InterfaceDisplay",setup(e){const{getPrefixCls:l}=de(),a=l("interface-display"),o=he(),{t:n}=fe(),s=t(o.getBreadcrumb),r=e=>{o.setBreadcrumb(e)},c=t(o.getBreadcrumbIcon),d=e=>{o.setBreadcrumbIcon(e)},m=t(o.getHamburger),f=e=>{o.setHamburger(e)},g=t(o.getScreenfull),b=e=>{o.setScreenfull(e)},y=t(o.getSize),w=e=>{o.setSize(e)},_=t(o.getLocale),C=e=>{o.setLocale(e)};t(o.getTagsView),t(o.getTagsViewIcon);const k=t(o.getLogo),M=e=>{o.setLogo(e)},I=t(o.getUniqueOpened),T=e=>{o.setUniqueOpened(e)},$=t(o.getFixedHeader),V=e=>{o.setFixedHeader(e)},P=t(o.getFooter),S=e=>{o.setFooter(e)},L=t(o.getGreyMode),z=e=>{o.setGreyMode(e)},O=t(o.getFixedMenu),A=e=>{o.setFixedMenu(e)},E=i((()=>o.getLayout));return W((()=>E.value),(e=>{"top"===e&&o.setCollapse(!1)})),(e,t)=>(u(),v("div",{class:h(p(a))},[j("div",ol,[j("span",nl,B(p(n)("setting.breadcrumb")),1),x(p(lt),{modelValue:s.value,"onUpdate:modelValue":t[0]||(t[0]=e=>s.value=e),onChange:r},null,8,["modelValue"])]),j("div",sl,[j("span",rl,B(p(n)("setting.breadcrumbIcon")),1),x(p(lt),{modelValue:c.value,"onUpdate:modelValue":t[1]||(t[1]=e=>c.value=e),onChange:d},null,8,["modelValue"])]),j("div",il,[j("span",ul,B(p(n)("setting.hamburgerIcon")),1),x(p(lt),{modelValue:m.value,"onUpdate:modelValue":t[2]||(t[2]=e=>m.value=e),onChange:f},null,8,["modelValue"])]),j("div",cl,[j("span",dl,B(p(n)("setting.screenfullIcon")),1),x(p(lt),{modelValue:g.value,"onUpdate:modelValue":t[3]||(t[3]=e=>g.value=e),onChange:b},null,8,["modelValue"])]),j("div",pl,[j("span",vl,B(p(n)("setting.sizeIcon")),1),x(p(lt),{modelValue:y.value,"onUpdate:modelValue":t[4]||(t[4]=e=>y.value=e),onChange:w},null,8,["modelValue"])]),j("div",ml,[j("span",hl,B(p(n)("setting.localeIcon")),1),x(p(lt),{modelValue:_.value,"onUpdate:modelValue":t[5]||(t[5]=e=>_.value=e),onChange:C},null,8,["modelValue"])]),j("div",fl,[j("span",gl,B(p(n)("setting.logo")),1),x(p(lt),{modelValue:k.value,"onUpdate:modelValue":t[6]||(t[6]=e=>k.value=e),onChange:M},null,8,["modelValue"])]),j("div",xl,[j("span",bl,B(p(n)("setting.uniqueOpened")),1),x(p(lt),{modelValue:I.value,"onUpdate:modelValue":t[7]||(t[7]=e=>I.value=e),onChange:T},null,8,["modelValue"])]),j("div",yl,[j("span",wl,B(p(n)("setting.fixedHeader")),1),x(p(lt),{modelValue:$.value,"onUpdate:modelValue":t[8]||(t[8]=e=>$.value=e),onChange:V},null,8,["modelValue"])]),j("div",_l,[j("span",Cl,B(p(n)("setting.footer")),1),x(p(lt),{modelValue:P.value,"onUpdate:modelValue":t[9]||(t[9]=e=>P.value=e),onChange:S},null,8,["modelValue"])]),j("div",kl,[j("span",Ml,B(p(n)("setting.greyMode")),1),x(p(lt),{modelValue:L.value,"onUpdate:modelValue":t[10]||(t[10]=e=>L.value=e),onChange:z},null,8,["modelValue"])]),j("div",Il,[j("span",Tl,B(p(n)("setting.fixedMenu")),1),x(p(lt),{modelValue:O.value,"onUpdate:modelValue":t[11]||(t[11]=e=>O.value=e),onChange:A},null,8,["modelValue"])])],2))}}),Vl=[(e=>(ge("data-v-82955980"),e=e(),xe(),e))((()=>j("div",{class:"absolute h-full w-[33%] top-0 left-[10%] bg-gray-200"},null,-1)))],Pl=me(s({__name:"LayoutRadioPicker",setup(e){const{getPrefixCls:t}=de(),l=t("layout-radio-picker"),a=he(),o=i((()=>a.getLayout));return(e,t)=>(u(),v("div",{class:h([p(l),"flex flex-wrap space-x-14px"])},[j("div",{class:h([`${p(l)}__classic`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":"classic"===o.value}]),onClick:t[0]||(t[0]=e=>p(a).setLayout("classic"))},null,2),j("div",{class:h([`${p(l)}__top-left`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":"topLeft"===o.value}]),onClick:t[1]||(t[1]=e=>p(a).setLayout("topLeft"))},null,2),j("div",{class:h([`${p(l)}__top`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":"top"===o.value}]),onClick:t[2]||(t[2]=e=>p(a).setLayout("top"))},null,2),j("div",{class:h([`${p(l)}__cut-menu`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":"cutMenu"===o.value}]),onClick:t[3]||(t[3]=e=>p(a).setLayout("top"))},Vl,2)],2))}}),[["__scopeId","data-v-82955980"]]),Sl={class:"text-16px font-700"},jl={class:"text-center"},Ll={class:"mt-5px"},Bl=me(s({__name:"Setting",setup(e){const{clear:l}=$e("localStorage"),{getPrefixCls:a}=de(),o=a("setting"),n=he(),{t:s}=fe(),r=i((()=>n.getLayout)),c=t(!1),m=t(n.getTheme.elColorPrimary),f=e=>{be("--el-color-primary",e),n.setTheme({elColorPrimary:e});const t=ye("--left-menu-bg-color",document.documentElement);_(we(p(t)))},g=t(n.getTheme.topHeaderBgColor||""),b=e=>{const t=_e(e),l=t?"#fff":"inherit",a=t?Ce(e,6):"#f6f6f6",o=t?e:"#eee";be("--top-header-bg-color",e),be("--top-header-text-color",l),be("--top-header-hover-color",a),n.setTheme({topHeaderBgColor:e,topHeaderTextColor:l,topHeaderHoverColor:a,topToolBorderColor:o}),"top"===p(r)&&_(e)},y=t(n.getTheme.leftMenuBgColor||""),_=e=>{const t=ye("--el-color-primary",document.documentElement),l=_e(e),a={leftMenuBorderColor:l?"inherit":"#eee",leftMenuBgColor:e,leftMenuBgLightColor:l?Ce(e,6):e,leftMenuBgActiveColor:l?"var(--el-color-primary)":ke(p(t),.1),leftMenuCollapseBgActiveColor:l?"var(--el-color-primary)":ke(p(t),.1),leftMenuTextColor:l?"#bfcbd9":"#333",leftMenuTextActiveColor:l?"#fff":"var(--el-color-primary)",logoTitleTextColor:l?"#fff":"inherit",logoBorderColor:l?e:"#eee"};n.setTheme(a),n.setCssVarTheme()};_(p(y)),W((()=>r.value),(e=>{"top"!==e||n.getIsDark?_(p(y)):(g.value="#fff",b("#fff"))}));const C=async()=>{const{copy:e,copied:t,isSupported:l}=Me({source:`\n      // 面包屑\n      breadcrumb: ${n.getBreadcrumb},\n      // 面包屑图标\n      breadcrumbIcon: ${n.getBreadcrumbIcon},\n      // 折叠图标\n      hamburger: ${n.getHamburger},\n      // 全屏图标\n      screenfull: ${n.getScreenfull},\n      // 尺寸图标\n      size: ${n.getSize},\n      // 多语言图标\n      locale: ${n.getLocale},\n      // 标签页\n      tagsView: ${n.getTagsView},\n      // 标签页图标\n      getTagsViewIcon: ${n.getTagsViewIcon},\n      // logo\n      logo: ${n.getLogo},\n      // 菜单手风琴\n      uniqueOpened: ${n.getUniqueOpened},\n      // 固定header\n      fixedHeader: ${n.getFixedHeader},\n      // 页脚\n      footer: ${n.getFooter},\n      // 灰色模式\n      greyMode: ${n.getGreyMode},\n      // layout布局\n      layout: '${n.getLayout}',\n      // 暗黑模式\n      isDark: ${n.getIsDark},\n      // 组件尺寸\n      currentSize: '${n.getCurrentSize}',\n      // 主题相关\n      theme: {\n        // 主题色\n        elColorPrimary: '${n.getTheme.elColorPrimary}',\n        // 左侧菜单边框颜色\n        leftMenuBorderColor: '${n.getTheme.leftMenuBorderColor}',\n        // 左侧菜单背景颜色\n        leftMenuBgColor: '${n.getTheme.leftMenuBgColor}',\n        // 左侧菜单浅色背景颜色\n        leftMenuBgLightColor: '${n.getTheme.leftMenuBgLightColor}',\n        // 左侧菜单选中背景颜色\n        leftMenuBgActiveColor: '${n.getTheme.leftMenuBgActiveColor}',\n        // 左侧菜单收起选中背景颜色\n        leftMenuCollapseBgActiveColor: '${n.getTheme.leftMenuCollapseBgActiveColor}',\n        // 左侧菜单字体颜色\n        leftMenuTextColor: '${n.getTheme.leftMenuTextColor}',\n        // 左侧菜单选中字体颜色\n        leftMenuTextActiveColor: '${n.getTheme.leftMenuTextActiveColor}',\n        // logo字体颜色\n        logoTitleTextColor: '${n.getTheme.logoTitleTextColor}',\n        // logo边框颜色\n        logoBorderColor: '${n.getTheme.logoBorderColor}',\n        // 头部背景颜色\n        topHeaderBgColor: '${n.getTheme.topHeaderBgColor}',\n        // 头部字体颜色\n        topHeaderTextColor: '${n.getTheme.topHeaderTextColor}',\n        // 头部悬停颜色\n        topHeaderHoverColor: '${n.getTheme.topHeaderHoverColor}',\n        // 头部边框颜色\n        topToolBorderColor: '${n.getTheme.topToolBorderColor}'\n      }\n    `,legacy:!0});l?(await e(),p(t)&&Ie.success(s("setting.copySuccess"))):Ie.error(s("setting.copyFailed"))},k=()=>{l(),window.location.reload()},M=()=>{const e=Te("--el-bg-color");_(e),b(e)};return(e,t)=>{const l=ue("Icon"),a=ue("BaseButton");return u(),v(Y,null,[j("div",{class:h([p(o),"fixed top-[90%] right-0 w-40px h-40px flex items-center justify-center bg-[var(--el-color-primary)] cursor-pointer z-10"]),onClick:t[0]||(t[0]=e=>c.value=!0)},[x(l,{icon:"ant-design:setting-outlined",color:"#fff"})],2),x(p(Ye),{modelValue:c.value,"onUpdate:modelValue":t[4]||(t[4]=e=>c.value=e),direction:"rtl",size:"350px","z-index":4e3},{header:d((()=>[j("span",Sl,B(p(s)("setting.projectSetting")),1)])),default:d((()=>[j("div",jl,[x(p(Xe),null,{default:d((()=>[ce(B(p(s)("setting.theme")),1)])),_:1}),x(p(et),{onChange:M}),x(p(Xe),null,{default:d((()=>[ce(B(p(s)("setting.layout")),1)])),_:1}),x(Pl),x(p(Xe),null,{default:d((()=>[ce(B(p(s)("setting.systemTheme")),1)])),_:1}),x(al,{modelValue:m.value,"onUpdate:modelValue":t[1]||(t[1]=e=>m.value=e),schema:["#409eff","#009688","#536dfe","#ff5c93","#ee4f12","#0096c7","#9c27b0","#ff9800"],onChange:f},null,8,["modelValue"]),x(p(Xe),null,{default:d((()=>[ce(B(p(s)("setting.headerTheme")),1)])),_:1}),x(al,{modelValue:g.value,"onUpdate:modelValue":t[2]||(t[2]=e=>g.value=e),schema:["#fff","#151515","#5172dc","#e74c3c","#24292e","#394664","#009688","#383f45"],onChange:b},null,8,["modelValue"]),"top"!==r.value?(u(),v(Y,{key:0},[x(p(Xe),null,{default:d((()=>[ce(B(p(s)("setting.menuTheme")),1)])),_:1}),x(al,{modelValue:y.value,"onUpdate:modelValue":t[3]||(t[3]=e=>y.value=e),schema:["#fff","#001529","#212121","#273352","#191b24","#383f45","#001628","#344058"],onChange:_},null,8,["modelValue"])],64)):w("",!0)]),x(p(Xe),null,{default:d((()=>[ce(B(p(s)("setting.interfaceDisplay")),1)])),_:1}),x($l),x(p(Xe)),j("div",null,[x(a,{type:"primary",class:"w-full",onClick:C},{default:d((()=>[ce(B(p(s)("setting.copy")),1)])),_:1})]),j("div",Ll,[x(a,{type:"danger",class:"w-full",onClick:k},{default:d((()=>[ce(B(p(s)("setting.clearAndReset")),1)])),_:1})])])),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-fafd9bf5"]]),zl=(e,t)=>(ot(e,(e=>e.path===t))||[]).map((e=>e.path)),{renderMenuTitle:Ol}={renderMenuTitle:e=>{const{t:t}=fe(),{title:l="Please set title",icon:a}=e;return a?x(Y,null,[x(Ve,{icon:e.icon},null),x("span",{class:"v-menu__title"},[t(l)])]):x("span",{class:"v-menu__title"},[t(l)])}},Al=e=>{const l=(a,o="/")=>a.map((a=>{const n=a.meta??{};if(!n.hidden){const{oneShowingChild:s,onlyOneChild:r}=((e=[],l)=>{const a=t(),o=e.filter((e=>!(e.meta??{}).hidden&&(a.value=e,!0)));return 1===o.length?{oneShowingChild:!0,onlyOneChild:p(a)}:o.length?{oneShowingChild:!1,onlyOneChild:p(a)}:(a.value={...l,path:"",noShowingChildren:!0},{oneShowingChild:!0,onlyOneChild:p(a)})})(a.children,a),i=Pe(a.path)?a.path:Se(o,a.path);if(!s||(null==r?void 0:r.children)&&!(null==r?void 0:r.noShowingChildren)||(null==n?void 0:n.alwaysShow)){const{getPrefixCls:t}=de(),o=t("menu-popper");return x(el,{index:i,popperClass:"vertical"===e?`${o}--vertical`:`${o}--horizontal`},{title:()=>Ol(n),default:()=>l(a.children,i)})}return x(Xt,{index:r?Se(i,r.path):i},{default:()=>Ol(r?null==r?void 0:r.meta:n)})}}));return{renderMenuItem:l}};const{getPrefixCls:El}=de(),Rl=El("menu"),Hl=me(s({name:"Menu",props:{menuSelect:{type:Function,default:void 0}},setup(e){const t=he(),l=i((()=>t.getLayout)),{push:a,currentRoute:o}=je(),n=Le(),s=i((()=>["classic","topLeft","cutMenu"].includes(p(l))?"vertical":"horizontal")),r=i((()=>"cutMenu"===p(l)?n.getMenuTabRouters:n.getRouters)),u=i((()=>t.getCollapse)),c=i((()=>t.getUniqueOpened)),d=i((()=>{const{meta:e,path:t}=p(o);return e.activeMenu?e.activeMenu:t})),v=t=>{e.menuSelect&&e.menuSelect(t),Pe(t)?window.open(t):a(t)},m=()=>{if("top"===p(l))return h();{let t;return x(Be,null,"function"==typeof(e=t=h())||"[object Object]"===Object.prototype.toString.call(e)&&!ze(e)?t:{default:()=>[t]})}var e},h=()=>x(Yt,{defaultActive:p(d),mode:p(s),collapse:"top"!==p(l)&&"cutMenu"!==p(l)&&p(u),uniqueOpened:"top"!==p(l)&&p(c),textColor:"var(--left-menu-text-color)",activeTextColor:"var(--left-menu-text-active-color)",onSelect:v},{default:()=>{const{renderMenuItem:e}=Al(p(s));return e(p(r))}});return()=>x("div",{id:Rl,class:[`${Rl} ${Rl}__${p(s)}`,"h-[100%] overflow-hidden flex-col bg-[var(--left-menu-bg-color)]",{"w-[var(--left-menu-min-width)]":p(u)&&"cutMenu"!==p(l),"w-[var(--left-menu-max-width)]":!p(u)&&"cutMenu"!==p(l)}]},[m()])}}),[["__scopeId","data-v-c1b8abb2"]]),Nl=D({}),Ul=(e,t)=>{const l=[];for(const a of e){let e=null;const o=a.meta??{};if(!o.hidden||o.canTo){const o=zl(t,a.path),n=Pe(a.path)?a.path:o.join("/");e=Oe(a),e.path=n,a.children&&e&&(e.children=Ul(a.children,t)),e&&l.push(e),o.length&&Reflect.has(Nl,o[0])&&Nl[o[0]].push(n)}}return l},{getPrefixCls:Fl,variables:ql}=de(),Dl=Fl("tab-menu"),Wl=me(s({name:"TabMenu",directives:{ClickOutside:it},setup(){const{push:e,currentRoute:l}=je(),{t:o}=fe(),n=he(),s=i((()=>n.getCollapse)),r=i((()=>n.getFixedMenu)),u=Le(),c=i((()=>u.getRouters)),d=i((()=>p(c).filter((e=>{var t;return!(null==(t=null==e?void 0:e.meta)?void 0:t.hidden)})))),v=()=>{n.setCollapse(!p(s))};a((()=>{var e;if(p(r)){const t=`/${p(l).path.split("/")[1]}`,a=null==(e=p(d).find((e=>{var l,a,o;return((null==(l=e.meta)?void 0:l.alwaysShow)||(null==(a=null==e?void 0:e.children)?void 0:a.length)&&(null==(o=null==e?void 0:e.children)?void 0:o.length)>1)&&e.path===t})))?void 0:e.children;f.value=t,a&&u.setMenuTabRouters(Oe(a).map((e=>(e.path=Se(p(f),e.path),e))))}})),W((()=>c.value),(e=>{(e=>{for(const t of e){const e=t.meta??{};(null==e?void 0:e.hidden)||(Nl[t.path]=[])}})(e),Ul(e,e)}),{immediate:!0,deep:!0});const m=t(!0);W((()=>s.value),(e=>{e?m.value=!e:setTimeout((()=>{m.value=!e}),200)}));const h=t(!!p(r)),f=t(""),g=e=>{const{path:t}=p(l);return!!Nl[e].includes(t)},b=()=>{p(r)||(h.value=!1)};return()=>Z(x("div",{id:`${ql.namespace}-menu`,class:[Dl,"relative bg-[var(--left-menu-bg-color)] top-1px layout-border__right",{"w-[var(--tab-menu-max-width)]":!p(s),"w-[var(--tab-menu-min-width)]":p(s)}]},[x(Be,{class:"!h-[calc(100%-var(--tab-menu-collapse-height)-1px)]"},{default:()=>[x("div",null,{default:()=>p(d).map((t=>{var l,a,n,s,r,i;const c=(null==(l=t.meta)?void 0:l.alwaysShow)||(null==(a=null==t?void 0:t.children)?void 0:a.length)&&(null==(n=null==t?void 0:t.children)?void 0:n.length)>1?t:{...(null==t?void 0:t.children)&&(null==t?void 0:t.children[0]),path:Se(t.path,null==(s=(null==t?void 0:t.children)&&(null==t?void 0:t.children[0]))?void 0:s.path)};return x("div",{class:[`${Dl}__item`,"text-center text-12px relative py-12px cursor-pointer",{"is-active":g(t.path)}],onClick:()=>{(t=>{if(Pe(t.path))return void window.open(t.path);const l=t.children?t.path:t.path.split("/")[0],a=p(f);f.value=t.children?t.path:t.path.split("/")[0],t.children?(l!==a&&p(h)||(h.value=!p(h)),p(h)&&u.setMenuTabRouters(Oe(t.children).map((e=>(e.path=Se(p(f),e.path),e))))):(e(t.path),u.setMenuTabRouters([]),h.value=!1)})(c)}},[x("div",null,[x(Ve,{icon:null==(r=null==c?void 0:c.meta)?void 0:r.icon},null)]),p(m)?x("p",{class:"break-words mt-5px px-2px"},[o((null==(i=c.meta)?void 0:i.title)||"")]):void 0])}))})]}),x("div",{class:[`${Dl}--collapse`,"text-center h-[var(--tab-menu-collapse-height)] leading-[var(--tab-menu-collapse-height)] cursor-pointer"],onClick:v},[x(Ve,{icon:p(s)?"ep:d-arrow-right":"ep:d-arrow-left"},null)]),x(Hl,{class:["!absolute top-0 z-1000",{"!left-[var(--tab-menu-min-width)]":p(s),"!left-[var(--tab-menu-max-width)]":!p(s),"!w-[var(--left-menu-max-width)]":p(h)||p(r),"!w-0":!p(h)&&!p(r)}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null)]),[[Ae("click-outside"),b]])}}),[["__scopeId","data-v-f15eb29d"]]),Gl=(e,t="")=>{let l=[];return e.forEach((e=>{const a=e.meta??{},o=Se(t,e.path);if((null==a?void 0:a.affix)&&l.push({...e,path:o,fullPath:o}),e.children){const t=Gl(e.children,o);t.length>=1&&(l=[...l,...t])}})),l},Jl=s({__name:"ContextMenu",props:{schema:{type:Array,default:()=>[]},trigger:{type:String,default:"contextmenu"},tagItem:{type:Object,default:()=>({})},id:{type:String,default:""}},emits:["visibleChange"],setup(e,{expose:l,emit:a}){const{getPrefixCls:o}=de(),n=o("context-menu"),{t:s}=fe(),r=a,i=e,m=e=>{e.command&&e.command(e)},f=e=>{r("visibleChange",e,i.tagItem,i.id)},b=t();return l({elDropdownMenuRef:b,tagItem:i.tagItem,id:i.id}),(t,l)=>{const a=ue("Icon");return u(),c(p(dt),{ref_key:"elDropdownMenuRef",ref:b,class:h(p(n)),trigger:e.trigger,placement:"bottom-start",onCommand:m,onVisibleChange:f,"popper-class":"v-context-menu-popper"},{dropdown:d((()=>[x(p(ut),null,{default:d((()=>[(u(!0),v(Y,null,ve(e.schema,((e,t)=>(u(),c(p(ct),{key:`dropdown${t}`,divided:e.divided,disabled:e.disabled,command:e},{default:d((()=>[x(a,{icon:e.icon},null,8,["icon"]),ce(" "+B(p(s)(e.label)),1)])),_:2},1032,["divided","disabled","command"])))),128))])),_:1})])),default:d((()=>[g(t.$slots,"default")])),_:3},8,["class","trigger"])}}});function Ql({el:e,position:l="scrollLeft",to:a,duration:o=500,callback:n}){const s=t(!1),r=e[l],i=a-r,u=20;let c=0;function d(){if(!p(s))return;c+=u;const t=(a=c,v=r,m=i,(a/=o/2)<1?m/2*a*a+v:-m/2*(--a*(a-2)-1)+v);var a,v,m;((e,t,l)=>{e[t]=l})(e,l,t),c<o&&p(s)?requestAnimationFrame(d):n&&n()}return{start:function(){s.value=!0,d()},stop:function(){s.value=!1}}}const Zl=["id"],Kl={class:"overflow-hidden flex-1"},Yl={class:"flex h-full"},Xl=["onClick"],ea=me(s({__name:"TagsView",setup(e){const{getPrefixCls:l}=de(),o=l("tags-view"),{t:n}=fe(),{currentRoute:s,push:r}=je(),{closeAll:m,closeLeft:g,closeRight:b,closeOther:y,closeCurrent:_,refreshPage:C}=(()=>{const e=Ee(),{replace:t,currentRoute:l}=je(),a=i((()=>e.getSelectedTag));return{closeAll:t=>{e.delAllViews(),null==t||t()},closeLeft:t=>{e.delLeftViews(p(a)),null==t||t()},closeRight:t=>{e.delRightViews(p(a)),null==t||t()},closeOther:t=>{e.delOthersViews(p(a)),null==t||t()},closeCurrent:(t,a)=>{var o;(null==(o=null==t?void 0:t.meta)?void 0:o.affix)||(e.delView(t||p(l)),null==a||a())},refreshPage:async(a,o)=>{e.delCachedView();const{path:n,query:s}=a||p(l);await re(),t({path:"/redirect"+n,query:s}),null==o||o()},setTitle:(t,l)=>{e.setTitle(t,l)}}})(),k=Le(),M=i((()=>k.getRouters)),I=Re(),T=i((()=>I.getVisitedViews)),$=t([]),V=i((()=>I.getSelectedTag)),P=I.setSelectedTag,S=he(),L=i((()=>S.getTagsViewIcon)),z=i((()=>S.getIsDark)),O=()=>{const{name:e}=p(s);e&&(P(p(s)),I.addView(p(s)))},A=e=>{_(e,(()=>{G(e)&&E()}))},E=()=>{const e=I.getVisitedViews.slice(-1)[0];if(e)r(e);else{if(p(s).path===k.getAddRouters[0].path||p(s).path===k.getAddRouters[0].redirect)return void O();r(k.getAddRouters[0].path)}},R=()=>{m((()=>{E()}))},H=()=>{y()},N=async e=>{C(e)},U=()=>{g()},F=()=>{b()},q=He(),D=e=>{var t;const l=null==(t=p(Z))?void 0:t.wrapRef;let a=null,n=null;const s=p(q);if(s.length>0&&(a=s[0],n=s[s.length-1]),(null==a?void 0:a.to).fullPath===e.fullPath){const{start:e}=Ql({el:l,position:"scrollLeft",to:0,duration:500});e()}else if((null==n?void 0:n.to).fullPath===e.fullPath){const{start:e}=Ql({el:l,position:"scrollLeft",to:l.scrollWidth-l.offsetWidth,duration:500});e()}else{const t=s.findIndex((t=>(null==t?void 0:t.to).fullPath===e.fullPath)),a=document.getElementsByClassName(`${o}__item`),n=a[t-1],r=a[t+1],i=r.offsetLeft+r.offsetWidth+4,u=n.offsetLeft-4;if(i>p(K)+l.offsetWidth){const{start:e}=Ql({el:l,position:"scrollLeft",to:i-l.offsetWidth,duration:500});e()}else if(u<p(K)){const{start:e}=Ql({el:l,position:"scrollLeft",to:u,duration:500});e()}}},G=e=>e.path===p(s).path,J=He(),Q=(e,t)=>{if(e)for(const l of p(J)){const e=l.elDropdownMenuRef;t.fullPath!==l.tagItem.fullPath&&(null==e||e.handleClose())}},Z=t(),K=t(0),X=({scrollLeft:e})=>{K.value=e},ee=e=>{var t;const l=null==(t=p(Z))?void 0:t.wrapRef,{start:a}=Ql({el:l,position:"scrollLeft",to:p(K)+e,duration:500});a()};return a((()=>{(()=>{$.value=Gl(p(M));for(const e of p($))e.name&&I.addVisitedView(Oe(e))})(),O()})),W((()=>s.value),(()=>{O(),(async()=>{await re();for(const e of p(T))if(e.fullPath===p(s).path){D(e),e.fullPath!==p(s).fullPath&&I.updateVisitedView(p(s));break}})()})),(e,t)=>{var l,a,s,r,i,m;const g=ue("Icon"),b=ue("router-link");return u(),v("div",{id:p(o),class:h([p(o),"flex w-full relative bg-[#fff] dark:bg-[var(--el-bg-color)]"])},[j("span",{class:h([`${p(o)}__tool ${p(o)}__tool--first`,"w-[var(--tags-view-height)] h-[var(--tags-view-height)] flex items-center justify-center cursor-pointer"]),onClick:t[0]||(t[0]=e=>ee(-200))},[x(g,{icon:"ep:d-arrow-left",color:"var(--el-text-color-placeholder)","hover-color":z.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),j("div",Kl,[x(p(Be),{ref_key:"scrollbarRef",ref:Z,class:"h-full",onScroll:X},{default:d((()=>[j("div",Yl,[(u(!0),v(Y,null,ve(T.value,(e=>{var t,l,a,s,r,i,v,m,y;return u(),c(p(Jl),{ref_for:!0,ref:p(J).set,schema:[{icon:"ant-design:sync-outlined",label:p(n)("common.reload"),disabled:(null==(t=V.value)?void 0:t.fullPath)!==e.fullPath,command:()=>{N(e)}},{icon:"ant-design:close-outlined",label:p(n)("common.closeTab"),disabled:!!(null==(l=T.value)?void 0:l.length)&&(null==(a=V.value)?void 0:a.meta.affix),command:()=>{A(e)}},{divided:!0,icon:"ant-design:vertical-right-outlined",label:p(n)("common.closeTheLeftTab"),disabled:!!(null==(s=T.value)?void 0:s.length)&&(e.fullPath===T.value[0].fullPath||(null==(r=V.value)?void 0:r.fullPath)!==e.fullPath),command:()=>{U()}},{icon:"ant-design:vertical-left-outlined",label:p(n)("common.closeTheRightTab"),disabled:!!(null==(i=T.value)?void 0:i.length)&&(e.fullPath===T.value[T.value.length-1].fullPath||(null==(v=V.value)?void 0:v.fullPath)!==e.fullPath),command:()=>{F()}},{divided:!0,icon:"ant-design:tag-outlined",label:p(n)("common.closeOther"),disabled:(null==(m=V.value)?void 0:m.fullPath)!==e.fullPath,command:()=>{H()}},{icon:"ant-design:line-outlined",label:p(n)("common.closeAll"),command:()=>{R()}}],key:e.fullPath,"tag-item":e,class:h([`${p(o)}__item`,(null==(y=null==e?void 0:e.meta)?void 0:y.affix)?`${p(o)}__item--affix`:"",{"is-active":G(e)}]),onVisibleChange:Q},{default:d((()=>[j("div",null,[x(b,{ref_for:!0,ref:p(q).set,to:{...e},custom:""},{default:d((({navigate:t})=>{var l,a,s,r,i;return[j("div",{onClick:t,class:"h-full flex justify-center items-center whitespace-nowrap pl-15px"},[(null==e?void 0:e.matched)&&(null==e?void 0:e.matched[1])&&(null==(a=null==(l=null==e?void 0:e.matched[1])?void 0:l.meta)?void 0:a.icon)&&L.value?(u(),c(g,{key:0,icon:null==(r=null==(s=null==e?void 0:e.matched[1])?void 0:s.meta)?void 0:r.icon,size:12,class:"mr-5px"},null,8,["icon"])):w("",!0),ce(" "+B(p(n)(null==(i=null==e?void 0:e.meta)?void 0:i.title))+" ",1),x(g,{class:h(`${p(o)}__item--close`),color:"#333",icon:"ant-design:close-outlined",size:12,onClick:f((t=>A(e)),["prevent","stop"])},null,8,["class","onClick"])],8,Xl)]})),_:2},1032,["to"])])])),_:2},1032,["schema","tag-item","class"])})),128))])])),_:1},512)]),j("span",{class:h([`${p(o)}__tool`,"w-[var(--tags-view-height)] h-[var(--tags-view-height)] flex items-center justify-center cursor-pointer"]),onClick:t[1]||(t[1]=e=>ee(200))},[x(g,{icon:"ep:d-arrow-right",color:"var(--el-text-color-placeholder)","hover-color":z.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),j("span",{class:h([`${p(o)}__tool`,"w-[var(--tags-view-height)] h-[var(--tags-view-height)] flex items-center justify-center cursor-pointer"]),onClick:t[2]||(t[2]=e=>N(V.value))},[x(g,{icon:"ant-design:reload-outlined",color:"var(--el-text-color-placeholder)","hover-color":z.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),x(p(Jl),{trigger:"click",schema:[{icon:"ant-design:sync-outlined",label:p(n)("common.reload"),command:()=>{N(V.value)}},{icon:"ant-design:close-outlined",label:p(n)("common.closeTab"),disabled:!!(null==(l=T.value)?void 0:l.length)&&(null==(a=V.value)?void 0:a.meta.affix),command:()=>{A(V.value)}},{divided:!0,icon:"ant-design:vertical-right-outlined",label:p(n)("common.closeTheLeftTab"),disabled:!!(null==(s=T.value)?void 0:s.length)&&(null==(r=V.value)?void 0:r.fullPath)===T.value[0].fullPath,command:()=>{U()}},{icon:"ant-design:vertical-left-outlined",label:p(n)("common.closeTheRightTab"),disabled:!!(null==(i=T.value)?void 0:i.length)&&(null==(m=V.value)?void 0:m.fullPath)===T.value[T.value.length-1].fullPath,command:()=>{F()}},{divided:!0,icon:"ant-design:tag-outlined",label:p(n)("common.closeOther"),command:()=>{H()}},{icon:"ant-design:line-outlined",label:p(n)("common.closeAll"),command:()=>{R()}}]},{default:d((()=>[j("span",{class:h([`${p(o)}__tool`,"w-[var(--tags-view-height)] h-[var(--tags-view-height)] flex items-center justify-center cursor-pointer block"])},[x(g,{icon:"ant-design:setting-outlined",color:"var(--el-text-color-placeholder)","hover-color":z.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2)])),_:1},8,["schema"])],10,Zl)}}}),[["__scopeId","data-v-7bf23422"]]),ta=j("img",{src:pt,class:"w-[calc(var(--logo-height)-10px)] h-[calc(var(--logo-height)-10px)]"},null,-1),la=s({__name:"Logo",setup(e){const{getPrefixCls:l}=de(),o=l("logo"),n=he(),s=t(!0),r=i((()=>n.getTitle)),c=i((()=>n.getLayout)),m=i((()=>n.getCollapse));return a((()=>{p(m)&&(s.value=!1)})),W((()=>m.value),(e=>{"topLeft"!==p(c)&&"cutMenu"!==p(c)?s.value=!e:s.value=!0})),W((()=>c.value),(e=>{"top"===e||"cutMenu"===e?s.value=!0:p(m)?s.value=!1:s.value=!0})),(e,t)=>{const l=ue("router-link");return u(),v("div",null,[x(l,{class:h([p(o),"classic"!==c.value?`${p(o)}__Top`:"","flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative decoration-none overflow-hidden"]),to:"/"},{default:d((()=>[ta,s.value?(u(),v("div",{key:0,class:h(["ml-10px text-16px font-700",{"text-[var(--logo-title-text-color)]":"classic"===c.value,"text-[var(--top-header-text-color)]":"topLeft"===c.value||"top"===c.value||"cutMenu"===c.value}])},B(r.value),3)):w("",!0)])),_:1},8,["class"])])}}}),aa=s({__name:"Footer",setup(e){const{getPrefixCls:t}=de(),l=t("footer"),a=he(),o=i((()=>a.getTitle));return(e,t)=>(u(),v("div",{class:h([p(l),"text-center text-[var(--el-text-color-placeholder)] bg-[var(--app-content-bg-color)] h-[var(--app-footer-height)] leading-[var(--app-footer-height)] dark:bg-[var(--el-bg-color)]"])}," Copyright ©2024-present "+B(o.value),3))}}),oa=s({__name:"AppView",setup(e){const t=he(),l=i((()=>t.getLayout)),a=i((()=>t.getFixedHeader)),o=i((()=>t.getFooter)),n=Re(),s=i((()=>n.getCachedViews)),r=i((()=>t.getTagsView));return(e,t)=>{const n=ue("router-view");return u(),v(Y,null,[j("section",{class:h(["p-[var(--app-content-padding)] w-[calc(100%-var(--app-content-padding)-var(--app-content-padding))] bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]",{"!min-h-[calc(100%-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height))]":a.value&&("classic"===l.value||"topLeft"===l.value||"top"===l.value)&&o.value||!r.value&&"top"===l.value&&o.value,"!min-h-[calc(100%-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height)-var(--tags-view-height))]":r.value&&"top"===l.value&&o.value,"!min-h-[calc(100%-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-var(--top-tool-height)-var(--app-footer-height))]":!a.value&&"classic"===l.value&&o.value,"!min-h-[calc(100%-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height))]":!a.value&&"topLeft"===l.value&&o.value,"!min-h-[calc(100%-var(--app-footer-height)-var(--app-content-padding)-var(--app-content-padding))]":a.value&&"cutMenu"===l.value&&o.value,"!min-h-[calc(100%-var(--app-footer-height)-var(--app-content-padding)-var(--app-content-padding)-var(--tags-view-height))]":!a.value&&"cutMenu"===l.value&&o.value}])},[x(n,null,{default:d((({Component:e,route:t})=>[(u(),c(Ne,{include:s.value},[(u(),c(L(e),{key:t.fullPath}))],1032,["include"]))])),_:1})],2),o.value?(u(),c(p(aa),{key:0})):w("",!0)],64)}}}),na=s({__name:"Collapse",props:{color:pe.string.def("")},setup(e){const{getPrefixCls:t}=de(),l=t("collapse"),a=he(),o=i((()=>a.getCollapse)),n=()=>{const e=p(o);a.setCollapse(!e)};return(t,a)=>{const s=ue("Icon");return u(),v("div",{class:h(p(l)),onClick:n},[x(s,{size:18,icon:o.value?"ant-design:menu-unfold-outlined":"ant-design:menu-fold-outlined",color:e.color,class:"cursor-pointer"},null,8,["icon","color"])],2)}}}),sa=s({__name:"SizeDropdown",props:{color:pe.string.def("")},setup(e){const{getPrefixCls:t}=de(),l=t("size-dropdown"),{t:a}=fe(),o=he(),n=i((()=>o.sizeMap)),s=e=>{o.setCurrentSize(e)};return(t,o)=>{const r=ue("Icon");return u(),c(p(dt),{class:h(p(l)),trigger:"click",onCommand:s},{dropdown:d((()=>[x(p(ut),null,{default:d((()=>[(u(!0),v(Y,null,ve(n.value,(e=>(u(),c(p(ct),{key:e,command:e},{default:d((()=>[ce(B(p(a)(`size.${e}`)),1)])),_:2},1032,["command"])))),128))])),_:1})])),default:d((()=>[x(r,{size:18,icon:"mdi:format-size",color:e.color,class:"cursor-pointer"},null,8,["color"])])),_:1},8,["class"])}}}),ra="/assets/logo-DIl1wTjy.png",ia=Ue("lock",{state:()=>({lockInfo:{}}),getters:{getLockInfo(){return this.lockInfo}},actions:{setLockInfo(e){this.lockInfo=e},resetLockInfo(){this.lockInfo={}},unLock(e){var t;return(null==(t=this.lockInfo)?void 0:t.password)===e&&(this.resetLockInfo(),!0)}},persist:!0}),ua=(e=>(ge("data-v-c72dedf5"),e=e(),xe(),e))((()=>j("div",{class:"flex flex-col items-center"},[j("img",{src:ra,alt:"",class:"w-70px h-70px rounded-[50%]"}),j("span",{class:"text-14px my-10px text-[var(--top-header-text-color)]"},"Admin")],-1))),ca=me(s({__name:"LockDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(e,{emit:l}){const{getPrefixCls:a}=de(),o=a("lock-dialog"),{required:n}=gt(),{t:s}=fe(),r=ia(),v=e,m=l,f=i({get:()=>v.modelValue,set:e=>{m("update:modelValue",e)}}),g=t(s("lock.lockScreen")),b=D({password:[n()]}),y=D([{label:s("lock.lockPassword"),field:"password",component:"Input",componentProps:{type:"password",showPassword:!0}}]),{formRegister:w,formMethods:_}=mt(),{getFormData:C,getElFormExpose:k}=_,M=async()=>{const e=await k();null==e||e.validate((async e=>{if(e){f.value=!1;const e=await C();r.setLockInfo({isLock:!0,...e})}}))};return(e,t)=>{const l=ue("BaseButton");return u(),c(p(vt),{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=e=>f.value=e),width:"500px","max-height":"170px",class:h(p(o)),title:g.value},{footer:d((()=>[x(l,{type:"primary",onClick:M},{default:d((()=>[ce(B(p(s)("lock.lock")),1)])),_:1})])),default:d((()=>[ua,x(p(ht),{"is-col":!1,schema:y,rules:b,onRegister:p(w)},null,8,["schema","rules","onRegister"])])),_:1},8,["modelValue","class","title"])}}}),[["__scopeId","data-v-c72dedf5"]]),da={class:"flex flex-col items-center"},pa=(e=>(ge("data-v-4868e6a4"),e=e(),xe(),e))((()=>j("img",{src:ra,alt:"",class:"w-70px h-70px rounded-[50%]"},null,-1))),va={class:"text-14px my-10px text-[var(--top-header-text-color)]"},ma=me(s({__name:"ChangePassword",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(e,{emit:l}){const a=Fe(),{getPrefixCls:o}=de(),n=o("lock-dialog"),{required:s}=gt(),{t:r}=fe(),v=e,m=l,f=i({get:()=>v.modelValue,set:e=>{m("update:modelValue",e)}}),g=t(r("common.changePassword")),b=D({newPassword:[s()]}),y=D([{label:r("common.newPassword"),field:"newPassword",component:"Input",componentProps:{type:"password"}}]),{formRegister:w,formMethods:_}=mt(),{getFormData:C,getElFormExpose:k}=_,M=async()=>{const e=await k();null==e||e.validate((async e=>{if(e){f.value=!1;const e={newPassword:(await C()).newPassword},t=await xt(e);200==t.code&&Ie.success(t.data.message)}}))},I=a.getUserInfo.username;return(e,t)=>{const l=ue("BaseButton");return u(),c(p(vt),{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=e=>f.value=e),width:"500px","max-height":"170px",class:h(p(n)),title:g.value},{footer:d((()=>[x(l,{type:"primary",onClick:M},{default:d((()=>[ce(B(p(r)("common.submit")),1)])),_:1})])),default:d((()=>[j("div",da,[pa,j("span",va,B(p(I)),1)]),x(p(ht),{"is-col":!1,schema:y,rules:b,onRegister:p(w)},null,8,["schema","rules","onRegister"])])),_:1},8,["modelValue","class","title"])}}}),[["__scopeId","data-v-4868e6a4"]]),ha=ft,fa={class:"flex w-screen h-screen justify-center items-center"},ga=(e=>(ge("data-v-10a3819b"),e=e(),xe(),e))((()=>j("div",{class:"flex flex-col items-center"},[j("img",{src:ra,alt:"",class:"w-70px h-70px rounded-[50%]"}),j("span",{class:"text-14px my-10px text-[var(--logo-title-text-color)]"},"Admin")],-1))),xa={class:"absolute bottom-5 w-full text-gray-300 xl:text-xl 2xl:text-3xl text-center enter-y"},ba={class:"text-5xl mb-4 enter-x"},ya={class:"text-3xl"},wa={class:"text-2xl"},_a=me(s({__name:"LockPage",setup(e){const l=Re(),{clear:a}=$e(),{replace:o}=je(),n=t(""),s=t(!1),r=t(!1),i=t(!0),{getPrefixCls:c}=de(),m=c("lock-page"),f=ia(),{hour:g,month:b,minute:y,meridiem:C,year:k,day:M,week:I}=((e=!0)=>{let t;const l=D({year:0,month:0,week:"",day:0,hour:"",minute:"",second:0,meridiem:""}),a=()=>{const e=ha(),t=e.format("HH"),a=e.format("mm"),o=e.get("s");l.year=e.get("y"),l.month=e.get("M")+1,l.week="星期"+["日","一","二","三","四","五","六"][e.day()],l.day=e.get("date"),l.hour=t,l.minute=a,l.second=o,l.meridiem=e.format("A")};function o(){a(),clearInterval(t),t=setInterval((()=>a()),1e3)}function n(){clearInterval(t)}return qe((()=>{e&&o()})),De((()=>{n()})),{...We(l),start:o,stop:n}})(!0),{t:T}=fe();async function $(){await bt().catch((()=>{}))&&(a(),l.delAllViews(),Je(),f.resetLockInfo(),o("/login"))}function V(e=!1){i.value=e}return(e,t)=>{const l=ue("BaseButton");return u(),v("div",{class:h([p(m),"fixed inset-0 flex h-screen w-screen bg-black items-center justify-center"])},[Z(j("div",{class:h([`${p(m)}__unlock`,"absolute top-0 left-1/2 flex pt-5 h-16 items-center justify-center sm:text-md xl:text-xl text-white flex-col cursor-pointer transform translate-x-1/2"]),onClick:t[0]||(t[0]=e=>V(!1))},[x(p(Ve),{icon:"ep:lock"}),j("span",null,B(p(T)("lock.unlock")),1)],2),[[K,i.value]]),j("div",fa,[j("div",{class:h([`${p(m)}__hour`,"relative mr-5 md:mr-20 w-2/5 h-2/5 md:h-4/5"])},[j("span",null,B(p(g)),1),Z(j("span",{class:"meridiem absolute left-5 top-5 text-md xl:text-xl"},B(p(C)),513),[[K,i.value]])],2),j("div",{class:h(`${p(m)}__minute w-2/5 h-2/5 md:h-4/5 `)},[j("span",null,B(p(y)),1)],2)]),x(_,{name:"fade-slide"},{default:d((()=>[Z(j("div",{class:h(`${p(m)}-entry`)},[j("div",{class:h(`${p(m)}-entry-content`)},[ga,x(p(Ge),{type:"password",placeholder:p(T)("lock.placeholder"),class:"enter-x",modelValue:n.value,"onUpdate:modelValue":t[1]||(t[1]=e=>n.value=e)},null,8,["placeholder","modelValue"]),r.value?(u(),v("span",{key:0,class:h(`text-14px ${p(m)}-entry__err-msg enter-x`)},B(p(T)("lock.message")),3)):w("",!0),j("div",{class:h(`${p(m)}-entry__footer enter-x`)},[x(l,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:s.value,onClick:t[2]||(t[2]=e=>V(!0))},{default:d((()=>[ce(B(p(T)("common.back")),1)])),_:1},8,["disabled"]),x(l,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:s.value,onClick:$},{default:d((()=>[ce(B(p(T)("lock.backToLogin")),1)])),_:1},8,["disabled"]),x(l,{type:"primary",class:"mt-2",size:"small",link:"",onClick:t[3]||(t[3]=e=>async function(){if(!n.value)return;let e=n.value;try{s.value=!0;const t=await f.unLock(e);r.value=!t}finally{s.value=!1}}()),disabled:s.value},{default:d((()=>[ce(B(p(T)("lock.entrySystem")),1)])),_:1},8,["disabled"])],2)],2)],2),[[K,!i.value]])])),_:1}),j("div",xa,[Z(j("div",ba,[ce(B(p(g))+":"+B(p(y))+" ",1),j("span",ya,B(p(C)),1)],512),[[K,!i.value]]),j("div",wa,B(p(k))+"/"+B(p(b))+"/"+B(p(M))+" "+B(p(I)),1)])],2)}}}),[["__scopeId","data-v-10a3819b"]]),Ca={class:"flex items-center"},ka=(e=>(ge("data-v-e72b282a"),e=e(),xe(),e))((()=>j("img",{src:ra,alt:"",class:"w-[calc(var(--logo-height)-25px)] rounded-[50%]"},null,-1))),Ma={class:"<lg:hidden text-14px pl-[5px] text-[var(--top-header-text-color)]"},Ia=me(s({__name:"UserInfo",setup(e){const l=Fe(),a=ia(),o=i((()=>{var e;return(null==(e=a.getLockInfo)?void 0:e.isLock)??!1})),{getPrefixCls:n}=de(),s=n("user-info"),{t:r}=fe(),m=()=>{l.logoutConfirm()},f=t(!1),g=()=>{f.value=!0},b=t(!1),y=()=>{b.value=!0};return(e,t)=>(u(),v(Y,null,[x(p(dt),{class:h(["custom-hover",p(s)]),trigger:"click"},{dropdown:d((()=>[x(p(ut),null,{default:d((()=>[x(p(ct),null,{default:d((()=>[j("div",{onClick:y},B(p(r)("common.changePassword")),1)])),_:1}),x(p(ct),{divided:""},{default:d((()=>[j("div",{onClick:g},B(p(r)("lock.lockScreen")),1)])),_:1}),x(p(ct),null,{default:d((()=>[j("div",{onClick:m},B(p(r)("common.loginOut")),1)])),_:1})])),_:1})])),default:d((()=>{var e;return[j("div",Ca,[ka,j("span",Ma,B(null==(e=p(l).getUserInfo)?void 0:e.username),1)])]})),_:1},8,["class"]),f.value?(u(),c(ca,{key:0,modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=e=>f.value=e)},null,8,["modelValue"])):w("",!0),b.value?(u(),c(ma,{key:1,modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=e=>b.value=e)},null,8,["modelValue"])):w("",!0),(u(),c(Qe,{to:"body"},[x(_,{name:"fade-bottom",mode:"out-in"},{default:d((()=>[o.value?(u(),c(_a,{key:0})):w("",!0)])),_:1})]))],64))}}),[["__scopeId","data-v-e72b282a"]]),Ta=s({__name:"Screenfull",props:{color:pe.string.def("")},setup(e){const{getPrefixCls:t}=de(),l=t("screenfull"),{toggle:a,isFullscreen:o}=Ze(),n=()=>{a()};return(t,a)=>(u(),v("div",{class:h(p(l)),onClick:n},[x(p(Ve),{size:18,icon:p(o)?"zmdi:fullscreen-exit":"zmdi:fullscreen",color:e.color},null,8,["icon","color"])],2))}}),$a=(e,t="")=>{var l;const a=[];for(const o of e){const e=null==o?void 0:o.meta;if(e.hidden&&!e.canTo)continue;const n=e.alwaysShow||1!==(null==(l=o.children)?void 0:l.length)?{...o}:{...o.children[0],path:Se(o.path,o.children[0].path)};n.path=Se(t,n.path),n.children&&(n.children=$a(n.children,n.path)),n&&a.push(n)}return a};const{getPrefixCls:Va}=de(),Pa=Va("breadcrumb"),Sa=he(),ja=i((()=>Sa.getBreadcrumbIcon)),La=me(s({name:"Breadcrumb",setup(){const{currentRoute:e}=je(),{t:l}=fe(),a=t([]),o=Le(),n=i((()=>{const e=o.getRouters;return $a(e)}));return W((()=>e.value),(t=>{t.path.startsWith("/redirect/")||(()=>{const t=e.value.matched.slice(-1)[0].path;a.value=nt(p(n),(e=>e.path===t))})()}),{immediate:!0}),()=>{let e;return x(Lt,{separator:"/",class:`${Pa} flex items-center h-full ml-[10px]`},{default:()=>{return[x(Ke,{appear:!0,"enter-active-class":"animate__animated animate__fadeInRight"},(t=e=st(p(a)).map((e=>{const t=!e.redirect||"noredirect"===e.redirect,a=e.meta;return x(Bt,{to:{path:t?"":e.path},key:e.name},{default:()=>{var t,o;return[(null==a?void 0:a.icon)&&ja.value?x(Y,null,[x(Ve,{icon:a.icon,class:"mr-[5px]"},null),ce(" "),l((null==(t=null==e?void 0:e.meta)?void 0:t.title)||"")]):l((null==(o=null==e?void 0:e.meta)?void 0:o.title)||"")]}})})),"function"==typeof t||"[object Object]"===Object.prototype.toString.call(t)&&!ze(t)?e:{default:()=>[e]}))];var t}})}}}),[["__scopeId","data-v-48a8fb48"]]),{getPrefixCls:Ba,variables:za}=de(),Oa=Ba("tool-header"),Aa=he(),Ea=i((()=>Aa.getBreadcrumb)),Ra=i((()=>Aa.getHamburger)),Ha=i((()=>Aa.getScreenfull)),Na=i((()=>Aa.getSize)),Ua=i((()=>Aa.getLayout)),Fa=i((()=>Aa.getLocale)),qa=me(s({name:"ToolHeader",setup:()=>()=>x("div",{id:`${za.namespace}-tool-header`,class:[Oa,"h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between","dark:bg-[var(--el-bg-color)]"]},["top"!==Ua.value?x("div",{class:"h-full flex items-center"},[Ra.value&&"cutMenu"!==Ua.value?x(na,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,Ea.value?x(La,{class:"<md:hidden"},null):void 0]):void 0,x("div",{class:"h-full flex items-center"},[Ha.value?x(Ta,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,Na.value?x(sa,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,Fa.value?x(tt,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,x(Ia,null,null)])])}),[["__scopeId","data-v-7e6529e4"]]),{getPrefixCls:Da}=de(),Wa=Da("layout"),Ga=he(),Ja=i((()=>Ga.getPageLoading)),Qa=i((()=>Ga.getTagsView)),Za=i((()=>Ga.getCollapse)),Ka=i((()=>Ga.logo)),Ya=i((()=>Ga.getFixedHeader)),Xa=i((()=>Ga.getMobile)),eo=i((()=>Ga.getFixedMenu)),to=()=>({renderClassic:()=>x(Y,null,[x("div",{class:["absolute top-0 left-0 h-full layout-border__right",{"!fixed z-3000":Xa.value}]},[Ka.value?x(la,{class:["bg-[var(--left-menu-bg-color)] relative",{"!pl-0":Xa.value&&Za.value,"w-[var(--left-menu-min-width)]":Ga.getCollapse,"w-[var(--left-menu-max-width)]":!Ga.getCollapse}],style:"transition: all var(--transition-time-02);"},null):void 0,x(Hl,{class:[{"!h-[calc(100%-var(--logo-height))]":Ka.value}]},null)]),x("div",{class:[`${Wa}-content`,"absolute top-0 h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":Za.value&&!Xa.value&&!Xa.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!Za.value&&!Xa.value&&!Xa.value,"fixed !w-full !left-0":Xa.value}],style:"transition: all var(--transition-time-02);"},[Z(x(Be,{class:[`${Wa}-content-scrollbar`,{"!h-[calc(100%-var(--top-tool-height)-var(--tags-view-height))] mt-[calc(var(--top-tool-height)+var(--tags-view-height))]":Ya.value}]},{default:()=>[x("div",{class:[{"fixed top-0 left-0 z-10":Ya.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)]":Za.value&&Ya.value&&!Xa.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)]":!Za.value&&Ya.value&&!Xa.value,"!w-full !left-0":Xa.value}],style:"transition: all var(--transition-time-02);"},[x(qa,{class:["bg-[var(--top-header-bg-color)]",{"layout-border__bottom":!Qa.value}]},null),Qa.value?x(ea,{class:"layout-border__bottom layout-border__top"},null):void 0]),x(oa,null,null)]}),[[Ae("loading"),Ja.value]])])]),renderTopLeft:()=>x(Y,null,[x("div",{class:"flex items-center bg-[var(--top-header-bg-color)] relative layout-border__bottom dark:bg-[var(--el-bg-color)]"},[Ka.value?x(la,{class:"custom-hover"},null):void 0,x(qa,{class:"flex-1"},null)]),x("div",{class:"absolute top-[var(--logo-height)+1px] left-0 w-full h-[calc(100%-1px-var(--logo-height))] flex"},[x(Hl,{class:"!h-full relative layout-border__right"},null),x("div",{class:[`${Wa}-content`,"h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":Za.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!Za.value}],style:"transition: all var(--transition-time-02);"},[Z(x(Be,{class:[`${Wa}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":Ya.value&&Qa.value}]},{default:()=>[Qa.value?x(ea,{class:["layout-border__bottom absolute",{"!fixed top-0 left-0 z-10":Ya.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)] mt-[calc(var(--logo-height)+1px)]":Za.value&&Ya.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)] mt-[calc(var(--logo-height)+1px)]":!Za.value&&Ya.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,x(oa,null,null)]}),[[Ae("loading"),Ja.value]])])])]),renderTop:()=>x(Y,null,[x("div",{class:["flex items-center justify-between bg-[var(--top-header-bg-color)] relative",{"layout-border__bottom":!Qa.value}]},[Ka.value?x(la,{class:"custom-hover"},null):void 0,x(Hl,{class:"flex-1 px-10px h-[var(--top-tool-height)]"},null),x(qa,null,null)]),x("div",{class:[`${Wa}-content`,"w-full",{"h-[calc(100%-var(--top-tool-height))]":!Ya.value,"h-[calc(100%-var(--tags-view-height)-var(--top-tool-height))]":Ya.value}]},[Z(x(Be,{class:[`${Wa}-content-scrollbar`,{"mt-[var(--tags-view-height)] !pb-[calc(var(--tags-view-height)+var(--app-footer-height))]":Ya.value,"pb-[var(--app-footer-height)]":!Ya.value}]},{default:()=>[Qa.value?x(ea,{class:["layout-border__bottom layout-border__top relative",{"!fixed w-full top-[calc(var(--top-tool-height)+1px)] left-0":Ya.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,x(oa,null,null)]}),[[Ae("loading"),Ja.value]])])]),renderCutMenu:()=>x(Y,null,[x("div",{class:"flex items-center bg-[var(--top-header-bg-color)] relative layout-border__bottom"},[Ka.value?x(la,{class:"custom-hover !pr-15px"},null):void 0,x(qa,{class:"flex-1"},null)]),x("div",{class:"absolute top-[var(--logo-height)] left-0 w-[calc(100%-2px)] h-[calc(100%-var(--logo-height))] flex"},[x(Wl,null,null),x("div",{class:[`${Wa}-content`,"h-[100%]",{"w-[calc(100%-var(--tab-menu-min-width))] left-[var(--tab-menu-min-width)]":Za.value&&!eo.value,"w-[calc(100%-var(--tab-menu-max-width))] left-[var(--tab-menu-max-width)]":!Za.value&&!eo.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":Za.value&&eo.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":!Za.value&&eo.value}],style:"transition: all var(--transition-time-02);"},[Z(x(Be,{class:[`${Wa}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":Ya.value&&Qa.value}]},{default:()=>[Qa.value?x(ea,{class:["relative layout-border__bottom layout-border__top",{"!fixed top-0 left-0 z-10":Ya.value,"w-[calc(100%-var(--tab-menu-min-width))] !left-[var(--tab-menu-min-width)] mt-[var(--logo-height)]":Za.value&&Ya.value,"w-[calc(100%-var(--tab-menu-max-width))] !left-[var(--tab-menu-max-width)] mt-[var(--logo-height)]":!Za.value&&Ya.value,"!fixed top-0 !left-[var(--tab-menu-min-width)+var(--left-menu-max-width)] z-10":Ya.value&&eo.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] !left-[var(--tab-menu-min-width)+var(--left-menu-max-width)] mt-[var(--logo-height)]":Za.value&&Ya.value&&eo.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] !left-[var(--tab-menu-max-width)+var(--left-menu-max-width)] mt-[var(--logo-height)]":!Za.value&&Ya.value&&eo.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,x(oa,null,null)]}),[[Ae("loading"),Ja.value]])])])])}),{getPrefixCls:lo}=de(),ao=lo("layout"),oo=he(),no=i((()=>oo.getMobile)),so=i((()=>oo.getCollapse)),ro=i((()=>oo.getLayout)),io=()=>{oo.setCollapse(!0)},uo=()=>{switch(p(ro)){case"classic":const{renderClassic:e}=to();return e();case"topLeft":const{renderTopLeft:t}=to();return t();case"top":const{renderTop:l}=to();return l();case"cutMenu":const{renderCutMenu:a}=to();return a()}},co=me(s({name:"Layout",setup:()=>()=>x("section",{class:[ao,`${ao}__${ro.value}`,"w-[100%] h-[100%] relative"]},[no.value&&!so.value?x("div",{class:"absolute top-0 left-0 w-full h-full opacity-30 z-99 bg-[var(--el-color-black)]",onClick:io},null):void 0,uo(),x(tl,null,null),x(Bl,null,null)])}),[["__scopeId","data-v-393f3095"]]);export{co as default};
