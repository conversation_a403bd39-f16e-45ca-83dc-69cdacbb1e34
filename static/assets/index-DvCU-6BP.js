import{r as a}from"./index-CnCQNuY4.js";const t=(t,e,i)=>a.post({url:"/api/dictionary/port/data",data:{search:t,pageIndex:e,pageSize:i}}),e=t=>a.post({url:"/api/dictionary/port/delete",data:{ids:t}}),i=(t,e,i)=>a.post({url:"/api/dictionary/port/upgrade",data:{id:t,name:e,value:i}}),d=(t,e)=>a.post({url:"/api/dictionary/port/add",data:{name:t,value:e}}),r=()=>a.get({url:"/api/dictionary/manage/list"}),o=t=>a.post({url:"/api/dictionary/manage/create",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}}),p=t=>a.get({url:"/api/dictionary/manage/download?id="+t,responseType:"blob"}),s=t=>a.post({url:"/api/dictionary/manage/delete",data:{ids:t}});export{p as a,e as b,o as c,s as d,t as e,d as f,r as g,i as u};
