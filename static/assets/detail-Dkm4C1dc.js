import{d as e,r as a,V as l,M as u,O as o,o as t,i as r,w as n,e as i,a as s,A as d,c as p,R as m,F as v,B as c,z as b,l as g,_ as f}from"./index-C6fb_XFi.js";import{a as _,E as y}from"./el-form-C2Y6uNCj.js";import"./el-tag-C_oEQYGz.js";import{a as S,E as V}from"./el-select-vbM8Rxr1.js";import"./el-popper-CeVwVUf9.js";import{E as h,a as j}from"./el-col-Dl4_4Pn5.js";import{j as w,o as U,T as x}from"./index-CZoUTVkP.js";import{h as P,s as A}from"./index-DWlzJn9A.js";import"./castArray-DRqY4cIf.js";import"./strings-BiUeKphX.js";import"./index-BWEJ0epC.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./index-CnCQNuY4.js";const F=f(e({__name:"detail",props:{closeDialog:{type:Function},getList:{type:Function},id:{}},setup(e){const{t:f}=g(),F=e,L=a({name:"",version:"",module:"",parameter:"",help:"",introduction:"",source:""}),D=()=>{L.value={name:"",version:"",module:"",parameter:"",help:"",introduction:"",source:""},C.value=""},R=a({name:[{required:!0,message:"",trigger:"blur"}],module:[{required:!0,message:"",trigger:"change"}],source:[{required:!0,message:"",trigger:"blur"}]}),k=a([{label:"TargetHandler",value:"TargetHandler"},{label:"SubdomainScan",value:"SubdomainScan"},{label:"SubdomainSecurity",value:"SubdomainSecurity"},{label:"PortScanPreparation",value:"PortScanPreparation"},{label:"PortScan",value:"PortScan"},{label:"AssetMapping",value:"AssetMapping"},{label:"URLScan",value:"URLScan"},{label:"WebCrawler",value:"WebCrawler"},{label:"DirScan",value:"DirScan"},{label:"VulnerabilityScan",value:"VulnerabilityScan"},{label:"AssetHandle",value:"AssetHandle"},{label:"PortFingerprint",value:"PortFingerprint"},{label:"URLSecurity",value:"URLSecurity"},{label:"PassiveScan",value:"PassiveScan"}]),C=a(""),E=[w(),U];l((async()=>{D(),F.id&&await q(F.id)}));const H=a("");(()=>{const e=localStorage.getItem("plugin_key");H.value=e})();const I=a(!1),q=async e=>{try{const a=await P(e);if(200===a.code){const e=a.data;L.value.name=e.name,L.value.version=e.version,L.value.module=e.module,L.value.parameter=e.parameter,L.value.help=e.help,L.value.introduction=e.introduction,C.value=e.source,I.value=e.isSystem}else u.error(`数据加载失败：${a.message}`)}catch(a){}},M=a(!1);o((()=>F.id),(async e=>{""===e?D():await q(e)}));const z=async()=>{if(M.value=!0,""==L.value.name)return u.error("name 不能为空"),void(M.value=!1);if(""==L.value.module)return u.error("module 不能为空"),void(M.value=!1);if(!I.value&&""==C.value)return u.error("源码 不能为空"),void(M.value=!1);try{505==(await A(F.id,L.value.name,L.value.version,L.value.module,L.value.parameter,L.value.help,L.value.introduction,C.value,H.value)).code&&localStorage.removeItem("plugin_key"),F.closeDialog(),F.getList()}catch(e){u.error("保存失败，请稍后再试。")}finally{M.value=!1}};return(e,a)=>(t(),r(s(y),{model:L.value,rules:R.value,"label-width":"100px"},{default:n((()=>[i(s(j),{gutter:20},{default:n((()=>[i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.name"),prop:"name"},{default:n((()=>[i(s(d),{modelValue:L.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>L.value.name=e),disabled:I.value},null,8,["modelValue","disabled"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.module"),prop:"module"},{default:n((()=>[i(s(S),{modelValue:L.value.module,"onUpdate:modelValue":a[1]||(a[1]=e=>L.value.module=e),disabled:I.value},{default:n((()=>[(t(!0),p(v,null,m(k.value,(e=>(t(),r(s(V),{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.parameter"),prop:"parameter"},{default:n((()=>[i(s(d),{modelValue:L.value.parameter,"onUpdate:modelValue":a[2]||(a[2]=e=>L.value.parameter=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.help"),prop:"help"},{default:n((()=>[i(s(d),{modelValue:L.value.help,"onUpdate:modelValue":a[3]||(a[3]=e=>L.value.help=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.version"),prop:"version"},{default:n((()=>[i(s(d),{modelValue:L.value.version,"onUpdate:modelValue":a[4]||(a[4]=e=>L.value.version=e),disabled:I.value},null,8,["modelValue","disabled"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:24},{default:n((()=>[i(s(_),{label:s(f)("plugin.introduction"),prop:"introduction"},{default:n((()=>[i(s(d),{modelValue:L.value.introduction,"onUpdate:modelValue":a[5]||(a[5]=e=>L.value.introduction=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:24},{default:n((()=>[i(s(_),{label:"源码",prop:"source"},{default:n((()=>[i(s(x),{modelValue:C.value,"onUpdate:modelValue":a[6]||(a[6]=e=>C.value=e),style:{height:"400px",width:"90%"},autofocus:!0,"indent-with-tab":!0,"tab-size":2,extensions:E,disabled:I.value},null,8,["modelValue","disabled"])])),_:1})])),_:1})])),_:1}),i(s(j),null,{default:n((()=>[i(s(h),{span:12,style:{"text-align":"right"}},{default:n((()=>[i(s(c),{type:"primary",onClick:z,loading:M.value},{default:n((()=>[b(" 保存 ")])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1},8,["model","rules"]))}}),[["__scopeId","data-v-13e80fb0"]]);export{F as default};
