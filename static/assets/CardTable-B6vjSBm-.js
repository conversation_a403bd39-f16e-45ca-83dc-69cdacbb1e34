import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as t,l as s,r as i,o as r,i as a,w as o,e as l,a as p,f as n,t as m,z as c}from"./index-3XfDPlIS.js";import{_ as d}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{g as j}from"./index-DkbkkiFT.js";import{E as u}from"./el-link-Dzmhaz1a.js";import{E as x}from"./el-divider-D9UCOo44.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./el-table-column-B5hg3WH6.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tag-DcMbxLLg.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./index-Dz8ZrwBc.js";const f={class:"flex cursor-pointer"},_={class:"pr-16px"},v=["src"],g={class:"mb-12px font-700 font-size-16px"},b={class:"line-clamp-3 font-size-12px"},y={class:"flex justify-center items-center"},k=["onClick"],w=["onClick"],C=t({__name:"CardTable",setup(t){const{t:C}=s(),h=i(!0);let z=i([]);(async e=>{const t=await j(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{h.value=!1}));t&&(z.value=t.data.list)})();return(t,s)=>(r(),a(p(e),{title:p(C)("tableDemo.cardTable")},{default:o((()=>[l(p(d),{columns:[],data:p(z),loading:h.value,"custom-content":"","card-wrap-style":{width:"200px",marginBottom:"20px",marginRight:"20px"}},{content:o((e=>[n("div",f,[n("div",_,[n("img",{src:e.logo,class:"w-48px h-48px rounded-[50%]",alt:""},null,8,v)]),n("div",null,[n("div",g,m(e.name),1),n("div",b,m(e.desc),1)])])])),"content-footer":o((e=>[n("div",y,[n("div",{class:"flex-1 text-center",onClick:()=>{}},[l(p(u),{underline:!1},{default:o((()=>[c("操作一")])),_:1})],8,k),l(p(x),{direction:"vertical"}),n("div",{class:"flex-1 text-center",onClick:()=>{}},[l(p(u),{underline:!1},{default:o((()=>[c("操作二")])),_:1})],8,w)])])),_:1},8,["data","loading"])])),_:1},8,["title"]))}});export{C as default};
