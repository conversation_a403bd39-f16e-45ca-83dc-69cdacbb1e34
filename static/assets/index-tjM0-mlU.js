import{cD as n,cE as e,cF as t,cG as r,cH as a,cI as o,cJ as u,cK as c,cL as i,cf as s,cM as f,cN as l,cO as v,bH as d,cP as b,cQ as h,cR as g,cS as p,cT as _,bx as y,bF as j,cU as m,aX as w,b_ as O}from"./index-3XfDPlIS.js";function A(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}function E(n,e){for(var t=-1,r=null==n?0:n.length;++t<r;)if(e(n[t],t,n))return!0;return!1}function L(n,e){return n.has(e)}A.prototype.add=A.prototype.push=function(n){return this.__data__.set(n,"__lodash_hash_undefined__"),this},A.prototype.has=function(n){return this.__data__.has(n)};var k=1,x=2;function F(n,e,t,r,a,o){var u=t&k,c=n.length,i=e.length;if(c!=i&&!(u&&i>c))return!1;var s=o.get(n),f=o.get(e);if(s&&f)return s==e&&f==n;var l=-1,v=!0,d=t&x?new A:void 0;for(o.set(n,e),o.set(e,n);++l<c;){var b=n[l],h=e[l];if(r)var g=u?r(h,b,l,e,n,o):r(b,h,l,n,e,o);if(void 0!==g){if(g)continue;v=!1;break}if(d){if(!E(e,(function(n,e){if(!L(d,e)&&(b===n||a(b,n,t,r,o)))return d.push(e)}))){v=!1;break}}else if(b!==h&&!a(b,h,t,r,o)){v=!1;break}}return o.delete(n),o.delete(e),v}function H(n){var e=-1,t=Array(n.size);return n.forEach((function(n,r){t[++e]=[r,n]})),t}function S(n){var e=-1,t=Array(n.size);return n.forEach((function(n){t[++e]=n})),t}var z=1,M=2,D="[object Boolean]",P="[object Date]",R="[object Error]",B="[object Map]",I="[object Number]",N="[object RegExp]",V="[object Set]",C="[object String]",G="[object Symbol]",J="[object ArrayBuffer]",K="[object DataView]",Q=e?e.prototype:void 0,T=Q?Q.valueOf:void 0;var U=1,X=Object.prototype.hasOwnProperty;var q=1,W="[object Arguments]",Y="[object Array]",Z="[object Object]",$=Object.prototype.hasOwnProperty;function nn(n,e,f,l,v,d){var b=s(n),h=s(e),g=b?Y:o(n),p=h?Y:o(e),_=(g=g==W?Z:g)==Z,y=(p=p==W?Z:p)==Z,j=g==p;if(j&&u(n)){if(!u(e))return!1;b=!0,_=!1}if(j&&!_)return d||(d=new c),b||i(n)?F(n,e,f,l,v,d):function(n,e,a,o,u,c,i){switch(a){case K:if(n.byteLength!=e.byteLength||n.byteOffset!=e.byteOffset)return!1;n=n.buffer,e=e.buffer;case J:return!(n.byteLength!=e.byteLength||!c(new r(n),new r(e)));case D:case P:case I:return t(+n,+e);case R:return n.name==e.name&&n.message==e.message;case N:case C:return n==e+"";case B:var s=H;case V:var f=o&z;if(s||(s=S),n.size!=e.size&&!f)return!1;var l=i.get(n);if(l)return l==e;o|=M,i.set(n,e);var v=F(s(n),s(e),o,u,c,i);return i.delete(n),v;case G:if(T)return T.call(n)==T.call(e)}return!1}(n,e,g,f,l,v,d);if(!(f&q)){var m=_&&$.call(n,"__wrapped__"),w=y&&$.call(e,"__wrapped__");if(m||w){var O=m?n.value():n,A=w?e.value():e;return d||(d=new c),v(O,A,f,l,d)}}return!!j&&(d||(d=new c),function(n,e,t,r,o,u){var c=t&U,i=a(n),s=i.length;if(s!=a(e).length&&!c)return!1;for(var f=s;f--;){var l=i[f];if(!(c?l in e:X.call(e,l)))return!1}var v=u.get(n),d=u.get(e);if(v&&d)return v==e&&d==n;var b=!0;u.set(n,e),u.set(e,n);for(var h=c;++f<s;){var g=n[l=i[f]],p=e[l];if(r)var _=c?r(p,g,l,e,n,u):r(g,p,l,n,e,u);if(!(void 0===_?g===p||o(g,p,t,r,u):_)){b=!1;break}h||(h="constructor"==l)}if(b&&!h){var y=n.constructor,j=e.constructor;y==j||!("constructor"in n)||!("constructor"in e)||"function"==typeof y&&y instanceof y&&"function"==typeof j&&j instanceof j||(b=!1)}return u.delete(n),u.delete(e),b}(n,e,f,l,v,d))}function en(n,e,t,r,a){return n===e||(null==n||null==e||!f(n)&&!f(e)?n!=n&&e!=e:nn(n,e,t,r,en,a))}var tn=1,rn=2;function an(n){return n==n&&!l(n)}function on(n,e){return function(t){return null!=t&&(t[n]===e&&(void 0!==e||n in Object(t)))}}function un(n){var e=function(n){for(var e=v(n),t=e.length;t--;){var r=e[t],a=n[r];e[t]=[r,a,an(a)]}return e}(n);return 1==e.length&&e[0][2]?on(e[0][0],e[0][1]):function(t){return t===n||function(n,e,t,r){var a=t.length,o=a,u=!r;if(null==n)return!o;for(n=Object(n);a--;){var i=t[a];if(u&&i[2]?i[1]!==n[i[0]]:!(i[0]in n))return!1}for(;++a<o;){var s=(i=t[a])[0],f=n[s],l=i[1];if(u&&i[2]){if(void 0===f&&!(s in n))return!1}else{var v=new c;if(r)var d=r(f,l,s,n,e,v);if(!(void 0===d?en(l,f,tn|rn,r,v):d))return!1}}return!0}(t,n,e)}}function cn(n,e){return null!=n&&e in Object(n)}function sn(n,e){return null!=n&&function(n,e,t){for(var r=-1,a=(e=d(e,n)).length,o=!1;++r<a;){var u=b(e[r]);if(!(o=null!=n&&t(n,u)))break;n=n[u]}return o||++r!=a?o:!!(a=null==n?0:n.length)&&h(a)&&g(u,a)&&(s(n)||p(n))}(n,e,cn)}var fn=1,ln=2;function vn(n){return _(n)?(e=b(n),function(n){return null==n?void 0:n[e]}):function(n){return function(e){return j(e,n)}}(n);var e}function dn(n){return"function"==typeof n?n:null==n?m:"object"==typeof n?s(n)?(e=n[0],t=n[1],_(e)&&an(t)?on(b(e),t):function(n){var r=y(n,e);return void 0===r&&r===t?sn(n,e):en(t,r,fn|ln)}):un(n):vn(n);var e,t}function bn(n,e){return en(n,e)}const hn=new Map;let gn;function pn(n,e){let t=[];return Array.isArray(e.arg)?t=e.arg:O(e.arg)&&t.push(e.arg),function(r,a){const o=e.instance.popperRef,u=r.target,c=null==a?void 0:a.target,i=!e||!e.instance,s=!u||!c,f=n.contains(u)||n.contains(c),l=n===u,v=t.length&&t.some((n=>null==n?void 0:n.contains(u)))||t.length&&t.includes(c),d=o&&(o.contains(u)||o.contains(c));i||s||f||l||v||d||e.value(r,a)}}w&&(document.addEventListener("mousedown",(n=>gn=n)),document.addEventListener("mouseup",(n=>{for(const e of hn.values())for(const{documentHandler:t}of e)t(n,gn)})));const _n={beforeMount(n,e){hn.has(n)||hn.set(n,[]),hn.get(n).push({documentHandler:pn(n,e),bindingFn:e.value})},updated(n,e){hn.has(n)||hn.set(n,[]);const t=hn.get(n),r=t.findIndex((n=>n.bindingFn===e.oldValue)),a={documentHandler:pn(n,e),bindingFn:e.value};r>=0?t.splice(r,1,a):t.push(a)},unmounted(n){hn.delete(n)}};export{_n as C,A as S,dn as b,L as c,sn as h,bn as i,S as s};
