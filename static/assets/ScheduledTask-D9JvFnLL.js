import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,r as a,s as l,e as o,L as s,G as i,F as n,H as r,o as d,i as u,w as p,a as m,z as c,t as f,A as g,B as v,f as _,I as y,J as h,l as j,M as b}from"./index-C6fb_XFi.js";import{E as x,a as w}from"./el-col-Dl4_4Pn5.js";import{E as k}from"./el-text-BnUG9HvL.js";import{a as V,E as S}from"./el-tab-pane-DDoZFwPS.js";import{E,a as A}from"./el-form-C2Y6uNCj.js";import{E as N}from"./el-input-number-DVs4I2j5.js";import"./el-tag-C_oEQYGz.js";import{E as C}from"./el-popper-CeVwVUf9.js";import"./el-virtual-list-D7NvYvyu.js";import{E as T}from"./el-select-v2-CaMVABoW.js";import{E as U}from"./el-checkbox-CvJzNe2E.js";import"./el-tooltip-l0sNRNKZ.js";import{E as z}from"./el-switch-Bh7JeorW.js";import{_ as P}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as D}from"./useTable-CijeIiBB.js";import{u as W}from"./useIcon-BxqaCND-.js";import{b as I,c as H,u as L}from"./index-B40b3p-m.js";import{_ as M}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{_ as R}from"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import{_ as F}from"./AddProject.vue_vue_type_script_setup_true_lang-B7RhpJiR.js";import{_ as O}from"./PageMonit.vue_vue_type_script_setup_true_lang-ePBLYKxC.js";import{a as B}from"./index-CBLGyxDn.js";import"./el-card-B37ahJ8o.js";import"./strings-BiUeKphX.js";import"./castArray-DRqY4cIf.js";import"./raf-DGOAeO92.js";import"./useInput-IB6tFdGu.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-table-column-C9CkC7I1.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";import"./el-divider-Bw95UAdD.js";import"./el-radio-group-hI5DSxSU.js";import"./DetailTemplate-Dao-XeZd.js";/* empty css                */import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";import"./index-CkmA3mDG.js";const G={class:"mb-10px"},J={style:{position:"relative",top:"12px"}};function q(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!h(e)}const K=t({__name:"ScheduledTask",setup(t){const h=W({icon:"iconoir:search"}),{t:K}=j(),X=a(""),Y=()=>{ie()},Q=l([{field:"selection",type:"selection",width:"55"},{field:"name",label:K("task.taskName"),minWidth:30},{field:"cycle",label:K("task.taskCycle")+"(h)",minWidth:20},{field:"type",label:K("task.typeTask"),minWidth:20},{field:"lastTime",label:K("task.lastTime"),minWidth:40,formatter:(e,t,a)=>""==a?"-":a},{field:"nextTime",label:K("task.nextTime"),minWidth:40,formatter:(e,t,a)=>""==a||0==e.state?"-":a},{field:"state",label:K("common.state"),minWidth:20,formatter:(e,t,a)=>{if(null==a)return o("div",null,null);let l="",i="";return 1==a?(l="#2eb98a",i=K("common.on")):(l="red",i=K("common.statusStop")),o(w,{gutter:20},{default:()=>[o(x,{span:1},{default:()=>[o(s,{icon:"clarity:circle-solid",color:l},null)]}),o(x,{span:5},{default:()=>[o(k,{type:"info"},q(i)?i:{default:()=>[i]})]})]})}},{field:"action",label:K("tableDemo.action"),minWidth:40,formatter:(e,t,a)=>{let l,s,r;return o(n,null,["page_monitoring"===e.id?o(i,{type:"success",onClick:()=>Ae(e)},q(l=K("common.edit"))?l:{default:()=>[l]}):o(n,null,[o(i,{type:"success",onClick:()=>fe(e)},q(s=K("common.edit"))?s:{default:()=>[s]}),o(i,{type:"danger",onClick:()=>ve(e)},q(r=K("common.delete"))?r:{default:()=>[r]})])])}}]),{tableRegister:Z,tableState:$,tableMethods:ee}=D({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=$,a=await H(X.value,e.value,t.value);return{list:a.data.list,total:a.data.total}},immediate:!0}),{loading:te,dataList:ae,total:le,currentPage:oe,pageSize:se}=$;se.value=20;const{getList:ie,getElTableExpose:ne}=ee;function re(){return{background:"var(--el-fill-color-light)"}}const de=a(!1);let ue=K("task.addTask");const pe=()=>{de.value=!1};let me=a(""),ce=a(!0);const fe=async e=>{me.value=e.id,ue=K("common.edit"),de.value=!0},ge=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await je()},ve=async e=>{window.confirm("Are you sure you want to delete the selected data?")&&await ye(e)},_e=a(!1),ye=async e=>{_e.value=!0;try{await I([e.id]);_e.value=!1,ie()}catch(t){_e.value=!1,ie()}},he=a([]),je=async()=>{const e=await ne(),t=(null==e?void 0:e.getSelectionRows())||[];he.value=t.map((e=>e.id)),_e.value=!0;try{await I(he.value);_e.value=!1,ie()}catch(a){_e.value=!1,ie()}};r((()=>{xe(),window.addEventListener("resize",xe)}));const be=a(0),xe=()=>{const e=window.innerHeight||document.documentElement.clientHeight;be.value=.75*e},we=a(!1),ke=()=>{we.value=!1},Ve=a(!1),Se=a(!1),Ee=l({hour:24,allNode:!0,node:[],state:!0}),Ae=async e=>{Ee.hour=e.cycle,Ee.allNode=e.allNode,Ee.node=e.node,Ee.state=e.state,Ve.value=!0},Ne=l([]),Ce=a(!1),Te=a(!1),Ue=e=>{Ce.value=!1,e?(Ee.allNode=!0,Ee.node=[],Ne.forEach((e=>Ee.node.push(e.value)))):(Ee.allNode=!1,Ee.node=[])};return(async()=>{const e=await B();e.data.list.length>0?(Te.value=!1,e.data.list.forEach((e=>{Ne.push({value:e,label:e})}))):(Te.value=!0,b.warning(K("node.onlineNodeMsg")))})(),(t,a)=>(d(),u(m(e),null,{default:p((()=>[o(m(w),null,{default:p((()=>[o(m(x),{span:1},{default:p((()=>[o(m(k),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:p((()=>[c(f(m(K)("task.taskName"))+":",1)])),_:1})])),_:1}),o(m(x),{span:5},{default:p((()=>[o(m(g),{modelValue:X.value,"onUpdate:modelValue":a[0]||(a[0]=e=>X.value=e),placeholder:m(K)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(m(x),{span:5,style:{position:"relative",left:"16px"}},{default:p((()=>[o(m(v),{type:"primary",icon:m(h),style:{height:"100%"},onClick:Y},{default:p((()=>[c("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(m(w),null,{default:p((()=>[o(m(x),{style:{position:"relative",top:"16px"}},{default:p((()=>[_("div",G,[o(m(i),{type:"danger",loading:_e.value,onClick:ge},{default:p((()=>[c(f(m(K)("task.delTask")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),_("div",J,[o(m(P),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:m(se),"onUpdate:pageSize":a[1]||(a[1]=e=>y(se)?se.value=e:null),currentPage:m(oe),"onUpdate:currentPage":a[2]||(a[2]=e=>y(oe)?oe.value=e:null),columns:Q,data:m(ae),stripe:"",border:!0,loading:m(te),"max-height":be.value,resizable:!0,pagination:{total:m(le),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:m(Z),headerCellStyle:re,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])]),o(m(M),{modelValue:de.value,"onUpdate:modelValue":a[3]||(a[3]=e=>de.value=e),title:m(ue),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[o(R,{closeDialog:pe,getList:m(ie),create:m(ce),taskid:m(me),schedule:!0,tp:"scan","target-ids":[]},null,8,["getList","create","taskid"])])),_:1},8,["modelValue","title"]),o(m(M),{modelValue:we.value,"onUpdate:modelValue":a[4]||(a[4]=e=>we.value=e),title:m(K)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[o(F,{closeDialog:ke,projectid:m(""),getProjectData:m(ie),schedule:!1},null,8,["projectid","getProjectData"])])),_:1},8,["modelValue","title"]),o(m(M),{modelValue:Ve.value,"onUpdate:modelValue":a[10]||(a[10]=e=>Ve.value=e),title:m(K)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[o(m(V),{type:"card"},{default:p((()=>[o(m(S),{label:m(K)("router.configuration")},{default:p((()=>[o(m(E),{model:Ee,"label-width":"100px","status-icon":"",ref:"ruleFormRef"},{default:p((()=>[o(m(C),{content:m(K)("task.selectNodeMsg"),placement:"top"},{default:p((()=>[o(m(A),{label:m(K)("task.nodeSelect"),prop:"node"},{default:p((()=>[o(m(T),{modelValue:Ee.node,"onUpdate:modelValue":a[6]||(a[6]=e=>Ee.node=e),filterable:"",options:Ne,placeholder:"Please select node",style:{width:"80%"},multiple:"","tag-type":"success","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":7},{header:p((()=>[o(m(U),{modelValue:Ee.allNode,"onUpdate:modelValue":a[5]||(a[5]=e=>Ee.allNode=e),disabled:Te.value,indeterminate:Ce.value,onChange:Ue},{default:p((()=>[c(" All ")])),_:1},8,["modelValue","disabled","indeterminate"])])),_:1},8,["modelValue","options"])])),_:1},8,["label"])])),_:1},8,["content"]),o(m(A),{label:m(K)("project.cycle"),prop:"type"},{default:p((()=>[o(m(N),{modelValue:Ee.hour,"onUpdate:modelValue":a[7]||(a[7]=e=>Ee.hour=e),min:1,"controls-position":"right",size:"small"},null,8,["modelValue"]),o(m(k),{style:{position:"relative",left:"16px"}},{default:p((()=>[c("Hour")])),_:1})])),_:1},8,["label"]),o(m(A),{label:m(K)("common.state")},{default:p((()=>[o(m(z),{modelValue:Ee.state,"onUpdate:modelValue":a[8]||(a[8]=e=>Ee.state=e),"inline-prompt":"","active-text":m(K)("common.switchAction"),"inactive-text":m(K)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),o(m(w),null,{default:p((()=>[o(m(x),{span:2,offset:8},{default:p((()=>[o(m(A),null,{default:p((()=>[o(m(v),{type:"primary",onClick:a[9]||(a[9]=e=>(async()=>{Se.value=!0,await L(Ee.hour,Ee.node,Ee.allNode,Ee.state),Se.value=!1,ie()})()),loading:Se.value},{default:p((()=>[c(f(m(K)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["label"]),o(m(S),{label:m(K)("task.data")},{default:p((()=>[o(O)])),_:1},8,["label"])])),_:1})])),_:1},8,["modelValue","title"])])),_:1}))}});export{K as default};
