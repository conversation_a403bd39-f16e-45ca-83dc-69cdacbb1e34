import{d as e,dE as l,r as a,s as o,o as u,i as s,w as t,e as n,a as i,A as r,f as v,c as d,R as m,F as p,ae as c,B as f,z as g,t as b,l as _,Y as w}from"./index-3XfDPlIS.js";import{a as y,E as h}from"./el-form-BY8piFS2.js";import{E as V,a as j}from"./el-col-CN1tVfqh.js";import{E as k}from"./el-divider-D9UCOo44.js";import{E as x}from"./el-tag-DcMbxLLg.js";import"./el-popper-DVoWBu_3.js";import"./el-virtual-list-Drl4IGmp.js";import{E as z}from"./el-select-v2-CJw7ZO42.js";import{j as E,o as F,T as C}from"./index-C7jW5IRr.js";import{u as U,c as D}from"./index-Cyn7ZzlS.js";const L={class:"flex gap-2"},M=e({__name:"Detail",props:{closeDialog:{type:Function},getList:{type:Function},pocForm:{}},setup(e){const{t:M}=_(),R=[E(),F],q=e,{pocForm:A}=l(q),B=a({...A.value}),N=o({name:[{required:!0,message:M("poc.nameMsg"),trigger:"blur"}],level:[{required:!0,message:M("poc.contentMsg"),trigger:"blur"}]}),P=[{value:"critical",label:"critical"},{value:"high",label:"high"},{value:"medium",label:"medium"},{value:"low",label:"low"},{value:"info",label:"info"},{value:"unknown",label:"unknown"}],T=a(!1),G=a(),I=a([...A.value.tags]),J=a(""),K=a(!1),O=a(),Y=()=>{K.value=!0,w((()=>{O.value.input.focus()}))},H=()=>{J.value&&I.value.push(J.value),K.value=!1,J.value=""};return(e,l)=>(u(),s(i(h),{model:B.value,"label-width":"120px",rules:N,"status-icon":"",ref_key:"ruleFormRef",ref:G},{default:t((()=>[n(i(y),{label:i(M)("poc.pocName"),prop:"name"},{default:t((()=>[n(i(r),{modelValue:B.value.name,"onUpdate:modelValue":l[0]||(l[0]=e=>B.value.name=e),placeholder:i(M)("poc.nameMsg")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),n(i(y),{label:i(M)("poc.content"),prop:"content"},{default:t((()=>[n(i(C),{modelValue:B.value.content,"onUpdate:modelValue":l[1]||(l[1]=e=>B.value.content=e),style:{height:"600px",width:"100%"},autofocus:!0,"indent-with-tab":!0,"tab-size":2,extensions:R},null,8,["modelValue"])])),_:1},8,["label"]),n(i(y),{label:i(M)("poc.level")},{default:t((()=>[n(i(z),{modelValue:B.value.level,"onUpdate:modelValue":l[2]||(l[2]=e=>B.value.level=e),placeholder:"Please select level",options:P},null,8,["modelValue"])])),_:1},8,["label"]),n(i(y),{label:"TAG"},{default:t((()=>[v("div",L,[(u(!0),d(p,null,m(I.value,(e=>(u(),s(i(x),{key:e,closable:"","disable-transitions":!1,onClose:l=>(e=>{I.value.splice(I.value.indexOf(e),1)})(e)},{default:t((()=>[g(b(e),1)])),_:2},1032,["onClose"])))),128)),K.value?(u(),s(i(r),{key:0,ref_key:"InputRef",ref:O,modelValue:J.value,"onUpdate:modelValue":l[3]||(l[3]=e=>J.value=e),class:"w-20",size:"small",onKeyup:c(H,["enter"]),onBlur:H},null,8,["modelValue"])):(u(),s(i(f),{key:1,class:"button-new-tag",size:"small",onClick:Y},{default:t((()=>[g(" + New Tag ")])),_:1}))])])),_:1}),n(i(k)),n(i(j),null,{default:t((()=>[n(i(V),{span:2,offset:8},{default:t((()=>[n(i(y),null,{default:t((()=>[n(i(f),{type:"primary",onClick:l[4]||(l[4]=e=>(async e=>{T.value=!0,e&&await e.validate((async(e,l)=>{if(e){let e;e=""!=B.value.id?await U(B.value.id,B.value.name,B.value.content,B.value.level,I.value):await D(B.value.name,B.value.content,B.value.level,I.value),200===e.code&&(q.getList(),q.closeDialog()),T.value=!1}else T.value=!1}))})(G.value)),loading:T.value},{default:t((()=>[g(b(i(M)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"]))}});export{M as _};
