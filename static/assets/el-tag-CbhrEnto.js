import{Z as a,ca as s,d as e,aF as l,a7 as o,a8 as n,o as t,c,f as i,a9 as r,n as u,a as p,i as f,w as d,e as k,bd as m,af as y,C as g,j as b,aH as v,h as C,aa as h,ah as _}from"./index-DfJTpRkj.js";const B=a({type:{type:String,values:["primary","success","info","warning","danger"],default:"primary"},closable:Boolean,disableTransitions:Boolean,hit:Boolean,color:String,size:{type:String,values:s},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),E={close:a=>a instanceof MouseEvent,click:a=>a instanceof MouseEvent},S=e({name:"ElTag"});const T=_(h(e({...S,props:B,emits:E,setup(a,{emit:s}){const e=a,h=l(),_=o("tag"),B=n((()=>{const{type:a,hit:s,effect:l,closable:o,round:n}=e;return[_.b(),_.is("closable",o),_.m(a||"primary"),_.m(h.value),_.m(l),_.is("hit",s),_.is("round",n)]})),E=a=>{s("close",a)},S=a=>{s("click",a)};return(a,s)=>a.disableTransitions?(t(),c("span",{key:0,class:u(p(B)),style:v({backgroundColor:a.color}),onClick:S},[i("span",{class:u(p(_).e("content"))},[r(a.$slots,"default")],2),a.closable?(t(),f(p(g),{key:0,class:u(p(_).e("close")),onClick:y(E,["stop"])},{default:d((()=>[k(p(m))])),_:1},8,["class","onClick"])):b("v-if",!0)],6)):(t(),f(C,{key:1,name:`${p(_).namespace.value}-zoom-in-center`,appear:""},{default:d((()=>[i("span",{class:u(p(B)),style:v({backgroundColor:a.color}),onClick:S},[i("span",{class:u(p(_).e("content"))},[r(a.$slots,"default")],2),a.closable?(t(),f(p(g),{key:0,class:u(p(_).e("close")),onClick:y(E,["stop"])},{default:d((()=>[k(p(m))])),_:1},8,["class","onClick"])):b("v-if",!0)],6)])),_:3},8,["name"]))}}),[["__file","tag.vue"]]));export{T as E,B as t};
