import{d as e,H as t,r as a,s as i,v as l,A as s,B as o,o as r,c as n,e as p,a as u,w as m,I as d,F as c,l as g,ai as j,_ as f}from"./index-C6fb_XFi.js";import{u as h}from"./useTable-CijeIiBB.js";import{E as v}from"./el-card-B37ahJ8o.js";import{E as b}from"./el-pagination-FWx5cl5J.js";import{E as y}from"./el-tag-C_oEQYGz.js";import"./el-select-vbM8Rxr1.js";import"./el-popper-CeVwVUf9.js";import{E as x,a as _}from"./el-col-Dl4_4Pn5.js";import{_ as S}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as E}from"./useCrudSchemas-CEXr0LRM.js";import{a as C,d as w,q as T}from"./index-BBupWySc.js";import k from"./Csearch-B51tl_vU.js";import"./index-BWEJ0epC.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./tree-BfZhwLPs.js";import"./index-CnCQNuY4.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import"./el-text-BnUG9HvL.js";import"./el-divider-Bw95UAdD.js";import"./el-tree-select-D8i5b8X5.js";import"./index-w1A_Rrgh.js";/* empty css                */import"./el-switch-Bh7JeorW.js";import"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import"./useIcon-BxqaCND-.js";import"./exportData.vue_vue_type_script_setup_true_lang-C3cw41xZ.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-space-CdSK6Ce1.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const V=f(e({__name:"Subdomain",props:{projectList:{}},setup(e){const{t:f}=g(),V=[{keyword:"ip",example:'ip="***********"',explain:f("searchHelp.ip")},{keyword:"domain",example:'domain="example.com"',explain:f("searchHelp.domain")},{keyword:"type",example:'type="CNAME"',explain:f("searchHelp.subdomainType")},{keyword:"value",example:'value="allcdn.example.com"',explain:f("searchHelp.subdoaminValue")},{keyword:"project",example:'project="Hackerone"',explain:f("searchHelp.project")}];t((()=>{z(),window.addEventListener("resize",z)}));const A=a(0),z=()=>{const e=window.innerHeight||document.documentElement.clientHeight;A.value=.7*e},H=a(""),N=e=>{H.value=e,X()},I=i({}),L=i([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:f("tableDemo.index"),type:"index",minWidth:"30"},{field:"host",label:f("subdomain.subdomainName"),minWidth:"200"},{field:"type",label:f("subdomain.recordType"),minWidth:"200",columnKey:"type",filters:[{text:"A",value:"A"},{text:"NS",value:"NS"},{text:"CNAME",value:"CNAME"},{text:"PTR",value:"PTR"},{text:"TXT",value:"TXT"}]},{field:"value",label:f("subdomain.recordValue"),minWidth:"250",formatter:(e,t,a)=>{let i="";return a.forEach(((e,t)=>{i+=`${e}\r\n`})),i}},{field:"ip",label:"IP",minWidth:"150",formatter:(e,t,a)=>{let i="";return a.forEach(((e,t)=>{i+=`${e}\r\n`})),i}},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,i)=>{null==i&&(i=[]),I[e.id]||(I[e.id]={inputVisible:!1,inputValue:"",inputRef:a(null)});const r=I[e.id],n=async()=>{r.inputValue&&(i.push(r.inputValue),C(e.id,P,r.inputValue)),r.inputVisible=!1,r.inputValue=""};return l(_,{},(()=>[...i.map((t=>l(x,{span:24,key:t},(()=>[l("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||te("tags",t)})(e,t)},[l(y,{closable:!0,onClose:()=>(async t=>{const a=i.indexOf(t);a>-1&&i.splice(a,1),w(e.id,P,t)})(t)},(()=>t))])])))),l(x,{span:24},r.inputVisible?()=>l(s,{ref:r.inputRef,modelValue:r.inputValue,"onUpdate:modelValue":e=>r.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&n()},onBlur:n}):()=>l(o,{class:"button-new-tag",size:"small",onClick:()=>(r.inputVisible=!0,void j((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:f("asset.time"),minWidth:"200"}]);let P="subdomain";L.forEach((e=>{e.hidden=e.hidden??!1}));let R=a(!1);const W=({field:e,hidden:t})=>{const a=L.findIndex((t=>t.field===e));-1!==a&&(L[a].hidden=t),(()=>{const e=L.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=R.value,localStorage.setItem(`columnConfig_${P}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${P}`)||"{}");L.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),R.value=e.statisticsHidden})();const{allSchemas:U}=E(L),{tableRegister:D,tableState:F,tableMethods:O}=h({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=F,a=await T(H.value,e.value,t.value,Y);return{list:a.data.list,total:a.data.total}},immediate:!1}),{loading:$,dataList:M,total:B,currentPage:K,pageSize:J}=F,{getList:X,getElTableExpose:q}=O;function G(){return{background:"var(--el-fill-color-light)"}}const Y=i({}),Q=async e=>{Object.assign(Y,e),X()},Z=(e,t)=>{Object.assign(Y,t),H.value=e,X()},ee=a([]),te=(e,t)=>{const a=`${e}=${t}`;ee.value=[...ee.value,a]},ae=e=>{if(ee.value){const[t,a]=e.split("=");t in Y&&Array.isArray(Y[t])&&(Y[t]=Y[t].filter((e=>e!==a)),0===Y[t].length&&delete Y[t]),ee.value=ee.value.filter((t=>t!==e))}},ie=()=>Y;return(e,t)=>(r(),n(c,null,[p(k,{getList:u(X),handleSearch:N,searchKeywordsData:V,index:"subdomain",getElTableExpose:u(q),projectList:e.$props.projectList,handleFilterSearch:Z,crudSchemas:L,dynamicTags:ee.value,handleClose:ae,onUpdateColumnVisibility:W,searchResultCount:u(B),getFilter:ie},null,8,["getList","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),p(u(_),null,{default:m((()=>[p(u(x),null,{default:m((()=>[p(u(v),{style:{height:"min-content"}},{default:m((()=>[p(u(S),{pageSize:u(J),"onUpdate:pageSize":t[0]||(t[0]=e=>d(J)?J.value=e:null),currentPage:u(K),"onUpdate:currentPage":t[1]||(t[1]=e=>d(K)?K.value=e:null),columns:u(U).tableColumns,data:u(M),stripe:"",border:!0,loading:u($),resizable:!0,onRegister:u(D),onFilterChange:Q,headerCellStyle:G,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","onRegister"])])),_:1})])),_:1}),p(u(x),{":span":24},{default:m((()=>[p(u(v),null,{default:m((()=>[p(u(b),{pageSize:u(J),"onUpdate:pageSize":t[2]||(t[2]=e=>d(J)?J.value=e:null),currentPage:u(K),"onUpdate:currentPage":t[3]||(t[3]=e=>d(K)?K.value=e:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:u(B)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-6581af87"]]);export{V as default};
