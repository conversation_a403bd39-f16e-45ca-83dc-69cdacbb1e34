import{d as e,r as t,s as l,e as a,L as i,v as o,A as s,B as n,G as r,z as u,F as p,H as d,y as m,o as c,c as v,a as g,w as f,I as h,t as b,f as x,J as y,l as j,ai as _,_ as w}from"./index-C6fb_XFi.js";import{u as S}from"./useTable-CijeIiBB.js";import{E as k}from"./el-card-B37ahJ8o.js";import{E as V}from"./el-pagination-FWx5cl5J.js";import{E as L}from"./el-tag-C_oEQYGz.js";import{E,a as R}from"./el-select-vbM8Rxr1.js";import"./el-popper-CeVwVUf9.js";import{E as C,a as H}from"./el-col-Dl4_4Pn5.js";import{E as z,a as U}from"./el-descriptions-item-DRtMZ3Vw.js";import{E as T}from"./el-text-BnUG9HvL.js";import{_ as I}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{_ as W}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as A}from"./useCrudSchemas-CEXr0LRM.js";import{r as D}from"./index-CnCQNuY4.js";import O from"./Csearch-B51tl_vU.js";import{u as q,a as F,d as P}from"./index-BBupWySc.js";import{_ as M}from"./Detail.vue_vue_type_script_setup_true_lang-CYU_gebw.js";import{b as N}from"./index-fOwuVARc.js";import"./index-BWEJ0epC.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./refs-3HtnmaOD.js";import"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./index-ghAu5K8t.js";import"./tree-BfZhwLPs.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import"./el-divider-Bw95UAdD.js";import"./el-tree-select-D8i5b8X5.js";import"./index-w1A_Rrgh.js";/* empty css                */import"./el-switch-Bh7JeorW.js";import"./useIcon-BxqaCND-.js";import"./exportData.vue_vue_type_script_setup_true_lang-C3cw41xZ.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-space-CdSK6Ce1.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";import"./index-DWlzJn9A.js";import"./index-CZoUTVkP.js";const $={style:{whiteSpace:"pre-line",width:"500px"}},K={style:{whiteSpace:"pre-line"}};function B(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!y(e)}const J=w(e({__name:"vul",props:{projectList:{}},setup(e){const{t:y}=j(),w=[{keyword:"url",example:'url="http://example.com"',explain:y("searchHelp.url")},{keyword:"vulname",example:'vulname="nginxwebui-runcmd-rce"',explain:y("searchHelp.vulname")},{keyword:"level",example:'level="info"',explain:y("searchHelp.level")},{keyword:"matched",example:'matched="https://example.com"',explain:y("searchHelp.matched")},{keyword:"request",example:'request="cmd=whoami"',explain:y("searchHelp.vulRequest")},{keyword:"response",example:'response="root"',explain:y("searchHelp.response")},{keyword:"project",example:'project="Hackerone"',explain:y("searchHelp.project")}],J=t(""),G=e=>{J.value=e,pe()},X=l({}),Y=l([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:y("tableDemo.index"),type:"index",minWidth:55},{field:"url",label:"URL",minWidth:100},{field:"vulnerability",label:"Vulnerability",minWidth:120},{field:"level",label:"Level",minWidth:100,columnKey:"level",formatter:(e,t,l)=>{if(null==l)return a("div",null,null);let o="",s="";return"critical"===l?(o="red",s=y("poc.critical")):"high"===l?(o="orange",s=y("poc.high")):"medium"===l?(o="yellow",s=y("poc.medium")):"low"===l?(o="blue",s=y("poc.low")):"info"===l?(o="green",s=y("poc.info")):"unknown"===l&&(o="gray",s=y("poc.unknown")),a(H,{gutter:20,style:"width: 80%"},{default:()=>[a(C,{span:1},{default:()=>[a(i,{icon:"clarity:circle-solid",color:o},null)]}),a(C,{span:5},{default:()=>[a(T,{type:"info"},B(s)?s:{default:()=>[s]})]})]})},filters:[{text:y("poc.critical"),value:"critical"},{text:y("poc.high"),value:"high"},{text:y("poc.medium"),value:"medium"},{text:y("poc.low"),value:"low"},{text:y("poc.info"),value:"info"},{text:y("poc.unknown"),value:"unknown"}]},{field:"matched",label:"Matched",minWidth:150},{field:"status",label:y("common.state"),minWidth:100,columnKey:"status",formatter:(e,t,l)=>{let i;null==e.status&&(e.status=1);const o=[{value:1,label:y("common.unprocessed")},{value:2,label:y("common.processing")},{value:3,label:y("common.ignored")},{value:4,label:y("common.suspected")},{value:5,label:y("common.confirmed")}];return a(R,{modelValue:e.status,"onUpdate:modelValue":async t=>{try{e.status=t,q(e.id,"vulnerability",t)}catch(l){}}},B(i=o.map((e=>a(E,{key:e.value,label:e.label,value:e.value},null))))?i:{default:()=>[i]})},filters:[{text:y("common.unprocessed"),value:1},{text:y("common.processing"),value:2},{text:y("common.ignored"),value:3},{text:y("common.suspected"),value:4},{text:y("common.confirmed"),value:5}]},{field:"tags",label:"TAG",fit:"true",formatter:(e,l,a)=>{null==a&&(a=[]),X[e.id]||(X[e.id]={inputVisible:!1,inputValue:"",inputRef:t(null)});const i=X[e.id],r=async()=>{i.inputValue&&(a.push(i.inputValue),F(e.id,Q,i.inputValue)),i.inputVisible=!1,i.inputValue=""};return o(H,{},(()=>[...a.map((t=>o(C,{span:24,key:t},(()=>[o("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||we("tags",t)})(e,t)},[o(L,{closable:!0,onClose:()=>(async t=>{const l=a.indexOf(t);l>-1&&a.splice(l,1),P(e.id,Q,t)})(t)},(()=>t))])])))),o(C,{span:24},i.inputVisible?()=>o(s,{ref:i.inputRef,modelValue:i.inputValue,"onUpdate:modelValue":e=>i.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&r()},onBlur:r}):()=>o(n,{class:"button-new-tag",size:"small",onClick:()=>(i.inputVisible=!0,void _((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:y("asset.time"),minWidth:200},{field:"action",label:y("tableDemo.action"),formatter:(e,t,l)=>{let i;return a(p,null,[a(r,{type:"primary",onClick:()=>be(e)},B(i=y("asset.detail"))?i:{default:()=>[i]}),a(r,{type:"success",onClick:()=>Le(e.vulnid)},{default:()=>[u("POC")]})])},minWidth:100}]);let Q="vulnerability";Y.forEach((e=>{e.hidden=e.hidden??!1}));let Z=t(!1);const ee=({field:e,hidden:t})=>{const l=Y.findIndex((t=>t.field===e));-1!==l&&(Y[l].hidden=t),(()=>{const e=Y.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=Z.value,localStorage.setItem(`columnConfig_${Q}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${Q}`)||"{}");Y.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),Z.value=e.statisticsHidden})();const{allSchemas:te}=A(Y),{tableRegister:le,tableState:ae,tableMethods:ie}=S({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=ae,l=await((e,t,l,a)=>D.post({url:"/api/vul/data",data:{search:e,pageIndex:t,pageSize:l,filter:a}}))(J.value,e.value,t.value,xe);return{list:l.data.list,total:l.data.total}},immediate:!1}),{loading:oe,dataList:se,total:ne,currentPage:re,pageSize:ue}=ae,{getList:pe,getElTableExpose:de}=ie;function me(){return{background:"var(--el-fill-color-light)"}}ue.value=20,d((()=>{ve(),window.addEventListener("resize",ve)}));const ce=t(0),ve=()=>{const e=window.innerHeight||document.documentElement.clientHeight;ce.value=.7*e},ge=l({URL:"",Vulnerability:"",Level:"",Matched:"",Time:"",Request:"",Response:""}),fe=t(""),he=t(!1),be=e=>{const t=e.level;fe.value="";let l="";"critical"===t?(fe.value="red",l=y("poc.critical")):"high"===t?(fe.value="orange",l=y("poc.high")):"medium"===t?(fe.value="yellow",l=y("poc.medium")):"low"===t?(fe.value="blue",l=y("poc.low")):"info"===t?(fe.value="green",l=y("poc.info")):"unknown"===t&&(fe.value="gray",l=y("poc.unknown")),ge.Level=l,ge.Vulnerability=e.vulnerability,ge.Matched=e.matched,ge.Time=e.time,ge.URL=e.url,ge.Request=e.request,ge.Response=e.response,he.value=!0},xe=l({}),ye=async e=>{Object.assign(xe,e),pe()},je=(e,t)=>{Object.assign(xe,t),J.value=e,pe()},_e=t([]),we=(e,t)=>{const l=`${e}=${t}`;_e.value=[..._e.value,l]},Se=e=>{if(_e.value){const[t,l]=e.split("=");t in xe&&Array.isArray(xe[t])&&(xe[t]=xe[t].filter((e=>e!==l)),0===xe[t].length&&delete xe[t]),_e.value=_e.value.filter((t=>t!==e))}};let ke=l({id:"",name:"",level:"",content:"",tags:[]});const Ve=t(!1),Le=async e=>{ke.id="",ke.name="",ke.level="",ke.content="",ke.tags=[];const t=await N(e);ke.id=t.data.data.id,ke.name=t.data.data.name,ke.level=t.data.data.level,ke.content=t.data.data.content,ke.tags=t.data.data.tags,Ve.value=!0},Ee=()=>{Ve.value=!1},Re=()=>xe;return(e,t)=>{const l=m("ElScrollbar");return c(),v(p,null,[a(O,{getList:g(pe),handleSearch:G,searchKeywordsData:w,index:"vulnerability",getElTableExpose:g(de),handleFilterSearch:je,projectList:e.$props.projectList,crudSchemas:Y,dynamicTags:_e.value,handleClose:Se,onUpdateColumnVisibility:ee,searchResultCount:g(ne),getFilter:Re},null,8,["getList","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),a(g(H),null,{default:f((()=>[a(g(C),null,{default:f((()=>[a(g(k),null,{default:f((()=>[a(g(W),{pageSize:g(ue),"onUpdate:pageSize":t[0]||(t[0]=e=>h(ue)?ue.value=e:null),currentPage:g(re),"onUpdate:currentPage":t[1]||(t[1]=e=>h(re)?re.value=e:null),columns:g(te).tableColumns,data:g(se),stripe:"",border:!0,"max-height":ce.value,loading:g(oe),resizable:!0,onRegister:g(le),onFilterChange:ye,headerCellStyle:me,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),a(g(C),{":span":24},{default:f((()=>[a(g(k),null,{default:f((()=>[a(g(V),{pageSize:g(ue),"onUpdate:pageSize":t[2]||(t[2]=e=>h(ue)?ue.value=e:null),currentPage:g(re),"onUpdate:currentPage":t[3]||(t[3]=e=>h(re)?re.value=e:null),"page-sizes":[20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:g(ne)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1}),a(g(I),{modelValue:he.value,"onUpdate:modelValue":t[4]||(t[4]=e=>he.value=e),title:g(y)("asset.detail"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},width:"70%","max-height":ce.value},{default:f((()=>[a(g(z),{border:!0,column:2},{default:f((()=>[a(g(U),{label:"URL"},{default:f((()=>[u(b(ge.URL),1)])),_:1}),a(g(U),{label:"Level",width:"100"},{default:f((()=>[a(g(i),{icon:"clarity:circle-solid",color:fe.value},null,8,["color"]),a(g(T),{type:"info"},{default:f((()=>[u(b(ge.Level),1)])),_:1})])),_:1}),a(g(U),{label:"Vulnerability"},{default:f((()=>[u(b(ge.Vulnerability),1)])),_:1}),a(g(U),{label:"Matched"},{default:f((()=>[u(b(ge.Matched),1)])),_:1}),a(g(U),{label:"Time",span:2},{default:f((()=>[u(b(ge.Time),1)])),_:1}),a(g(U),{label:"Request"},{default:f((()=>[a(l,{"max-height":ce.value,"max-width":"maxHeight"},{default:f((()=>[x("div",$,b(ge.Request),1)])),_:1},8,["max-height"])])),_:1}),a(g(U),{label:"Response"},{default:f((()=>[a(l,{"max-height":ce.value},{default:f((()=>[x("div",K,b(ge.Response),1)])),_:1},8,["max-height"])])),_:1})])),_:1})])),_:1},8,["modelValue","title","max-height"]),a(g(I),{modelValue:Ve.value,"onUpdate:modelValue":t[5]||(t[5]=e=>Ve.value=e),title:g(ke).id?e.$t("common.edit"):e.$t("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:800},{default:f((()=>[a(M,{closeDialog:Ee,pocForm:g(ke),getList:g(pe)},null,8,["pocForm","getList"])])),_:1},8,["modelValue","title"])],64)}}}),[["__scopeId","data-v-fcbd4559"]]);export{J as default};
