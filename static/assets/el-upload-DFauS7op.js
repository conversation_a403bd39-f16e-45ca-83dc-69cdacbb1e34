import{bu as e,a4 as t,b7 as a,Y as s,Z as l,$ as o,aT as i,d as n,aA as r,a6 as u,ba as c,r as d,a7 as p,o as f,i as v,w as y,c as m,F as g,R as h,n as b,a as k,ad as w,a8 as F,j as R,f as E,ae as S,e as T,C as $,cs as x,t as C,aH as L,ct as _,aO as U,bd as P,cu as O,cv as j,T as B,a9 as D,aa as q,b5 as H,cw as A,aW as K,cx as M,aS as X,cy as z,O as N,aC as J,x as Q,a5 as W,bh as Y,bQ as Z,aJ as G,ag as I}from"./index-C6fb_XFi.js";import{E as V}from"./el-progress-sY5OgffI.js";import{i as ee}from"./index-BWEJ0epC.js";const te=Symbol("uploadContextKey");class ae extends Error{constructor(e,t,a,s){super(e),this.name="UploadAjaxError",this.status=t,this.method=a,this.url=s}}function se(e,t,a){let s;return s=a.response?`${a.response.error||a.response}`:a.responseText?`${a.responseText}`:`fail to ${t.method} ${e} ${a.status}`,new ae(s,a.status,t.method,e)}const le=["text","picture","picture-card"];let oe=1;const ie=()=>Date.now()+oe++,ne=s({action:{type:String,default:"#"},headers:{type:l(Object)},method:{type:String,default:"post"},data:{type:l([Object,Function,Promise]),default:()=>o({})},multiple:{type:Boolean,default:!1},name:{type:String,default:"file"},drag:{type:Boolean,default:!1},withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:{type:String,default:""},fileList:{type:l(Array),default:()=>o([])},autoUpload:{type:Boolean,default:!0},listType:{type:String,values:le,default:"text"},httpRequest:{type:l(Function),default:s=>{"undefined"==typeof XMLHttpRequest&&e("ElUpload","XMLHttpRequest is undefined");const l=new XMLHttpRequest,o=s.action;l.upload&&l.upload.addEventListener("progress",(e=>{const t=e;t.percent=e.total>0?e.loaded/e.total*100:0,s.onProgress(t)}));const i=new FormData;if(s.data)for(const[e,a]of Object.entries(s.data))t(a)&&a.length?i.append(e,...a):i.append(e,a);i.append(s.filename,s.file,s.file.name),l.addEventListener("error",(()=>{s.onError(se(o,s,l))})),l.addEventListener("load",(()=>{if(l.status<200||l.status>=300)return s.onError(se(o,s,l));s.onSuccess(function(e){const t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch(a){return t}}(l))})),l.open(s.method,o,!0),s.withCredentials&&"withCredentials"in l&&(l.withCredentials=!0);const n=s.headers||{};if(n instanceof Headers)n.forEach(((e,t)=>l.setRequestHeader(t,e)));else for(const[e,t]of Object.entries(n))a(t)||l.setRequestHeader(e,String(t));return l.send(i),l}},disabled:Boolean,limit:Number}),re=s({...ne,beforeUpload:{type:l(Function),default:i},beforeRemove:{type:l(Function)},onRemove:{type:l(Function),default:i},onChange:{type:l(Function),default:i},onPreview:{type:l(Function),default:i},onSuccess:{type:l(Function),default:i},onProgress:{type:l(Function),default:i},onError:{type:l(Function),default:i},onExceed:{type:l(Function),default:i},crossorigin:{type:l(String)}}),ue=s({files:{type:l(Array),default:()=>o([])},disabled:{type:Boolean,default:!1},handlePreview:{type:l(Function),default:i},listType:{type:String,values:le,default:"text"},crossorigin:{type:l(String)}}),ce=["onKeydown"],de=["src","crossorigin"],pe=["onClick"],fe=["title"],ve=["onClick"],ye=["onClick"],me=n({name:"ElUploadList"});var ge=D(n({...me,props:ue,emits:{remove:e=>!!e},setup(e,{emit:t}){const a=e,{t:s}=r(),l=u("upload"),o=u("icon"),i=u("list"),n=c(),D=d(!1),q=p((()=>[l.b("list"),l.bm("list",a.listType),l.is("disabled",a.disabled)])),H=e=>{t("remove",e)};return(e,t)=>(f(),v(B,{tag:"ul",class:b(k(q)),name:k(i).b()},{default:y((()=>[(f(!0),m(g,null,h(e.files,(a=>(f(),m("li",{key:a.uid||a.name,class:b([k(l).be("list","item"),k(l).is(a.status),{focusing:D.value}]),tabindex:"0",onKeydown:w((e=>!k(n)&&H(a)),["delete"]),onFocus:t[0]||(t[0]=e=>D.value=!0),onBlur:t[1]||(t[1]=e=>D.value=!1),onClick:t[2]||(t[2]=e=>D.value=!1)},[F(e.$slots,"default",{file:a},(()=>["picture"===e.listType||"uploading"!==a.status&&"picture-card"===e.listType?(f(),m("img",{key:0,class:b(k(l).be("list","item-thumbnail")),src:a.url,crossorigin:e.crossorigin,alt:""},null,10,de)):R("v-if",!0),"uploading"===a.status||"picture-card"!==e.listType?(f(),m("div",{key:1,class:b(k(l).be("list","item-info"))},[E("a",{class:b(k(l).be("list","item-name")),onClick:S((t=>e.handlePreview(a)),["prevent"])},[T(k($),{class:b(k(o).m("document"))},{default:y((()=>[T(k(x))])),_:1},8,["class"]),E("span",{class:b(k(l).be("list","item-file-name")),title:a.name},C(a.name),11,fe)],10,pe),"uploading"===a.status?(f(),v(k(V),{key:0,type:"picture-card"===e.listType?"circle":"line","stroke-width":"picture-card"===e.listType?6:2,percentage:Number(a.percentage),style:L("picture-card"===e.listType?"":"margin-top: 0.5rem")},null,8,["type","stroke-width","percentage","style"])):R("v-if",!0)],2)):R("v-if",!0),E("label",{class:b(k(l).be("list","item-status-label"))},["text"===e.listType?(f(),v(k($),{key:0,class:b([k(o).m("upload-success"),k(o).m("circle-check")])},{default:y((()=>[T(k(_))])),_:1},8,["class"])):["picture-card","picture"].includes(e.listType)?(f(),v(k($),{key:1,class:b([k(o).m("upload-success"),k(o).m("check")])},{default:y((()=>[T(k(U))])),_:1},8,["class"])):R("v-if",!0)],2),k(n)?R("v-if",!0):(f(),v(k($),{key:2,class:b(k(o).m("close")),onClick:e=>H(a)},{default:y((()=>[T(k(P))])),_:2},1032,["class","onClick"])),R(" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn"),R(" This is a bug which needs to be fixed "),R(" TODO: Fix the incorrect navigation interaction "),k(n)?R("v-if",!0):(f(),m("i",{key:3,class:b(k(o).m("close-tip"))},C(k(s)("el.upload.deleteTip")),3)),"picture-card"===e.listType?(f(),m("span",{key:4,class:b(k(l).be("list","item-actions"))},[E("span",{class:b(k(l).be("list","item-preview")),onClick:t=>e.handlePreview(a)},[T(k($),{class:b(k(o).m("zoom-in"))},{default:y((()=>[T(k(O))])),_:1},8,["class"])],10,ve),k(n)?R("v-if",!0):(f(),m("span",{key:0,class:b(k(l).be("list","item-delete")),onClick:e=>H(a)},[T(k($),{class:b(k(o).m("delete"))},{default:y((()=>[T(k(j))])),_:1},8,["class"])],10,ye))],2)):R("v-if",!0)]))],42,ce)))),128)),F(e.$slots,"append")])),_:3},8,["class","name"]))}}),[["__file","upload-list.vue"]]);const he=s({disabled:{type:Boolean,default:!1}}),be={file:e=>t(e)},ke=["onDrop","onDragover"],we="ElUploadDrag",Fe=n({name:we});var Re=D(n({...Fe,props:he,emits:be,setup(t,{emit:a}){q(te)||e(we,"usage: <el-upload><el-upload-dragger /></el-upload>");const s=u("upload"),l=d(!1),o=c(),i=e=>{if(o.value)return;l.value=!1,e.stopPropagation();const t=Array.from(e.dataTransfer.files);a("file",t)},n=()=>{o.value||(l.value=!0)};return(e,t)=>(f(),m("div",{class:b([k(s).b("dragger"),k(s).is("dragover",l.value)]),onDrop:S(i,["prevent"]),onDragover:S(n,["prevent"]),onDragleave:t[0]||(t[0]=S((e=>l.value=!1),["prevent"]))},[F(e.$slots,"default")],42,ke))}}),[["__file","upload-dragger.vue"]]);const Ee=s({...ne,beforeUpload:{type:l(Function),default:i},onRemove:{type:l(Function),default:i},onStart:{type:l(Function),default:i},onSuccess:{type:l(Function),default:i},onProgress:{type:l(Function),default:i},onError:{type:l(Function),default:i},onExceed:{type:l(Function),default:i}}),Se=["onKeydown"],Te=["name","multiple","accept"],$e=n({name:"ElUploadContent",inheritAttrs:!1});var xe=D(n({...$e,props:Ee,setup(e,{expose:t}){const a=e,s=u("upload"),l=c(),o=H({}),i=H(),n=e=>{if(0===e.length)return;const{autoUpload:t,limit:s,fileList:l,multiple:o,onStart:i,onExceed:n}=a;if(s&&l.length+e.length>s)n(e,l);else{o||(e=e.slice(0,1));for(const a of e){const e=a;e.uid=ie(),i(e),t&&r(e)}}},r=async e=>{if(i.value.value="",!a.beforeUpload)return d(e);let t,s={};try{const l=a.data,o=a.beforeUpload(e);s=A(a.data)?K(a.data):a.data,t=await o,A(a.data)&&ee(l,s)&&(s=K(a.data))}catch(o){t=!1}if(!1===t)return void a.onRemove(e);let l=e;t instanceof Blob&&(l=t instanceof File?t:new File([t],e.name,{type:e.type})),d(Object.assign(l,{uid:e.uid}),s)},d=async(e,t)=>{const{headers:s,data:l,method:i,withCredentials:n,name:r,action:u,onProgress:c,onSuccess:d,onError:p,httpRequest:f}=a;try{t=await(async(e,t)=>X(e)?e(t):e)(null!=t?t:l,e)}catch(g){return void a.onRemove(e)}const{uid:v}=e,y={headers:s||{},withCredentials:n,file:e,data:t,method:i,filename:r,action:u,onProgress:t=>{c(t,e)},onSuccess:t=>{d(t,e),delete o.value[v]},onError:t=>{p(t,e),delete o.value[v]}},m=f(y);o.value[v]=m,m instanceof Promise&&m.then(y.onSuccess,y.onError)},p=e=>{const t=e.target.files;t&&n(Array.from(t))},g=()=>{l.value||(i.value.value="",i.value.click())},h=()=>{g()};return t({abort:e=>{M(o.value).filter(e?([t])=>String(e.uid)===t:()=>!0).forEach((([e,t])=>{t instanceof XMLHttpRequest&&t.abort(),delete o.value[e]}))},upload:r}),(e,t)=>(f(),m("div",{class:b([k(s).b(),k(s).m(e.listType),k(s).is("drag",e.drag)]),tabindex:"0",onClick:g,onKeydown:w(S(h,["self"]),["enter","space"])},[e.drag?(f(),v(Re,{key:0,disabled:k(l),onFile:n},{default:y((()=>[F(e.$slots,"default")])),_:3},8,["disabled"])):F(e.$slots,"default",{key:1}),E("input",{ref_key:"inputRef",ref:i,class:b(k(s).e("input")),name:e.name,multiple:e.multiple,accept:e.accept,type:"file",onChange:p,onClick:t[0]||(t[0]=S((()=>{}),["stop"]))},null,42,Te)],42,Se))}}),[["__file","upload-content.vue"]]);const Ce="ElUpload",Le=e=>{var t;(null==(t=e.url)?void 0:t.startsWith("blob:"))&&URL.revokeObjectURL(e.url)},_e=n({name:"ElUpload"});const Ue=I(D(n({..._e,props:re,setup(t,{expose:s}){const l=t,o=c(),i=H(),{abort:n,submit:r,clearFiles:u,uploadFiles:d,handleStart:g,handleError:h,handleRemove:b,handleSuccess:w,handleProgress:E,revokeFileObjectURL:S}=((t,s)=>{const l=z(t,"fileList",void 0,{passive:!0}),o=e=>l.value.find((t=>t.uid===e.uid));function i(e){var t;null==(t=s.value)||t.abort(e)}return N((()=>t.listType),(e=>{"picture-card"!==e&&"picture"!==e||(l.value=l.value.map((e=>{const{raw:a,url:s}=e;if(!s&&a)try{e.url=URL.createObjectURL(a)}catch(o){t.onError(o,e,l.value)}return e})))})),N(l,(e=>{for(const t of e)t.uid||(t.uid=ie()),t.status||(t.status="success")}),{immediate:!0,deep:!0}),{uploadFiles:l,abort:i,clearFiles:function(e=["ready","uploading","success","fail"]){l.value=l.value.filter((t=>!e.includes(t.status)))},handleError:(e,a)=>{const s=o(a);s&&(s.status="fail",l.value.splice(l.value.indexOf(s),1),t.onError(e,s,l.value),t.onChange(s,l.value))},handleProgress:(e,a)=>{const s=o(a);s&&(t.onProgress(e,s,l.value),s.status="uploading",s.percentage=Math.round(e.percent))},handleStart:e=>{a(e.uid)&&(e.uid=ie());const s={name:e.name,percentage:0,status:"ready",size:e.size,raw:e,uid:e.uid};if("picture-card"===t.listType||"picture"===t.listType)try{s.url=URL.createObjectURL(e)}catch(o){J(Ce,o.message),t.onError(o,s,l.value)}l.value=[...l.value,s],t.onChange(s,l.value)},handleSuccess:(e,a)=>{const s=o(a);s&&(s.status="success",s.response=e,t.onSuccess(e,s,l.value),t.onChange(s,l.value))},handleRemove:async a=>{const s=a instanceof File?o(a):a;s||e(Ce,"file to be removed not found");const n=e=>{i(e);const a=l.value;a.splice(a.indexOf(e),1),t.onRemove(e,a),Le(e)};t.beforeRemove?!1!==await t.beforeRemove(s,l.value)&&n(s):n(s)},submit:function(){l.value.filter((({status:e})=>"ready"===e)).forEach((({raw:e})=>{var t;return e&&(null==(t=s.value)?void 0:t.upload(e))}))},revokeFileObjectURL:Le}})(l,i),$=p((()=>"picture-card"===l.listType)),x=p((()=>({...l,fileList:d.value,onStart:g,onProgress:E,onSuccess:w,onError:h,onRemove:b})));return Q((()=>{d.value.forEach(S)})),W(te,{accept:Y(l,"accept")}),s({abort:n,submit:r,clearFiles:u,handleStart:g,handleRemove:b}),(e,t)=>(f(),m("div",null,[k($)&&e.showFileList?(f(),v(ge,{key:0,disabled:k(o),"list-type":e.listType,files:k(d),crossorigin:e.crossorigin,"handle-preview":e.onPreview,onRemove:k(b)},Z({append:y((()=>[T(xe,G({ref_key:"uploadRef",ref:i},k(x)),{default:y((()=>[e.$slots.trigger?F(e.$slots,"trigger",{key:0}):R("v-if",!0),!e.$slots.trigger&&e.$slots.default?F(e.$slots,"default",{key:1}):R("v-if",!0)])),_:3},16)])),_:2},[e.$slots.file?{name:"default",fn:y((({file:t})=>[F(e.$slots,"file",{file:t})]))}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):R("v-if",!0),!k($)||k($)&&!e.showFileList?(f(),v(xe,G({key:1,ref_key:"uploadRef",ref:i},k(x)),{default:y((()=>[e.$slots.trigger?F(e.$slots,"trigger",{key:0}):R("v-if",!0),!e.$slots.trigger&&e.$slots.default?F(e.$slots,"default",{key:1}):R("v-if",!0)])),_:3},16)):R("v-if",!0),e.$slots.trigger?F(e.$slots,"default",{key:2}):R("v-if",!0),F(e.$slots,"tip"),!k($)&&e.showFileList?(f(),v(ge,{key:3,disabled:k(o),"list-type":e.listType,files:k(d),crossorigin:e.crossorigin,"handle-preview":e.onPreview,onRemove:k(b)},Z({_:2},[e.$slots.file?{name:"default",fn:y((({file:t})=>[F(e.$slots,"file",{file:t})]))}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):R("v-if",!0)]))}}),[["__file","upload.vue"]]));export{Ue as E};
