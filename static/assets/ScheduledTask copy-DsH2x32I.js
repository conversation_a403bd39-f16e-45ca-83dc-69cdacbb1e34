import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as t,l as a,r as l,s as o,e as s,L as i,G as n,F as r,H as d,o as u,i as p,w as m,a as c,z as f,t as g,A as v,B as y,f as _,I as h,J as j,M as b}from"./index-3XfDPlIS.js";import{E as x,a as w}from"./el-col-CN1tVfqh.js";import{E as k}from"./el-text-CLWE0mUm.js";import{a as S,E as V}from"./el-tab-pane-xcqYouKU.js";import{E,a as N}from"./el-form-BY8piFS2.js";import{E as A}from"./el-input-number-CfcpPMpr.js";import"./el-tag-DcMbxLLg.js";import{E as C}from"./el-popper-DVoWBu_3.js";import"./el-virtual-list-Drl4IGmp.js";import{E as T}from"./el-select-v2-CJw7ZO42.js";import{E as U}from"./el-checkbox-DjLAvZXr.js";import"./el-tooltip-l0sNRNKZ.js";import{E as z}from"./el-switch-C-DLgt5X.js";import{_ as I}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as L}from"./useTable-BezX3TfM.js";import{u as P}from"./useIcon-k-uSyz6l.js";import{e as W,c as H,u as M}from"./index-BuRY9bVn.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{_ as F}from"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import{_ as D}from"./PageMonit.vue_vue_type_script_setup_true_lang-CRgNw5qN.js";import{a as O}from"./index-lpN3i-fa.js";import"./el-card-CuEws33_.js";import"./strings-Dm4Pnsdt.js";import"./castArray-uOT054sj.js";import"./raf-BoCEWvzN.js";import"./useInput-SkgDzq11.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-table-column-B5hg3WH6.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./index-Dz8ZrwBc.js";import"./el-divider-D9UCOo44.js";import"./el-radio-group-evFfsZkP.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-Cyn7ZzlS.js";import"./index-D4TGn7aE.js";const B={class:"mb-10px"},G={style:{position:"relative",top:"12px"}};function J(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!j(e)}const K=t({__name:"ScheduledTask copy",setup(t){const j=P({icon:"iconoir:search"}),{t:K}=a(),Q=l(""),Z=()=>{se()},$=o([{field:"selection",type:"selection",width:"55"},{field:"name",label:K("task.taskName"),minWidth:30},{field:"cycle",label:K("task.taskCycle")+"(h)",minWidth:20},{field:"type",label:K("task.typeTask"),minWidth:20},{field:"lastTime",label:K("task.lastTime"),minWidth:40,formatter:(e,t,a)=>""==a?"-":a},{field:"nextTime",label:K("task.nextTime"),minWidth:40,formatter:(e,t,a)=>""==a||0==e.state?"-":a},{field:"state",label:K("common.state"),minWidth:20,formatter:(e,t,a)=>{if(null==a)return s("div",null,null);let l="",o="";return 1==a?(l="#2eb98a",o=K("common.on")):(l="red",o=K("common.statusStop")),s(w,{gutter:20},{default:()=>[s(x,{span:1},{default:()=>[s(i,{icon:"clarity:circle-solid",color:l},null)]}),s(x,{span:5},{default:()=>[s(k,{type:"info"},J(o)?o:{default:()=>[o]})]})]})}},{field:"action",label:K("tableDemo.action"),minWidth:40,formatter:(e,t,a)=>{let l,o,i;return e.type,s(r,null,["page_monitoring"===e.id?s(n,{type:"success",onClick:()=>Ve(e)},J(l=K("common.edit"))?l:{default:()=>[l]}):s(r,null,[s(n,{type:"success",onClick:()=>ce(e)},J(o=K("common.edit"))?o:{default:()=>[o]}),s(n,{type:"danger",onClick:()=>ge(e)},J(i=K("common.delete"))?i:{default:()=>[i]})])])}}]),{tableRegister:q,tableState:X,tableMethods:Y}=L({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=X,a=await W(Q.value,e.value,t.value);return{list:a.data.list,total:a.data.total}},immediate:!0}),{loading:ee,dataList:te,total:ae,currentPage:le,pageSize:oe}=X;oe.value=20;const{getList:se,getElTableExpose:ie}=Y;function ne(){return{background:"var(--el-fill-color-light)"}}const re=l(!1);let de=K("task.addTask");const ue=()=>{re.value=!1};let pe="",me=l(!0);const ce=async e=>{"Scan"==e.type?pe=e.id:xe.value=!0,de=K("common.edit")},fe=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await he()},ge=async e=>{window.confirm("Are you sure you want to delete the selected data?")&&await ye(e)},ve=l(!1),ye=async e=>{ve.value=!0;try{await H([e.id]);ve.value=!1,se()}catch(t){ve.value=!1,se()}},_e=l([]),he=async()=>{const e=await ie(),t=(null==e?void 0:e.getSelectionRows())||[];_e.value=t.map((e=>e.id)),ve.value=!0;try{await H(_e.value);ve.value=!1,se()}catch(a){ve.value=!1,se()}};d((()=>{be(),window.addEventListener("resize",be)}));const je=l(0),be=()=>{const e=window.innerHeight||document.documentElement.clientHeight;je.value=.75*e},xe=l(!1),we=l(!1),ke=l(!1),Se=o({hour:24,allNode:!0,node:[],state:!0}),Ve=async e=>{Se.hour=e.cycle,Se.allNode=e.allNode,Se.node=e.node,Se.state=e.state,we.value=!0},Ee=o([]),Ne=l(!1),Ae=l(!1),Ce=e=>{Ne.value=!1,e?(Se.allNode=!0,Se.node=[],Ee.forEach((e=>Se.node.push(e.value)))):(Se.allNode=!1,Se.node=[])};return(async()=>{const e=await O();e.data.list.length>0?(Ae.value=!1,e.data.list.forEach((e=>{Ee.push({value:e,label:e})}))):(Ae.value=!0,b.warning(K("node.onlineNodeMsg")))})(),(t,a)=>(u(),p(c(e),null,{default:m((()=>[s(c(w),null,{default:m((()=>[s(c(x),{span:1},{default:m((()=>[s(c(k),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:m((()=>[f(g(c(K)("task.taskName"))+":",1)])),_:1})])),_:1}),s(c(x),{span:5},{default:m((()=>[s(c(v),{modelValue:Q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value=e),placeholder:c(K)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),s(c(x),{span:5,style:{position:"relative",left:"16px"}},{default:m((()=>[s(c(y),{type:"primary",icon:c(j),style:{height:"100%"},onClick:Z},{default:m((()=>[f("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),s(c(w),null,{default:m((()=>[s(c(x),{style:{position:"relative",top:"16px"}},{default:m((()=>[_("div",B,[s(c(n),{type:"danger",loading:ve.value,onClick:fe},{default:m((()=>[f(g(c(K)("task.delTask")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),_("div",G,[s(c(I),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:c(oe),"onUpdate:pageSize":a[1]||(a[1]=e=>h(oe)?oe.value=e:null),currentPage:c(le),"onUpdate:currentPage":a[2]||(a[2]=e=>h(le)?le.value=e:null),columns:$,data:c(te),stripe:"",border:!0,loading:c(ee),"max-height":je.value,resizable:!0,pagination:{total:c(ae),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:c(q),headerCellStyle:ne,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])]),s(c(R),{modelValue:re.value,"onUpdate:modelValue":a[3]||(a[3]=e=>re.value=e),title:c(de),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:m((()=>[s(F,{closeDialog:ue,getList:c(se),create:c(me),taskid:c(pe),schedule:!0},null,8,["getList","create","taskid"])])),_:1},8,["modelValue","title"]),s(c(R),{modelValue:we.value,"onUpdate:modelValue":a[9]||(a[9]=e=>we.value=e),title:c(K)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:m((()=>[s(c(S),{type:"card"},{default:m((()=>[s(c(V),{label:c(K)("router.configuration")},{default:m((()=>[s(c(E),{model:Se,"label-width":"100px","status-icon":"",ref:"ruleFormRef"},{default:m((()=>[s(c(C),{content:c(K)("task.selectNodeMsg"),placement:"top"},{default:m((()=>[s(c(N),{label:c(K)("task.nodeSelect"),prop:"node"},{default:m((()=>[s(c(T),{modelValue:Se.node,"onUpdate:modelValue":a[5]||(a[5]=e=>Se.node=e),filterable:"",options:Ee,placeholder:"Please select node",style:{width:"80%"},multiple:"","tag-type":"success","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":7},{header:m((()=>[s(c(U),{modelValue:Se.allNode,"onUpdate:modelValue":a[4]||(a[4]=e=>Se.allNode=e),disabled:Ae.value,indeterminate:Ne.value,onChange:Ce},{default:m((()=>[f(" All ")])),_:1},8,["modelValue","disabled","indeterminate"])])),_:1},8,["modelValue","options"])])),_:1},8,["label"])])),_:1},8,["content"]),s(c(N),{label:c(K)("project.cycle"),prop:"type"},{default:m((()=>[s(c(A),{modelValue:Se.hour,"onUpdate:modelValue":a[6]||(a[6]=e=>Se.hour=e),min:1,"controls-position":"right",size:"small"},null,8,["modelValue"]),s(c(k),{style:{position:"relative",left:"16px"}},{default:m((()=>[f("Hour")])),_:1})])),_:1},8,["label"]),s(c(N),{label:c(K)("common.state")},{default:m((()=>[s(c(z),{modelValue:Se.state,"onUpdate:modelValue":a[7]||(a[7]=e=>Se.state=e),"inline-prompt":"","active-text":c(K)("common.switchAction"),"inactive-text":c(K)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),s(c(w),null,{default:m((()=>[s(c(x),{span:2,offset:8},{default:m((()=>[s(c(N),null,{default:m((()=>[s(c(y),{type:"primary",onClick:a[8]||(a[8]=e=>(async()=>{ke.value=!0,await M(Se.hour,Se.node,Se.allNode,Se.state),ke.value=!1,se()})()),loading:ke.value},{default:m((()=>[f(g(c(K)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["label"]),s(c(V),{label:c(K)("task.data")},{default:m((()=>[s(D)])),_:1},8,["label"])])),_:1})])),_:1},8,["modelValue","title"])])),_:1}))}});export{K as default};
