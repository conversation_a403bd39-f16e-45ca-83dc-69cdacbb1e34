import{d as e,s as t,V as o,r as a,o as s,c as i,e as l,w as r,a as m,f as n,t as p,A as d,B as u,z as f,F as j,l as c,_}from"./index-3XfDPlIS.js";import{a as g,E as y}from"./el-col-CN1tVfqh.js";import{E as x,a as b}from"./el-form-BY8piFS2.js";import{E as w}from"./el-text-CLWE0mUm.js";import{E as h}from"./el-divider-D9UCOo44.js";import{E as v}from"./el-card-CuEws33_.js";import z from"./notification-ByzagbP0.js";import C from"./Deduplication-D61C6wnO.js";import{g as M,s as V}from"./index-CNICMfYX.js";import{j as E,o as k,T as A}from"./index-C7jW5IRr.js";import"./castArray-uOT054sj.js";import"./el-radio-group-evFfsZkP.js";import"./el-switch-C-DLgt5X.js";import"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import"./refs-CSSW5x_d.js";import"./el-popper-DVoWBu_3.js";import"./Table.vue_vue_type_script_lang-B7lrRql4.js";import"./el-table-column-B5hg3WH6.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-DcMbxLLg.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./index-DkclijAA.js";import"./useTable-BezX3TfM.js";import"./el-input-number-CfcpPMpr.js";import"./index-Dz8ZrwBc.js";const D=_(e({__name:"system",setup(e){const _=[E(),k],{t:D}=c(),I=t({timezone:"",ModulesConfig:""});o((async()=>{try{const e=await M();200==e.code&&(I.timezone=e.data.timezone,I.ModulesConfig=e.data.ModulesConfig)}catch(e){}}));const B=async()=>{window.confirm("Do you want to save the data?")&&await F()},F=async()=>{H.value=!0;(await V(I.timezone,I.ModulesConfig)).code,H.value=!1},H=a(!1);return(e,t)=>(s(),i(j,null,[l(m(v),{shadow:"never",class:"mb-20px"},{header:r((()=>[l(m(g),null,{default:r((()=>[l(m(y),{span:3,style:{height:"100%"}},{default:r((()=>[n("span",null,p(m(D)("configuration.system")),1)])),_:1})])),_:1})])),default:r((()=>[l(m(x),{model:I,"label-width":"auto",style:{"max-width":"600px"}},{default:r((()=>[l(m(b),{label:m(D)("configuration.timezone")},{default:r((()=>[l(m(d),{modelValue:I.timezone,"onUpdate:modelValue":t[0]||(t[0]=e=>I.timezone=e)},null,8,["modelValue"])])),_:1},8,["label"]),l(m(b),{label:"Module Config"},{default:r((()=>[l(m(A),{modelValue:I.ModulesConfig,"onUpdate:modelValue":t[1]||(t[1]=e=>I.ModulesConfig=e),extensions:_,autofocus:!0,"indent-with-tab":!0,"tab-size":2,style:{height:"550px",width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),l(m(g),null,{default:r((()=>[l(m(y),{span:12,offset:2},{default:r((()=>[l(m(u),{type:"primary",onClick:B,loading:H.value},{default:r((()=>[f("Save")])),_:1},8,["loading"]),l(m(h),{direction:"vertical"}),l(m(w),{size:"small",type:"danger"},{default:r((()=>[f(p(m(D)("configuration.threadMsg")),1)])),_:1})])),_:1})])),_:1})])),_:1}),l(z),l(C)],64))}}),[["__scopeId","data-v-bc860df1"]]);export{D as default};
