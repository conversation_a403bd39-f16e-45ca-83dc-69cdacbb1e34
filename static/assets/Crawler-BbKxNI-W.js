import{d as e,s as t,r as a,v as l,A as i,B as s,H as o,o as r,c as n,e as p,a as d,w as u,I as m,F as c,l as g,ai as j,_ as f}from"./index-C6fb_XFi.js";import{u as h}from"./useTable-CijeIiBB.js";import{E as b}from"./el-card-B37ahJ8o.js";import{E as _}from"./el-pagination-FWx5cl5J.js";import{E as v}from"./el-tag-C_oEQYGz.js";import"./el-select-vbM8Rxr1.js";import"./el-popper-CeVwVUf9.js";import{E as y,a as x}from"./el-col-Dl4_4Pn5.js";import{_ as S}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as w}from"./useCrudSchemas-CEXr0LRM.js";import{a as E,d as C,i as k}from"./index-BBupWySc.js";import z from"./Csearch-B51tl_vU.js";import"./index-BWEJ0epC.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./tree-BfZhwLPs.js";import"./index-CnCQNuY4.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import"./el-text-BnUG9HvL.js";import"./el-divider-Bw95UAdD.js";import"./el-tree-select-D8i5b8X5.js";import"./index-w1A_Rrgh.js";/* empty css                */import"./el-switch-Bh7JeorW.js";import"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import"./useIcon-BxqaCND-.js";import"./exportData.vue_vue_type_script_setup_true_lang-C3cw41xZ.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-space-CdSK6Ce1.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const V=f(e({__name:"Crawler",props:{projectList:{}},setup(e){const{t:f}=g(),V=t({}),A=[{keyword:"url",example:'url="http://example.com"',explain:f("searchHelp.url")},{keyword:"method",example:'method="POST"',explain:f("searchHelp.method")},{keyword:"body",example:'body="username=admin"',explain:f("searchHelp.crawlerBody")},{keyword:"project",example:'project="Hackerone"',explain:f("searchHelp.project")}],H=a(""),L=e=>{H.value=e,q()},T=t([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:f("tableDemo.index"),type:"index",minWidth:55},{field:"method",label:"Method",minWidth:100},{field:"url",label:"URL",minWidth:500},{field:"body",label:f("crawler.postParameter"),minWidth:300},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,o)=>{null==o&&(o=[]),V[e.id]||(V[e.id]={inputVisible:!1,inputValue:"",inputRef:a(null)});const r=V[e.id],n=async()=>{r.inputValue&&(o.push(r.inputValue),E(e.id,I,r.inputValue)),r.inputVisible=!1,r.inputValue=""};return l(x,{},(()=>[...o.map((t=>l(y,{span:24,key:t},(()=>[l("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||U("tags",t)})(e,t)},[l(v,{closable:!0,onClose:()=>(async t=>{const a=o.indexOf(t);a>-1&&o.splice(a,1),C(e.id,I,t)})(t)},(()=>t))])])))),l(y,{span:24},r.inputVisible?()=>l(i,{ref:r.inputRef,modelValue:r.inputValue,"onUpdate:modelValue":e=>r.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&n()},onBlur:n}):()=>l(s,{class:"button-new-tag",size:"small",onClick:()=>(r.inputVisible=!0,void j((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:f("asset.time"),minWidth:"170"}]);let I="crawler";const P=a([]),U=(e,t)=>{const a=`${e}=${t}`;P.value=[...P.value,a]},O=e=>{if(P.value){const[t,a]=e.split("=");t in ee&&Array.isArray(ee[t])&&(ee[t]=ee[t].filter((e=>e!==a)),0===ee[t].length&&delete ee[t]),P.value=P.value.filter((t=>t!==e))}};T.forEach((e=>{e.hidden=e.hidden??!1}));let R=a(!1);const W=({field:e,hidden:t})=>{const a=T.findIndex((t=>t.field===e));-1!==a&&(T[a].hidden=t),(()=>{const e=T.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=R.value,localStorage.setItem(`columnConfig_${I}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${I}`)||"{}");T.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),R.value=e.statisticsHidden})();const{allSchemas:D}=w(T),{tableRegister:N,tableState:F,tableMethods:B}=h({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=F,a=await k(H.value,e.value,t.value,ee);return{list:a.data.list,total:a.data.total}},immediate:!1}),{loading:$,dataList:M,total:J,currentPage:K,pageSize:G}=F,{getList:q,getElTableExpose:X}=B;function Y(){return{background:"var(--el-fill-color-light)"}}o((()=>{Z(),window.addEventListener("resize",Z)}));const Q=a(0),Z=()=>{const e=window.innerHeight||document.documentElement.clientHeight;Q.value=.7*e},ee=t({}),te=(e,t)=>{Object.keys(ee).forEach((e=>delete ee[e])),Object.assign(ee,t),H.value=e,q()},ae=()=>ee;return(e,t)=>(r(),n(c,null,[p(z,{getList:d(q),handleSearch:L,searchKeywordsData:A,index:"crawler",dynamicTags:P.value,handleClose:O,getElTableExpose:d(X),projectList:e.$props.projectList,handleFilterSearch:te,crudSchemas:T,onUpdateColumnVisibility:W,searchResultCount:d(J),getFilter:ae},null,8,["getList","dynamicTags","getElTableExpose","projectList","crudSchemas","searchResultCount"]),p(d(x),null,{default:u((()=>[p(d(y),null,{default:u((()=>[p(d(b),null,{default:u((()=>[p(d(S),{pageSize:d(G),"onUpdate:pageSize":t[0]||(t[0]=e=>m(G)?G.value=e:null),currentPage:d(K),"onUpdate:currentPage":t[1]||(t[1]=e=>m(K)?K.value=e:null),columns:d(D).tableColumns,data:d(M),"max-height":Q.value,stripe:"",border:!0,loading:d($),resizable:!0,onRegister:d(N),headerCellStyle:Y,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!1},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),p(d(y),{":span":24},{default:u((()=>[p(d(b),null,{default:u((()=>[p(d(_),{pageSize:d(G),"onUpdate:pageSize":t[2]||(t[2]=e=>m(G)?G.value=e:null),currentPage:d(K),"onUpdate:currentPage":t[3]||(t[3]=e=>m(K)?K.value=e:null),"page-sizes":[20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:d(J)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-196961db"]]);export{V as default};
