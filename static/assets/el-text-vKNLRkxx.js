import{Z as a,ca as e,d as s,aF as t,a7 as l,a8 as n,aN as i,o as p,i as r,w as u,a9 as c,n as m,a as o,aH as d,aI as y,aa as f,ah as g}from"./index-DfJTpRkj.js";const x=a({type:{type:String,values:["primary","success","info","warning","danger",""],default:""},size:{type:String,values:e,default:""},truncated:{type:Boolean},lineClamp:{type:[String,Number]},tag:{type:String,default:"span"}}),v=s({name:"ElText"});const S=g(f(s({...v,props:x,setup(a){const e=a,s=t(),f=l("text"),g=n((()=>[f.b(),f.m(e.type),f.m(s.value),f.is("truncated",e.truncated),f.is("line-clamp",!i(e.lineClamp))]));return(a,e)=>(p(),r(y(a.tag),{class:m(o(g)),style:d({"-webkit-line-clamp":a.lineClamp})},{default:u((()=>[c(a.$slots,"default")])),_:3},8,["class","style"]))}}),[["__file","text.vue"]]));export{S as E};
