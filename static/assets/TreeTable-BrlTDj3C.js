import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,a,l,s as o,e as i,G as r,o as s,i as p,w as n,I as m,J as d,_ as c}from"./index-C6fb_XFi.js";import{_ as u}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{b}from"./index-Bh6CT4kq.js";import{E as g}from"./el-tag-C_oEQYGz.js";import{u as j}from"./useTable-CijeIiBB.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";const f=c(t({__name:"TreeTable",setup(t){const{tableRegister:c,tableState:f}=j({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=f,l=await b({pageIndex:a(e),pageSize:a(t)});return{list:l.data.list,total:l.data.total}}}),{loading:_,dataList:y,total:D,currentPage:x,pageSize:v}=f,{t:w}=l(),S=o([{field:"selection",type:"selection"},{field:"index",label:w("tableDemo.index"),type:"index"},{field:"content",label:w("tableDemo.header"),children:[{field:"title",label:w("tableDemo.title")},{field:"author",label:w("tableDemo.author")},{field:"display_time",label:w("tableDemo.displayTime")},{field:"importance",label:w("tableDemo.importance"),formatter:(e,t,a)=>i(g,{type:1===a?"success":2===a?"warning":"danger"},{default:()=>[w(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")]})},{field:"pageviews",label:w("tableDemo.pageviews")}]},{field:"action",label:w("tableDemo.action"),slots:{default:e=>{let t;return i(r,{type:"primary",onClick:()=>h(e)},"function"==typeof(a=t=w("tableDemo.action"))||"[object Object]"===Object.prototype.toString.call(a)&&!d(a)?t:{default:()=>[t]});var a}}}]),h=e=>{};return(t,l)=>(s(),p(a(e),{title:`${a(w)("router.treeTable")} ${a(w)("tableDemo.example")}`},{default:n((()=>[i(a(u),{pageSize:a(v),"onUpdate:pageSize":l[0]||(l[0]=e=>m(v)?v.value=e:null),currentPage:a(x),"onUpdate:currentPage":l[1]||(l[1]=e=>m(x)?x.value=e:null),columns:S,data:a(y),"row-key":"id",loading:a(_),sortable:"",pagination:{total:a(D)},onRegister:a(c)},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister"])])),_:1},8,["title"]))}}),[["__scopeId","data-v-ef67ff34"]]);export{f as default};
