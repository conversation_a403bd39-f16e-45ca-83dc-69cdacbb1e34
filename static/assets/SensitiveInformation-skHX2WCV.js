import{d as e,r as t,s as a,e as l,L as i,E as o,v as s,A as n,B as r,G as u,F as p,H as d,o as m,c,a as f,w as v,I as g,f as h,t as y,z as j,R as x,J as b,l as _,ai as w,_ as S}from"./index-C6fb_XFi.js";import{u as k}from"./useTable-CijeIiBB.js";import{E as V}from"./el-card-B37ahJ8o.js";import{E as C}from"./el-pagination-FWx5cl5J.js";import{E}from"./el-tag-C_oEQYGz.js";import{E as z,a as A}from"./el-select-vbM8Rxr1.js";import"./el-popper-CeVwVUf9.js";import{E as I,a as L}from"./el-col-Dl4_4Pn5.js";import{E as H}from"./el-drawer-DSWQeoTQ.js";import{E as R,a as U}from"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./el-tooltip-l0sNRNKZ.js";import{_ as W}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{_ as O}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as T}from"./useCrudSchemas-CEXr0LRM.js";import{u as N,m as P,a as D,d as F,n as K,o as G,p as $}from"./index-BBupWySc.js";import B from"./Csearch-B51tl_vU.js";import"./index-BWEJ0epC.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./refs-3HtnmaOD.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./index-ghAu5K8t.js";import"./tree-BfZhwLPs.js";import"./index-CnCQNuY4.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import"./el-text-BnUG9HvL.js";import"./el-divider-Bw95UAdD.js";import"./el-tree-select-D8i5b8X5.js";import"./index-w1A_Rrgh.js";/* empty css                */import"./el-switch-Bh7JeorW.js";import"./useIcon-BxqaCND-.js";import"./exportData.vue_vue_type_script_setup_true_lang-C3cw41xZ.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-space-CdSK6Ce1.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const J={style:{whiteSpace:"pre-line"}},M=["onClick"],Y=["onClick"];function q(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!b(e)}const X=S(e({__name:"SensitiveInformation",props:{projectList:{}},setup(e){const{t:b}=_(),S=[{keyword:"url",example:'url="http://example.com"',explain:b("searchHelp.url")},{keyword:"sname",example:'sname="twilio_account_sid"',explain:b("searchHelp.sname")},{keyword:"info",example:'info="api-key-example"',explain:b("searchHelp.sinfo")},{keyword:"project",example:'project="Hackerone"',explain:b("searchHelp.project")},{keyword:"md5",example:'md5=="1d49e5e190f7a38ab498e28e6578f64f"',explain:b("searchHelp.sensMd5")},{keyword:"level",example:'level=="rad"',explain:b("searchHelp.sensLevel")}],X=t(""),Q=e=>{X.value=e,Ee()},Z=a({}),ee=a([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:b("tableDemo.index"),type:"index",minWidth:55},{field:"url",label:"URL",minWidth:200},{field:"name",label:b("sensitiveInformation.sensitiveName"),minWidth:150},{field:"color",label:"Level",minWidth:50,columnKey:"color",formatter:(e,t,a)=>{if(a)return l(i,{icon:"clarity:circle-solid",color:a,style:"transform: translateY(-35%)"},null)},filters:[{text:"Red",value:"red"},{text:"Green",value:"green"},{text:"Cyan",value:"cyan"},{text:"Yellow",value:"yellow"},{text:"Orange",value:"orange"},{text:"Gray",value:"gray"},{text:"Pink",value:"pink"},{text:"Null",value:"null"}]},{field:"match",label:"Info",minWidth:150,formatter:(e,t,a)=>{if(!a)return;const i=a.map(((e,t)=>l("div",{key:t},[e])));return l(o,{height:"100px"},{default:()=>[l("div",{class:"scrollbar-demo-item"},[i])]})}},{field:"status",label:b("common.state"),minWidth:100,columnKey:"status",formatter:(e,t,a)=>{let i;if(e.id.includes("//"))return;null==e.status&&(e.status=1);const o=[{value:1,label:b("common.unprocessed")},{value:2,label:b("common.processing")},{value:3,label:b("common.ignored")},{value:4,label:b("common.suspected")},{value:5,label:b("common.confirmed")}];return l(A,{modelValue:e.status,"onUpdate:modelValue":async t=>{try{e.status=t,N(e.id,"SensitiveResult",t)}catch(a){}}},q(i=o.map((e=>l(z,{key:e.value,label:e.label,value:e.value},null))))?i:{default:()=>[i]})},filters:[{text:b("common.unprocessed"),value:1},{text:b("common.processing"),value:2},{text:b("common.ignored"),value:3},{text:b("common.suspected"),value:4},{text:b("common.confirmed"),value:5}]},{field:"tags",label:"TAG",fit:"true",formatter:(e,a,l)=>{if(e.id.includes("//"))return;null==l&&(l=[]),Z[e.id]||(Z[e.id]={inputVisible:!1,inputValue:"",inputRef:t(null)});const i=Z[e.id],o=async()=>{i.inputValue&&(l.push(i.inputValue),D(e.id,te,i.inputValue)),i.inputVisible=!1,i.inputValue=""};return s(L,{},(()=>[...l.map((t=>s(I,{span:24,key:t},(()=>[s("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||Ae("tags",t)})(e,t)},[s(E,{closable:!0,onClose:()=>(async t=>{const a=l.indexOf(t);a>-1&&l.splice(a,1),F(e.id,te,t)})(t)},(()=>t))])])))),s(I,{span:24},i.inputVisible?()=>s(n,{ref:i.inputRef,modelValue:i.inputValue,"onUpdate:modelValue":e=>i.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&o()},onBlur:o}):()=>s(r,{class:"button-new-tag",size:"small",onClick:()=>(i.inputVisible=!0,void w((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:b("asset.time"),minWidth:200},{field:"action",label:b("tableDemo.action"),formatter:(e,t,a)=>{if(e.body_id){let t;return l(p,null,[l(u,{type:"primary",onClick:()=>be(e.body_id)},q(t=b("asset.detail"))?t:{default:()=>[t]})])}},minWidth:100}]);let te="SensitiveResult";ee.forEach((e=>{e.hidden=e.hidden??!1}));let ae=t(!1);const le=({field:e,hidden:t})=>{const a=ee.findIndex((t=>t.field===e));-1!==a&&(ee[a].hidden=t),(()=>{const e=ee.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=ae.value,localStorage.setItem(`columnConfig_${te}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${te}`)||"{}");ee.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),ae.value=e.statisticsHidden})();const ie=a({}),{allSchemas:oe}=T(ee),{tableRegister:se,tableState:ne,tableMethods:re}=k({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=ne,a=await K(X.value,e.value,t.value,ie);return{list:a.data.list,total:a.data.total}},immediate:!1}),{loading:ue,dataList:pe,total:de,currentPage:me,pageSize:ce}=ne,{getList:fe,getElTableExpose:ve}=re;function ge(){return{background:"var(--el-fill-color-light)"}}d((()=>{ye(),window.addEventListener("resize",ye)}));const he=t(0),ye=()=>{const e=window.innerHeight||document.documentElement.clientHeight;he.value=.7*e},je=t(!1),xe=t(""),be=async e=>{e.replace("md5==","");const t=await P(e);xe.value=t.data.body,je.value=!0},_e=async e=>{Object.assign(ie,e),Ee()},we=(e,t)=>{Object.assign(ie,t),X.value=e,Ee()},Se=t(!1),ke=()=>{Se.value=!0},Ve=t([]),Ce=async()=>{try{const e=await $(X.value,ie);e&&e.data&&Array.isArray(e.data.list)&&(Ve.value=e.data.list.map((e=>({name:e.name,color:e.color,count:e.count}))))}catch(e){}},Ee=async()=>{try{await Promise.all([fe(),Ce()])}catch(e){}},ze=t([]),Ae=(e,t)=>{const a=`${e}=${t}`;ze.value=[...ze.value,a]},Ie=e=>{if(ze.value){const[t,a]=e.split("=");t in ie&&Array.isArray(ie[t])&&(ie[t]=ie[t].filter((e=>e!==a)),0===ie[t].length&&delete ie[t]),ze.value=ze.value.filter((t=>t!==e))}},Le=()=>ie,He=t(!1),Re=t([]),Ue=t("");return(e,t)=>(m(),c(p,null,[l(B,{getList:f(fe),handleSearch:Q,searchKeywordsData:S,index:"SensitiveResult",getElTableExpose:f(ve),handleFilterSearch:we,projectList:e.$props.projectList,openAggregation:ke,dynamicTags:ze.value,handleClose:Ie,crudSchemas:ee,onUpdateColumnVisibility:le,searchResultCount:f(de),getFilter:Le},null,8,["getList","getElTableExpose","projectList","dynamicTags","crudSchemas","searchResultCount"]),l(f(L),null,{default:v((()=>[l(f(I),null,{default:v((()=>[l(f(V),null,{default:v((()=>[l(f(O),{pageSize:f(ce),"onUpdate:pageSize":t[0]||(t[0]=e=>g(ce)?ce.value=e:null),currentPage:f(me),"onUpdate:currentPage":t[1]||(t[1]=e=>g(me)?me.value=e:null),columns:f(oe).tableColumns,data:f(pe),stripe:"",rowKey:"id",border:!0,"max-height":he.value,loading:f(ue),resizable:!0,onRegister:f(se),onFilterChange:_e,headerCellStyle:ge,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!0,showAfter:0,popperOptions:{},popperClass:"test",placement:"top",hideAfter:0,disabled:!1},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),l(f(I),{":span":24},{default:v((()=>[l(f(V),null,{default:v((()=>[l(f(C),{pageSize:f(ce),"onUpdate:pageSize":t[2]||(t[2]=e=>g(ce)?ce.value=e:null),currentPage:f(me),"onUpdate:currentPage":t[3]||(t[3]=e=>g(me)?me.value=e:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:f(de)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1}),l(f(W),{modelValue:je.value,"onUpdate:modelValue":t[4]||(t[4]=e=>je.value=e),title:f(b)("asset.detail"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},width:"70%","max-height":he.value},{default:v((()=>[l(f(o),{"max-height":he.value},{default:v((()=>[h("div",J,y(xe.value),1)])),_:1},8,["max-height"])])),_:1},8,["modelValue","title","max-height"]),l(f(H),{modelValue:Se.value,"onUpdate:modelValue":t[5]||(t[5]=e=>Se.value=e),title:f(b)("sensitiveInformation.sensAggre"),direction:"rtl",size:"30%"},{default:v((()=>[l(f(R),{data:Ve.value},{default:v((()=>[l(f(U),{prop:"name",label:f(b)("sensitiveInformation.sensitiveName"),width:"180"},{default:v((e=>[h("div",{style:{display:"flex","align-items":"center"},onClick:t=>Ae("sname",e.row.name)},[l(f(E),null,{default:v((()=>[j(y(e.row.name),1)])),_:2},1024)],8,M)])),_:1},8,["label"]),l(f(U),{prop:"color",label:"color",width:"100"},{default:v((e=>[l(f(E),{color:e.row.color,round:"",effect:"plain",size:"small",style:{width:"20px"}},null,8,["color"])])),_:1}),l(f(U),{prop:"count",label:f(b)("common.quantity"),width:"130"},null,8,["label"]),l(f(U),{label:f(b)("tableDemo.operate"),width:"180"},{default:v((e=>[h("div",{style:{display:"flex","align-items":"center"},onClick:t=>(async e=>{Ue.value=e;const t=await G(e,X.value,ie);Re.value=t.data.list,He.value=!0})(e.row.name)},[l(f(r),{type:"success"},{default:v((()=>[j("info")])),_:1})],8,Y)])),_:1},8,["label"])])),_:1},8,["data"])])),_:1},8,["modelValue","title"]),l(f(W),{modelValue:He.value,"onUpdate:modelValue":t[6]||(t[6]=e=>He.value=e),title:Ue.value},{default:v((()=>[(m(!0),c(p,null,x(Re.value,((e,t)=>(m(),c("div",{key:t},[h("p",null,y(e),1)])))),128))])),_:1},8,["modelValue","title"])],64))}}),[["__scopeId","data-v-013c495a"]]);export{X as default};
