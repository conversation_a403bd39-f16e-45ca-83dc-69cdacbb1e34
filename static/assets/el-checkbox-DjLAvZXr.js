import{bF as e,bG as a,bH as l,bI as n,ay as t,a1 as u,a4 as o,a3 as i,a$ as s,ab as d,a8 as r,aN as c,ba as v,aB as b,aQ as m,O as h,aC as p,Y as f,r as x,a5 as k,bJ as g,bq as y,bK as C,aF as L,bb as V,bL as B,d as S,bg as I,a7 as F,o as E,i as z,w as N,f as w,n as O,a as D,Q as $,c as _,I as G,af as U,bM as j,a9 as R,F as A,z as H,t as K,j as M,aI as Q,aa as q,aH as J,Z as P,$ as Y,a6 as Z,bv as T,ah as W,ai as X}from"./index-3XfDPlIS.js";import{h as ee,i as ae}from"./index-tjM0-mlU.js";function le(n,t){return function(n,t,u){for(var o=-1,i=t.length,s={};++o<i;){var d=t[o],r=e(n,d);u(r,d)&&a(s,l(d,n),r)}return s}(n,t,(function(e,a){return ee(n,a)}))}var ne=n((function(e,a){return null==e?{}:le(e,a)}));const te={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},value:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueValue:{type:[String,Number],default:void 0},falseValue:{type:[String,Number],default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},controls:{type:String,default:void 0},border:Boolean,size:t,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0}},ue={[u]:e=>o(e)||i(e)||s(e),change:e=>o(e)||i(e)||s(e)},oe=Symbol("checkboxGroupContextKey"),ie=(e,{model:a,isLimitExceeded:l,hasOwnLabel:n,isDisabled:t,isLabeledByFormItem:u})=>{const o=d(oe,void 0),{formItem:i}=b(),{emit:s}=m();function c(a){var l,n,t,u;return[!0,e.trueValue,e.trueLabel].includes(a)?null==(n=null!=(l=e.trueValue)?l:e.trueLabel)||n:null!=(u=null!=(t=e.falseValue)?t:e.falseLabel)&&u}const v=r((()=>(null==o?void 0:o.validateEvent)||e.validateEvent));return h((()=>e.modelValue),(()=>{v.value&&(null==i||i.validate("change").catch((e=>p())))})),{handleChange:function(e){if(l.value)return;const a=e.target;s("change",c(a.checked),e)},onClickRoot:async function(o){if(!l.value&&!n.value&&!t.value&&u.value){o.composedPath().some((e=>"LABEL"===e.tagName))||(a.value=c([!1,e.falseValue,e.falseLabel].includes(a.value)),await f(),function(e,a){s("change",c(e),a)}(a.value,o))}}}},se=(e,a)=>{const{formItem:l}=b(),{model:n,isGroup:t,isLimitExceeded:o}=(e=>{const a=x(!1),{emit:l}=m(),n=d(oe,void 0),t=r((()=>!1===c(n))),o=x(!1),i=r({get(){var l,u;return t.value?null==(l=null==n?void 0:n.modelValue)?void 0:l.value:null!=(u=e.modelValue)?u:a.value},set(e){var s,d;t.value&&k(e)?(o.value=void 0!==(null==(s=null==n?void 0:n.max)?void 0:s.value)&&e.length>(null==n?void 0:n.max.value)&&e.length>i.value.length,!1===o.value&&(null==(d=null==n?void 0:n.changeEvent)||d.call(n,e))):(l(u,e),a.value=e)}});return{model:i,isGroup:t,isLimitExceeded:o}})(e),{isFocused:i,isChecked:h,checkboxButtonSize:p,checkboxSize:f,hasOwnLabel:S,actualValue:I}=((e,a,{model:l})=>{const n=d(oe,void 0),t=x(!1),u=r((()=>g(e.value)?e.label:e.value)),o=r((()=>{const a=l.value;return s(a)?a:k(a)?y(u.value)?a.map(C).some((e=>ae(e,u.value))):a.map(C).includes(u.value):null!=a?a===e.trueValue||a===e.trueLabel:!!a}));return{checkboxButtonSize:L(r((()=>{var e;return null==(e=null==n?void 0:n.size)?void 0:e.value})),{prop:!0}),isChecked:o,isFocused:t,checkboxSize:L(r((()=>{var e;return null==(e=null==n?void 0:n.size)?void 0:e.value}))),hasOwnLabel:r((()=>!!a.default||!g(u.value))),actualValue:u}})(e,a,{model:n}),{isDisabled:F}=(({model:e,isChecked:a})=>{const l=d(oe,void 0),n=r((()=>{var n,t;const u=null==(n=null==l?void 0:l.max)?void 0:n.value,o=null==(t=null==l?void 0:l.min)?void 0:t.value;return!c(u)&&e.value.length>=u&&!a.value||!c(o)&&e.value.length<=o&&a.value}));return{isDisabled:v(r((()=>(null==l?void 0:l.disabled.value)||n.value))),isLimitDisabled:n}})({model:n,isChecked:h}),{inputId:E,isLabeledByFormItem:z}=V(e,{formItemContext:l,disableIdGeneration:S,disableIdManagement:t}),{handleChange:N,onClickRoot:w}=ie(e,{model:n,isLimitExceeded:o,hasOwnLabel:S,isDisabled:F,isLabeledByFormItem:z});var O,D;return e.checked&&(k(n.value)&&!n.value.includes(I.value)?n.value.push(I.value):n.value=null==(D=null!=(O=e.trueValue)?O:e.trueLabel)||D),B({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},r((()=>t.value&&g(e.value)))),B({from:"true-label",replacement:"true-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},r((()=>!!e.trueLabel))),B({from:"false-label",replacement:"false-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},r((()=>!!e.falseLabel))),{inputId:E,isLabeledByFormItem:z,isChecked:h,isDisabled:F,isFocused:i,checkboxButtonSize:p,checkboxSize:f,hasOwnLabel:S,model:n,actualValue:I,handleChange:N,onClickRoot:w}},de=["id","indeterminate","name","tabindex","disabled","true-value","false-value"],re=["id","indeterminate","disabled","value","name","tabindex"],ce=S({name:"ElCheckbox"});var ve=q(S({...ce,props:te,emits:ue,setup(e){const a=e,l=I(),{inputId:n,isLabeledByFormItem:t,isChecked:u,isDisabled:o,isFocused:i,checkboxSize:s,hasOwnLabel:d,model:c,actualValue:v,handleChange:b,onClickRoot:m}=se(a,l),h=F("checkbox"),p=r((()=>[h.b(),h.m(s.value),h.is("disabled",o.value),h.is("bordered",a.border),h.is("checked",u.value)])),f=r((()=>[h.e("input"),h.is("disabled",o.value),h.is("checked",u.value),h.is("indeterminate",a.indeterminate),h.is("focus",i.value)]));return(e,a)=>(E(),z(Q(!D(d)&&D(t)?"span":"label"),{class:O(D(p)),"aria-controls":e.indeterminate?e.controls:null,onClick:D(m)},{default:N((()=>{var l,t;return[w("span",{class:O(D(f))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?$((E(),_("input",{key:0,id:D(n),"onUpdate:modelValue":a[0]||(a[0]=e=>G(c)?c.value=e:null),class:O(D(h).e("original")),type:"checkbox",indeterminate:e.indeterminate,name:e.name,tabindex:e.tabindex,disabled:D(o),"true-value":null!=(l=e.trueValue)?l:e.trueLabel,"false-value":null!=(t=e.falseValue)?t:e.falseLabel,onChange:a[1]||(a[1]=(...e)=>D(b)&&D(b)(...e)),onFocus:a[2]||(a[2]=e=>i.value=!0),onBlur:a[3]||(a[3]=e=>i.value=!1),onClick:a[4]||(a[4]=U((()=>{}),["stop"]))},null,42,de)),[[j,D(c)]]):$((E(),_("input",{key:1,id:D(n),"onUpdate:modelValue":a[5]||(a[5]=e=>G(c)?c.value=e:null),class:O(D(h).e("original")),type:"checkbox",indeterminate:e.indeterminate,disabled:D(o),value:D(v),name:e.name,tabindex:e.tabindex,onChange:a[6]||(a[6]=(...e)=>D(b)&&D(b)(...e)),onFocus:a[7]||(a[7]=e=>i.value=!0),onBlur:a[8]||(a[8]=e=>i.value=!1),onClick:a[9]||(a[9]=U((()=>{}),["stop"]))},null,42,re)),[[j,D(c)]]),w("span",{class:O(D(h).e("inner"))},null,2)],2),D(d)?(E(),_("span",{key:0,class:O(D(h).e("label"))},[R(e.$slots,"default"),e.$slots.default?M("v-if",!0):(E(),_(A,{key:0},[H(K(e.label),1)],64))],2)):M("v-if",!0)]})),_:3},8,["class","aria-controls","onClick"]))}}),[["__file","checkbox.vue"]]);const be=["name","tabindex","disabled","true-value","false-value"],me=["name","tabindex","disabled","value"],he=S({name:"ElCheckboxButton"});var pe=q(S({...he,props:te,emits:ue,setup(e){const a=e,l=I(),{isFocused:n,isChecked:t,isDisabled:u,checkboxButtonSize:o,model:i,actualValue:s,handleChange:c}=se(a,l),v=d(oe,void 0),b=F("checkbox"),m=r((()=>{var e,a,l,n;const t=null!=(a=null==(e=null==v?void 0:v.fill)?void 0:e.value)?a:"";return{backgroundColor:t,borderColor:t,color:null!=(n=null==(l=null==v?void 0:v.textColor)?void 0:l.value)?n:"",boxShadow:t?`-1px 0 0 0 ${t}`:void 0}})),h=r((()=>[b.b("button"),b.bm("button",o.value),b.is("disabled",u.value),b.is("checked",t.value),b.is("focus",n.value)]));return(e,a)=>{var l,o;return E(),_("label",{class:O(D(h))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?$((E(),_("input",{key:0,"onUpdate:modelValue":a[0]||(a[0]=e=>G(i)?i.value=e:null),class:O(D(b).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:D(u),"true-value":null!=(l=e.trueValue)?l:e.trueLabel,"false-value":null!=(o=e.falseValue)?o:e.falseLabel,onChange:a[1]||(a[1]=(...e)=>D(c)&&D(c)(...e)),onFocus:a[2]||(a[2]=e=>n.value=!0),onBlur:a[3]||(a[3]=e=>n.value=!1),onClick:a[4]||(a[4]=U((()=>{}),["stop"]))},null,42,be)),[[j,D(i)]]):$((E(),_("input",{key:1,"onUpdate:modelValue":a[5]||(a[5]=e=>G(i)?i.value=e:null),class:O(D(b).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:D(u),value:D(s),onChange:a[6]||(a[6]=(...e)=>D(c)&&D(c)(...e)),onFocus:a[7]||(a[7]=e=>n.value=!0),onBlur:a[8]||(a[8]=e=>n.value=!1),onClick:a[9]||(a[9]=U((()=>{}),["stop"]))},null,42,me)),[[j,D(i)]]),e.$slots.default||e.label?(E(),_("span",{key:2,class:O(D(b).be("button","inner")),style:J(D(t)?D(m):void 0)},[R(e.$slots,"default",{},(()=>[H(K(e.label),1)]))],6)):M("v-if",!0)],2)}}}),[["__file","checkbox-button.vue"]]);const fe=P({modelValue:{type:Y(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:t,label:String,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0}}),xe={[u]:e=>k(e),change:e=>k(e)},ke=S({name:"ElCheckboxGroup"});var ge=q(S({...ke,props:fe,emits:xe,setup(e,{emit:a}){const l=e,n=F("checkbox"),{formItem:t}=b(),{inputId:o,isLabeledByFormItem:i}=V(l,{formItemContext:t}),s=async e=>{a(u,e),await f(),a("change",e)},d=r({get:()=>l.modelValue,set(e){s(e)}});return Z(oe,{...ne(T(l),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:d,changeEvent:s}),h((()=>l.modelValue),(()=>{l.validateEvent&&(null==t||t.validate("change").catch((e=>p())))})),(e,a)=>{var l;return E(),z(Q(e.tag),{id:D(o),class:O(D(n).b("group")),role:"group","aria-label":D(i)?void 0:e.label||"checkbox-group","aria-labelledby":D(i)?null==(l=D(t))?void 0:l.labelId:void 0},{default:N((()=>[R(e.$slots,"default")])),_:3},8,["id","class","aria-label","aria-labelledby"])}}}),[["__file","checkbox-group.vue"]]);const ye=W(ve,{CheckboxButton:pe,CheckboxGroup:ge}),Ce=X(pe),Le=X(ge);export{ye as E,Le as a,Ce as b,ne as p};
