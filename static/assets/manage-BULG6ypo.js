import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as a,N as t,s as o,e as l,v as s,M as r,B as i,G as n,F as d,r as m,o as c,c as u,w as p,a as f,f as y,z as g,t as b,A as v,C as _,U as j,J as w,l as h,_ as x}from"./index-C6fb_XFi.js";import{a as E,E as U}from"./el-col-Dl4_4Pn5.js";import{E as C}from"./el-upload-DFauS7op.js";import"./el-progress-sY5OgffI.js";import{E as k,a as S}from"./el-form-C2Y6uNCj.js";import{E as A}from"./el-tag-C_oEQYGz.js";import{_ as V}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{u as z}from"./useTable-CijeIiBB.js";import{d as D,a as F,g as H,c as I}from"./index-DvCU-6BP.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./index-BWEJ0epC.js";import"./castArray-DRqY4cIf.js";import"./el-table-column-C9CkC7I1.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";const L={class:"mb-10px"},W={class:"mb-10px"};function B(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!w(e)}const M=x(a({__name:"manage",setup(a){const{t:w}=h(),x={Authorization:`${t().getToken}`},M=o([{field:"selection",type:"selection",width:"55"},{field:"id",hidden:!0},{field:"name",label:w("common.name"),minWidth:40},{field:"category",label:w("common.category"),minWidth:40,formatter:(e,a,t)=>l(A,{type:"success",effect:"light",size:"large"},B(t)?t:{default:()=>[t]})},{field:"size",label:w("common.filesize"),formatter:(e,a,t)=>t+"MB"},{field:"upload",label:w("common.upload"),formatter:(e,a,t)=>{const o=`/api/dictionary/manage/save?id=${e.id}`;return s("div",[s(C,{class:"upload-demo",action:o,headers:x,onSuccess:e=>{200===e.code?r.success("Upload succes"):r.error("Upload failed"),q()},onError:e=>{r.error(`Upload failed: ${e}`)}},(()=>s(i,{type:"warning"},w("common.cover"))))])}},{field:"download",label:w("common.download"),formatter:(e,a,t)=>{let o;return l(d,null,[l(n,{type:"primary",onClick:()=>se(e.id,e.name)},B(o=w("common.download"))?o:{default:()=>[o]})])}},{field:"action",label:w("tableDemo.action"),minWidth:40,formatter:(e,a,t)=>{let o;return l(d,null,[l(n,{type:"danger",onClick:()=>K(e)},B(o=w("common.delete"))?o:{default:()=>[o]})])}}]),{tableRegister:N,tableState:O,tableMethods:T}=z({fetchDataApi:async()=>({list:(await H()).data.list})}),{loading:$,dataList:J}=O,{getList:q,getElTableExpose:G}=T;function P(){return{background:"var(--el-fill-color-light)"}}const Q=m(!1),X=m({name:"",category:""}),Y=m(!1),K=async e=>{if(window.confirm("Are you sure you want to delete this data?")){Y.value=!0;try{await D([e.id]);Y.value=!1,q()}catch(a){Y.value=!1,q()}}},Z=m([]),ee=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{const e=await G(),a=(null==e?void 0:e.getSelectionRows())||[];Z.value=a.map((e=>e.id)),Y.value=!0;try{await D(Z.value),Y.value=!1,q()}catch(t){Y.value=!1,q()}})()},ae=m();async function te(e){try{const a=e.file,t=new FormData;t.append("file",a),t.append("name",X.value.name),t.append("category",X.value.category),await I(t),q(),Q.value=!1,r.success("Upload success")}catch(a){r.error(`Upload failed: ${a}`)}}async function oe(){ae.value.submit()}const le=async()=>{X.value.name="",Q.value=!0},se=async(e,a)=>{const t=await F(e),o=window.URL.createObjectURL(new Blob([t.data])),l=document.createElement("a");l.href=o,l.setAttribute("download",a),document.body.appendChild(l),l.click(),document.body.removeChild(l)};return(a,t)=>(c(),u(d,null,[l(f(e),null,{default:p((()=>[l(f(E),{gutter:60},{default:p((()=>[l(f(U),{span:1},{default:p((()=>[y("div",L,[l(f(i),{type:"primary",onClick:le},{default:p((()=>[g(b(f(w)("common.new")),1)])),_:1})])])),_:1}),l(f(U),{span:1},{default:p((()=>[y("div",W,[l(f(n),{type:"danger",loading:Y.value,onClick:ee},{default:p((()=>[g(b(f(w)("common.delete")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),l(f(V),{columns:M,data:f(J),stripe:"",border:!0,loading:f($),resizable:!0,onRegister:f(N),headerCellStyle:P,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["columns","data","loading","onRegister"])])),_:1}),l(f(R),{modelValue:Q.value,"onUpdate:modelValue":t[2]||(t[2]=e=>Q.value=e),title:f(w)("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:430},{default:p((()=>[l(f(k),{model:X.value,"label-width":"120px",class:"upload-form"},{default:p((()=>[l(f(S),{label:f(w)("common.name")},{default:p((()=>[l(f(v),{modelValue:X.value.name,"onUpdate:modelValue":t[0]||(t[0]=e=>X.value.name=e),placeholder:"Enter name"},null,8,["modelValue"])])),_:1},8,["label"]),l(f(S),{label:f(w)("common.category")},{default:p((()=>[l(f(v),{modelValue:X.value.category,"onUpdate:modelValue":t[1]||(t[1]=e=>X.value.category=e),placeholder:"Enter category"},null,8,["modelValue"])])),_:1},8,["label"]),l(f(S),{label:""},{default:p((()=>[l(f(C),{class:"upload-demo",drag:"",ref_key:"upload",ref:ae,"auto-upload":!1,limit:1,httpRequest:te},{default:p((()=>[l(f(_),{class:"avatar-uploader-icon"},{default:p((()=>[l(f(j))])),_:1})])),_:1},512)])),_:1}),l(f(S),null,{default:p((()=>[l(f(i),{type:"primary",onClick:oe},{default:p((()=>[g("Submit")])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])],64))}}),[["__scopeId","data-v-a03cd13e"]]);export{M as default};
