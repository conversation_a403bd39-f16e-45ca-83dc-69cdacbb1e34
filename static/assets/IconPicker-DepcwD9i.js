import{I as e}from"./IconPicker-DBEypS2S.js";import{_ as t}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as o,l as s,r,o as a,i,w as p,e as l,a as n}from"./index-DfJTpRkj.js";import"./el-popper-D2BmgSQA.js";import"./el-tab-pane-BijWf7kq.js";import"./strings-CUyZ1T6U.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./el-tag-CbhrEnto.js";import"./index-DE7jtbbk.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";const m=o({__name:"IconPicker",setup(o){const{t:m}=s(),u=r("tdesign:book-open");return(o,s)=>(a(),i(n(t),{title:n(m)("router.iconPicker")},{default:p((()=>[l(n(e),{modelValue:u.value,"onUpdate:modelValue":s[0]||(s[0]=e=>u.value=e)},null,8,["modelValue"])])),_:1},8,["title"]))}});export{m as default};
