import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as o,l as i,y as n,o as s,c as t,e as c,a,w as l,f as p,z as u,F as m}from"./index-3XfDPlIS.js";import{_ as r}from"./Infotip.vue_vue_type_script_setup_true_lang-DGvDVvj1.js";import{u as d}from"./useIcon-k-uSyz6l.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./Highlight.vue_vue_type_script_lang-zuUE9qRJ.js";const _={class:"flex justify-between"},f={class:"flex justify-between"},y={class:"flex justify-between"},v=o({__name:"Icon",setup(o){const{t:v}=i(),g=e=>{e===v("iconDemo.accessAddress")&&window.open("https://iconify.design/")},j=d({icon:"svg-icon:peoples"}),D=d({icon:"svg-icon:money"}),b=d({icon:"ep:aim"}),h=d({icon:"ep:alarm-clock"});return(o,i)=>{const d=n("Icon",!0),w=n("BaseButton");return s(),t(m,null,[c(a(r),{"show-index":!1,title:`${a(v)("iconDemo.recommendedUse")}${a(v)("iconDemo.iconify")}`,schema:[{label:a(v)("iconDemo.recommendeDes"),keys:["Iconify"]},{label:a(v)("iconDemo.accessAddress"),keys:[a(v)("iconDemo.accessAddress")]}],onClick:g},null,8,["title","schema"]),c(a(e),{title:a(v)("iconDemo.localIcon")},{default:l((()=>[p("div",_,[c(d,{icon:"svg-icon:peoples"}),c(d,{icon:"svg-icon:money"}),c(d,{icon:"svg-icon:message"}),c(d,{icon:"svg-icon:shopping"})])])),_:1},8,["title"]),c(a(e),{title:a(v)("iconDemo.iconify")},{default:l((()=>[p("div",f,[c(d,{icon:"ep:aim"}),c(d,{icon:"ep:alarm-clock"}),c(d,{icon:"ep:baseball"}),c(d,{icon:"ep:chat-line-round"})])])),_:1},8,["title"]),c(a(e),{title:"useIcon"},{default:l((()=>[p("div",y,[c(w,{icon:a(j)},{default:l((()=>[u("Button")])),_:1},8,["icon"]),c(w,{icon:a(D)},{default:l((()=>[u("Button")])),_:1},8,["icon"]),c(w,{icon:a(b)},{default:l((()=>[u("Button")])),_:1},8,["icon"]),c(w,{icon:a(h)},{default:l((()=>[u("Button")])),_:1},8,["icon"])])])),_:1})],64)}}});export{v as default};
