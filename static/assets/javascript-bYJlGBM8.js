import{conf as e,language as t}from"./typescript-Cps2HMJe.js";import"./MonacoDiffEditor-By1hxVEP.js";import"./index-C6fb_XFi.js";
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.52.0(f6dc0eb8fce67e57f6036f4769d92c1666cdf546)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var i=e,s={defaultToken:"invalid",tokenPostfix:".js",keywords:["break","case","catch","class","continue","const","constructor","debugger","default","delete","do","else","export","extends","false","finally","for","from","function","get","if","import","in","instanceof","let","new","null","return","set","static","super","switch","symbol","this","throw","true","try","typeof","undefined","var","void","while","with","yield","async","await","of"],typeKeywords:[],operators:t.operators,symbols:t.symbols,escapes:t.escapes,digits:t.digits,octaldigits:t.octaldigits,binarydigits:t.binarydigits,hexdigits:t.hexdigits,regexpctl:t.regexpctl,regexpesc:t.regexpesc,tokenizer:t.tokenizer};export{i as conf,s as language};
