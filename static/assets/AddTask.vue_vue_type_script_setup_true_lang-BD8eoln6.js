import{d as e,s as a,r as l,H as t,O as o,o as d,c as s,e as u,w as r,a as n,A as c,i as p,j as i,z as m,t as y,B as g,af as h,F as k,R as f,l as b,M as v,f as _}from"./index-DfJTpRkj.js";import{E as V}from"./el-checkbox-DU4wMKRd.js";import{E as w}from"./el-divider-0NmzbuNU.js";import{a as j,E as T}from"./el-form-DsaI0u2w.js";import{E as x,a as N}from"./el-col-B4Ik8fnS.js";import{E as S}from"./el-switch-C5ZBDFmL.js";import"./el-tooltip-l0sNRNKZ.js";import{E}from"./el-popper-D2BmgSQA.js";import{E as U,b as P}from"./el-radio-group-CTAZlJKV.js";import"./el-tag-CbhrEnto.js";import"./el-virtual-list-DQOsVxKt.js";import{E as F}from"./el-select-v2-D406kAkc.js";import{a as A,E as D}from"./el-select-BkpcrSfo.js";/* empty css                */import{E as M}from"./el-tree-select-P6ZpyTcB.js";import{a as C}from"./index-KH6atv8j.js";import{i as I,j as R,k as q,h as z,l as L,m as B}from"./index-B0JD2UFG.js";import{_ as $}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import H from"./DetailTemplate-DU3RXrBH.js";import{g as J}from"./index-jyMftxhc.js";const O={style:{float:"left"}},Q=e({__name:"AddTask",props:{closeDialog:{type:Function},getList:{type:Function},create:{type:Boolean},schedule:{type:Boolean},taskid:{},tp:{},targetIds:{},getFilter:{type:Function},searchParams:{}},setup(e){const{t:Q}=b(),G=e,K=!!G.tp.includes("Source"),W=a({name:[{required:!0,message:Q("task.msgTaskName"),trigger:"blur"}],target:[{required:!K,message:Q("task.msgTarget"),trigger:"blur"}],node:[{required:!0,message:Q("task.nodeMsg"),trigger:"blur"}],template:[{required:!0,message:"Please select template",trigger:"blur"}],day:[{message:"1-31",trigger:"change",validator:(e,a,l)=>{a&&/^\d+$/.test(a)?a<1||a>31?l(new Error("1-31")):l():l(new Error("1-31"))}}],hour:[{message:"0-24",trigger:"change",validator:(e,a,l)=>{a&&/^\d+$/.test(a)?a<0||a>24?l(new Error("0-24")):l():l(new Error("0-24"))}}],minute:[{message:"0-60",trigger:"change",validator:(e,a,l)=>{a&&/^\d+$/.test(a)?a<0||a>60?l(new Error("0-60")):l():l(new Error("0-60"))}}]}),X=l(!1),Y=l(),Z=a([]),ee=a([]),ae=async()=>{ee.length=0;const e=await z("",1,1e3);e.data.list.length>0&&e.data.list.forEach((e=>{ee.push({value:e.id,label:e.name})}))};t((()=>{(async()=>{const e=await C();e.data.list.length>0?(te.value=!1,e.data.list.forEach((e=>{Z.push({value:e,label:e})}))):(te.value=!0,v.warning(Q("node.onlineNodeMsg")))})(),ae()}));const le=l(!1),te=l(!1),oe=a({name:"",target:"",ignore:"",node:[],allNode:!0,scheduledTasks:!1,duplicates:"None",template:"",cycleType:"daily",search:"",project:[],targetSource:"general",day:1,hour:1,minute:30,week:1,bindProject:null}),de=e=>{le.value=!1,e?(oe.node=[],Z.forEach((e=>oe.node.push(e.value)))):oe.node=[]},se=l(""),ue=l(!1);let re=Q("task.addTemplate");const ne=async e=>{se.value=e,""!=e&&(re=Q("task.editTemplate")),ue.value=!0},ce=()=>{ue.value=!1};o((()=>G.taskid),(async e=>{e?G.schedule?await(async e=>{const a=await B(e);oe.name=a.data.name,oe.target=a.data.target,oe.ignore=a.data.ignore,oe.node=a.data.node,oe.allNode=a.data.allNode,oe.scheduledTasks=a.data.scheduledTasks,oe.hour=a.data.hour,oe.duplicates=a.data.duplicates,oe.template=a.data.template,oe.project=a.data.project,oe.targetSource=a.data.targetSource,oe.day=a.data.day,oe.minute=a.data.minute,oe.week=a.data.week,oe.cycleType=a.data.cycleType})(e):await(async e=>{const a=await L(e);oe.name=a.data.name,oe.target=a.data.target,oe.ignore=a.data.ignore,oe.node=a.data.node,oe.allNode=a.data.allNode,oe.scheduledTasks=a.data.scheduledTasks,oe.hour=a.data.hour,oe.duplicates=a.data.duplicates,oe.template=a.data.template,oe.day=a.data.day,oe.minute=a.data.minute,oe.week=a.data.week,oe.cycleType=a.data.cycleType})(e):(G.schedule?oe.scheduledTasks=!0:oe.scheduledTasks=!1,oe.name="",oe.target="",oe.ignore="",oe.node=[],oe.allNode=!0,oe.duplicates="None",oe.template="")}),{immediate:!0});const pe=l("select"),ie=l(0),me=[{label:Q("task.general"),value:"general"},{label:Q("task.fromProject"),value:"project"},{label:Q("task.fromAsset"),value:"asset"},{label:Q("task.fromRootDomain"),value:"RootDomain"},{label:Q("task.fromSubdomain"),value:"subdomain"}],ye=a([]);return(async()=>{(await J()).data.list.forEach((e=>{ye.push(e)}))})(),(e,l)=>(d(),s(k,null,[u(n(T),{model:oe,"label-width":"auto",rules:W,"status-icon":"",ref_key:"ruleFormRef",ref:Y,disabled:!e.create},{default:r((()=>[u(n(j),{label:n(Q)("task.taskName"),prop:"name"},{default:r((()=>[u(n(c),{modelValue:oe.name,"onUpdate:modelValue":l[0]||(l[0]=e=>oe.name=e),placeholder:n(Q)("task.msgTaskName")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),n(K)?i("",!0):(d(),p(n(j),{key:0,label:n(Q)("task.targetSource")},{default:r((()=>[u(n(F),{style:{width:"50%"},modelValue:oe.targetSource,"onUpdate:modelValue":l[1]||(l[1]=e=>oe.targetSource=e),options:me},null,8,["modelValue"])])),_:1},8,["label"])),"project"==oe.targetSource||"general"==oe.targetSource||n(K)?i("",!0):(d(),p(n(N),{key:1},{default:r((()=>[u(n(x),{span:12},{default:r((()=>[u(n(j),{label:n(Q)("task.search")},{default:r((()=>[n(K)?i("",!0):(d(),p(n(c),{key:0,modelValue:oe.search,"onUpdate:modelValue":l[2]||(l[2]=e=>oe.search=e),placeholder:n(Q)("form.input")},null,8,["modelValue","placeholder"]))])),_:1},8,["label"])])),_:1}),u(n(x),{span:12},{default:r((()=>[u(n(j),{label:n(Q)("task.targetProject")},{default:r((()=>[u(n(M),{modelValue:oe.project,"onUpdate:modelValue":l[3]||(l[3]=e=>oe.project=e),data:ye,placeholder:n(Q)("project.project"),multiple:"",filterable:"","show-checkbox":"","collapse-tags":"","max-collapse-tags":1},null,8,["modelValue","data","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1})),"project"==oe.targetSource?(d(),p(n(j),{key:2,label:n(Q)("task.targetProject")},{default:r((()=>[u(n(M),{modelValue:oe.project,"onUpdate:modelValue":l[4]||(l[4]=e=>oe.project=e),data:ye,placeholder:n(Q)("project.project"),multiple:"",filterable:"","show-checkbox":"","collapse-tags":"","max-collapse-tags":1},null,8,["modelValue","data","placeholder"])])),_:1},8,["label"])):i("",!0),"general"==oe.targetSource?(d(),p(n(j),{key:3,label:n(Q)("task.taskTarget"),prop:"target"},{default:r((()=>[n(K)?i("",!0):(d(),p(n(c),{key:0,modelValue:oe.target,"onUpdate:modelValue":l[5]||(l[5]=e=>oe.target=e),placeholder:n(Q)("task.msgTarget"),type:"textarea",rows:"10"},null,8,["modelValue","placeholder"])),n(K)?(d(),p(n(U),{key:1,modelValue:pe.value,"onUpdate:modelValue":l[6]||(l[6]=e=>pe.value=e)},{default:r((()=>[u(n(P),{value:"select"},{default:r((()=>[m(y(n(Q)("task.select")),1)])),_:1}),u(n(P),{value:"search"},{default:r((()=>[m(y(n(Q)("export.exportTypeSearch")),1)])),_:1})])),_:1},8,["modelValue"])):i("",!0)])),_:1},8,["label"])):i("",!0),"search"==pe.value&&n(K)?(d(),p(n(j),{key:4,label:n(Q)("task.targetNumber")},{default:r((()=>[u(n(c),{modelValue:ie.value,"onUpdate:modelValue":l[7]||(l[7]=e=>ie.value=e)},null,8,["modelValue"])])),_:1},8,["label"])):i("",!0),"project"!=oe.targetSource?(d(),p(n(j),{key:5,label:n(Q)("task.ignore"),prop:"ignore"},{default:r((()=>[u(n(c),{modelValue:oe.ignore,"onUpdate:modelValue":l[8]||(l[8]=e=>oe.ignore=e),placeholder:n(Q)("task.ignoreMsg"),type:"textarea",rows:"5"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])):i("",!0),u(n(N),null,{default:r((()=>[u(n(x),{span:12},{default:r((()=>[u(n(j),{label:n(Q)("task.nodeSelect"),prop:"node"},{default:r((()=>[u(n(F),{modelValue:oe.node,"onUpdate:modelValue":l[9]||(l[9]=e=>oe.node=e),filterable:"",options:Z,placeholder:"Please select node",style:{width:"80%"},multiple:"","tag-type":"success","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":7},{header:r((()=>[u(n(V),{disabled:te.value,indeterminate:le.value,onChange:de},{default:r((()=>[m(" All ")])),_:1},8,["disabled","indeterminate"])])),_:1},8,["modelValue","options"])])),_:1},8,["label"])])),_:1}),u(n(x),{span:12},{default:r((()=>[u(n(j),{label:n(Q)("task.autoNode")},{default:r((()=>[u(n(E),{effect:"dark",content:n(Q)("task.selectNodeMsg"),placement:"top"},{default:r((()=>[u(n(S),{modelValue:oe.allNode,"onUpdate:modelValue":l[10]||(l[10]=e=>oe.allNode=e),"inline-prompt":"","active-text":n(Q)("common.switchAction"),"inactive-text":n(Q)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["content"])])),_:1},8,["label"])])),_:1})])),_:1}),u(n(j),{label:n(Q)("project.scheduledTasks")},{default:r((()=>[u(n(E),{effect:"dark",content:n(Q)("project.msgScheduledTasks"),placement:"top"},{default:r((()=>[u(n(S),{modelValue:oe.scheduledTasks,"onUpdate:modelValue":l[11]||(l[11]=e=>oe.scheduledTasks=e),"inline-prompt":"","active-text":n(Q)("common.switchAction"),"inactive-text":n(Q)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["content"])])),_:1},8,["label"]),oe.scheduledTasks?(d(),p(n(j),{key:6,label:n(Q)("project.cycle"),prop:"type"},{default:r((()=>[u(n(N),{gutter:10,style:{width:"100%"}},{default:r((()=>[u(n(x),{span:5},{default:r((()=>[u(n(A),{modelValue:oe.cycleType,"onUpdate:modelValue":l[12]||(l[12]=e=>oe.cycleType=e),style:{width:"100%"}},{default:r((()=>[u(n(D),{label:n(Q)("task.daily"),value:"daily"},null,8,["label"]),u(n(D),{label:n(Q)("task.ndays"),value:"ndays"},null,8,["label"]),u(n(D),{label:n(Q)("task.nhours"),value:"nhours"},null,8,["label"]),u(n(D),{label:n(Q)("task.weekly"),value:"weekly"},null,8,["label"]),u(n(D),{label:n(Q)("task.monthly"),value:"monthly"},null,8,["label"])])),_:1},8,["modelValue"])])),_:1}),"weekly"==oe.cycleType?(d(),p(n(x),{key:0,span:5},{default:r((()=>[u(n(A),{modelValue:oe.week,"onUpdate:modelValue":l[13]||(l[13]=e=>oe.week=e),style:{width:"100%"}},{default:r((()=>[u(n(D),{label:n(Q)("task.monday"),value:"0"},null,8,["label"]),u(n(D),{label:n(Q)("task.tuesday"),value:"1"},null,8,["label"]),u(n(D),{label:n(Q)("task.wednesday"),value:"2"},null,8,["label"]),u(n(D),{label:n(Q)("task.thursday"),value:"3"},null,8,["label"]),u(n(D),{label:n(Q)("task.friday"),value:"4"},null,8,["label"]),u(n(D),{label:n(Q)("task.saturday"),value:"5"},null,8,["label"]),u(n(D),{label:n(Q)("task.sunday"),value:"6"},null,8,["label"])])),_:1},8,["modelValue"])])),_:1})):i("",!0),"ndays"===oe.cycleType||"monthly"==oe.cycleType?(d(),p(n(x),{key:1,span:5},{default:r((()=>[u(n(j),{prop:"day"},{default:r((()=>[u(n(c),{style:{width:"100%"},modelValue:oe.day,"onUpdate:modelValue":l[14]||(l[14]=e=>oe.day=e)},{append:r((()=>[m(y(n(Q)("task.day")),1)])),_:1},8,["modelValue"])])),_:1})])),_:1})):i("",!0),"daily"===oe.cycleType||"ndays"===oe.cycleType||"nhours"==oe.cycleType||"weekly"==oe.cycleType||"monthly"==oe.cycleType?(d(),p(n(x),{key:2,span:5},{default:r((()=>[u(n(j),{prop:"hour"},{default:r((()=>[u(n(c),{style:{width:"100%"},modelValue:oe.hour,"onUpdate:modelValue":l[15]||(l[15]=e=>oe.hour=e)},{append:r((()=>[m(y(n(Q)("task.hour")),1)])),_:1},8,["modelValue"])])),_:1})])),_:1})):i("",!0),"daily"===oe.cycleType||"ndays"===oe.cycleType||"nhours"==oe.cycleType||"weekly"==oe.cycleType||"monthly"==oe.cycleType?(d(),p(n(x),{key:3,span:5},{default:r((()=>[u(n(j),{prop:"minute"},{default:r((()=>[u(n(c),{style:{width:"100%"},modelValue:oe.minute,"onUpdate:modelValue":l[16]||(l[16]=e=>oe.minute=e)},{append:r((()=>[m(y(n(Q)("task.minute")),1)])),_:1},8,["modelValue"])])),_:1})])),_:1})):i("",!0)])),_:1})])),_:1},8,["label"])):i("",!0),u(n(w),{"content-position":"center",style:{width:"60%",left:"20%"}},{default:r((()=>[m(y(n(Q)("task.duplicates")),1)])),_:1}),u(n(N),null,{default:r((()=>[u(n(x),{span:24},{default:r((()=>[u(n(j),{label:n(Q)("task.duplicates"),prop:"type"},{default:r((()=>[u(n(U),{modelValue:oe.duplicates,"onUpdate:modelValue":l[17]||(l[17]=e=>oe.duplicates=e)},{default:r((()=>[u(n(P),{label:"None",name:"duplicates",checked:!0,value:"None"}),u(n(E),{effect:"dark",content:n(Q)("task.duplicatesMsg"),placement:"top"},{default:r((()=>[u(n(P),{label:n(Q)("task.duplicatesSubdomain"),name:"duplicates",value:"subdomain"},null,8,["label"])])),_:1},8,["content"])])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),u(n(w),{"content-position":"center",style:{width:"60%",left:"20%"}},{default:r((()=>[m(y(n(Q)("router.scanTemplate")),1)])),_:1}),u(n(j),{label:n(Q)("router.scanTemplate"),prop:"template"},{default:r((()=>[u(n(A),{modelValue:oe.template,"onUpdate:modelValue":l[19]||(l[19]=e=>oe.template=e),placeholder:"Please select template",style:{width:"30%"}},{footer:r((()=>[u(n(g),{type:"success",size:"small",plain:"",style:{"margin-left":"15px"},onClick:l[18]||(l[18]=h((e=>ne("")),["stop"]))},{default:r((()=>[m(y(n(Q)("common.new")),1)])),_:1})])),default:r((()=>[(d(!0),s(k,null,f(ee,(e=>(d(),p(n(D),{key:e.value,label:e.label,value:e.value},{default:r((()=>[u(n(N),null,{default:r((()=>[u(n(x),{span:16},{default:r((()=>[_("span",O,y(e.label),1)])),_:2},1024),u(n(x),{span:8},{default:r((()=>[u(n(g),{type:"primary",size:"small",style:{"margin-left":"15px"},onClick:h((a=>ne(e.value)),["stop"])},{default:r((()=>[m(y(n(Q)("common.edit")),1)])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),u(n(w)),u(n(N),null,{default:r((()=>[u(n(x),{span:2,offset:10},{default:r((()=>[u(n(j),null,{default:r((()=>[u(n(g),{type:"primary",onClick:l[20]||(l[20]=e=>(async e=>{X.value=!0;try{if(!e)return;if(await e.validate()){let e,l=a({});"search"==pe.value&&(G.getFilter&&(l=G.getFilter()),G.searchParams&&(oe.search=G.searchParams)),K&&(oe.targetSource=G.tp),e=G.taskid?await I(G.taskid,oe.name,oe.target,oe.ignore,oe.node,oe.allNode,oe.duplicates,oe.scheduledTasks,oe.hour,oe.template,pe.value,oe.search,l,ie.value,G.targetIds,oe.project,oe.targetSource,oe.day,oe.minute,oe.week,oe.bindProject,oe.cycleType):G.schedule?await R(oe.name,oe.target,oe.ignore,oe.node,oe.allNode,oe.duplicates,oe.scheduledTasks,oe.hour,oe.template,pe.value,oe.search,l,ie.value,G.targetIds,oe.project,oe.targetSource,oe.day,oe.minute,oe.week,oe.bindProject,oe.cycleType):await q(oe.name,oe.target,oe.ignore,oe.node,oe.allNode,oe.duplicates,oe.scheduledTasks,oe.hour,oe.template,pe.value,oe.search,l,ie.value,G.targetIds,oe.project,oe.targetSource,oe.day,oe.minute,oe.week,oe.bindProject,oe.cycleType),200===e.code&&(G.closeDialog(),G.getList())}}catch(l){}finally{X.value=!1}})(Y.value)),loading:X.value},{default:r((()=>[m(y(n(Q)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules","disabled"]),u(n($),{modelValue:ue.value,"onUpdate:modelValue":l[21]||(l[21]=e=>ue.value=e),title:n(re),center:"",fullscreen:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:r((()=>[u(H,{closeDialog:ce,getList:ae,id:se.value},null,8,["id"])])),_:1},8,["modelValue","title"])],64))}});export{Q as _};
