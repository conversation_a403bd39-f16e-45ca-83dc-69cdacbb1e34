import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,l as s,r as i,o as r,i as a,w as o,e as l,a as p,f as n,t as m,z as c}from"./index-DfJTpRkj.js";import{_ as d}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{g as j}from"./index-E-CbF_DZ.js";import{E as u}from"./el-link-Bi4jWYBx.js";import{E as x}from"./el-divider-0NmzbuNU.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tag-CbhrEnto.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";const f={class:"flex cursor-pointer"},_={class:"pr-16px"},v=["src"],g={class:"mb-12px font-700 font-size-16px"},b={class:"line-clamp-3 font-size-12px"},y={class:"flex justify-center items-center"},k=["onClick"],w=["onClick"],C=t({__name:"CardTable",setup(t){const{t:C}=s(),h=i(!0);let z=i([]);(async e=>{const t=await j(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{h.value=!1}));t&&(z.value=t.data.list)})();return(t,s)=>(r(),a(p(e),{title:p(C)("tableDemo.cardTable")},{default:o((()=>[l(p(d),{columns:[],data:p(z),loading:h.value,"custom-content":"","card-wrap-style":{width:"200px",marginBottom:"20px",marginRight:"20px"}},{content:o((e=>[n("div",f,[n("div",_,[n("img",{src:e.logo,class:"w-48px h-48px rounded-[50%]",alt:""},null,8,v)]),n("div",null,[n("div",g,m(e.name),1),n("div",b,m(e.desc),1)])])])),"content-footer":o((e=>[n("div",y,[n("div",{class:"flex-1 text-center",onClick:()=>{}},[l(p(u),{underline:!1},{default:o((()=>[c("操作一")])),_:1})],8,k),l(p(x),{direction:"vertical"}),n("div",{class:"flex-1 text-center",onClick:()=>{}},[l(p(u),{underline:!1},{default:o((()=>[c("操作二")])),_:1})],8,w)])])),_:1},8,["data","loading"])])),_:1},8,["title"]))}});export{C as default};
