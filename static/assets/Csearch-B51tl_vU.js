import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as a,dC as t,s as l,r as s,O as o,o as i,c as n,e as r,w as p,a as c,B as u,f as d,t as m,z as v,C as g,F as f,R as h,i as y,j as _,S as x,n as j,l as b,K as k,M as w,_ as S}from"./index-C6fb_XFi.js";import{a as C,E as V}from"./el-col-Dl4_4Pn5.js";import{E as F,a as H}from"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import{E as $}from"./el-tag-C_oEQYGz.js";import{E}from"./el-text-BnUG9HvL.js";import{E as T}from"./el-divider-Bw95UAdD.js";import{E as A,a as L}from"./el-tree-select-D8i5b8X5.js";import{b as z,E as D,a as I}from"./el-dropdown-item-DpH7Woj3.js";import"./el-select-vbM8Rxr1.js";/* empty css                */import{E as U}from"./el-switch-Bh7JeorW.js";import{_ as O}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{u as B}from"./useIcon-BxqaCND-.js";import{_ as K}from"./exportData.vue_vue_type_script_setup_true_lang-C3cw41xZ.js";import{t as N}from"./index-BBupWySc.js";import{_ as R}from"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import"./el-card-B37ahJ8o.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./index-w1A_Rrgh.js";import"./strings-BiUeKphX.js";import"./el-pagination-FWx5cl5J.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./useInput-IB6tFdGu.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-space-CdSK6Ce1.js";/* empty css                          */import"./index-CnCQNuY4.js";import"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./index-ghAu5K8t.js";import"./useTable-CijeIiBB.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const W={style:{float:"left"}},q={style:{float:"right",color:"var(--el-text-color-secondary)","font-size":"13px"}},J={class:"custom-dropdown"},P={key:0,class:"dropdown-item"},M={class:"label-text"},G={class:"label-text"},X={class:"segment-control"},Y={class:"flex gap-2"},Q={style:{color:"#888"}},Z={style:{"font-weight":"bold",color:"#333333"}},ee={style:{color:"#888"}},ae=S(a({__name:"Csearch",props:{getList:{type:Function},handleSearch:{type:Function},searchKeywordsData:{},index:{},getElTableExpose:{type:Function},handleFilterSearch:{type:Function},projectList:{},dynamicTags:{},handleClose:{type:Function},openAggregation:{type:Function},crudSchemas:{},statisticsHidden:{type:Boolean},changeStatisticsHidden:{type:Function},searchResultCount:{},activeSegment:{},setActiveSegment:{type:Function},getFilter:{type:Function}},emits:["update-column-visibility"],setup(a,{emit:S}){const{t:ae}=b(),{query:te}=t(),le=a,se=l([...le.searchKeywordsData]),oe={keyword:"task",example:'task=="test"',explain:ae("searchHelp.taskName")};se.push(oe),se.push({keyword:"tag",example:'tag=="test"',explain:"find tags"});const ie=[{operator:"=",meaning:ae("searchHelp.like"),value:'=""'},{operator:"!=",meaning:ae("searchHelp.notIn"),value:'!=""'},{operator:"==",meaning:ae("searchHelp.equal"),value:'==""'}],ne=[{operator:"&&",meaning:ae("searchHelp.and"),value:"&&",logic:"1"},{operator:"||",meaning:ae("searchHelp.or"),value:"||",logic:"1"},{operator:"()",meaning:ae("searchHelp.brackets"),value:"()",logic:"1"}],re=ie.concat(ne),pe=s(!1);(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${le.index}`)||"{}");le.crudSchemas.forEach((a=>{void 0!==e[a.field]&&(a.hidden=e[a.field])}))})(),o((()=>le.crudSchemas),(()=>{(()=>{const e=le.crudSchemas.reduce(((e,a)=>("select"!=a.field&&(e[a.field]=a.hidden),e)),{});localStorage.setItem(`columnConfig_${le.index}`,JSON.stringify(e))})()}),{deep:!0});const ce=()=>{pe.value=!0};function ue(){return{background:"var(--el-fill-color-light)"}}const de=s(""),me=B({icon:"iconoir:search"}),ve=B({icon:"tdesign:chat-bubble-help"}),ge=B({icon:"ri:arrow-drop-down-line"}),fe=B({icon:"ph:export-light"}),he=B({icon:"carbon:data-vis-1"}),ye=B({icon:"openmoji:delete"}),_e=B({icon:"carbon:task-complete"}),xe=s(!1),je=()=>{xe.value=!0},be=s([]),ke=async()=>{const e=await le.getElTableExpose(),a=(null==e?void 0:e.getSelectionRows())||[];return be.value=a.map((e=>e.id)),be.value},we=async()=>{k.confirm("Whether to delete?","Warning",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{await ke(),await N(be.value,le.index),le.getList()})).catch((()=>{w({type:"info",message:"Delete canceled"})}))},Se=s(""),Ce=s(!1),Ve=s(!1);let Fe=s(!0),He=s(!1),$e=s(!1);const Ee=(e,a)=>{if(Se.value=e,""==e&&(Fe.value=!0,$e.value=!1,He.value=!1),Fe.value){$e.value&&(e=e.replace(Se.value,"").trim());a(se.filter((a=>a.keyword.toLowerCase().includes(e.toLowerCase()))))}else if(He.value){const t=e.replace(Se.value,"").trim();a(ie.filter((e=>e.operator.includes(t))))}else if($e.value&&e.endsWith(" ")){const t=e.replace(de.value,"").trim();a(ne.filter((e=>e.operator.includes(t))))}else a([])},Te=e=>{if(e.keyword){let a="";a=$e.value?Se.value+e.keyword:e.keyword,Se.value=a,de.value=a,Ce.value=!0,Fe.value=!1,He.value=!0}else e.logic?(de.value=`${Se.value}${e.value}`,Se.value=de.value,Fe.value=!0):(de.value=`${Se.value}${e.value}`,Se.value=de.value,Ve.value=!0,He.value=!1,$e.value=!0)},Ae=s(!1),Le=s([]);o((()=>Le.value),(e=>{(async()=>{le.handleFilterSearch(de.value,{project:Le.value})})()}));const ze=s(le.dynamicTags?[...le.dynamicTags]:[]),De=()=>{const e={};ze.value.forEach((a=>{const[t,l]=a.split("=");t in e?e[t].push(l):e[t]=[l]})),e.project=Le.value,le.handleFilterSearch(de.value,e)};let Ie=te.task;void 0!==Ie&&""!==Ie&&ze.value.push(`task=${Ie}`);const Ue=JSON.parse(localStorage.getItem("assetActiveSegment")||"{}");Ue&&Ue.activeSegment&&le.setActiveSegment&&le.setActiveSegment(Ue.activeSegment,!1),De(),o((()=>le.dynamicTags),(e=>{e?(ze.value=[...e],void 0!==Ie&&""!==Ie&&ze.value.push(`task=${Ie}`)):ze.value=[],De()}),{immediate:!1});const Oe=S,Be=s(le.statisticsHidden),Ke=()=>{location.reload()},Ne=B({icon:"icons8:insert-table"}),Re=B({icon:"flowbite:grid-solid"});function We(e){le.setActiveSegment&&le.setActiveSegment(e,!0)}const qe=s(!1);let Je=ae("task.addTask");const Pe=()=>{qe.value=!1},Me=async()=>{await ke(),qe.value=!0};return(a,t)=>(i(),n(f,null,[r(c(e),null,{default:p((()=>[r(c(C),{class:"row-bg",gutter:20},{default:p((()=>[r(c(V),{span:6},{default:p((()=>[r(c(A),{modelValue:de.value,"onUpdate:modelValue":t[0]||(t[0]=e=>de.value=e),"fetch-suggestions":Ee,placeholder:c(ae)("form.input"),popperClass:"my-autocomplete",onSelect:Te,style:{width:"100%"}},{append:p((()=>[r(c(u),{onClick:ce,text:"",style:{display:"contents"},icon:c(ve)},null,8,["icon"])])),default:p((({item:e})=>[d("span",W,m(e.keyword||e.operator),1),d("span",q,m(e.explain||e.meaning),1)])),_:1},8,["modelValue","placeholder"])])),_:1}),r(c(V),{span:1.5},{default:p((()=>[r(c(u),{type:"primary",icon:c(me),onClick:t[1]||(t[1]=e=>a.$props.handleSearch(de.value))},{default:p((()=>[v(m(c(ae)("form.input")),1)])),_:1},8,["icon"])])),_:1}),r(c(V),{span:1.5},{default:p((()=>[r(c(u),{type:"primary",onClick:je,icon:c(fe)},{default:p((()=>[v(m(c(ae)("asset.export")),1)])),_:1},8,["icon"])])),_:1}),r(c(V),{span:4},{default:p((()=>[r(c(L),{loading:Ae.value,modelValue:Le.value,"onUpdate:modelValue":t[2]||(t[2]=e=>Le.value=e),data:a.$props.projectList,placeholder:c(ae)("project.project"),multiple:"",filterable:"","show-checkbox":"","collapse-tags":"","max-collapse-tags":1},null,8,["loading","modelValue","data","placeholder"])])),_:1}),r(c(V),{span:1.5,xs:1.5,sm:1.5,md:1.5},{default:p((()=>[r(c(z),{trigger:"click"},{dropdown:p((()=>[r(c(D),null,{default:p((()=>[r(c(I),{icon:c(ye),onClick:we},{default:p((()=>[v(m(c(ae)("common.delete")),1)])),_:1},8,["icon"]),r(c(I),{icon:c(_e),onClick:Me},{default:p((()=>[v(m(c(ae)("task.addTask")),1)])),_:1},8,["icon"])])),_:1})])),default:p((()=>[r(c(u),{plain:"",class:"custom-button align-bottom"},{default:p((()=>[v(m(c(ae)("common.operation"))+" ",1),r(c(g),{class:"el-icon--right"},{default:p((()=>[r(c(ge))])),_:1})])),_:1})])),_:1})])),_:1}),r(c(V),{span:1,style:{display:"flex","align-items":"center"}},{default:p((()=>[r(c(z),null,{dropdown:p((()=>[r(c(D),null,{default:p((()=>[(i(!0),n(f,null,h(a.crudSchemas,((e,a)=>(i(),y(c(I),{key:a},{default:p((()=>["selection"!=e.field?(i(),n("div",P,[d("span",M,m(e.label),1),r(c(U),{size:"small",modelValue:e.hidden,"onUpdate:modelValue":a=>e.hidden=a,"active-value":!1,"inactive-value":!0,onChange:a=>(e=>{Oe("update-column-visibility",{field:e.field,hidden:e.hidden})})(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])):_("",!0)])),_:2},1024)))),128)),"asset"==a.$props.index?(i(),y(c(I),{key:0},{default:p((()=>[d("span",G,m(c(ae)("asset.Chart")),1),r(c(U),{size:"small",modelValue:Be.value,"onUpdate:modelValue":t[3]||(t[3]=e=>Be.value=e),"active-value":!1,"inactive-value":!0,onChange:t[4]||(t[4]=e=>a.changeStatisticsHidden(Be.value))},null,8,["modelValue"])])),_:1})):_("",!0),r(c(I),{divided:""},{default:p((()=>[r(c(u),{style:{width:"100%"},type:"primary",onClick:Ke},{default:p((()=>[v("Save")])),_:1})])),_:1})])),_:1})])),default:p((()=>[d("div",J,[r(c(x),{icon:"ant-design:setting-outlined",class:"cursor-pointer"})])])),_:1})])),_:1}),"asset"==a.index?(i(),y(c(V),{key:0,span:2,style:{display:"flex","align-items":"center"}},{default:p((()=>[d("div",X,[d("div",{class:j(["segment",{active:"tableSegment"===le.activeSegment}]),onClick:t[5]||(t[5]=e=>We("tableSegment"))},[r(c(g),null,{default:p((()=>[r(c(Ne))])),_:1})],2),d("div",{class:j(["segment",{active:"cardSegment"===le.activeSegment}]),onClick:t[6]||(t[6]=e=>We("cardSegment"))},[r(c(g),null,{default:p((()=>[r(c(Re))])),_:1})],2)])])),_:1})):_("",!0),r(c(V),{span:2,xs:2,sm:2,md:2},{default:p((()=>["SensitiveResult"==a.index?(i(),y(c(u),{key:0,type:"success",onClick:a.$props.openAggregation,icon:c(he)},{default:p((()=>[v(m(c(ae)("project.aggregation")),1)])),_:1},8,["onClick","icon"])):_("",!0)])),_:1})])),_:1}),r(c(C),{style:{"margin-top":"10px"}},{default:p((()=>[r(c(V),{span:24},{default:p((()=>[d("div",Y,[d("span",Q,m(c(ae)("asset.total")),1),d("span",Z,m(le.searchResultCount),1),d("span",ee,m(c(ae)("asset.result")),1),(i(!0),n(f,null,h(ze.value,(e=>(i(),y(c($),{key:e,closable:"","disable-transitions":!1,type:"info",size:"small",onClose:a=>function(e){e.includes("task=")&&(Ie=""),le.handleClose&&le.handleClose(e)}(e)},{default:p((()=>[v(m(e),1)])),_:2},1032,["onClose"])))),128))])])),_:1})])),_:1})])),_:1}),r(c(O),{modelValue:pe.value,"onUpdate:modelValue":t[7]||(t[7]=e=>pe.value=e),title:c(ae)("common.querysyntax"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[r(c(C),null,{default:p((()=>[r(c(V),null,{default:p((()=>[r(c(E),{tag:"b",size:"small"},{default:p((()=>[v(m(c(ae)("searchHelp.operator")),1)])),_:1}),r(c(T),{direction:"vertical"}),r(c(E),{size:"small",type:"danger"},{default:p((()=>[v(m(c(ae)("searchHelp.notice")),1)])),_:1})])),_:1}),r(c(V),{style:{"margin-top":"10px"}},{default:p((()=>[r(c(F),{headerCellStyle:ue,data:c(re)},{default:p((()=>[r(c(H),{prop:"operator",label:c(ae)("searchHelp.operator"),width:"300"},null,8,["label"]),r(c(H),{prop:"meaning",label:c(ae)("searchHelp.meaning")},null,8,["label"])])),_:1},8,["data"])])),_:1}),r(c(V),{style:{"margin-top":"15px"}},{default:p((()=>[r(c(E),{tag:"b",size:"small"},{default:p((()=>[v(m(c(ae)("searchHelp.keywords")),1)])),_:1})])),_:1}),r(c(V),{style:{"margin-top":"10px"}},{default:p((()=>[r(c(F),{headerCellStyle:ue,data:se},{default:p((()=>[r(c(H),{prop:"keyword",label:c(ae)("searchHelp.keywords")},null,8,["label"]),r(c(H),{prop:"example",label:c(ae)("searchHelp.example")},null,8,["label"]),r(c(H),{prop:"explain",label:c(ae)("searchHelp.explain")},null,8,["label"])])),_:1},8,["data"])])),_:1})])),_:1})])),_:1},8,["modelValue","title"]),r(c(O),{modelValue:xe.value,"onUpdate:modelValue":t[8]||(t[8]=e=>xe.value=e),title:c(ae)("asset.export"),center:"","max-height":"300",width:"70%",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[r(K,{index:a.$props.index,searchParams:de.value,getFilter:a.$props.getFilter},null,8,["index","searchParams","getFilter"])])),_:1},8,["modelValue","title"]),r(c(O),{modelValue:qe.value,"onUpdate:modelValue":t[9]||(t[9]=e=>qe.value=e),title:c(Je),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[r(R,{closeDialog:Pe,create:!0,taskid:"",schedule:!1,getList:function(){},tp:a.$props.index+"Source","target-ids":be.value,getFilter:a.$props.getFilter,searchParams:de.value},null,8,["getList","tp","target-ids","getFilter","searchParams"])])),_:1},8,["modelValue","title"])],64))}}),[["__scopeId","data-v-d75acf77"]]);export{ae as default};
