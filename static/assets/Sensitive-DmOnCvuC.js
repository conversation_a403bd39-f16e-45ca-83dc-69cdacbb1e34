import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,r as a,s as l,e as o,S as i,G as s,F as n,o as r,c as u,w as m,a as p,z as d,t as c,A as f,B as g,f as v,I as y,J as _,l as j}from"./index-DfJTpRkj.js";import{E as b,a as h}from"./el-col-B4Ik8fnS.js";import{E as x}from"./el-text-vKNLRkxx.js";import{_ as w}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{_ as S}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{u as k}from"./useTable-CtyddZqf.js";import{u as C}from"./useIcon-CNpM61rT.js";import{d as I,_ as z,g as A,u as E}from"./Detail.vue_vue_type_script_setup_true_lang-aMK82Tfg.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tag-CbhrEnto.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./el-form-DsaI0u2w.js";import"./el-divider-0NmzbuNU.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./el-switch-C5ZBDFmL.js";import"./index-D1ADinPR.js";const L={class:"mb-10px"},R={class:"mb-10px"},U={class:"mb-10px"},F={class:"mb-10px"};function N(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!_(e)}const P=t({__name:"Sensitive",setup(t){const _=C({icon:"iconoir:search"}),{t:P}=j(),V=a(""),W=()=>{K()},D=l([{field:"selection",type:"selection",width:"55"},{field:"id",hidden:!0},{field:"name",label:P("sensitiveInformation.sensitiveName"),minWidth:40},{field:"regular",label:P("sensitiveInformation.sensitiveRegular"),minWidth:100},{field:"color",label:P("sensitiveInformation.sensitiveColor"),minWidth:20},{field:"state",label:P("common.state"),minWidth:40,formatter:(e,t,a)=>{if(null==a)return o("div",null,null);let l="",s="";return 1==a?(l="#2eb98a",s=P("common.on")):(l="red",s=P("common.off")),o(h,{gutter:20},{default:()=>[o(b,{span:1},{default:()=>[o(i,{icon:"clarity:circle-solid",color:l,size:10},null)]}),o(b,{span:5},{default:()=>[o(x,{type:"info"},N(s)?s:{default:()=>[s]})]})]})}},{field:"action",label:P("tableDemo.action"),minWidth:40,formatter:(e,t,a)=>{let l,i;return o(n,null,[o(s,{type:"primary",onClick:()=>te(e)},N(l=P("common.edit"))?l:{default:()=>[l]}),o(s,{type:"danger",onClick:()=>le(e)},N(i=P("common.delete"))?i:{default:()=>[i]})])}}]),{tableRegister:H,tableState:T,tableMethods:B}=k({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=T,a=await A(V.value,e.value,t.value);return{list:a.data.list,total:a.data.total}}}),{loading:M,dataList:O,total:$,currentPage:G,pageSize:J}=T,{getList:K,getElTableExpose:q}=B;function Q(){return{background:"var(--el-fill-color-light)"}}const X=a(!1),Y=async()=>{ee.id="",ee.color="null",ee.regular="",ee.name="",ee.state=!0,X.value=!0},Z=()=>{X.value=!1};let ee=l({id:"",name:"",regular:"",color:"null",state:!0});const te=e=>{ee.id=e.id,ee.color=e.color,ee.regular=e.regular,ee.name=e.name,ee.state=e.state,X.value=!0},ae=a(!1),le=async e=>{ae.value=!0;try{await I([e.id]);ae.value=!1,K()}catch(t){ae.value=!1,K()}},oe=a([]),ie=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{oe.value=[];const e=await q(),t=(null==e?void 0:e.getSelectionRows())||[];oe.value=t.map((e=>e.id)),ae.value=!0;try{await I(oe.value),ae.value=!1,K()}catch(a){ae.value=!1,K()}})()},se=async e=>{if(window.confirm("Are you sure you want to update the selected data?")){oe.value=[];const t=await q(),a=(null==t?void 0:t.getSelectionRows())||[];oe.value=a.map((e=>e.id)),await E(oe.value,e),K()}};return(t,a)=>(r(),u(n,null,[o(p(e),null,{default:m((()=>[o(p(h),{gutter:20,style:{"margin-bottom":"15px"}},{default:m((()=>[o(p(b),{span:1},{default:m((()=>[o(p(x),{class:"mx-1",style:{position:"relative",top:"8px",left:"30%"}},{default:m((()=>[d(c(p(P)("sensitiveInformation.sensitiveName"))+" :",1)])),_:1})])),_:1}),o(p(b),{span:5},{default:m((()=>[o(p(f),{modelValue:V.value,"onUpdate:modelValue":a[0]||(a[0]=e=>V.value=e),placeholder:p(P)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(p(b),{span:5},{default:m((()=>[o(p(g),{type:"primary",icon:p(_),style:{height:"38px"},onClick:W},{default:m((()=>[d("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(p(h),{gutter:60},{default:m((()=>[o(p(b),{span:1},{default:m((()=>[v("div",L,[o(p(g),{type:"primary",onClick:Y},{default:m((()=>[d(c(p(P)("common.new")),1)])),_:1})])])),_:1}),o(p(b),{span:1},{default:m((()=>[v("div",R,[o(p(g),{type:"success",onClick:a[1]||(a[1]=e=>se(!0))},{default:m((()=>[d(c(p(P)("common.on")),1)])),_:1})])])),_:1}),o(p(b),{span:1},{default:m((()=>[v("div",U,[o(p(g),{type:"danger",onClick:a[2]||(a[2]=e=>se(!1))},{default:m((()=>[d(c(p(P)("common.off")),1)])),_:1})])])),_:1}),o(p(b),{span:1},{default:m((()=>[v("div",F,[o(p(s),{type:"danger",loading:ae.value,onClick:ie},{default:m((()=>[d(c(p(P)("common.delete")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),o(p(w),{pageSize:p(J),"onUpdate:pageSize":a[3]||(a[3]=e=>y(J)?J.value=e:null),currentPage:p(G),"onUpdate:currentPage":a[4]||(a[4]=e=>y(G)?G.value=e:null),columns:D,data:p(O),stripe:"",border:!0,loading:p(M),resizable:!0,pagination:{total:p($),pageSizes:[10,20,50,100,200,500,1e3]},onRegister:p(H),headerCellStyle:Q,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister"])])),_:1}),o(p(S),{modelValue:X.value,"onUpdate:modelValue":a[5]||(a[5]=e=>X.value=e),title:p(ee).id?t.$t("common.edit"):t.$t("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:300},{default:m((()=>[o(z,{closeDialog:Z,sensitiveForm:p(ee),getList:p(K)},null,8,["sensitiveForm","getList"])])),_:1},8,["modelValue","title"])],64))}});export{P as default};
