import{d as e,dC as t,H as l,r as a,s,e as i,z as o,F as n,A as r,o as m,i as d,w as p,a as c,B as u,j as h,J as v,l as f,K as g,M as j,_ as y}from"./index-3XfDPlIS.js";import{u as b}from"./useTable-BezX3TfM.js";import{E as x}from"./el-card-CuEws33_.js";import{E as _,a as w}from"./el-col-CN1tVfqh.js";import{E as S}from"./el-text-CLWE0mUm.js";import{_ as C}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as E}from"./useCrudSchemas-6tFKup3N.js";import{f as z}from"./index-BfB8wPHh.js";import{y as W}from"./index-BAb9yQka.js";import"./el-table-column-B5hg3WH6.js";import"./el-popper-DVoWBu_3.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-DcMbxLLg.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./tree-BfZhwLPs.js";import"./index-Dz8ZrwBc.js";function I(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!v(e)}const V=y(e({__name:"Service",setup(e){const{t:v}=f(),{query:y}=t();l((()=>{k(),window.addEventListener("resize",k)}));const V=a(0),k=()=>{const e=window.innerHeight||document.documentElement.clientHeight;V.value=.8*e};a("");const A=s({});A.project=[y.id];const U=async e=>{Object.assign(A,e),K()},H=s([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:v("tableDemo.index"),type:"index",minWidth:"30"},{field:"service",label:v("asset.service"),minWidth:"100",formatter:(e,t,l)=>e.count?i(n,null,[i(S,null,I(l)?l:{default:()=>[l]}),i(S,{type:"info"},{default:()=>[o("("),e.count,o(")")]})]):i(S,null,I(l)?l:{default:()=>[l]}),slots:{header:()=>i("div",null,[i("span",null,[v("asset.service")]),i(r,{modelValue:P.value,"onUpdate:modelValue":e=>P.value=e,placeholder:"Search",style:"width: 80px; margin-left: 10px;",size:"small",onChange:()=>$("service_service")},null)])}},{field:"host",label:v("asset.domain"),minWidth:"200",slots:{header:()=>i("div",null,[i("span",null,[v("asset.domain")]),i(r,{modelValue:q.value,"onUpdate:modelValue":e=>q.value=e,placeholder:"Search",style:"width: 80px; margin-left: 10px;",size:"small",onChange:()=>$("service_domain")},null)])}},{field:"ip",label:"IP",minWidth:"250",slots:{header:()=>i("div",null,[i("span",null,[o("IP")]),i(r,{modelValue:J.value,"onUpdate:modelValue":e=>J.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>$("service_ip")},null)])}},{field:"port",label:v("asset.port"),minWidth:"250",slots:{header:()=>i("div",null,[i("span",null,[v("asset.port")]),i(r,{modelValue:N.value,"onUpdate:modelValue":e=>N.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>$("service_port")},null)])}},{field:"time",label:v("asset.time"),minWidth:"200"}]),{allSchemas:R}=E(H),{tableRegister:B,tableState:F,tableMethods:O}=b({fetchDataApi:async()=>({list:(await z("",A,G)).data.list}),immediate:!0}),{loading:T,dataList:D}=F,{getList:K,getElTableExpose:L}=O;function M(){return{background:"var(--el-fill-color-light)"}}const N=a(""),q=a(""),J=a(""),P=a(""),G=s({}),$=async e=>{let t="";"service_port"==e&&(t=N.value),"service_domain"==e&&(t=q.value),"service_ip"==e&&(t=J.value),"service_service"==e&&(t=P.value),G[e]=t,K()},Q=a([]),X=async()=>{g.confirm("Whether to delete?","Warning",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{const e=await L(),t=(null==e?void 0:e.getSelectionRows())||[];Q.value=t.map((e=>e.id)),await W(Q.value,"asset"),K()})).catch((()=>{j({type:"info",message:"Delete canceled"})}))};let Y=a(!1);const Z=async()=>{const e=await L(),t=(null==e?void 0:e.getSelectionRows())||[];Q.value=t.map((e=>e.id)),0!=Q.value.length?Y.value=!0:Y.value=!1};return(e,t)=>(m(),d(c(w),null,{default:p((()=>[i(c(_),null,{default:p((()=>[i(c(x),{style:{height:"min-content"}},{default:p((()=>[c(Y)?(m(),d(c(u),{key:0,onClick:X,type:"danger",size:"small"},{default:p((()=>[o("Dlete")])),_:1})):h("",!0),i(c(C),{columns:c(R).tableColumns,data:c(D),"max-height":V.value,border:!0,loading:c(T),onSelectionChange:Z,rowKey:"id",resizable:!0,onRegister:c(B),onFilterChange:U,headerCellStyle:M,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["columns","data","max-height","loading","onRegister"])])),_:1})])),_:1})])),_:1}))}}),[["__scopeId","data-v-f2d00836"]]);export{V as default};
