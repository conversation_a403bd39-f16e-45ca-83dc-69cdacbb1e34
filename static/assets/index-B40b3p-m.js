import{r as a}from"./index-CnCQNuY4.js";const t=(t,e,s)=>a.post({url:"/api/task/data",data:{search:t,pageIndex:e,pageSize:s}}),e=t=>a.post({url:"/api/task/stop",data:{id:t}}),s=t=>a.post({url:"/api/task/start",data:{id:t}}),d=(t,e,s,d,p,l,i,r,o,u,g,k,n,c,h)=>a.post({url:"/api/task/add",data:{name:t,target:e,ignore:s,node:d,allNode:p,duplicates:l,scheduledTasks:i,hour:r,template:o,tp:u,targetTp:g,search:k,filter:n,targetNumber:c,targetIds:h}}),p=(t,e,s,d,p,l,i,r,o,u)=>a.post({url:"/api/task/scheduled/update",data:{id:t,name:e,target:s,ignore:d,node:p,allNode:l,duplicates:i,scheduledTasks:r,hour:o,template:u}}),l=t=>a.post({url:"/api/task/detail",data:{id:t}}),i=t=>a.post({url:"/api/task/scheduled/detail",data:{id:t}}),r=(t,e)=>a.post({url:"/api/task/delete",data:{ids:t,delA:e}}),o=t=>a.post({url:"/api/task/retest",data:{id:t}}),u=(t,e,s)=>a.post({url:"/api/task/progress/info",data:{id:t,pageIndex:e,pageSize:s}}),g=(t,e,s)=>a.post({url:"/api/task/scheduled/data",data:{search:t,pageIndex:e,pageSize:s}}),k=t=>a.post({url:"/api/task/scheduled/delete",data:{ids:t}}),n=(t,e,s)=>a.post({url:"/api/task/scheduled/pagemonit/data",data:{search:t,pageIndex:e,pageSize:s}}),c=t=>a.post({url:"/api/task/scheduled/pagemonit/delete",data:{ids:t}}),h=(t,e,s,d)=>a.post({url:"/api/task/scheduled/pagemonit/update",data:{hour:t,node:e,allNode:s,state:d}}),m=t=>a.post({url:"/api/task/scheduled/pagemonit/add",data:{url:t}}),x=(t,e,s)=>a.post({url:"/api/task/template/list",data:{search:t,pageIndex:e,pageSize:s}}),I=t=>a.post({url:"/api/task/template/detail",data:{id:t}}),z=(t,e)=>a.post({url:"/api/task/template/save",data:{result:t,id:e}}),S=t=>a.post({url:"/api/task/template/delete",data:{ids:t}});export{s as a,k as b,g as c,r as d,S as e,x as f,t as g,p as h,d as i,l as j,i as k,z as l,I as m,u as n,c as o,n as p,m as q,o as r,e as s,h as u};
