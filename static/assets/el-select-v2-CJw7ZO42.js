import{cn as e,d as t,aQ as l,a7 as a,r as o,a3 as s,a8 as n,a as i,H as r,aX as u,cb as c,aI as d,v as p,a4 as f,Y as m,b8 as v,aa as h,o as g,c as b,t as S,n as y,aH as x,f as w,bx as O,Z as I,$ as V,bm as C,ax as T,ay as M,ab as z,a9 as R,af as k,O as B,aN as E,e as D,aJ as L,bq as F,bK as H,aK as $,aA as K,aB as N,bb as W,s as j,bc as P,a5 as A,b7 as _,D as q,ci as Q,aF as U,aC as G,b9 as J,b1 as X,aS as Y,a1 as Z,a2 as ee,C as te,bv as le,a6 as ae,y as oe,P as se,Q as ne,w as ie,j as re,F as ue,R as ce,i as de,ae as pe,b2 as fe,ag as me,bQ as ve,bO as he,bP as ge}from"./index-3XfDPlIS.js";import{u as be,a as Se,E as ye}from"./el-popper-DVoWBu_3.js";import{t as xe,E as we}from"./el-tag-DcMbxLLg.js";import{H as Oe,V as Ie,v as Ve,I as Ce,S as Te,u as Me,i as ze,R as Re,g as ke,a as Be,b as Ee,c as De,B as Le,F as Fe,d as He,A as $e,e as Ke,C as Ne,E as We,f as je,h as Pe,D as Ae}from"./el-virtual-list-Drl4IGmp.js";import{c as _e,r as qe}from"./raf-BoCEWvzN.js";import{u as Qe,f as Ue}from"./useInput-SkgDzq11.js";import{e as Ge}from"./strings-Dm4Pnsdt.js";import{d as Je}from"./debounce-Cb7r1Afr.js";import{i as Xe,C as Ye}from"./index-tjM0-mlU.js";const Ze={[Oe]:"deltaX",[Ie]:"deltaY"},et=({name:h,getOffset:g,getItemSize:b,getItemOffset:S,getEstimatedTotalSize:y,getStartIndexForOffset:x,getStopIndexForStartIndex:w,initCache:O,clearCache:I,validateProps:V})=>t({name:null!=h?h:"ElVirtualList",props:Ve,emits:[Ce,Te],setup(t,{emit:d,expose:p}){V(t);const f=l(),h=a("vl"),C=o(O(t,f)),T=Me(),M=o(),z=o(),R=o(),k=o({isScrolling:!1,scrollDir:"forward",scrollOffset:s(t.initScrollOffset)?t.initScrollOffset:0,updateRequested:!1,isScrollbarDragging:!1,scrollbarAlwaysOn:t.scrollbarAlwaysOn}),B=n((()=>{const{total:e,cache:l}=t,{isScrolling:a,scrollDir:o,scrollOffset:s}=i(k);if(0===e)return[0,0,0,0];const n=x(t,s,i(C)),r=w(t,n,s,i(C)),u=a&&o!==Le?1:Math.max(1,l),c=a&&o!==Fe?1:Math.max(1,l);return[Math.max(0,n-u),Math.max(0,Math.min(e-1,r+c)),n,r]})),E=n((()=>y(t,i(C)))),D=n((()=>ze(t.layout))),L=n((()=>[{position:"relative",["overflow-"+(D.value?"x":"y")]:"scroll",WebkitOverflowScrolling:"touch",willChange:"transform"},{direction:t.direction,height:s(t.height)?`${t.height}px`:t.height,width:s(t.width)?`${t.width}px`:t.width},t.style])),F=n((()=>{const e=i(E),t=i(D);return{height:t?"100%":`${e}px`,pointerEvents:i(k).isScrolling?"none":void 0,width:t?`${e}px`:"100%"}})),H=n((()=>D.value?t.width:t.height)),{onWheel:$}=(({atEndEdge:t,atStartEdge:l,layout:a},o)=>{let s,n=0;const i=e=>e<0&&l.value||e>0&&t.value;return{hasReachedEdge:i,onWheel:t=>{_e(s);const l=t[Ze[a.value]];i(n)&&i(n+l)||(n+=l,e()||t.preventDefault(),s=qe((()=>{o(n),n=0})))}}})({atStartEdge:n((()=>k.value.scrollOffset<=0)),atEndEdge:n((()=>k.value.scrollOffset>=E.value)),layout:n((()=>t.layout))},(e=>{var t,l;null==(l=(t=R.value).onMouseUp)||l.call(t),N(Math.min(k.value.scrollOffset+e,E.value-H.value))})),K=()=>{const{total:e}=t;if(e>0){const[e,t,l,a]=i(B);d(Ce,e,t,l,a)}const{scrollDir:l,scrollOffset:a,updateRequested:o}=i(k);d(Te,l,a,o)},N=e=>{(e=Math.max(e,0))!==i(k).scrollOffset&&(k.value={...i(k),scrollOffset:e,scrollDir:He(i(k).scrollOffset,e),updateRequested:!0},m(j))},W=(e,l=$e)=>{const{scrollOffset:a}=i(k);e=Math.max(0,Math.min(e,t.total-1)),N(g(t,e,l,a,i(C)))},j=()=>{k.value.isScrolling=!1,m((()=>{T.value(-1,null,null)}))},P=()=>{const e=M.value;e&&(e.scrollTop=0)};r((()=>{if(!u)return;const{initScrollOffset:e}=t,l=i(M);s(e)&&l&&(i(D)?l.scrollLeft=e:l.scrollTop=e),K()})),c((()=>{const{direction:e,layout:l}=t,{scrollOffset:a,updateRequested:o}=i(k),s=i(M);if(o&&s)if(l===Oe)if(e===Re)switch(ke()){case Ee:s.scrollLeft=-a;break;case Be:s.scrollLeft=a;break;default:{const{clientWidth:e,scrollWidth:t}=s;s.scrollLeft=t-e-a;break}}else s.scrollLeft=a;else s.scrollTop=a}));const A={ns:h,clientSize:H,estimatedTotalSize:E,windowStyle:L,windowRef:M,innerRef:z,innerStyle:F,itemsToRender:B,scrollbarRef:R,states:k,getItemStyle:e=>{const{direction:l,itemSize:a,layout:o}=t,s=T.value(I&&a,I&&o,I&&l);let n;if(v(s,String(e)))n=s[e];else{const a=S(t,e,i(C)),o=b(t,e,i(C)),r=i(D),u=l===Re,c=r?a:0;s[e]=n={position:"absolute",left:u?void 0:`${c}px`,right:u?`${c}px`:void 0,top:r?0:`${a}px`,height:r?"100%":`${o}px`,width:r?`${o}px`:"100%"}}return n},onScroll:e=>{i(D)?(e=>{const{clientWidth:l,scrollLeft:a,scrollWidth:o}=e.currentTarget,s=i(k);if(s.scrollOffset===a)return;const{direction:n}=t;let r=a;if(n===Re)switch(ke()){case Ee:r=-a;break;case Ke:r=o-l-a}r=Math.max(0,Math.min(r,o-l)),k.value={...s,isScrolling:!0,scrollDir:He(s.scrollOffset,r),scrollOffset:r,updateRequested:!1},m(j)})(e):(e=>{const{clientHeight:t,scrollHeight:l,scrollTop:a}=e.currentTarget,o=i(k);if(o.scrollOffset===a)return;const s=Math.max(0,Math.min(a,l-t));k.value={...o,isScrolling:!0,scrollDir:He(o.scrollOffset,s),scrollOffset:s,updateRequested:!1},m(j)})(e),K()},onScrollbarScroll:(e,t)=>{const l=(E.value-H.value)/t*e;N(Math.min(E.value-H.value,l))},onWheel:$,scrollTo:N,scrollToItem:W,resetScrollTop:P};return p({windowRef:M,innerRef:z,getItemStyleCache:T,scrollTo:N,scrollToItem:W,resetScrollTop:P,states:k}),A},render(e){var t;const{$slots:l,className:a,clientSize:o,containerElement:s,data:n,getItemStyle:i,innerElement:r,itemsToRender:u,innerStyle:c,layout:m,total:v,onScroll:h,onScrollbarScroll:g,onWheel:b,states:S,useIsScrolling:y,windowStyle:x,ns:w}=e,[O,I]=u,V=d(s),C=d(r),T=[];if(v>0)for(let d=O;d<=I;d++)T.push(null==(t=l.default)?void 0:t.call(l,{data:n,key:d,index:d,isScrolling:y?S.isScrolling:void 0,style:i(d)}));const M=[p(C,{style:c,ref:"innerRef"},f(C)?T:{default:()=>T})],z=p(De,{ref:"scrollbarRef",clientSize:o,layout:m,onScroll:g,ratio:100*o/this.estimatedTotalSize,scrollFrom:S.scrollOffset/(this.estimatedTotalSize-o),total:v}),R=p(V,{class:[w.e("window"),a],style:x,onScroll:h,onWheel:b,ref:"windowRef",key:0},f(V)?[M]:{default:()=>[M]});return p("div",{key:0,class:[w.e("wrapper"),S.scrollbarAlwaysOn?"always-on":""]},[R,z])}}),tt=et({name:"ElFixedSizeList",getItemOffset:({itemSize:e},t)=>t*e,getItemSize:({itemSize:e})=>e,getEstimatedTotalSize:({total:e,itemSize:t})=>t*e,getOffset:({height:e,total:t,itemSize:l,layout:a,width:o},s,n,i)=>{const r=ze(a)?o:e,u=Math.max(0,t*l-r),c=Math.min(u,s*l),d=Math.max(0,(s+1)*l-r);switch(n===Pe&&(n=i>=d-r&&i<=c+r?$e:Ne),n){case je:return c;case We:return d;case Ne:{const e=Math.round(d+(c-d)/2);return e<Math.ceil(r/2)?0:e>u+Math.floor(r/2)?u:e}default:return i>=d&&i<=c?i:i<d?d:c}},getStartIndexForOffset:({total:e,itemSize:t},l)=>Math.max(0,Math.min(e-1,Math.floor(l/t))),getStopIndexForStartIndex:({height:e,total:t,itemSize:l,layout:a,width:o},s,n)=>{const i=s*l,r=ze(a)?o:e,u=Math.ceil((r+n-i)/l);return Math.max(0,Math.min(t-1,s+u-1))},initCache(){},clearCache:!0,validateProps(){}}),lt=(e,t,l)=>{const{itemSize:a}=e,{items:o,lastVisitedIndex:s}=l;if(t>s){let e=0;if(s>=0){const t=o[s];e=t.offset+t.size}for(let l=s+1;l<=t;l++){const t=a(l);o[l]={offset:e,size:t},e+=t}l.lastVisitedIndex=t}return o[t]},at=(e,t,l,a,o)=>{for(;l<=a;){const s=l+Math.floor((a-l)/2),n=lt(e,s,t).offset;if(n===o)return s;n<o?l=s+1:n>o&&(a=s-1)}return Math.max(0,l-1)},ot=(e,t,l,a)=>{const{total:o}=e;let s=1;for(;l<o&&lt(e,l,t).offset<a;)l+=s,s*=2;return at(e,t,Math.floor(l/2),Math.min(l,o-1),a)},st=({total:e},{items:t,estimatedItemSize:l,lastVisitedIndex:a})=>{let o=0;if(a>=e&&(a=e-1),a>=0){const e=t[a];o=e.offset+e.size}return o+(e-a-1)*l},nt=et({name:"ElDynamicSizeList",getItemOffset:(e,t,l)=>lt(e,t,l).offset,getItemSize:(e,t,{items:l})=>l[t].size,getEstimatedTotalSize:st,getOffset:(e,t,l,a,o)=>{const{height:s,layout:n,width:i}=e,r=ze(n)?i:s,u=lt(e,t,o),c=st(e,o),d=Math.max(0,Math.min(c-r,u.offset)),p=Math.max(0,u.offset-r+u.size);switch(l===Pe&&(l=a>=p-r&&a<=d+r?$e:Ne),l){case je:return d;case We:return p;case Ne:return Math.round(p+(d-p)/2);default:return a>=p&&a<=d?a:a<p?p:d}},getStartIndexForOffset:(e,t,l)=>((e,t,l)=>{const{items:a,lastVisitedIndex:o}=t;return(o>0?a[o].offset:0)>=l?at(e,t,0,o,l):ot(e,t,Math.max(0,o),l)})(e,l,t),getStopIndexForStartIndex:(e,t,l,a)=>{const{height:o,total:s,layout:n,width:i}=e,r=ze(n)?i:o,u=lt(e,t,a),c=l+r;let d=u.offset+u.size,p=t;for(;p<s-1&&d<c;)p++,d+=lt(e,p,a).size;return p},initCache({estimatedItemSize:e=Ae},t){const l={items:{},estimatedItemSize:e,lastVisitedIndex:-1,clearCacheAfterIndex:(e,a=!0)=>{var o,s;l.lastVisitedIndex=Math.min(l.lastVisitedIndex,e-1),null==(o=t.exposed)||o.getItemStyleCache(-1),a&&(null==(s=t.proxy)||s.$forceUpdate())}};return l},clearCache:!1,validateProps:({itemSize:e})=>{}});var it=h(t({props:{item:{type:Object,required:!0},style:Object,height:Number},setup:()=>({ns:a("select")})}),[["render",function(e,t,l,a,o,s){return e.item.isTitle?(g(),b("div",{key:0,class:y(e.ns.be("group","title")),style:x([e.style,{lineHeight:`${e.height}px`}])},S(e.item.label),7)):(g(),b("div",{key:1,class:y(e.ns.be("group","split")),style:x(e.style)},[w("span",{class:y(e.ns.be("group","split-dash")),style:x({top:e.height/2+"px"})},null,6)],6))}],["__file","group-item.vue"]]);const rt={label:"label",value:"value",disabled:"disabled",options:"options"};function ut(e){const t=n((()=>({...rt,...e.props})));return{aliasProps:t,getLabel:e=>O(e,t.value.label),getValue:e=>O(e,t.value.value),getDisabled:e=>O(e,t.value.disabled),getOptions:e=>O(e,t.value.options)}}const ct=I({allowCreate:Boolean,autocomplete:{type:V(String),default:"none"},automaticDropdown:Boolean,clearable:Boolean,clearIcon:{type:C,default:T},effect:{type:V(String),default:"light"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},defaultFirstOption:Boolean,disabled:Boolean,estimatedOptionHeight:{type:Number,default:void 0},filterable:Boolean,filterMethod:Function,height:{type:Number,default:274},itemHeight:{type:Number,default:34},id:String,loading:Boolean,loadingText:String,modelValue:{type:V([Array,String,Number,Boolean,Object])},multiple:Boolean,multipleLimit:{type:Number,default:0},name:String,noDataText:String,noMatchText:String,remoteMethod:Function,reserveKeyword:{type:Boolean,default:!0},options:{type:V(Array),required:!0},placeholder:{type:String},teleported:be.teleported,persistent:{type:Boolean,default:!0},popperClass:{type:String,default:""},popperOptions:{type:V(Object),default:()=>({})},remote:Boolean,size:M,props:{type:V(Object),default:()=>rt},valueKey:{type:String,default:"value"},scrollbarAlwaysOn:Boolean,validateEvent:{type:Boolean,default:!0},placement:{type:V(String),values:Se,default:"bottom-start"},fallbackPlacements:{type:V(Array),default:["bottom-start","top-start","right","left"]},tagType:{...xe.type,default:"info"},ariaLabel:{type:String,default:void 0}}),dt=I({data:Array,disabled:Boolean,hovering:Boolean,item:{type:V(Object),required:!0},index:Number,style:Object,selected:Boolean,created:Boolean}),pt=Symbol("ElSelectV2Injection"),ft=t({props:dt,emits:["select","hover"],setup(e,{emit:t}){const l=z(pt),o=a("select"),{hoverItem:s,selectOptionClick:n}=function(e,{emit:t}){return{hoverItem:()=>{e.disabled||t("hover",e.index)},selectOptionClick:()=>{e.disabled||t("select",e.item,e.index)}}}(e,{emit:t}),{getLabel:i}=ut(l.props);return{ns:o,hoverItem:s,selectOptionClick:n,getLabel:i}}}),mt=["aria-selected"];var vt=h(ft,[["render",function(e,t,l,a,o,s){return g(),b("li",{"aria-selected":e.selected,style:x(e.style),class:y([e.ns.be("dropdown","item"),e.ns.is("selected",e.selected),e.ns.is("disabled",e.disabled),e.ns.is("created",e.created),e.ns.is("hovering",e.hovering)]),onMouseenter:t[0]||(t[0]=(...t)=>e.hoverItem&&e.hoverItem(...t)),onClick:t[1]||(t[1]=k(((...t)=>e.selectOptionClick&&e.selectOptionClick(...t)),["stop"]))},[R(e.$slots,"default",{item:e.item,index:e.index,disabled:e.disabled},(()=>[w("span",null,S(e.getLabel(e.item)),1)]))],46,mt)}],["__file","option-item.vue"]]),ht=t({name:"ElSelectDropdown",props:{loading:Boolean,data:{type:Array,required:!0},hoveringIndex:Number,width:Number},setup(e,{slots:t,expose:l}){const s=z(pt),r=a("select"),{getLabel:u,getValue:c,getDisabled:d}=ut(s.props),p=o([]),f=o(),m=n((()=>e.data.length));B((()=>m.value),(()=>{var e,t;null==(t=(e=s.tooltipRef.value).updatePopper)||t.call(e)}));const v=n((()=>E(s.props.estimatedOptionHeight))),h=n((()=>v.value?{itemSize:s.props.itemHeight}:{estimatedSize:s.props.estimatedOptionHeight,itemSize:e=>p.value[e]})),g=(e,t)=>s.props.multiple?((e=[],t)=>{const{props:{valueKey:l}}=s;return F(t)?e&&e.some((e=>H(O(e,l))===O(t,l))):e.includes(t)})(e,c(t)):((e,t)=>{if(F(t)){const{valueKey:l}=s.props;return O(e,l)===O(t,l)}return e===t})(e,c(t)),b=(e,t)=>{const{disabled:l,multiple:a,multipleLimit:o}=s.props;return l||!t&&!!a&&o>0&&e.length>=o},S=t=>e.hoveringIndex===t;l({listRef:f,isSized:v,isItemDisabled:b,isItemHovering:S,isItemSelected:g,scrollToItem:e=>{const t=f.value;t&&t.scrollToItem(e)},resetScrollTop:()=>{const e=f.value;e&&e.resetScrollTop()}});const y=e=>{const{index:l,data:a,style:o}=e,n=i(v),{itemSize:r,estimatedSize:c}=i(h),{modelValue:p}=s.props,{onSelect:f,onHover:m}=s,y=a[l];if("Group"===y.type)return D(it,{item:y,style:o,height:n?r:c},null);const x=g(p,y),w=b(p,x),O=S(l);return D(vt,L(e,{selected:x,disabled:d(y)||w,created:!!y.created,hovering:O,item:y,onSelect:f,onHover:m}),{default:e=>{var l;return(null==(l=t.default)?void 0:l.call(t,e))||D("span",null,[u(y)])}})},{onKeyboardNavigate:x,onKeyboardSelect:w}=s,I=e=>{const{code:t}=e,{tab:l,esc:a,down:o,up:n,enter:i}=$;switch(t!==l&&(e.preventDefault(),e.stopPropagation()),t){case l:case a:s.expanded=!1;break;case o:x("forward");break;case n:x("backward");break;case i:w()}};return()=>{var l,a,o,n;const{data:u,width:c}=e,{height:d,multiple:p,scrollbarAlwaysOn:m}=s.props,g=i(v)?tt:nt;return D("div",{class:[r.b("dropdown"),r.is("multiple",p)],style:{width:`${c}px`}},[null==(l=t.header)?void 0:l.call(t),(null==(a=t.loading)?void 0:a.call(t))||(null==(o=t.empty)?void 0:o.call(t))||D(g,L({ref:f},i(h),{className:r.be("dropdown","list"),scrollbarAlwaysOn:m,data:u,height:d,width:c,total:u.length,onKeydown:I}),{default:e=>D(y,e,null)}),null==(n=t.footer)?void 0:n.call(t)])}}});function gt(e,t){const{aliasProps:l,getLabel:a,getValue:s}=ut(e),i=o(0),r=o(null),u=n((()=>e.allowCreate&&e.filterable));return{createNewOption:function(a){if(u.value)if(a&&a.length>0){if(function(l){const a=e=>s(e)===l;return e.options&&e.options.some(a)||t.createdOptions.some(a)}(a))return;const o={[l.value.value]:a,[l.value.label]:a,created:!0,[l.value.disabled]:!1};t.createdOptions.length>=i.value?t.createdOptions[i.value]=o:t.createdOptions.push(o)}else if(e.multiple)t.createdOptions.length=i.value;else{const e=r.value;t.createdOptions.length=0,e&&e.created&&t.createdOptions.push(e)}},removeNewOption:function(l){if(!u.value||!l||!l.created||l.created&&e.reserveKeyword&&t.inputValue===a(l))return;const o=t.createdOptions.findIndex((e=>s(e)===s(l)));~o&&(t.createdOptions.splice(o,1),i.value--)},selectNewOption:function(t){u.value&&(e.multiple&&t.created?i.value++:r.value=t)},clearAllNewOption:function(){u.value&&(t.createdOptions.length=0,i.value=0)}}}const bt=(e,t)=>{const{t:l}=K(),s=a("select"),i=a("input"),{form:u,formItem:c}=N(),{inputId:d}=W(e,{formItemContext:c}),{getLabel:p,getValue:f,getDisabled:v,getOptions:h}=ut(e),g=j({inputValue:"",cachedOptions:[],createdOptions:[],hoveringIndex:-1,inputHovering:!1,selectionWidth:0,calculatorWidth:0,collapseItemWidth:0,previousQuery:null,previousValue:void 0,selectedLabel:"",menuVisibleOnFocus:!1,isBeforeHide:!1}),b=o(-1),S=o(-1),y=o(null),x=o(null),w=o(null),I=o(null),V=o(null),C=o(null),T=o(null),M=o(null),z=o(null),R=o(null),k=o(null),{wrapperRef:E,isFocused:D,handleFocus:L,handleBlur:H}=P(V,{afterFocus(){e.automaticDropdown&&!ae.value&&(ae.value=!0,g.menuVisibleOnFocus=!0)},beforeBlur(e){var t,l;return(null==(t=w.value)?void 0:t.isFocusInsideContent(e))||(null==(l=I.value)?void 0:l.isFocusInsideContent(e))},afterBlur(){ae.value=!1,g.menuVisibleOnFocus=!1}}),te=o([]),le=o([]),ae=o(!1),oe=n((()=>e.disabled||(null==u?void 0:u.disabled))),se=n((()=>{const t=le.value.length*e.itemHeight;return t>e.height?e.height:t})),ne=n((()=>te.value.some((e=>""===f(e))))),ie=n((()=>e.multiple?A(e.modelValue)&&e.modelValue.length>0:!_(e.modelValue)&&(""!==e.modelValue||ne.value))),re=n((()=>e.clearable&&!oe.value&&g.inputHovering&&ie.value)),ue=n((()=>e.remote&&e.filterable?"":q)),ce=n((()=>ue.value&&s.is("reverse",ae.value))),de=n((()=>(null==c?void 0:c.validateState)||"")),pe=n((()=>Q[de.value])),fe=n((()=>e.remote?300:0)),me=n((()=>e.loading?e.loadingText||l("el.select.loading"):!(e.remote&&!g.inputValue&&0===te.value.length)&&(e.filterable&&g.inputValue&&te.value.length>0&&0===le.value.length?e.noMatchText||l("el.select.noMatch"):0===te.value.length?e.noDataText||l("el.select.noData"):null))),ve=t=>{const l=l=>{if(e.filterable&&Y(e.filterMethod))return!0;if(e.filterable&&e.remote&&Y(e.remoteMethod))return!0;const a=new RegExp(Ge(t),"i");return!t||a.test(p(l)||"")};return e.loading?[]:[...g.createdOptions,...e.options].reduce(((t,a)=>{const o=h(a);if(A(o)){const e=o.filter(l);e.length>0&&t.push({label:p(a),isTitle:!0,type:"Group"},...e,{type:"Group"})}else(e.remote||l(a))&&t.push(a);return t}),[])},he=()=>{te.value=ve(""),le.value=ve(g.inputValue)},ge=n((()=>{const e=new Map;return te.value.forEach(((t,l)=>{e.set(Ye(f(t)),{option:t,index:l})})),e})),be=n((()=>{const e=new Map;return le.value.forEach(((t,l)=>{e.set(Ye(f(t)),{option:t,index:l})})),e})),Se=n((()=>le.value.every((e=>v(e))))),ye=U(),xe=n((()=>"small"===ye.value?"small":"default")),we=()=>{var e;S.value=(null==(e=y.value)?void 0:e.offsetWidth)||200},Oe=n((()=>{const t=(()=>{if(!x.value)return 0;const e=window.getComputedStyle(x.value);return Number.parseFloat(e.gap||"6px")})();return{maxWidth:`${k.value&&1===e.maxCollapseTags?g.selectionWidth-g.collapseItemWidth-t:g.selectionWidth}px`}})),Ie=n((()=>({maxWidth:`${g.selectionWidth}px`}))),Ve=n((()=>({width:`${Math.max(g.calculatorWidth,11)}px`}))),Ce=n((()=>A(e.modelValue)?0===e.modelValue.length&&!g.inputValue:!e.filterable||!g.inputValue)),Te=n((()=>{var t;const a=null!=(t=e.placeholder)?t:l("el.select.placeholder");return e.multiple||!ie.value?a:g.selectedLabel})),Me=n((()=>{var e,t;return null==(t=null==(e=w.value)?void 0:e.popperRef)?void 0:t.contentRef})),ze=n((()=>{if(e.multiple){const t=e.modelValue.length;if(e.modelValue.length>0&&be.value.has(e.modelValue[t-1])){const{index:l}=be.value.get(e.modelValue[t-1]);return l}}else if(e.modelValue&&be.value.has(e.modelValue)){const{index:t}=be.value.get(e.modelValue);return t}return-1})),Re=n({get:()=>ae.value&&!1!==me.value,set(e){ae.value=e}}),ke=n((()=>e.multiple?e.collapseTags?g.cachedOptions.slice(0,e.maxCollapseTags):g.cachedOptions:[])),Be=n((()=>e.multiple&&e.collapseTags?g.cachedOptions.slice(e.maxCollapseTags):[])),{createNewOption:Ee,removeNewOption:De,selectNewOption:Le,clearAllNewOption:Fe}=gt(e,g),{handleCompositionStart:He,handleCompositionUpdate:$e,handleCompositionEnd:Ke}=Qe((e=>rt(e))),Ne=()=>{oe.value||(g.menuVisibleOnFocus?g.menuVisibleOnFocus=!1:ae.value=!ae.value)},We=()=>{g.inputValue.length>0&&!ae.value&&(ae.value=!0),Ee(g.inputValue),Pe(g.inputValue)},je=Je(We,fe.value),Pe=t=>{g.previousQuery!==t&&(g.previousQuery=t,e.filterable&&Y(e.filterMethod)?e.filterMethod(t):e.filterable&&e.remote&&Y(e.remoteMethod)&&e.remoteMethod(t),e.defaultFirstOption&&(e.filterable||e.remote)&&le.value.length?m(Ae):m(it))},Ae=()=>{const e=le.value.filter((e=>!e.disabled&&"Group"!==e.type)),t=e.find((e=>e.created)),l=e[0];g.hoveringIndex=qe(le.value,t||l)},_e=l=>{t(Z,l),(l=>{Xe(e.modelValue,l)||t(ee,l)})(l),g.previousValue=String(l)},qe=(t=[],l)=>{if(!F(l))return t.indexOf(l);const a=e.valueKey;let o=-1;return t.some(((e,t)=>O(e,a)===O(l,a)&&(o=t,!0))),o},Ye=t=>F(t)?O(t,e.valueKey):t,Ze=()=>{we()},et=()=>{g.selectionWidth=x.value.getBoundingClientRect().width},tt=()=>{g.calculatorWidth=C.value.getBoundingClientRect().width},lt=()=>{var e,t;null==(t=null==(e=w.value)?void 0:e.updatePopper)||t.call(e)},at=()=>{var e,t;null==(t=null==(e=I.value)?void 0:e.updatePopper)||t.call(e)},ot=(t,l)=>{if(e.multiple){let l=e.modelValue.slice();const a=qe(l,f(t));a>-1?(l=[...l.slice(0,a),...l.slice(a+1)],g.cachedOptions.splice(a,1),De(t)):(e.multipleLimit<=0||l.length<e.multipleLimit)&&(l=[...l,f(t)],g.cachedOptions.push(t),Le(t)),_e(l),t.created&&Pe(""),e.filterable&&!e.reserveKeyword&&(g.inputValue="")}else b.value=l,g.selectedLabel=p(t),_e(f(t)),ae.value=!1,Le(t),t.created||Fe();st()},st=()=>{var e;null==(e=V.value)||e.focus()},nt=(e,t=void 0)=>{const l=le.value;if(!["forward","backward"].includes(e)||oe.value||l.length<=0||Se.value)return;if(!ae.value)return Ne();void 0===t&&(t=g.hoveringIndex);let a=-1;"forward"===e?(a=t+1,a>=l.length&&(a=0)):"backward"===e&&(a=t-1,(a<0||a>=l.length)&&(a=l.length-1));const o=l[a];if(v(o)||"Group"===o.type)return nt(e,a);g.hoveringIndex=a,ct(a)},it=()=>{e.multiple?g.hoveringIndex=le.value.findIndex((t=>e.modelValue.some((e=>Ye(e)===Ye(t))))):g.hoveringIndex=le.value.findIndex((t=>Ye(t)===Ye(e.modelValue)))},rt=t=>{if(g.inputValue=t.target.value,!e.remote)return We();je()},ct=e=>{z.value.scrollToItem(e)},dt=e=>{const t=Ye(e);if(ge.value.has(t)){const{option:e}=ge.value.get(t);return e}return{value:e,label:e}},pt=()=>{if(e.multiple)if(e.modelValue.length>0){g.cachedOptions.length=0,g.previousValue=e.modelValue.toString();for(const t of e.modelValue){const e=dt(t);g.cachedOptions.push(e)}}else g.cachedOptions=[],g.previousValue=void 0;else if(ie.value){g.previousValue=e.modelValue;const t=le.value,l=t.findIndex((t=>Ye(f(t))===Ye(e.modelValue)));g.selectedLabel=~l?p(t[l]):Ye(e.modelValue)}else g.selectedLabel="",g.previousValue=void 0;Fe(),we()};return B(ae,(e=>{e?Pe(""):(g.inputValue="",g.previousQuery=null,g.isBeforeHide=!0,Ee("")),t("visible-change",e)})),B((()=>e.modelValue),((t,l)=>{var a;t&&t.toString()===g.previousValue||pt(),!Xe(t,l)&&e.validateEvent&&(null==(a=null==c?void 0:c.validate)||a.call(c,"change").catch((e=>G())))}),{deep:!0}),B((()=>e.options),(()=>{const e=V.value;(!e||e&&document.activeElement!==e)&&pt()}),{deep:!0,flush:"post"}),B((()=>le.value),(()=>z.value&&m(z.value.resetScrollTop))),J((()=>{g.isBeforeHide||he()})),J((()=>{const{valueKey:t,options:l}=e,a=new Map;for(const e of l){const l=f(e);let o=l;if(F(o)&&(o=O(l,t)),a.get(o))break;a.set(o,!0)}})),r((()=>{pt()})),X(y,Ze),X(x,et),X(C,tt),X(z,lt),X(E,lt),X(R,at),X(k,(()=>{g.collapseItemWidth=k.value.getBoundingClientRect().width})),{inputId:d,collapseTagSize:xe,currentPlaceholder:Te,expanded:ae,emptyText:me,popupHeight:se,debounce:fe,allOptions:te,filteredOptions:le,iconComponent:ue,iconReverse:ce,tagStyle:Oe,collapseTagStyle:Ie,inputStyle:Ve,popperSize:S,dropdownMenuVisible:Re,hasModelValue:ie,shouldShowPlaceholder:Ce,selectDisabled:oe,selectSize:ye,showClearBtn:re,states:g,isFocused:D,nsSelect:s,nsInput:i,calculatorRef:C,inputRef:V,menuRef:z,tagMenuRef:R,tooltipRef:w,tagTooltipRef:I,selectRef:y,wrapperRef:E,selectionRef:x,prefixRef:T,suffixRef:M,collapseItemRef:k,popperRef:Me,validateState:de,validateIcon:pe,showTagList:ke,collapseTagList:Be,debouncedOnInputChange:je,deleteTag:(l,a)=>{let o=e.modelValue.slice();const s=qe(o,f(a));s>-1&&!oe.value&&(o=[...e.modelValue.slice(0,s),...e.modelValue.slice(s+1)],g.cachedOptions.splice(s,1),_e(o),t("remove-tag",f(a)),De(a)),l.stopPropagation(),st()},getLabel:p,getValue:f,getDisabled:v,getValueKey:Ye,handleBlur:H,handleClear:()=>{let l;l=A(e.modelValue)?[]:void 0,e.multiple?g.cachedOptions=[]:g.selectedLabel="",ae.value=!1,_e(l),t("clear"),Fe(),st()},handleClickOutside:e=>{if(ae.value=!1,D.value){const t=new FocusEvent("focus",e);H(t)}},handleDel:t=>{if(e.multiple&&(t.code!==$.delete&&0===g.inputValue.length)){t.preventDefault();const l=e.modelValue.slice(),a=Ue(l,(e=>!g.cachedOptions.some((t=>f(t)===e&&v(t)))));if(a<0)return;l.splice(a,1);const o=g.cachedOptions[a];g.cachedOptions.splice(a,1),De(o),_e(l)}},handleEsc:()=>{g.inputValue.length>0?g.inputValue="":ae.value=!1},handleFocus:L,focus:st,blur:()=>{var e;null==(e=V.value)||e.blur()},handleMenuEnter:()=>m((()=>{~ze.value&&ct(g.hoveringIndex)})),handleResize:Ze,resetSelectionWidth:et,resetCalculatorWidth:tt,updateTooltip:lt,updateTagTooltip:at,updateOptions:he,toggleMenu:Ne,scrollTo:ct,onInput:rt,onKeyboardNavigate:nt,onKeyboardSelect:()=>{if(!ae.value)return Ne();~g.hoveringIndex&&le.value[g.hoveringIndex]&&ot(le.value[g.hoveringIndex],g.hoveringIndex)},onSelect:ot,onHover:e=>{g.hoveringIndex=e},handleCompositionStart:He,handleCompositionEnd:Ke,handleCompositionUpdate:$e}},St=t({name:"ElSelectV2",components:{ElSelectMenu:ht,ElTag:we,ElTooltip:ye,ElIcon:te},directives:{ClickOutside:Ye},props:ct,emits:[Z,ee,"remove-tag","clear","visible-change","focus","blur"],setup(e,{emit:t}){const l=n((()=>{const{modelValue:t,multiple:l}=e,a=l?[]:void 0;return A(t)?l?t:a:l?a:t})),a=bt(j({...le(e),modelValue:l}),t);return ae(pt,{props:j({...le(e),height:a.popupHeight,modelValue:l}),tooltipRef:a.tooltipRef,onSelect:a.onSelect,onHover:a.onHover,onKeyboardNavigate:a.onKeyboardNavigate,onKeyboardSelect:a.onKeyboardSelect}),{...a,modelValue:l}}}),yt=["id","autocomplete","aria-expanded","aria-label","disabled","readonly","name"],xt=["textContent"];var wt=h(St,[["render",function(e,t,l,a,o,s){const n=oe("el-tag"),i=oe("el-tooltip"),r=oe("el-icon"),u=oe("el-select-menu"),c=se("click-outside");return ne((g(),b("div",{ref:"selectRef",class:y([e.nsSelect.b(),e.nsSelect.m(e.selectSize)]),onMouseenter:t[14]||(t[14]=t=>e.states.inputHovering=!0),onMouseleave:t[15]||(t[15]=t=>e.states.inputHovering=!1),onClick:t[16]||(t[16]=k(((...t)=>e.toggleMenu&&e.toggleMenu(...t)),["prevent","stop"]))},[D(i,{ref:"tooltipRef",visible:e.dropdownMenuVisible,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"gpu-acceleration":!1,"stop-popper-mouse-event":!1,"popper-options":e.popperOptions,"fallback-placements":e.fallbackPlacements,effect:e.effect,placement:e.placement,pure:"",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,trigger:"click",persistent:e.persistent,onBeforeShow:e.handleMenuEnter,onHide:t[13]||(t[13]=t=>e.states.isBeforeHide=!1)},{default:ie((()=>[w("div",{ref:"wrapperRef",class:y([e.nsSelect.e("wrapper"),e.nsSelect.is("focused",e.isFocused),e.nsSelect.is("hovering",e.states.inputHovering),e.nsSelect.is("filterable",e.filterable),e.nsSelect.is("disabled",e.selectDisabled)])},[e.$slots.prefix?(g(),b("div",{key:0,ref:"prefixRef",class:y(e.nsSelect.e("prefix"))},[R(e.$slots,"prefix")],2)):re("v-if",!0),w("div",{ref:"selectionRef",class:y([e.nsSelect.e("selection"),e.nsSelect.is("near",e.multiple&&!e.$slots.prefix&&!!e.modelValue.length)])},[e.multiple?R(e.$slots,"tag",{key:0},(()=>[(g(!0),b(ue,null,ce(e.showTagList,(t=>(g(),b("div",{key:e.getValueKey(e.getValue(t)),class:y(e.nsSelect.e("selected-item"))},[D(n,{closable:!e.selectDisabled&&!e.getDisabled(t),size:e.collapseTagSize,type:e.tagType,"disable-transitions":"",style:x(e.tagStyle),onClose:l=>e.deleteTag(l,t)},{default:ie((()=>[w("span",{class:y(e.nsSelect.e("tags-text"))},S(e.getLabel(t)),3)])),_:2},1032,["closable","size","type","style","onClose"])],2)))),128)),e.collapseTags&&e.modelValue.length>e.maxCollapseTags?(g(),de(i,{key:0,ref:"tagTooltipRef",disabled:e.dropdownMenuVisible||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:ie((()=>[w("div",{ref:"collapseItemRef",class:y(e.nsSelect.e("selected-item"))},[D(n,{closable:!1,size:e.collapseTagSize,type:e.tagType,style:x(e.collapseTagStyle),"disable-transitions":""},{default:ie((()=>[w("span",{class:y(e.nsSelect.e("tags-text"))}," + "+S(e.modelValue.length-e.maxCollapseTags),3)])),_:1},8,["size","type","style"])],2)])),content:ie((()=>[w("div",{ref:"tagMenuRef",class:y(e.nsSelect.e("selection"))},[(g(!0),b(ue,null,ce(e.collapseTagList,(t=>(g(),b("div",{key:e.getValueKey(e.getValue(t)),class:y(e.nsSelect.e("selected-item"))},[D(n,{class:"in-tooltip",closable:!e.selectDisabled&&!e.getDisabled(t),size:e.collapseTagSize,type:e.tagType,"disable-transitions":"",onClose:l=>e.deleteTag(l,t)},{default:ie((()=>[w("span",{class:y(e.nsSelect.e("tags-text"))},S(e.getLabel(t)),3)])),_:2},1032,["closable","size","type","onClose"])],2)))),128))],2)])),_:1},8,["disabled","effect","teleported"])):re("v-if",!0)])):re("v-if",!0),e.selectDisabled?re("v-if",!0):(g(),b("div",{key:1,class:y([e.nsSelect.e("selected-item"),e.nsSelect.e("input-wrapper"),e.nsSelect.is("hidden",!e.filterable)])},[ne(w("input",{id:e.inputId,ref:"inputRef","onUpdate:modelValue":t[0]||(t[0]=t=>e.states.inputValue=t),style:x(e.inputStyle),autocomplete:e.autocomplete,"aria-autocomplete":"list","aria-haspopup":"listbox",autocapitalize:"off","aria-expanded":e.expanded,"aria-label":e.ariaLabel,class:y([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,role:"combobox",readonly:!e.filterable,spellcheck:"false",type:"text",name:e.name,onFocus:t[1]||(t[1]=(...t)=>e.handleFocus&&e.handleFocus(...t)),onBlur:t[2]||(t[2]=(...t)=>e.handleBlur&&e.handleBlur(...t)),onInput:t[3]||(t[3]=(...t)=>e.onInput&&e.onInput(...t)),onCompositionstart:t[4]||(t[4]=(...t)=>e.handleCompositionStart&&e.handleCompositionStart(...t)),onCompositionupdate:t[5]||(t[5]=(...t)=>e.handleCompositionUpdate&&e.handleCompositionUpdate(...t)),onCompositionend:t[6]||(t[6]=(...t)=>e.handleCompositionEnd&&e.handleCompositionEnd(...t)),onKeydown:[t[7]||(t[7]=pe(k((t=>e.onKeyboardNavigate("backward")),["stop","prevent"]),["up"])),t[8]||(t[8]=pe(k((t=>e.onKeyboardNavigate("forward")),["stop","prevent"]),["down"])),t[9]||(t[9]=pe(k(((...t)=>e.onKeyboardSelect&&e.onKeyboardSelect(...t)),["stop","prevent"]),["enter"])),t[10]||(t[10]=pe(k(((...t)=>e.handleEsc&&e.handleEsc(...t)),["stop","prevent"]),["esc"])),t[11]||(t[11]=pe(k(((...t)=>e.handleDel&&e.handleDel(...t)),["stop"]),["delete"]))],onClick:t[12]||(t[12]=k(((...t)=>e.toggleMenu&&e.toggleMenu(...t)),["stop"]))},null,46,yt),[[fe,e.states.inputValue]]),e.filterable?(g(),b("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:y(e.nsSelect.e("input-calculator")),textContent:S(e.states.inputValue)},null,10,xt)):re("v-if",!0)],2)),e.shouldShowPlaceholder?(g(),b("div",{key:2,class:y([e.nsSelect.e("selected-item"),e.nsSelect.e("placeholder"),e.nsSelect.is("transparent",!e.hasModelValue||e.expanded&&!e.states.inputValue)])},[w("span",null,S(e.currentPlaceholder),1)],2)):re("v-if",!0)],2),w("div",{ref:"suffixRef",class:y(e.nsSelect.e("suffix"))},[e.iconComponent?ne((g(),de(r,{key:0,class:y([e.nsSelect.e("caret"),e.nsInput.e("icon"),e.iconReverse])},{default:ie((()=>[(g(),de(d(e.iconComponent)))])),_:1},8,["class"])),[[me,!e.showClearBtn]]):re("v-if",!0),e.showClearBtn&&e.clearIcon?(g(),de(r,{key:1,class:y([e.nsSelect.e("caret"),e.nsInput.e("icon")]),onClick:k(e.handleClear,["prevent","stop"])},{default:ie((()=>[(g(),de(d(e.clearIcon)))])),_:1},8,["class","onClick"])):re("v-if",!0),e.validateState&&e.validateIcon?(g(),de(r,{key:2,class:y([e.nsInput.e("icon"),e.nsInput.e("validateIcon")])},{default:ie((()=>[(g(),de(d(e.validateIcon)))])),_:1},8,["class"])):re("v-if",!0)],2)],2)])),content:ie((()=>[D(u,{ref:"menuRef",data:e.filteredOptions,width:e.popperSize,"hovering-index":e.states.hoveringIndex,"scrollbar-always-on":e.scrollbarAlwaysOn},ve({default:ie((t=>[R(e.$slots,"default",he(ge(t)))])),_:2},[e.$slots.header?{name:"header",fn:ie((()=>[w("div",{class:y(e.nsSelect.be("dropdown","header"))},[R(e.$slots,"header")],2)]))}:void 0,e.$slots.loading&&e.loading?{name:"loading",fn:ie((()=>[w("div",{class:y(e.nsSelect.be("dropdown","loading"))},[R(e.$slots,"loading")],2)]))}:e.loading||0===e.filteredOptions.length?{name:"empty",fn:ie((()=>[w("div",{class:y(e.nsSelect.be("dropdown","empty"))},[R(e.$slots,"empty",{},(()=>[w("span",null,S(e.emptyText),1)]))],2)]))}:void 0,e.$slots.footer?{name:"footer",fn:ie((()=>[w("div",{class:y(e.nsSelect.be("dropdown","footer"))},[R(e.$slots,"footer")],2)]))}:void 0]),1032,["data","width","hovering-index","scrollbar-always-on"])])),_:3},8,["visible","teleported","popper-class","popper-options","fallback-placements","effect","placement","transition","persistent","onBeforeShow"])],34)),[[c,e.handleClickOutside,e.popperRef]])}],["__file","select.vue"]]);wt.install=e=>{e.component(wt.name,wt)};const Ot=wt;export{Ot as E,tt as F};
