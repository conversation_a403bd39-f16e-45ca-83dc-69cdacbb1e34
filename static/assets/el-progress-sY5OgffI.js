import{Y as e,Z as t,d as a,a6 as s,a7 as r,cz as n,ct as o,ax as i,aO as l,bd as c,aS as u,a3 as d,o as p,c as f,n as h,a as y,f as g,aH as v,a8 as k,t as b,j as x,i as $,w as m,aI as w,C as I,a9 as N,ag as B}from"./index-C6fb_XFi.js";const F=e({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:e=>e>=0&&e<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:{type:Boolean,default:!1},duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:t(String),default:"round"},textInside:{type:Boolean,default:!1},width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:t([String,Array,Function]),default:""},striped:Boolean,stripedFlow:<PERSON>olean,format:{type:t(Function),default:e=>`${e}%`}}),S=["aria-valuenow"],D={viewBox:"0 0 100 100"},T=["d","stroke","stroke-linecap","stroke-width"],W=["d","stroke","opacity","stroke-linecap","stroke-width"],L={key:0},_=a({name:"ElProgress"});const j=B(N(a({..._,props:F,setup(e){const t=e,a={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},N=s("progress"),B=r((()=>({width:`${t.percentage}%`,animationDuration:`${t.duration}s`,backgroundColor:Y(t.percentage)}))),F=r((()=>(t.strokeWidth/t.width*100).toFixed(1))),_=r((()=>["circle","dashboard"].includes(t.type)?Number.parseInt(""+(50-Number.parseFloat(F.value)/2),10):0)),j=r((()=>{const e=_.value,a="dashboard"===t.type;return`\n          M 50 50\n          m 0 ${a?"":"-"}${e}\n          a ${e} ${e} 0 1 1 0 ${a?"-":""}${2*e}\n          a ${e} ${e} 0 1 1 0 ${a?"":"-"}${2*e}\n          `})),z=r((()=>2*Math.PI*_.value)),C=r((()=>"dashboard"===t.type?.75:1)),E=r((()=>`${-1*z.value*(1-C.value)/2}px`)),M=r((()=>({strokeDasharray:`${z.value*C.value}px, ${z.value}px`,strokeDashoffset:E.value}))),P=r((()=>({strokeDasharray:`${z.value*C.value*(t.percentage/100)}px, ${z.value}px`,strokeDashoffset:E.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"}))),A=r((()=>{let e;return e=t.color?Y(t.percentage):a[t.status]||a.default,e})),H=r((()=>"warning"===t.status?n:"line"===t.type?"success"===t.status?o:i:"success"===t.status?l:c)),O=r((()=>"line"===t.type?12+.4*t.strokeWidth:.111111*t.width+2)),V=r((()=>t.format(t.percentage)));const Y=e=>{var a;const{color:s}=t;if(u(s))return s(e);if(d(s))return s;{const t=function(e){const t=100/e.length;return e.map(((e,a)=>d(e)?{color:e,percentage:(a+1)*t}:e)).sort(((e,t)=>e.percentage-t.percentage))}(s);for(const a of t)if(a.percentage>e)return a.color;return null==(a=t[t.length-1])?void 0:a.color}};return(e,t)=>(p(),f("div",{class:h([y(N).b(),y(N).m(e.type),y(N).is(e.status),{[y(N).m("without-text")]:!e.showText,[y(N).m("text-inside")]:e.textInside}]),role:"progressbar","aria-valuenow":e.percentage,"aria-valuemin":"0","aria-valuemax":"100"},["line"===e.type?(p(),f("div",{key:0,class:h(y(N).b("bar"))},[g("div",{class:h(y(N).be("bar","outer")),style:v({height:`${e.strokeWidth}px`})},[g("div",{class:h([y(N).be("bar","inner"),{[y(N).bem("bar","inner","indeterminate")]:e.indeterminate},{[y(N).bem("bar","inner","striped")]:e.striped},{[y(N).bem("bar","inner","striped-flow")]:e.stripedFlow}]),style:v(y(B))},[(e.showText||e.$slots.default)&&e.textInside?(p(),f("div",{key:0,class:h(y(N).be("bar","innerText"))},[k(e.$slots,"default",{percentage:e.percentage},(()=>[g("span",null,b(y(V)),1)]))],2)):x("v-if",!0)],6)],6)],2)):(p(),f("div",{key:1,class:h(y(N).b("circle")),style:v({height:`${e.width}px`,width:`${e.width}px`})},[(p(),f("svg",D,[g("path",{class:h(y(N).be("circle","track")),d:y(j),stroke:`var(${y(N).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-linecap":e.strokeLinecap,"stroke-width":y(F),fill:"none",style:v(y(M))},null,14,T),g("path",{class:h(y(N).be("circle","path")),d:y(j),stroke:y(A),fill:"none",opacity:e.percentage?1:0,"stroke-linecap":e.strokeLinecap,"stroke-width":y(F),style:v(y(P))},null,14,W)]))],6)),!e.showText&&!e.$slots.default||e.textInside?x("v-if",!0):(p(),f("div",{key:2,class:h(y(N).e("text")),style:v({fontSize:`${y(O)}px`})},[k(e.$slots,"default",{percentage:e.percentage},(()=>[e.status?(p(),$(y(I),{key:1},{default:m((()=>[(p(),$(w(y(H))))])),_:1})):(p(),f("span",L,b(y(V)),1))]))],6))],10,S))}}),[["__file","progress.vue"]]));export{j as E};
