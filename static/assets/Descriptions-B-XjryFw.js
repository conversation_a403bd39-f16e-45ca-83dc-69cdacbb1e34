import{D as e}from"./Descriptions-Cpy883Lr.js";import{d as s,l,s as a,e as o,A as t,y as i,o as r,c as m,a as p,w as d,f as n,z as u,t as c,F as j,_ as f}from"./index-DfJTpRkj.js";import{u as b,F as x}from"./useForm-BObJP2_c.js";import{a as _}from"./el-form-DsaI0u2w.js";import{u as h}from"./useValidator-qKFa4-ga.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-col-B4Ik8fnS.js";import"./index-Co3LqSsp.js";import"./el-tag-CbhrEnto.js";import"./el-checkbox-DU4wMKRd.js";import"./index-DE7jtbbk.js";import"./el-radio-group-CTAZlJKV.js";/* empty css                          */import"./el-input-number-DV6Zl9Iq.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./el-virtual-list-DQOsVxKt.js";import"./raf-zH43jzIi.js";import"./el-select-v2-D406kAkc.js";import"./el-switch-C5ZBDFmL.js";import"./el-autocomplete-CyglTUOR.js";import"./el-divider-0NmzbuNU.js";/* empty css                */import"./el-tree-select-P6ZpyTcB.js";import"./el-upload-BW2TOFr8.js";import"./el-progress-Wzr98AjO.js";import"./InputPassword-vnvlRugC.js";import"./style.css_vue_type_style_index_0_src_true_lang-CCQeJPcg.js";import"./JsonEditor.vue_vue_type_script_setup_true_lang-BI1SzYeb.js";import"./IconPicker-DBEypS2S.js";import"./el-tab-pane-BijWf7kq.js";import"./el-pagination-FJcT0ZDj.js";import"./isArrayLikeObject-DtpcG_un.js";import"./tsxHelper-DrslCeSo.js";/* empty css                        */import"./castArray-CvwAI87l.js";const D={class:"text-center mt-10px"},k=f(s({__name:"Descriptions",setup(s){const{required:f}=h(),{t:k}=l(),g=a({username:"chenkl",nickName:"梦似花落。",age:26,phone:"13655971xxxx",email:"<EMAIL>",addr:"这是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的地址",sex:"男",certy:"3505831994xxxxxxxx"}),V=a([{field:"username",label:k("descriptionsDemo.username")},{field:"nickName",label:k("descriptionsDemo.nickName")},{field:"phone",label:k("descriptionsDemo.phone")},{field:"email",label:k("descriptionsDemo.email")},{field:"addr",label:k("descriptionsDemo.addr"),span:24}]),N=a([{field:"username",label:k("descriptionsDemo.username"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"username"},{default:()=>[o(t,{modelValue:v.username,"onUpdate:modelValue":e=>v.username=e},null)]})}},{field:"nickName",label:k("descriptionsDemo.nickName"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"nickName"},{default:()=>[o(t,{modelValue:v.nickName,"onUpdate:modelValue":e=>v.nickName=e},null)]})}},{field:"phone",label:k("descriptionsDemo.phone"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"phone"},{default:()=>[o(t,{modelValue:v.phone,"onUpdate:modelValue":e=>v.phone=e},null)]})}},{field:"email",label:k("descriptionsDemo.email"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"email"},{default:()=>[o(t,{modelValue:v.email,"onUpdate:modelValue":e=>v.email=e},null)]})}},{field:"addr",label:k("descriptionsDemo.addr"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"addr"},{default:()=>[o(t,{modelValue:v.addr,"onUpdate:modelValue":e=>v.addr=e},null)]})},span:24}]),v=a({username:"",nickName:"",phone:"",email:"",addr:""}),y=a({username:[f()],nickName:[f()],phone:[f()],email:[f()],addr:[f()]}),{formRegister:q,formMethods:U}=b(),{getElFormExpose:w}=U,F=async()=>{const e=await w();null==e||e.validate((e=>{}))};return(s,l)=>{const a=i("BaseButton");return r(),m(j,null,[o(p(e),{title:p(k)("descriptionsDemo.descriptions"),message:p(k)("descriptionsDemo.descriptionsDes"),data:g,schema:V},null,8,["title","message","data","schema"]),o(p(x),{"is-custom":"",model:v,rules:y,onRegister:p(q)},{default:d((()=>[o(p(e),{title:p(k)("descriptionsDemo.form"),data:g,schema:N,class:"mt-20px"},null,8,["title","data","schema"]),n("div",D,[o(a,{onClick:F},{default:d((()=>[u(c(p(k)("formDemo.formValidation")),1)])),_:1})])])),_:1},8,["model","rules","onRegister"])],64)}}}),[["__scopeId","data-v-25b10d4d"]]);export{k as default};
