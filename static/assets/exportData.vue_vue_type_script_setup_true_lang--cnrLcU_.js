import{d as e,s as a,e as t,G as l,F as o,r as i,H as r,o as s,i as n,w as u,a as d,z as p,t as m,A as f,c,R as y,B as v,J as x,l as b}from"./index-3XfDPlIS.js";import{E as _,a as g}from"./el-tab-pane-xcqYouKU.js";import{E as h,a as w}from"./el-form-BY8piFS2.js";import{E as j,b as V}from"./el-radio-group-evFfsZkP.js";import{E}from"./el-tag-DcMbxLLg.js";import{E as C}from"./el-space-BFgHxiAK.js";import{E as S,a as T}from"./el-checkbox-DjLAvZXr.js";/* empty css                          */import{r as k}from"./index-Dz8ZrwBc.js";import{_ as A}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as R}from"./useTable-BezX3TfM.js";const U=e=>k.post({url:"/api/export/delete",data:{ids:e}});function F(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!x(e)}const q=e({__name:"exportData",props:{index:{},searchParams:{},getFilter:{type:Function}},setup(e){const{t:x}=b(),q=e,z=a({type:"all",quantity:0}),D=async()=>{J.value=!0;const e=q.getFilter();await((e,a,t,l,o,i,r)=>k.post({url:"/api/export",data:{index:e,quantity:a,type:t,search:l,filter:o,field:i,filetype:r}}))(q.index,z.quantity,z.type,q.searchParams,e,oe.value,se.value),J.value=!1},N=a([{field:"selection",type:"selection"},{field:"file_name",label:x("export.fileName")},{field:"state",label:x("export.state"),formatter:(e,a,l)=>{if(0==l){let e;return t(E,{type:"info"},F(e=x("export.run"))?e:{default:()=>[e]})}if(1==l){let e;return t(E,{type:"success"},F(e=x("export.success"))?e:{default:()=>[e]})}{let e;return t(E,{type:"danger"},F(e=x("export.fail"))?e:{default:()=>[e]})}}},{field:"create_time",label:x("export.createTime")},{field:"end_time",label:x("export.endTime"),formatter:(e,a,t)=>""==t?"-":t},{field:"data_type",label:x("export.type")},{field:"file_size",label:x("export.fileSize"),formatter:(e,a,t)=>""==t?"-":t+" MB"},{field:"action",label:x("tableDemo.action"),fixed:"right",formatter:(e,a,i)=>{let r,s;return t(o,null,[t(l,{type:"success",onClick:()=>G(e.file_name)},F(r=x("export.download"))?r:{default:()=>[r]}),t(l,{type:"danger",onClick:()=>K(e)},F(s=x("common.delete"))?s:{default:()=>[s]})])}}]),{tableRegister:B,tableState:I,tableMethods:M}=R({fetchDataApi:async()=>({list:(await k.get({url:"/api/export/record"})).data.list}),immediate:!1}),{dataList:P,loading:H}=I,{getList:L,getElTableExpose:O}=M,G=async e=>{const a=document.createElement("a");a.href="/api/export/download?file_name="+e,a.click()},J=i(!1),Q=e=>{"exportRecords"==e&&L()},Z=i(!1),K=async e=>{window.confirm("Are you sure you want to delete the selected data?")&&await W(e)},W=async e=>{Z.value=!0;try{await U([e.file_name]);Z.value=!1,L()}catch(a){Z.value=!1,L()}},X=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await $()},Y=i([]),$=async()=>{const e=await O(),a=(null==e?void 0:e.getSelectionRows())||[];Y.value=a.map((e=>e.file_name)),Z.value=!0;try{await U(Y.value);Z.value=!1,L()}catch(t){Z.value=!1,L()}},ee=i([]),ae=async()=>{const e=await(a=q.index,k.post({url:"/api/getfield",data:{index:a}}));var a;ee.value=e.data.field};r((()=>{ae()}));const te=i(!0),le=i(!1),oe=i([]),ie=e=>{oe.value=e?ee.value:[],te.value=!1},re=e=>{const a=e.length;le.value=a===ee.value.length,te.value=a>0&&a<ee.value.length},se=i("csv");return(e,a)=>(s(),n(d(g),{tabPosition:"left",onTabChange:Q,"model-value":"export"},{default:u((()=>[t(d(_),{label:d(x)("asset.export"),name:"export"},{default:u((()=>[t(d(h),{model:z,"label-width":"auto",style:{position:"relative"}},{default:u((()=>[t(d(w),{label:d(x)("export.exportType")},{default:u((()=>[t(d(j),{modelValue:z.type,"onUpdate:modelValue":a[0]||(a[0]=e=>z.type=e)},{default:u((()=>[t(d(V),{value:"all"},{default:u((()=>[p(m(d(x)("export.exportTypeAll")),1)])),_:1}),t(d(V),{value:"search"},{default:u((()=>[p(m(d(x)("export.exportTypeSearch")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),t(d(w),{label:d(x)("export.exportQuantity")},{default:u((()=>[t(d(f),{modelValue:z.quantity,"onUpdate:modelValue":a[1]||(a[1]=e=>z.quantity=e)},null,8,["modelValue"])])),_:1},8,["label"]),t(d(w),{label:d(x)("export.field")},{default:u((()=>[t(d(S),{modelValue:le.value,"onUpdate:modelValue":a[2]||(a[2]=e=>le.value=e),indeterminate:te.value,onChange:ie},{default:u((()=>[p(" All ")])),_:1},8,["modelValue","indeterminate"])])),_:1},8,["label"]),t(d(w),{label:" "},{default:u((()=>[t(d(T),{modelValue:oe.value,"onUpdate:modelValue":a[3]||(a[3]=e=>oe.value=e),onChange:re},{default:u((()=>[(s(!0),c(o,null,y(ee.value,(e=>(s(),n(d(S),{key:e,label:e,value:e},{default:u((()=>[p(m(e),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),t(d(w),{label:d(x)("export.fileType")},{default:u((()=>[t(d(j),{modelValue:se.value,"onUpdate:modelValue":a[4]||(a[4]=e=>se.value=e)},{default:u((()=>[t(d(V),{value:"csv"},{default:u((()=>[p("csv")])),_:1}),t(d(V),{value:"json"},{default:u((()=>[p("json")])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),t(d(w),null,{default:u((()=>[t(d(v),{type:"primary",onClick:D,style:{left:"40%",position:"relative"},loading:J.value},{default:u((()=>[p(" Create ")])),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])])),_:1},8,["label"]),t(d(_),{label:d(x)("export.exportRecords"),name:"exportRecords"},{default:u((()=>[t(d(C),{direction:"vertical",alignment:"flex-start",style:{width:"100%"}},{default:u((()=>[t(d(l),{type:"danger",loading:Z.value,onClick:X},{default:u((()=>[p(m(d(x)("common.delete")),1)])),_:1},8,["loading"])])),_:1}),t(d(A),{onRegister:d(B),columns:N,data:d(P),loading:d(H),"max-height":"500",style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji",width:"100%"}},null,8,["onRegister","columns","data","loading"])])),_:1},8,["label"])])),_:1}))}});export{q as _};
