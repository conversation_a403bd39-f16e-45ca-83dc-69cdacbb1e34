import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,l as a,v as o,e as l,G as i,r as s,o as r,i as m,w as p,a as n,J as d}from"./index-DfJTpRkj.js";import{_ as c}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{a as b}from"./index-E-CbF_DZ.js";import{E as j}from"./el-tag-CbhrEnto.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";const u=t({__name:"DefaultTable",setup(t){const{t:u}=a(),f=[{field:"title",label:u("tableDemo.title")},{field:"author",label:u("tableDemo.author")},{field:"display_time",label:u("tableDemo.displayTime"),sortable:!0},{field:"importance",label:u("tableDemo.importance"),formatter:(e,t,a)=>o(j,{type:1===a?"success":2===a?"warning":"danger"},(()=>u(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")))},{field:"pageviews",label:u("tableDemo.pageviews")},{field:"action",label:u("tableDemo.action"),slots:{default:e=>{let t;return l(i,{type:"primary",onClick:()=>y(e)},"function"==typeof(a=t=u("tableDemo.action"))||"[object Object]"===Object.prototype.toString.call(a)&&!d(a)?t:{default:()=>[t]});var a}}}],g=s(!0);let _=s([]);(async e=>{const t=await b(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{g.value=!1}));t&&(_.value=t.data.list)})();const y=e=>{};return(t,a)=>(r(),m(n(e),{title:n(u)("tableDemo.table"),message:n(u)("tableDemo.tableDes")},{default:p((()=>[l(n(c),{columns:f,data:n(_),loading:g.value,defaultSort:{prop:"display_time",order:"descending"}},null,8,["data","loading"])])),_:1},8,["title","message"]))}});export{u as default};
