import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as a,N as t,s as o,e as l,v as s,M as r,B as i,G as n,F as d,r as m,o as c,c as u,w as p,a as f,f as y,z as g,t as b,A as v,C as j,U as _,J as w,l as h,_ as x}from"./index-DfJTpRkj.js";import{a as U,E as k}from"./el-col-B4Ik8fnS.js";import{E}from"./el-upload-BW2TOFr8.js";import"./el-progress-Wzr98AjO.js";import{E as C,a as S}from"./el-form-DsaI0u2w.js";import{E as A}from"./el-tag-CbhrEnto.js";import{_ as V}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{u as z}from"./useTable-CtyddZqf.js";import{d as I,a as M,g as B,c as F}from"./index-CrwqUA4w.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./index-DE7jtbbk.js";import"./castArray-CvwAI87l.js";import"./el-table-column-7FjdLFwR.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";const L={class:"mb-10px"},T={class:"mb-10px"};function $(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!w(e)}const D=x(a({__name:"manage",setup(a){const{t:w}=h(),x={Authorization:`${t().getToken}`},D=o([{field:"selection",type:"selection",width:"55"},{field:"id",hidden:!0},{field:"name",label:w("common.name"),minWidth:40},{field:"category",label:w("common.category"),minWidth:40,formatter:(e,a,t)=>l(A,{type:"success",effect:"light",size:"large"},$(t)?t:{default:()=>[t]})},{field:"size",label:w("common.filesize"),formatter:(e,a,t)=>t+"MB"},{field:"upload",label:w("common.upload"),formatter:(e,a,t)=>{const o=`/api/dictionary/manage/save?id=${e.id}`;return s("div",[s(E,{class:"upload-demo",action:o,headers:x,onSuccess:e=>{200===e.code?r.success("Upload succes"):r.error("Upload failed"),q()},onError:e=>{r.error(`Upload failed: ${e}`)}},(()=>s(i,{type:"warning"},w("common.cover"))))])}},{field:"download",label:w("common.download"),formatter:(e,a,t)=>{let o;return l(d,null,[l(n,{type:"primary",onClick:()=>se(e.id,e.name)},$(o=w("common.download"))?o:{default:()=>[o]})])}},{field:"action",label:w("tableDemo.action"),minWidth:40,formatter:(e,a,t)=>{let o;return l(d,null,[l(n,{type:"danger",onClick:()=>Y(e)},$(o=w("common.delete"))?o:{default:()=>[o]})])}}]),{tableRegister:H,tableState:N,tableMethods:O}=z({fetchDataApi:async()=>({list:(await B()).data.list})}),{loading:W,dataList:G}=N,{getList:q,getElTableExpose:J}=O;function K(){return{background:"var(--el-fill-color-light)"}}const P=m(!1),Q=m({name:"",category:""}),X=m(!1),Y=async e=>{if(window.confirm("Are you sure you want to delete this data?")){X.value=!0;try{await I([e.id]);X.value=!1,q()}catch(a){X.value=!1,q()}}},Z=m([]),ee=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{const e=await J(),a=(null==e?void 0:e.getSelectionRows())||[];Z.value=a.map((e=>e.id)),X.value=!0;try{await I(Z.value),X.value=!1,q()}catch(t){X.value=!1,q()}})()},ae=m();async function te(e){try{const a=e.file,t=new FormData;t.append("file",a),t.append("name",Q.value.name),t.append("category",Q.value.category),await F(t),q(),P.value=!1,r.success("Upload success")}catch(a){r.error(`Upload failed: ${a}`)}}async function oe(){ae.value.submit()}const le=async()=>{Q.value.name="",P.value=!0},se=async(e,a)=>{const t=await M(e),o=window.URL.createObjectURL(new Blob([t.data])),l=document.createElement("a");l.href=o,l.setAttribute("download",a),document.body.appendChild(l),l.click(),document.body.removeChild(l)};return(a,t)=>(c(),u(d,null,[l(f(e),null,{default:p((()=>[l(f(U),{gutter:60},{default:p((()=>[l(f(k),{span:1},{default:p((()=>[y("div",L,[l(f(i),{type:"primary",onClick:le},{default:p((()=>[g(b(f(w)("common.new")),1)])),_:1})])])),_:1}),l(f(k),{span:1},{default:p((()=>[y("div",T,[l(f(n),{type:"danger",loading:X.value,onClick:ee},{default:p((()=>[g(b(f(w)("common.delete")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),l(f(V),{columns:D,data:f(G),stripe:"",border:!0,loading:f(W),resizable:!0,onRegister:f(H),headerCellStyle:K,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["columns","data","loading","onRegister"])])),_:1}),l(f(R),{modelValue:P.value,"onUpdate:modelValue":t[2]||(t[2]=e=>P.value=e),title:f(w)("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:430},{default:p((()=>[l(f(C),{model:Q.value,"label-width":"120px",class:"upload-form"},{default:p((()=>[l(f(S),{label:f(w)("common.name")},{default:p((()=>[l(f(v),{modelValue:Q.value.name,"onUpdate:modelValue":t[0]||(t[0]=e=>Q.value.name=e),placeholder:"Enter name"},null,8,["modelValue"])])),_:1},8,["label"]),l(f(S),{label:f(w)("common.category")},{default:p((()=>[l(f(v),{modelValue:Q.value.category,"onUpdate:modelValue":t[1]||(t[1]=e=>Q.value.category=e),placeholder:"Enter category"},null,8,["modelValue"])])),_:1},8,["label"]),l(f(S),{label:""},{default:p((()=>[l(f(E),{class:"upload-demo",drag:"",ref_key:"upload",ref:ae,"auto-upload":!1,limit:1,httpRequest:te},{default:p((()=>[l(f(j),{class:"avatar-uploader-icon"},{default:p((()=>[l(f(_))])),_:1})])),_:1},512)])),_:1}),l(f(S),null,{default:p((()=>[l(f(i),{type:"primary",onClick:oe},{default:p((()=>[g("Submit")])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])],64))}}),[["__scopeId","data-v-a03cd13e"]]);export{D as default};
