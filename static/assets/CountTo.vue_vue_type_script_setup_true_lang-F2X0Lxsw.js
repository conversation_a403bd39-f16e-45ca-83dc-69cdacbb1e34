import{d as a,aq as t,s as l,bh as e,H as r,a8 as n,O as s,a as i,o,c as u,t as c,n as p,k as m,cA as d}from"./index-DfJTpRkj.js";const V=a({__name:"CountTo",props:{startVal:t.number.def(0),endVal:t.number.def(2021),duration:t.number.def(3e3),autoplay:t.bool.def(!0),decimals:t.number.validate((a=>a>=0)).def(0),decimal:t.string.def("."),separator:t.string.def(","),prefix:t.string.def(""),suffix:t.string.def(""),useEasing:t.bool.def(!0),easingFn:{type:Function,default:(a,t,l,e)=>l*(1-Math.pow(2,-10*a/e))*1024/1023+t}},emits:["mounted","callback"],setup(a,{expose:t,emit:V}){const{getPrefixCls:f}=m(),F=f("count-to"),g=a,A=V,S=a=>{const{decimals:t,decimal:l,separator:e,suffix:r,prefix:n}=g;a=Number(a).toFixed(t);const s=(a+="").split(".");let i=s[0];const o=s.length>1?l+s[1]:"",u=/(\d+)(\d{3})/;if(e&&!d(e))for(;u.test(i);)i=i.replace(u,"$1"+e+"$2");return n+i+o+r},b=l({localStartVal:g.startVal,displayValue:S(g.startVal),printVal:null,paused:!1,localDuration:g.duration,startTime:null,timestamp:null,remaining:null,rAF:null}),x=e(b,"displayValue");r((()=>{g.autoplay&&y(),A("mounted")}));const D=n((()=>g.startVal>g.endVal));s([()=>g.startVal,()=>g.endVal],(()=>{g.autoplay&&y()}));const y=()=>{const{startVal:a,duration:t}=g;b.localStartVal=a,b.startTime=null,b.localDuration=t,b.paused=!1,b.rAF=requestAnimationFrame(h)},T=()=>{cancelAnimationFrame(b.rAF)},q=()=>{b.startTime=null,b.localDuration=+b.remaining,b.localStartVal=+b.printVal,requestAnimationFrame(h)},h=a=>{const{useEasing:t,easingFn:l,endVal:e}=g;b.startTime||(b.startTime=a),b.timestamp=a;const r=a-b.startTime;b.remaining=b.localDuration-r,t?i(D)?b.printVal=b.localStartVal-l(r,0,b.localStartVal-e,b.localDuration):b.printVal=l(r,b.localStartVal,e-b.localStartVal,b.localDuration):i(D)?b.printVal=b.localStartVal-(b.localStartVal-e)*(r/b.localDuration):b.printVal=b.localStartVal+(e-b.localStartVal)*(r/b.localDuration),i(D)?b.printVal=b.printVal<e?e:b.printVal:b.printVal=b.printVal>e?e:b.printVal,b.displayValue=S(b.printVal),r<b.localDuration?b.rAF=requestAnimationFrame(h):A("callback")};return t({pauseResume:()=>{b.paused?(q(),b.paused=!1):(T(),b.paused=!0)},reset:()=>{b.startTime=null,cancelAnimationFrame(b.rAF),b.displayValue=S(g.startVal)},start:y,pause:T}),(a,t)=>(o(),u("span",{class:p(i(F))},c(x.value),3))}});export{V as _};
