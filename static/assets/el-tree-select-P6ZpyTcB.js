import{a as e,E as t,s as n}from"./el-select-BkpcrSfo.js";import{b8 as d,s as o,bq as a,bJ as r,aa as s,d as l,a7 as i,ab as c,v as h,a9 as u,a6 as p,r as f,cp as v,cq as y,C as k,aP as g,aQ as C,O as N,Y as x,cr as b,y as m,Q as K,ag as E,o as D,c as w,f as S,i as A,w as O,aI as L,n as B,af as T,j as M,e as $,aH as _,F as q,R as I,aS as j,a4 as z,b5 as F,H,cb as R,bt as V,aK as P,bm as U,aA as Q,a8 as W,bp as Y,t as G,bv as J,a1 as X}from"./index-DfJTpRkj.js";import{_ as Z}from"./index-Co3LqSsp.js";import{E as ee,p as te}from"./el-checkbox-DU4wMKRd.js";import{e as ne}from"./strings-CUyZ1T6U.js";import{i as de}from"./index-DE7jtbbk.js";const oe="$treeNodeId",ae=function(e,t){t&&!t[oe]&&Object.defineProperty(t,oe,{value:e.id,enumerable:!1,configurable:!1,writable:!1})},re=function(e,t){return e?t[e]:t[oe]},se=(e,t,n)=>{const d=e.value.currentNode;n();const o=e.value.currentNode;d!==o&&t("current-change",o?o.data:null,o)},le=e=>{let t=!0,n=!0,d=!0;for(let o=0,a=e.length;o<a;o++){const a=e[o];(!0!==a.checked||a.indeterminate)&&(t=!1,a.disabled||(d=!1)),(!1!==a.checked||a.indeterminate)&&(n=!1)}return{all:t,none:n,allWithoutDisable:d,half:!t&&!n}},ie=function(e){if(0===e.childNodes.length||e.loading)return;const{all:t,none:n,half:d}=le(e.childNodes);t?(e.checked=!0,e.indeterminate=!1):d?(e.checked=!1,e.indeterminate=!0):n&&(e.checked=!1,e.indeterminate=!1);const o=e.parent;o&&0!==o.level&&(e.store.checkStrictly||ie(o))},ce=function(e,t){const n=e.store.props,d=e.data||{},o=n[t];if("function"==typeof o)return o(d,e);if("string"==typeof o)return d[o];if(void 0===o){const e=d[t];return void 0===e?"":e}};let he=0;class ue{constructor(e){this.id=he++,this.text=null,this.checked=!1,this.indeterminate=!1,this.data=null,this.expanded=!1,this.parent=null,this.visible=!0,this.isCurrent=!1,this.canFocus=!1;for(const t in e)d(e,t)&&(this[t]=e[t]);this.level=0,this.loaded=!1,this.childNodes=[],this.loading=!1,this.parent&&(this.level=this.parent.level+1)}initialize(){const e=this.store;if(!e)throw new Error("[Node]store is required!");e.registerNode(this);const t=e.props;if(t&&void 0!==t.isLeaf){const e=ce(this,"isLeaf");"boolean"==typeof e&&(this.isLeafByUser=e)}if(!0!==e.lazy&&this.data?(this.setData(this.data),e.defaultExpandAll&&(this.expanded=!0,this.canFocus=!0)):this.level>0&&e.lazy&&e.defaultExpandAll&&this.expand(),Array.isArray(this.data)||ae(this,this.data),!this.data)return;const n=e.defaultExpandedKeys,d=e.key;d&&n&&n.includes(this.key)&&this.expand(null,e.autoExpandParent),d&&void 0!==e.currentNodeKey&&this.key===e.currentNodeKey&&(e.currentNode=this,e.currentNode.isCurrent=!0),e.lazy&&e._initDefaultCheckedNode(this),this.updateLeafState(),!this.parent||1!==this.level&&!0!==this.parent.expanded||(this.canFocus=!0)}setData(e){let t;Array.isArray(e)||ae(this,e),this.data=e,this.childNodes=[],t=0===this.level&&Array.isArray(this.data)?this.data:ce(this,"children")||[];for(let n=0,d=t.length;n<d;n++)this.insertChild({data:t[n]})}get label(){return ce(this,"label")}get key(){const e=this.store.key;return this.data?this.data[e]:null}get disabled(){return ce(this,"disabled")}get nextSibling(){const e=this.parent;if(e){const t=e.childNodes.indexOf(this);if(t>-1)return e.childNodes[t+1]}return null}get previousSibling(){const e=this.parent;if(e){const t=e.childNodes.indexOf(this);if(t>-1)return t>0?e.childNodes[t-1]:null}return null}contains(e,t=!0){return(this.childNodes||[]).some((n=>n===e||t&&n.contains(e)))}remove(){const e=this.parent;e&&e.removeChild(this)}insertChild(e,t,n){if(!e)throw new Error("InsertChild error: child is required.");if(!(e instanceof ue)){if(!n){const n=this.getChildren(!0);n.includes(e.data)||(void 0===t||t<0?n.push(e.data):n.splice(t,0,e.data))}Object.assign(e,{parent:this,store:this.store}),(e=o(new ue(e)))instanceof ue&&e.initialize()}e.level=this.level+1,void 0===t||t<0?this.childNodes.push(e):this.childNodes.splice(t,0,e),this.updateLeafState()}insertBefore(e,t){let n;t&&(n=this.childNodes.indexOf(t)),this.insertChild(e,n)}insertAfter(e,t){let n;t&&(n=this.childNodes.indexOf(t),-1!==n&&(n+=1)),this.insertChild(e,n)}removeChild(e){const t=this.getChildren()||[],n=t.indexOf(e.data);n>-1&&t.splice(n,1);const d=this.childNodes.indexOf(e);d>-1&&(this.store&&this.store.deregisterNode(e),e.parent=null,this.childNodes.splice(d,1)),this.updateLeafState()}removeChildByData(e){let t=null;for(let n=0;n<this.childNodes.length;n++)if(this.childNodes[n].data===e){t=this.childNodes[n];break}t&&this.removeChild(t)}expand(e,t){const n=()=>{if(t){let e=this.parent;for(;e.level>0;)e.expanded=!0,e=e.parent}this.expanded=!0,e&&e(),this.childNodes.forEach((e=>{e.canFocus=!0}))};this.shouldLoadData()?this.loadData((e=>{Array.isArray(e)&&(this.checked?this.setChecked(!0,!0):this.store.checkStrictly||ie(this),n())})):n()}doCreateChildren(e,t={}){e.forEach((e=>{this.insertChild(Object.assign({data:e},t),void 0,!0)}))}collapse(){this.expanded=!1,this.childNodes.forEach((e=>{e.canFocus=!1}))}shouldLoadData(){return!0===this.store.lazy&&this.store.load&&!this.loaded}updateLeafState(){if(!0===this.store.lazy&&!0!==this.loaded&&void 0!==this.isLeafByUser)return void(this.isLeaf=this.isLeafByUser);const e=this.childNodes;!this.store.lazy||!0===this.store.lazy&&!0===this.loaded?this.isLeaf=!e||0===e.length:this.isLeaf=!1}setChecked(e,t,n,d){if(this.indeterminate="half"===e,this.checked=!0===e,this.store.checkStrictly)return;if(!this.shouldLoadData()||this.store.checkDescendants){const{all:n,allWithoutDisable:o}=le(this.childNodes);this.isLeaf||n||!o||(this.checked=!1,e=!1);const a=()=>{if(t){const n=this.childNodes;for(let r=0,s=n.length;r<s;r++){const o=n[r];d=d||!1!==e;const a=o.disabled?o.checked:d;o.setChecked(a,t,!0,d)}const{half:o,all:a}=le(n);a||(this.checked=a,this.indeterminate=o)}};if(this.shouldLoadData())return void this.loadData((()=>{a(),ie(this)}),{checked:!1!==e});a()}const o=this.parent;o&&0!==o.level&&(n||ie(o))}getChildren(e=!1){if(0===this.level)return this.data;const t=this.data;if(!t)return null;const n=this.store.props;let d="children";return n&&(d=n.children||"children"),void 0===t[d]&&(t[d]=null),e&&!t[d]&&(t[d]=[]),t[d]}updateChildren(){const e=this.getChildren()||[],t=this.childNodes.map((e=>e.data)),n={},d=[];e.forEach(((e,o)=>{const a=e[oe];!!a&&t.findIndex((e=>e[oe]===a))>=0?n[a]={index:o,data:e}:d.push({index:o,data:e})})),this.store.lazy||t.forEach((e=>{n[e[oe]]||this.removeChildByData(e)})),d.forEach((({index:e,data:t})=>{this.insertChild({data:t},e)})),this.updateLeafState()}loadData(e,t={}){if(!0!==this.store.lazy||!this.store.load||this.loaded||this.loading&&!Object.keys(t).length)e&&e.call(this);else{this.loading=!0;const n=n=>{this.childNodes=[],this.doCreateChildren(n,t),this.loaded=!0,this.loading=!1,this.updateLeafState(),e&&e.call(this,n)},d=()=>{this.loading=!1};this.store.load(this,n,d)}}eachNode(e){const t=[this];for(;t.length;){const n=t.shift();t.unshift(...n.childNodes),e(n)}}reInitChecked(){this.store.checkStrictly||ie(this)}}class pe{constructor(e){this.currentNode=null,this.currentNodeKey=null;for(const t in e)d(e,t)&&(this[t]=e[t]);this.nodesMap={}}initialize(){if(this.root=new ue({data:this.data,store:this}),this.root.initialize(),this.lazy&&this.load){(0,this.load)(this.root,(e=>{this.root.doCreateChildren(e),this._initDefaultCheckedNodes()}))}else this._initDefaultCheckedNodes()}filter(e){const t=this.filterNodeMethod,n=this.lazy,d=function(o){const a=o.root?o.root.childNodes:o.childNodes;if(a.forEach((n=>{n.visible=t.call(n,e,n.data,n),d(n)})),!o.visible&&a.length){let e=!0;e=!a.some((e=>e.visible)),o.root?o.root.visible=!1===e:o.visible=!1===e}e&&o.visible&&!o.isLeaf&&(n&&!o.loaded||o.expand())};d(this)}setData(e){e!==this.root.data?(this.root.setData(e),this._initDefaultCheckedNodes()):this.root.updateChildren()}getNode(e){if(e instanceof ue)return e;const t=a(e)?re(this.key,e):e;return this.nodesMap[t]||null}insertBefore(e,t){const n=this.getNode(t);n.parent.insertBefore({data:e},n)}insertAfter(e,t){const n=this.getNode(t);n.parent.insertAfter({data:e},n)}remove(e){const t=this.getNode(e);t&&t.parent&&(t===this.currentNode&&(this.currentNode=null),t.parent.removeChild(t))}append(e,t){const n=r(t)?this.root:this.getNode(t);n&&n.insertChild({data:e})}_initDefaultCheckedNodes(){const e=this.defaultCheckedKeys||[],t=this.nodesMap;e.forEach((e=>{const n=t[e];n&&n.setChecked(!0,!this.checkStrictly)}))}_initDefaultCheckedNode(e){(this.defaultCheckedKeys||[]).includes(e.key)&&e.setChecked(!0,!this.checkStrictly)}setDefaultCheckedKey(e){e!==this.defaultCheckedKeys&&(this.defaultCheckedKeys=e,this._initDefaultCheckedNodes())}registerNode(e){const t=this.key;if(e&&e.data)if(t){void 0!==e.key&&(this.nodesMap[e.key]=e)}else this.nodesMap[e.id]=e}deregisterNode(e){this.key&&e&&e.data&&(e.childNodes.forEach((e=>{this.deregisterNode(e)})),delete this.nodesMap[e.key])}getCheckedNodes(e=!1,t=!1){const n=[],d=function(o){(o.root?o.root.childNodes:o.childNodes).forEach((o=>{(o.checked||t&&o.indeterminate)&&(!e||e&&o.isLeaf)&&n.push(o.data),d(o)}))};return d(this),n}getCheckedKeys(e=!1){return this.getCheckedNodes(e).map((e=>(e||{})[this.key]))}getHalfCheckedNodes(){const e=[],t=function(n){(n.root?n.root.childNodes:n.childNodes).forEach((n=>{n.indeterminate&&e.push(n.data),t(n)}))};return t(this),e}getHalfCheckedKeys(){return this.getHalfCheckedNodes().map((e=>(e||{})[this.key]))}_getAllNodes(){const e=[],t=this.nodesMap;for(const n in t)d(t,n)&&e.push(t[n]);return e}updateChildren(e,t){const n=this.nodesMap[e];if(!n)return;const d=n.childNodes;for(let o=d.length-1;o>=0;o--){const e=d[o];this.remove(e.data)}for(let o=0,a=t.length;o<a;o++){const e=t[o];this.append(e,n.data)}}_setCheckedKeys(e,t=!1,n){const d=this._getAllNodes().sort(((e,t)=>e.level-t.level)),o=Object.create(null),a=Object.keys(n);d.forEach((e=>e.setChecked(!1,!1)));const r=t=>{t.childNodes.forEach((t=>{var n;o[t.data[e]]=!0,(null==(n=t.childNodes)?void 0:n.length)&&r(t)}))};for(let s=0,l=d.length;s<l;s++){const n=d[s],l=n.data[e].toString();if(a.includes(l)){if(n.childNodes.length&&r(n),n.isLeaf||this.checkStrictly)n.setChecked(!0,!1);else if(n.setChecked(!0,!0),t){n.setChecked(!1,!1);const e=function(t){t.childNodes.forEach((t=>{t.isLeaf||t.setChecked(!1,!1),e(t)}))};e(n)}}else n.checked&&!o[l]&&n.setChecked(!1,!1)}}setCheckedNodes(e,t=!1){const n=this.key,d={};e.forEach((e=>{d[(e||{})[n]]=!0})),this._setCheckedKeys(n,t,d)}setCheckedKeys(e,t=!1){this.defaultCheckedKeys=e;const n=this.key,d={};e.forEach((e=>{d[e]=!0})),this._setCheckedKeys(n,t,d)}setDefaultExpandedKeys(e){e=e||[],this.defaultExpandedKeys=e,e.forEach((e=>{const t=this.getNode(e);t&&t.expand(null,this.autoExpandParent)}))}setChecked(e,t,n){const d=this.getNode(e);d&&d.setChecked(!!t,n)}getCurrentNode(){return this.currentNode}setCurrentNode(e){const t=this.currentNode;t&&(t.isCurrent=!1),this.currentNode=e,this.currentNode.isCurrent=!0}setUserCurrentNode(e,t=!0){const n=e[this.key],d=this.nodesMap[n];this.setCurrentNode(d),t&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0)}setCurrentNodeKey(e,t=!0){if(null==e)return this.currentNode&&(this.currentNode.isCurrent=!1),void(this.currentNode=null);const n=this.getNode(e);n&&(this.setCurrentNode(n),t&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0))}}var fe=s(l({name:"ElTreeNodeContent",props:{node:{type:Object,required:!0},renderContent:Function},setup(e){const t=i("tree"),n=c("NodeInstance"),d=c("RootTree");return()=>{const o=e.node,{data:a,store:r}=o;return e.renderContent?e.renderContent(h,{_self:n,node:o,data:a,store:r}):u(d.ctx.slots,"default",{node:o,data:a},(()=>[h("span",{class:t.be("node","label")},[o.label])]))}}}),[["__file","tree-node-content.vue"]]);function ve(e){const t=c("TreeNodeMap",null),n={treeNodeExpand:t=>{e.node!==t&&e.node.collapse()},children:[]};return t&&t.children.push(n),p("TreeNodeMap",n),{broadcastExpanded:t=>{if(e.accordion)for(const e of n.children)e.treeNodeExpand(t)}}}const ye=Symbol("dragEvents");const ke=l({name:"ElTreeNode",components:{ElCollapseTransition:Z,ElCheckbox:ee,NodeContent:fe,ElIcon:k,Loading:g},props:{node:{type:ue,default:()=>({})},props:{type:Object,default:()=>({})},accordion:Boolean,renderContent:Function,renderAfterExpand:Boolean,showCheckbox:{type:Boolean,default:!1}},emits:["node-expand"],setup(e,t){const n=i("tree"),{broadcastExpanded:d}=ve(e),o=c("RootTree"),a=f(!1),r=f(!1),s=f(null),l=f(null),h=f(null),u=c(ye),v=C();p("NodeInstance",v),e.node.expanded&&(a.value=!0,r.value=!0);const y=o.props.props.children||"children";N((()=>{const t=e.node.data[y];return t&&[...t]}),(()=>{e.node.updateChildren()})),N((()=>e.node.indeterminate),(t=>{k(e.node.checked,t)})),N((()=>e.node.checked),(t=>{k(t,e.node.indeterminate)})),N((()=>e.node.childNodes.length),(()=>e.node.reInitChecked())),N((()=>e.node.expanded),(e=>{x((()=>a.value=e)),e&&(r.value=!0)}));const k=(t,n)=>{s.value===t&&l.value===n||o.ctx.emit("check-change",e.node.data,t,n),s.value=t,l.value=n},g=()=>{e.node.isLeaf||(a.value?(o.ctx.emit("node-collapse",e.node.data,e.node,v),e.node.collapse()):(e.node.expand(),t.emit("node-expand",e.node.data,e.node,v)))},m=(t,n)=>{e.node.setChecked(n.target.checked,!o.props.checkStrictly),x((()=>{const t=o.store.value;o.ctx.emit("check",e.node.data,{checkedNodes:t.getCheckedNodes(),checkedKeys:t.getCheckedKeys(),halfCheckedNodes:t.getHalfCheckedNodes(),halfCheckedKeys:t.getHalfCheckedKeys()})}))};return{ns:n,node$:h,tree:o,expanded:a,childNodeRendered:r,oldChecked:s,oldIndeterminate:l,getNodeKey:e=>re(o.props.nodeKey,e.data),getNodeClass:t=>{const n=e.props.class;if(!n)return{};let d;if(j(n)){const{data:e}=t;d=n(e,t)}else d=n;return z(d)?{[d]:!0}:d},handleSelectChange:k,handleClick:t=>{se(o.store,o.ctx.emit,(()=>o.store.value.setCurrentNode(e.node))),o.currentNode.value=e.node,o.props.expandOnClickNode&&g(),o.props.checkOnClickNode&&!e.node.disabled&&m(null,{target:{checked:!e.node.checked}}),o.ctx.emit("node-click",e.node.data,e.node,v,t)},handleContextMenu:t=>{o.instance.vnode.props.onNodeContextmenu&&(t.stopPropagation(),t.preventDefault()),o.ctx.emit("node-contextmenu",t,e.node.data,e.node,v)},handleExpandIconClick:g,handleCheckChange:m,handleChildNodeExpand:(e,t,n)=>{d(t),o.ctx.emit("node-expand",e,t,n)},handleDragStart:t=>{o.props.draggable&&u.treeNodeDragStart({event:t,treeNode:e})},handleDragOver:t=>{t.preventDefault(),o.props.draggable&&u.treeNodeDragOver({event:t,treeNode:{$el:h.value,node:e.node}})},handleDrop:e=>{e.preventDefault()},handleDragEnd:e=>{o.props.draggable&&u.treeNodeDragEnd(e)},CaretRight:b}}}),ge=["aria-expanded","aria-disabled","aria-checked","draggable","data-key"],Ce=["aria-expanded"];var Ne=s(l({name:"ElTree",components:{ElTreeNode:s(ke,[["render",function(e,t,n,d,o,a){const r=m("el-icon"),s=m("el-checkbox"),l=m("loading"),i=m("node-content"),c=m("el-tree-node"),h=m("el-collapse-transition");return K((D(),w("div",{ref:"node$",class:B([e.ns.b("node"),e.ns.is("expanded",e.expanded),e.ns.is("current",e.node.isCurrent),e.ns.is("hidden",!e.node.visible),e.ns.is("focusable",!e.node.disabled),e.ns.is("checked",!e.node.disabled&&e.node.checked),e.getNodeClass(e.node)]),role:"treeitem",tabindex:"-1","aria-expanded":e.expanded,"aria-disabled":e.node.disabled,"aria-checked":e.node.checked,draggable:e.tree.props.draggable,"data-key":e.getNodeKey(e.node),onClick:t[1]||(t[1]=T(((...t)=>e.handleClick&&e.handleClick(...t)),["stop"])),onContextmenu:t[2]||(t[2]=(...t)=>e.handleContextMenu&&e.handleContextMenu(...t)),onDragstart:t[3]||(t[3]=T(((...t)=>e.handleDragStart&&e.handleDragStart(...t)),["stop"])),onDragover:t[4]||(t[4]=T(((...t)=>e.handleDragOver&&e.handleDragOver(...t)),["stop"])),onDragend:t[5]||(t[5]=T(((...t)=>e.handleDragEnd&&e.handleDragEnd(...t)),["stop"])),onDrop:t[6]||(t[6]=T(((...t)=>e.handleDrop&&e.handleDrop(...t)),["stop"]))},[S("div",{class:B(e.ns.be("node","content")),style:_({paddingLeft:(e.node.level-1)*e.tree.props.indent+"px"})},[e.tree.props.icon||e.CaretRight?(D(),A(r,{key:0,class:B([e.ns.be("node","expand-icon"),e.ns.is("leaf",e.node.isLeaf),{expanded:!e.node.isLeaf&&e.expanded}]),onClick:T(e.handleExpandIconClick,["stop"])},{default:O((()=>[(D(),A(L(e.tree.props.icon||e.CaretRight)))])),_:1},8,["class","onClick"])):M("v-if",!0),e.showCheckbox?(D(),A(s,{key:1,"model-value":e.node.checked,indeterminate:e.node.indeterminate,disabled:!!e.node.disabled,onClick:t[0]||(t[0]=T((()=>{}),["stop"])),onChange:e.handleCheckChange},null,8,["model-value","indeterminate","disabled","onChange"])):M("v-if",!0),e.node.loading?(D(),A(r,{key:2,class:B([e.ns.be("node","loading-icon"),e.ns.is("loading")])},{default:O((()=>[$(l)])),_:1},8,["class"])):M("v-if",!0),$(i,{node:e.node,"render-content":e.renderContent},null,8,["node","render-content"])],6),$(h,null,{default:O((()=>[!e.renderAfterExpand||e.childNodeRendered?K((D(),w("div",{key:0,class:B(e.ns.be("node","children")),role:"group","aria-expanded":e.expanded},[(D(!0),w(q,null,I(e.node.childNodes,(t=>(D(),A(c,{key:e.getNodeKey(t),"render-content":e.renderContent,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,node:t,accordion:e.accordion,props:e.props,onNodeExpand:e.handleChildNodeExpand},null,8,["render-content","render-after-expand","show-checkbox","node","accordion","props","onNodeExpand"])))),128))],10,Ce)),[[E,e.expanded]]):M("v-if",!0)])),_:1})],42,ge)),[[E,e.node.visible]])}],["__file","tree-node.vue"]])},props:{data:{type:Array,default:()=>[]},emptyText:{type:String},renderAfterExpand:{type:Boolean,default:!0},nodeKey:String,checkStrictly:Boolean,defaultExpandAll:Boolean,expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:Boolean,checkDescendants:{type:Boolean,default:!1},autoExpandParent:{type:Boolean,default:!0},defaultCheckedKeys:Array,defaultExpandedKeys:Array,currentNodeKey:[String,Number],renderContent:Function,showCheckbox:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1},allowDrag:Function,allowDrop:Function,props:{type:Object,default:()=>({children:"children",label:"label",disabled:"disabled"})},lazy:{type:Boolean,default:!1},highlightCurrent:Boolean,load:Function,filterNodeMethod:Function,accordion:Boolean,indent:{type:Number,default:18},icon:{type:U}},emits:["check-change","current-change","node-click","node-contextmenu","node-collapse","node-expand","check","node-drag-start","node-drag-end","node-drop","node-drag-leave","node-drag-enter","node-drag-over"],setup(e,t){const{t:n}=Q(),d=i("tree"),o=f(new pe({key:e.nodeKey,data:e.data,lazy:e.lazy,props:e.props,load:e.load,currentNodeKey:e.currentNodeKey,checkStrictly:e.checkStrictly,checkDescendants:e.checkDescendants,defaultCheckedKeys:e.defaultCheckedKeys,defaultExpandedKeys:e.defaultExpandedKeys,autoExpandParent:e.autoExpandParent,defaultExpandAll:e.defaultExpandAll,filterNodeMethod:e.filterNodeMethod}));o.value.initialize();const a=f(o.value.root),r=f(null),s=f(null),l=f(null),{broadcastExpanded:c}=ve(e),{dragState:h}=function({props:e,ctx:t,el$:n,dropIndicator$:d,store:o}){const a=i("tree"),r=f({showDropIndicator:!1,draggingNode:null,dropNode:null,allowDrop:!0,dropType:null});return p(ye,{treeNodeDragStart:({event:n,treeNode:d})=>{if("function"==typeof e.allowDrag&&!e.allowDrag(d.node))return n.preventDefault(),!1;n.dataTransfer.effectAllowed="move";try{n.dataTransfer.setData("text/plain","")}catch(o){}r.value.draggingNode=d,t.emit("node-drag-start",d.node,n)},treeNodeDragOver:({event:o,treeNode:s})=>{const l=s,i=r.value.dropNode;i&&i.node.id!==l.node.id&&v(i.$el,a.is("drop-inner"));const c=r.value.draggingNode;if(!c||!l)return;let h=!0,u=!0,p=!0,f=!0;"function"==typeof e.allowDrop&&(h=e.allowDrop(c.node,l.node,"prev"),f=u=e.allowDrop(c.node,l.node,"inner"),p=e.allowDrop(c.node,l.node,"next")),o.dataTransfer.dropEffect=u||h||p?"move":"none",(h||u||p)&&(null==i?void 0:i.node.id)!==l.node.id&&(i&&t.emit("node-drag-leave",c.node,i.node,o),t.emit("node-drag-enter",c.node,l.node,o)),r.value.dropNode=h||u||p?l:null,l.node.nextSibling===c.node&&(p=!1),l.node.previousSibling===c.node&&(h=!1),l.node.contains(c.node,!1)&&(u=!1),(c.node===l.node||c.node.contains(l.node))&&(h=!1,u=!1,p=!1);const k=l.$el.querySelector(`.${a.be("node","content")}`).getBoundingClientRect(),g=n.value.getBoundingClientRect();let C;const N=h?u?.25:p?.45:1:-1,x=p?u?.75:h?.55:0:1;let b=-9999;const m=o.clientY-k.top;C=m<k.height*N?"before":m>k.height*x?"after":u?"inner":"none";const K=l.$el.querySelector(`.${a.be("node","expand-icon")}`).getBoundingClientRect(),E=d.value;"before"===C?b=K.top-g.top:"after"===C&&(b=K.bottom-g.top),E.style.top=`${b}px`,E.style.left=K.right-g.left+"px","inner"===C?y(l.$el,a.is("drop-inner")):v(l.$el,a.is("drop-inner")),r.value.showDropIndicator="before"===C||"after"===C,r.value.allowDrop=r.value.showDropIndicator||f,r.value.dropType=C,t.emit("node-drag-over",c.node,l.node,o)},treeNodeDragEnd:e=>{const{draggingNode:n,dropType:d,dropNode:s}=r.value;if(e.preventDefault(),e.dataTransfer.dropEffect="move",n&&s){const r={data:n.node.data};"none"!==d&&n.node.remove(),"before"===d?s.node.parent.insertBefore(r,s.node):"after"===d?s.node.parent.insertAfter(r,s.node):"inner"===d&&s.node.insertChild(r),"none"!==d&&(o.value.registerNode(r),o.value.key&&n.node.eachNode((e=>{var t;null==(t=o.value.nodesMap[e.data[o.value.key]])||t.setChecked(e.checked,!o.value.checkStrictly)}))),v(s.$el,a.is("drop-inner")),t.emit("node-drag-end",n.node,s.node,d,e),"none"!==d&&t.emit("node-drop",n.node,s.node,d,e)}n&&!s&&t.emit("node-drag-end",n.node,null,d,e),r.value.showDropIndicator=!1,r.value.draggingNode=null,r.value.dropNode=null,r.value.allowDrop=!0}}),{dragState:r}}({props:e,ctx:t,el$:s,dropIndicator$:l,store:o});!function({el$:e},t){const n=i("tree"),d=F([]),o=F([]);H((()=>{a()})),R((()=>{d.value=Array.from(e.value.querySelectorAll("[role=treeitem]")),o.value=Array.from(e.value.querySelectorAll("input[type=checkbox]"))})),N(o,(e=>{e.forEach((e=>{e.setAttribute("tabindex","-1")}))})),V(e,"keydown",(o=>{const a=o.target;if(!a.className.includes(n.b("node")))return;const r=o.code;d.value=Array.from(e.value.querySelectorAll(`.${n.is("focusable")}[role=treeitem]`));const s=d.value.indexOf(a);let l;if([P.up,P.down].includes(r)){if(o.preventDefault(),r===P.up){l=-1===s?0:0!==s?s-1:d.value.length-1;const e=l;for(;!t.value.getNode(d.value[l].dataset.key).canFocus;){if(l--,l===e){l=-1;break}l<0&&(l=d.value.length-1)}}else{l=-1===s?0:s<d.value.length-1?s+1:0;const e=l;for(;!t.value.getNode(d.value[l].dataset.key).canFocus;){if(l++,l===e){l=-1;break}l>=d.value.length&&(l=0)}}-1!==l&&d.value[l].focus()}[P.left,P.right].includes(r)&&(o.preventDefault(),a.click());const i=a.querySelector('[type="checkbox"]');[P.enter,P.space].includes(r)&&i&&(o.preventDefault(),i.click())}));const a=()=>{var t;d.value=Array.from(e.value.querySelectorAll(`.${n.is("focusable")}[role=treeitem]`)),o.value=Array.from(e.value.querySelectorAll("input[type=checkbox]"));const a=e.value.querySelectorAll(`.${n.is("checked")}[role=treeitem]`);a.length?a[0].setAttribute("tabindex","0"):null==(t=d.value[0])||t.setAttribute("tabindex","0")}}({el$:s},o);const u=W((()=>{const{childNodes:e}=a.value;return!e||0===e.length||e.every((({visible:e})=>!e))}));N((()=>e.currentNodeKey),(e=>{o.value.setCurrentNodeKey(e)})),N((()=>e.defaultCheckedKeys),(e=>{o.value.setDefaultCheckedKey(e)})),N((()=>e.defaultExpandedKeys),(e=>{o.value.setDefaultExpandedKeys(e)})),N((()=>e.data),(e=>{o.value.setData(e)}),{deep:!0}),N((()=>e.checkStrictly),(e=>{o.value.checkStrictly=e}));const k=()=>{const e=o.value.getCurrentNode();return e?e.data:null};return p("RootTree",{ctx:t,props:e,store:o,root:a,currentNode:r,instance:C()}),p(Y,void 0),{ns:d,store:o,root:a,currentNode:r,dragState:h,el$:s,dropIndicator$:l,isEmpty:u,filter:t=>{if(!e.filterNodeMethod)throw new Error("[Tree] filterNodeMethod is required when filter");o.value.filter(t)},getNodeKey:t=>re(e.nodeKey,t.data),getNodePath:t=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getNodePath");const n=o.value.getNode(t);if(!n)return[];const d=[n.data];let r=n.parent;for(;r&&r!==a.value;)d.push(r.data),r=r.parent;return d.reverse()},getCheckedNodes:(e,t)=>o.value.getCheckedNodes(e,t),getCheckedKeys:e=>o.value.getCheckedKeys(e),getCurrentNode:k,getCurrentKey:()=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in getCurrentKey");const t=k();return t?t[e.nodeKey]:null},setCheckedNodes:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedNodes");o.value.setCheckedNodes(t,n)},setCheckedKeys:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedKeys");o.value.setCheckedKeys(t,n)},setChecked:(e,t,n)=>{o.value.setChecked(e,t,n)},getHalfCheckedNodes:()=>o.value.getHalfCheckedNodes(),getHalfCheckedKeys:()=>o.value.getHalfCheckedKeys(),setCurrentNode:(n,d=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentNode");se(o,t.emit,(()=>o.value.setUserCurrentNode(n,d)))},setCurrentKey:(n,d=!0)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentKey");se(o,t.emit,(()=>o.value.setCurrentNodeKey(n,d)))},t:n,getNode:e=>o.value.getNode(e),remove:e=>{o.value.remove(e)},append:(e,t)=>{o.value.append(e,t)},insertBefore:(e,t)=>{o.value.insertBefore(e,t)},insertAfter:(e,t)=>{o.value.insertAfter(e,t)},handleNodeExpand:(e,n,d)=>{c(n),t.emit("node-expand",e,n,d)},updateKeyChildren:(t,n)=>{if(!e.nodeKey)throw new Error("[Tree] nodeKey is required in updateKeyChild");o.value.updateChildren(t,n)}}}}),[["render",function(e,t,n,d,o,a){const r=m("el-tree-node");return D(),w("div",{ref:"el$",class:B([e.ns.b(),e.ns.is("dragging",!!e.dragState.draggingNode),e.ns.is("drop-not-allow",!e.dragState.allowDrop),e.ns.is("drop-inner","inner"===e.dragState.dropType),{[e.ns.m("highlight-current")]:e.highlightCurrent}]),role:"tree"},[(D(!0),w(q,null,I(e.root.childNodes,(t=>(D(),A(r,{key:e.getNodeKey(t),node:t,props:e.props,accordion:e.accordion,"render-after-expand":e.renderAfterExpand,"show-checkbox":e.showCheckbox,"render-content":e.renderContent,onNodeExpand:e.handleNodeExpand},null,8,["node","props","accordion","render-after-expand","show-checkbox","render-content","onNodeExpand"])))),128)),e.isEmpty?(D(),w("div",{key:0,class:B(e.ns.e("empty-block"))},[u(e.$slots,"empty",{},(()=>{var t;return[S("span",{class:B(e.ns.e("empty-text"))},G(null!=(t=e.emptyText)?t:e.t("el.tree.emptyText")),3)]}))],2)):M("v-if",!0),K(S("div",{ref:"dropIndicator$",class:B(e.ns.e("drop-indicator"))},null,2),[[E,e.dragState.showDropIndicator]])],2)}],["__file","tree.vue"]]);Ne.install=e=>{e.component(Ne.name,Ne)};const xe=Ne,be=l({extends:t,setup(e,n){const d=t.setup(e,n);delete d.selectOptionClick;const o=C().proxy;return x((()=>{d.select.states.cachedOptions.get(o.value)||d.select.onOptionCreate(o)})),d},methods:{selectOptionClick(){this.$el.parentElement.click()}}});function me(e){return e||0===e}function Ke(e){return Array.isArray(e)&&e.length}function Ee(e){return Array.isArray(e)?e:me(e)?[e]:[]}function De(e,t,n,d,o){for(let a=0;a<e.length;a++){const r=e[a];if(t(r,a,e,o))return d?d(r,a,e,o):r;{const e=n(r);if(Ke(e)){const o=De(e,t,n,d,r);if(o)return o}}}}function we(e,t,n,d){for(let o=0;o<e.length;o++){const a=e[o];t(a,o,e,d);const r=n(a);Ke(r)&&we(r,t,n,a)}}var Se=l({props:{data:{type:Array,default:()=>[]}},setup(e){const t=c(n);return N((()=>e.data),(()=>{var n;e.data.forEach((e=>{t.states.cachedOptions.has(e.value)||t.states.cachedOptions.set(e.value,e)}));const d=(null==(n=t.selectRef)?void 0:n.querySelectorAll("input"))||[];Array.from(d).includes(document.activeElement)||t.setSelected()}),{flush:"post",immediate:!0}),()=>{}}});var Ae=s(l({name:"ElTreeSelect",inheritAttrs:!1,props:{...e.props,...xe.props,cacheData:{type:Array,default:()=>[]}},setup(t,n){const{slots:d,expose:a}=n,r=f(),s=f(),l=W((()=>t.nodeKey||t.valueKey||"value")),c=((t,{attrs:n,emit:d},{select:o,tree:a,key:r})=>{const s=i("tree-select");return N((()=>t.data),(()=>{t.filterable&&x((()=>{var e,t;null==(t=a.value)||t.filter(null==(e=o.value)?void 0:e.states.inputValue)}))}),{flush:"post"}),{...te(J(t),Object.keys(e.props)),...n,"onUpdate:modelValue":e=>d(X,e),valueKey:r,popperClass:W((()=>{const e=[s.e("popper")];return t.popperClass&&e.push(t.popperClass),e.join(" ")})),filterMethod:(e="")=>{var n;t.filterMethod?t.filterMethod(e):t.remoteMethod?t.remoteMethod(e):null==(n=a.value)||n.filter(e)}}})(t,n,{select:r,tree:s,key:l}),{cacheOptions:u,...p}=((e,{attrs:t,slots:n,emit:d},{select:o,tree:a,key:r})=>{N((()=>e.modelValue),(()=>{e.showCheckbox&&x((()=>{const t=a.value;t&&!de(t.getCheckedKeys(),Ee(e.modelValue))&&t.setCheckedKeys(Ee(e.modelValue))}))}),{immediate:!0,deep:!0});const s=W((()=>({value:r.value,label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf",...e.props}))),l=(e,t)=>{var n;const d=s.value[e];return j(d)?d(t,null==(n=a.value)?void 0:n.getNode(l("value",t))):t[d]},i=Ee(e.modelValue).map((t=>De(e.data||[],(e=>l("value",e)===t),(e=>l("children",e)),((e,t,n,d)=>d&&l("value",d))))).filter((e=>me(e))),c=W((()=>{if(!e.renderAfterExpand&&!e.lazy)return[];const t=[];return we(e.data.concat(e.cacheData),(e=>{const n=l("value",e);t.push({value:n,currentLabel:l("label",e),isDisabled:l("disabled",e)})}),(e=>l("children",e))),t}));return{...te(J(e),Object.keys(xe.props)),...t,nodeKey:r,expandOnClickNode:W((()=>!e.checkStrictly&&e.expandOnClickNode)),defaultExpandedKeys:W((()=>e.defaultExpandedKeys?e.defaultExpandedKeys.concat(i):i)),renderContent:(t,{node:d,data:o,store:a})=>t(be,{value:l("value",o),label:l("label",o),disabled:l("disabled",o)},e.renderContent?()=>e.renderContent(t,{node:d,data:o,store:a}):n.default?()=>n.default({node:d,data:o,store:a}):void 0),filterNodeMethod:(t,n,d)=>e.filterNodeMethod?e.filterNodeMethod(t,n,d):!t||new RegExp(ne(t),"i").test(l("label",n)||""),onNodeClick:(n,d,a)=>{var r,s,i,c;if(null==(r=t.onNodeClick)||r.call(t,n,d,a),!e.showCheckbox||!e.checkOnClickNode){if(e.showCheckbox||!e.checkStrictly&&!d.isLeaf)e.expandOnClickNode&&a.proxy.handleExpandIconClick();else if(!l("disabled",n)){const e=null==(s=o.value)?void 0:s.states.options.get(l("value",n));null==(i=o.value)||i.handleOptionSelect(e)}null==(c=o.value)||c.focus()}},onCheck:(n,r)=>{var s;if(!e.showCheckbox)return;const i=l("value",n),c={};we([a.value.store.root],(e=>c[e.key]=e),(e=>e.childNodes));const h=r.checkedKeys,u=e.multiple?Ee(e.modelValue).filter((e=>!(e in c)&&!h.includes(e))):[],p=u.concat(h);if(e.checkStrictly)d(X,e.multiple?p:p.includes(i)?i:void 0);else if(e.multiple)d(X,u.concat(a.value.getCheckedKeys(!0)));else{const t=De([n],(e=>!Ke(l("children",e))&&!l("disabled",e)),(e=>l("children",e))),o=t?l("value",t):void 0,a=me(e.modelValue)&&!!De([n],(t=>l("value",t)===e.modelValue),(e=>l("children",e)));d(X,o===e.modelValue||a?void 0:o)}x((()=>{var d;const o=Ee(e.modelValue);a.value.setCheckedKeys(o),null==(d=t.onCheck)||d.call(t,n,{checkedKeys:a.value.getCheckedKeys(),checkedNodes:a.value.getCheckedNodes(),halfCheckedKeys:a.value.getHalfCheckedKeys(),halfCheckedNodes:a.value.getHalfCheckedNodes()})})),null==(s=o.value)||s.focus()},cacheOptions:c}})(t,n,{select:r,tree:s,key:l}),v=o({});return a(v),H((()=>{Object.assign(v,{...te(s.value,["filter","updateKeyChildren","getCheckedNodes","setCheckedNodes","getCheckedKeys","setCheckedKeys","setChecked","getHalfCheckedNodes","getHalfCheckedKeys","getCurrentKey","getCurrentNode","setCurrentKey","setCurrentNode","getNode","remove","append","insertBefore","insertAfter"]),...te(r.value,["focus","blur"])})})),()=>h(e,o({...c,ref:e=>r.value=e}),{...d,default:()=>[h(Se,{data:u.value}),h(xe,o({...p,ref:e=>s.value=e}))]})}}),[["__file","tree-select.vue"]]);Ae.install=e=>{e.component(Ae.name,Ae)};const Oe=Ae;export{Oe as E};
