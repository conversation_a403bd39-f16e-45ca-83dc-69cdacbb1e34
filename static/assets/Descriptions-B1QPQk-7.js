import{D as e}from"./Descriptions-CxXasHgY.js";import{d as s,l,s as a,e as t,A as i,y as o,o as r,c as m,a as p,w as d,f as n,z as u,t as c,F as j,_ as f}from"./index-C6fb_XFi.js";import{u as b,F as x}from"./useForm-RHT9mrs1.js";import{a as _}from"./el-form-C2Y6uNCj.js";import{u as h}from"./useValidator-B7S-lU_d.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./el-col-Dl4_4Pn5.js";import"./index-w1A_Rrgh.js";import"./el-tag-C_oEQYGz.js";import"./el-checkbox-CvJzNe2E.js";import"./index-BWEJ0epC.js";import"./el-radio-group-hI5DSxSU.js";/* empty css                          */import"./el-input-number-DVs4I2j5.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./el-virtual-list-D7NvYvyu.js";import"./raf-DGOAeO92.js";import"./el-select-v2-CaMVABoW.js";import"./el-switch-Bh7JeorW.js";import"./el-tree-select-D8i5b8X5.js";import"./el-divider-Bw95UAdD.js";/* empty css                */import"./el-upload-DFauS7op.js";import"./el-progress-sY5OgffI.js";import"./InputPassword-ywconkEY.js";import"./style.css_vue_type_style_index_0_src_true_lang-DFrnfRdK.js";import"./JsonEditor.vue_vue_type_script_setup_true_lang-BhiHGTWK.js";import"./IconPicker-DMD4uMJR.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-pagination-FWx5cl5J.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./tsxHelper-CeCzRM_x.js";import"./castArray-DRqY4cIf.js";const D={class:"text-center mt-10px"},k=f(s({__name:"Descriptions",setup(s){const{required:f}=h(),{t:k}=l(),g=a({username:"chenkl",nickName:"梦似花落。",age:26,phone:"13655971xxxx",email:"<EMAIL>",addr:"这是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的地址",sex:"男",certy:"3505831994xxxxxxxx"}),V=a([{field:"username",label:k("descriptionsDemo.username")},{field:"nickName",label:k("descriptionsDemo.nickName")},{field:"phone",label:k("descriptionsDemo.phone")},{field:"email",label:k("descriptionsDemo.email")},{field:"addr",label:k("descriptionsDemo.addr"),span:24}]),N=a([{field:"username",label:k("descriptionsDemo.username"),slots:{label:e=>t("span",{class:"is-required--item"},[e.label]),default:()=>t(_,{prop:"username"},{default:()=>[t(i,{modelValue:v.username,"onUpdate:modelValue":e=>v.username=e},null)]})}},{field:"nickName",label:k("descriptionsDemo.nickName"),slots:{label:e=>t("span",{class:"is-required--item"},[e.label]),default:()=>t(_,{prop:"nickName"},{default:()=>[t(i,{modelValue:v.nickName,"onUpdate:modelValue":e=>v.nickName=e},null)]})}},{field:"phone",label:k("descriptionsDemo.phone"),slots:{label:e=>t("span",{class:"is-required--item"},[e.label]),default:()=>t(_,{prop:"phone"},{default:()=>[t(i,{modelValue:v.phone,"onUpdate:modelValue":e=>v.phone=e},null)]})}},{field:"email",label:k("descriptionsDemo.email"),slots:{label:e=>t("span",{class:"is-required--item"},[e.label]),default:()=>t(_,{prop:"email"},{default:()=>[t(i,{modelValue:v.email,"onUpdate:modelValue":e=>v.email=e},null)]})}},{field:"addr",label:k("descriptionsDemo.addr"),slots:{label:e=>t("span",{class:"is-required--item"},[e.label]),default:()=>t(_,{prop:"addr"},{default:()=>[t(i,{modelValue:v.addr,"onUpdate:modelValue":e=>v.addr=e},null)]})},span:24}]),v=a({username:"",nickName:"",phone:"",email:"",addr:""}),y=a({username:[f()],nickName:[f()],phone:[f()],email:[f()],addr:[f()]}),{formRegister:q,formMethods:U}=b(),{getElFormExpose:w}=U,F=async()=>{const e=await w();null==e||e.validate((e=>{}))};return(s,l)=>{const a=o("BaseButton");return r(),m(j,null,[t(p(e),{title:p(k)("descriptionsDemo.descriptions"),message:p(k)("descriptionsDemo.descriptionsDes"),data:g,schema:V},null,8,["title","message","data","schema"]),t(p(x),{"is-custom":"",model:v,rules:y,onRegister:p(q)},{default:d((()=>[t(p(e),{title:p(k)("descriptionsDemo.form"),data:g,schema:N,class:"mt-20px"},null,8,["title","data","schema"]),n("div",D,[t(a,{onClick:F},{default:d((()=>[u(c(p(k)("formDemo.formValidation")),1)])),_:1})])])),_:1},8,["model","rules","onRegister"])],64)}}}),[["__scopeId","data-v-25b10d4d"]]);export{k as default};
