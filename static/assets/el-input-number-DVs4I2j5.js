import{aS as e,Y as a,ay as l,a2 as t,a1 as n,bs as r,b7 as u,a0 as s,d as i,aA as o,a6 as d,r as c,s as m,aB as b,a7 as v,aN as p,aF as f,ba as N,O as y,H as V,cb as h,o as I,c as g,Q as x,a as S,n as E,ad as w,a8 as A,e as F,w as k,i as B,D as _,ch as K,C as M,j as O,aM as T,U as C,ae as $,A as z,a9 as D,bu as P,a3 as Y,aC as j,ag as L}from"./index-C6fb_XFi.js";const G=100,H=600,Q={beforeMount(a,l){const t=l.value,{interval:n=G,delay:r=H}=e(t)?{}:t;let u,s;const i=()=>e(t)?t():t.handler(),o=()=>{s&&(clearTimeout(s),s=void 0),u&&(clearInterval(u),u=void 0)};a.addEventListener("mousedown",(e=>{0===e.button&&(o(),i(),document.addEventListener("mouseup",(()=>o()),{once:!0}),s=setTimeout((()=>{u=setInterval((()=>{i()}),n)}),r))}))}},U=a({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:Number,readonly:Boolean,disabled:Boolean,size:l,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:e=>null===e||t(e)||["min","max"].includes(e),default:null},name:String,label:String,placeholder:String,precision:{type:Number,validator:e=>e>=0&&e===Number.parseInt(`${e}`,10)},validateEvent:{type:Boolean,default:!0}}),W={[n]:(e,a)=>a!==e,blur:e=>e instanceof FocusEvent,focus:e=>e instanceof FocusEvent,[r]:e=>t(e)||u(e),[s]:e=>t(e)||u(e)},q=["aria-label","onKeydown"],J=["aria-label","onKeydown"],R=i({name:"ElInputNumber"});const X=L(D(i({...R,props:U,emits:W,setup(e,{expose:a,emit:l}){const i=e,{t:D}=o(),L=d("input-number"),G=c(),H=m({currentValue:i.modelValue,userInput:null}),{formItem:U}=b(),W=v((()=>t(i.modelValue)&&i.modelValue<=i.min)),R=v((()=>t(i.modelValue)&&i.modelValue>=i.max)),X=v((()=>{const e=ne(i.step);return p(i.precision)?Math.max(ne(i.modelValue),e):(i.precision,i.precision)})),Z=v((()=>i.controls&&"right"===i.controlsPosition)),ee=f(),ae=N(),le=v((()=>{if(null!==H.userInput)return H.userInput;let e=H.currentValue;if(u(e))return"";if(t(e)){if(Number.isNaN(e))return"";p(i.precision)||(e=e.toFixed(i.precision))}return e})),te=(e,a)=>{if(p(a)&&(a=X.value),0===a)return Math.round(e);let l=String(e);const t=l.indexOf(".");if(-1===t)return e;if(!l.replace(".","").split("")[t+a])return e;const n=l.length;return"5"===l.charAt(n-1)&&(l=`${l.slice(0,Math.max(0,n-1))}6`),Number.parseFloat(Number(l).toFixed(a))},ne=e=>{if(u(e))return 0;const a=e.toString(),l=a.indexOf(".");let t=0;return-1!==l&&(t=a.length-l-1),t},re=(e,a=1)=>t(e)?te(e+i.step*a):H.currentValue,ue=()=>{if(i.readonly||ae.value||R.value)return;const e=Number(le.value)||0,a=re(e);oe(a),l(r,H.currentValue),ve()},se=()=>{if(i.readonly||ae.value||W.value)return;const e=Number(le.value)||0,a=re(e,-1);oe(a),l(r,H.currentValue),ve()},ie=(e,a)=>{const{max:t,min:n,step:r,precision:o,stepStrictly:d,valueOnClear:c}=i;t<n&&P("InputNumber","min should not be greater than max.");let m=Number(e);if(u(e)||Number.isNaN(m))return null;if(""===e){if(null===c)return null;m=Y(c)?{min:n,max:t}[c]:c}return d&&(m=te(Math.round(m/r)*r,o)),p(o)||(m=te(m,o)),(m>t||m<n)&&(m=m>t?t:n,a&&l(s,m)),m},oe=(e,a=!0)=>{var t;const r=H.currentValue,u=ie(e);a?r===u&&e||(H.userInput=null,l(s,u),r!==u&&l(n,u,r),i.validateEvent&&(null==(t=null==U?void 0:U.validate)||t.call(U,"change").catch((e=>j()))),H.currentValue=u):l(s,u)},de=e=>{H.userInput=e;const a=""===e?null:Number(e);l(r,a),oe(a,!1)},ce=e=>{const a=""!==e?Number(e):"";(t(a)&&!Number.isNaN(a)||""===e)&&oe(a),ve(),H.userInput=null},me=e=>{l("focus",e)},be=e=>{var a;H.userInput=null,l("blur",e),i.validateEvent&&(null==(a=null==U?void 0:U.validate)||a.call(U,"blur").catch((e=>j())))},ve=()=>{H.currentValue!==i.modelValue&&(H.currentValue=i.modelValue)},pe=e=>{document.activeElement===e.target&&e.preventDefault()};return y((()=>i.modelValue),((e,a)=>{const l=ie(e,!0);null===H.userInput&&l!==a&&(H.currentValue=l)}),{immediate:!0}),V((()=>{var e;const{min:a,max:n,modelValue:r}=i,u=null==(e=G.value)?void 0:e.input;if(u.setAttribute("role","spinbutton"),Number.isFinite(n)?u.setAttribute("aria-valuemax",String(n)):u.removeAttribute("aria-valuemax"),Number.isFinite(a)?u.setAttribute("aria-valuemin",String(a)):u.removeAttribute("aria-valuemin"),u.setAttribute("aria-valuenow",H.currentValue||0===H.currentValue?String(H.currentValue):""),u.setAttribute("aria-disabled",String(ae.value)),!t(r)&&null!=r){let e=Number(r);Number.isNaN(e)&&(e=null),l(s,e)}})),h((()=>{var e,a;const l=null==(e=G.value)?void 0:e.input;null==l||l.setAttribute("aria-valuenow",`${null!=(a=H.currentValue)?a:""}`)})),a({focus:()=>{var e,a;null==(a=null==(e=G.value)?void 0:e.focus)||a.call(e)},blur:()=>{var e,a;null==(a=null==(e=G.value)?void 0:e.blur)||a.call(e)}}),(e,a)=>(I(),g("div",{class:E([S(L).b(),S(L).m(S(ee)),S(L).is("disabled",S(ae)),S(L).is("without-controls",!e.controls),S(L).is("controls-right",S(Z))]),onDragstart:a[0]||(a[0]=$((()=>{}),["prevent"]))},[e.controls?x((I(),g("span",{key:0,role:"button","aria-label":S(D)("el.inputNumber.decrease"),class:E([S(L).e("decrease"),S(L).is("disabled",S(W))]),onKeydown:w(se,["enter"])},[A(e.$slots,"decrease-icon",{},(()=>[F(S(M),null,{default:k((()=>[S(Z)?(I(),B(S(_),{key:0})):(I(),B(S(K),{key:1}))])),_:1})]))],42,q)),[[S(Q),se]]):O("v-if",!0),e.controls?x((I(),g("span",{key:1,role:"button","aria-label":S(D)("el.inputNumber.increase"),class:E([S(L).e("increase"),S(L).is("disabled",S(R))]),onKeydown:w(ue,["enter"])},[A(e.$slots,"increase-icon",{},(()=>[F(S(M),null,{default:k((()=>[S(Z)?(I(),B(S(T),{key:0})):(I(),B(S(C),{key:1}))])),_:1})]))],42,J)),[[S(Q),ue]]):O("v-if",!0),F(S(z),{id:e.id,ref_key:"input",ref:G,type:"number",step:e.step,"model-value":S(le),placeholder:e.placeholder,readonly:e.readonly,disabled:S(ae),size:S(ee),max:e.max,min:e.min,name:e.name,label:e.label,"validate-event":!1,onWheel:pe,onKeydown:[w($(ue,["prevent"]),["up"]),w($(se,["prevent"]),["down"])],onBlur:be,onFocus:me,onInput:de,onChange:ce},null,8,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","label","onKeydown"])],34))}}),[["__file","input-number.vue"]]));export{X as E,Q as v};
