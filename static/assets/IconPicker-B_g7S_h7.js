import{I as e}from"./IconPicker-DMD4uMJR.js";import{_ as t}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as o,l as s,r,o as a,i,w as p,e as l,a as n}from"./index-C6fb_XFi.js";import"./el-popper-CeVwVUf9.js";import"./el-tab-pane-DDoZFwPS.js";import"./strings-BiUeKphX.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./el-tag-C_oEQYGz.js";import"./index-BWEJ0epC.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";const m=o({__name:"IconPicker",setup(o){const{t:m}=s(),u=r("tdesign:book-open");return(o,s)=>(a(),i(n(t),{title:n(m)("router.iconPicker")},{default:p((()=>[l(n(e),{modelValue:u.value,"onUpdate:modelValue":s[0]||(s[0]=e=>u.value=e)},null,8,["modelValue"])])),_:1},8,["title"]))}});export{m as default};
