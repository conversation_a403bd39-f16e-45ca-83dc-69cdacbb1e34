import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,u as a,r as s,s as l,v as o,B as i,C as r,D as n,e as p,G as m,F as d,H as u,o as c,c as g,w as f,a as y,z as v,t as k,A as j,f as _,I as h,J as b,l as x,K as w}from"./index-C6fb_XFi.js";import{a as S,E as I}from"./el-col-Dl4_4Pn5.js";import{E as C}from"./el-text-BnUG9HvL.js";import{E as T}from"./el-progress-sY5OgffI.js";import{E}from"./el-tag-C_oEQYGz.js";import{E as A}from"./el-switch-Bh7JeorW.js";import{E as P,a as V,b as D}from"./el-dropdown-item-DpH7Woj3.js";import"./el-popper-CeVwVUf9.js";import{_ as z}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as U}from"./useTable-CijeIiBB.js";import{u as W}from"./useIcon-BxqaCND-.js";import{s as L,a as N,g as H,d as F,r as O}from"./index-B40b3p-m.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{_ as B}from"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import{_ as G}from"./ProgressInfo.vue_vue_type_script_setup_true_lang-DhMx0S3W.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./index-BWEJ0epC.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";import"./el-divider-Bw95UAdD.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./DetailTemplate-Dao-XeZd.js";/* empty css                */import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const J={class:"mb-10px"},K={style:{position:"relative",top:"12px"}};function M(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!b(e)}const q=t({__name:"Task",setup(t){const{push:b}=a(),q=W({icon:"iconoir:search"}),{t:Q}=x(),X=s(""),Y=()=>{ge()},$=l([{field:"selection",type:"selection",minWidth:55},{field:"name",label:Q("task.taskName"),minWidth:100},{field:"taskNum",label:Q("task.taskCount"),minWidth:70,formatter:(e,t,a)=>o(E,{type:"info"},(()=>a))},{field:"progress",label:Q("task.taskProgress"),minWidth:200,formatter:(e,t,a)=>o(T,{percentage:a,type:"line",striped:!0,status:a<100?"":"success",stripedFlow:a<100})},{field:"status",label:Q("common.state"),minWidth:200,formatter:(e,t,a)=>{let s,l;switch(a){case 1:s="info",l=Q("task.running");break;case 2:s="warning",l=Q("task.stop");break;case 3:s="success",l=Q("task.finish");break;default:s="default",l=""}return o(E,{type:s},(()=>l))}},{field:"creatTime",minWidth:200,label:Q("task.createTime")},{field:"endTime",label:Q("task.endTime"),minWidth:200,formatter:(e,t,a)=>""==a?"-":a},{field:"action",label:Q("tableDemo.action"),minWidth:"420",fixed:"right",formatter:(e,t,a)=>{let s,l,u;const c=o(D,{onCommand:t=>{switch(t){case"retest":Ae(e);break;case"delete":Se(e);break;case"stop":se(e.id);break;case"start":le(e.id)}}},{default:()=>o(i,{style:{outline:"none",boxShadow:"none"}},(()=>[Q("common.operation"),o(r,{},(()=>o(n)))])),dropdown:()=>o(P,null,(()=>3===e.status?[o(V,{command:"retest"},(()=>Q("task.retest"))),o(V,{command:"delete"},(()=>Q("common.delete")))]:[o(V,{command:"start"},(()=>Q("task.start"))),o(V,{command:"stop"},(()=>Q("task.stop"))),o(V,{command:"retest"},(()=>Q("task.retest"))),o(V,{command:"delete"},(()=>Q("common.delete")))]))});return p(d,null,[c,p(m,{type:"primary",onClick:()=>ae(e.name),style:{marginLeft:"10px"}},M(s=Q("task.result"))?s:{default:()=>[s]}),p(m,{type:"success",onClick:()=>xe(e),style:{marginLeft:"10px"}},M(l=Q("common.view"))?l:{default:()=>[l]}),p(i,{type:"warning",onClick:()=>te(e.id)},M(u=Q("task.taskProgress"))?u:{default:()=>[u]})])}}]),Z=s(!1);let ee="";const te=async e=>{ee=e,Z.value=!0},ae=async e=>{b(`/asset-information/index?task=${e}`)},se=async e=>{await L(e)},le=async e=>{await N(e)},oe=()=>{Z.value=!1},{tableRegister:ie,tableState:re,tableMethods:ne}=U({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=re,a=await H(X.value,e.value,t.value);return{list:a.data.list,total:a.data.total}},immediate:!0}),{loading:pe,dataList:me,total:de,currentPage:ue,pageSize:ce}=re;ce.value=20;const{getList:ge,getElTableExpose:fe}=ne;function ye(){return{background:"var(--el-fill-color-light)"}}const ve=s(!1),ke=async()=>{be.value="",je=Q("task.addTask"),he.value=!0,ve.value=!0};let je=Q("task.addTask");const _e=()=>{ve.value=!1};let he=s(!0);const be=s(""),xe=async e=>{be.value=e.id,ve.value=!0,he.value=!1,je=Q("common.view")},we=async()=>{const e=s(!1);w({title:"Delete",draggable:!0,message:()=>o("div",{style:{display:"flex",alignItems:"center"}},[o("p",{style:{margin:"0 10px 0 0"}},Q("task.delAsset")),o(A,{modelValue:e.value,"onUpdate:modelValue":t=>{e.value=t}})])}).then((async()=>{await Ee(e.value)}))},Se=async e=>{const t=s(!1);w({title:"Delete",draggable:!0,message:()=>o("div",{style:{display:"flex",alignItems:"center"}},[o("p",{style:{margin:"0 10px 0 0"}},Q("task.delAsset")),o(A,{modelValue:t.value,"onUpdate:modelValue":e=>{t.value=e}})])}).then((async()=>{await Ce(e,t.value)}))},Ie=s(!1),Ce=async(e,t)=>{Ie.value=!0;try{await F([e.id],t);Ie.value=!1,ge()}catch(a){Ie.value=!1,ge()}},Te=s([]),Ee=async e=>{const t=await fe(),a=(null==t?void 0:t.getSelectionRows())||[];Te.value=a.map((e=>e.id)),Ie.value=!0;try{await F(Te.value,e);Ie.value=!1,ge()}catch(s){Ie.value=!1,ge()}},Ae=async e=>{window.confirm("Are you sure you want to retest?")&&await Pe(e)},Pe=async e=>{try{await O(e.id),ge()}catch(t){ge()}};u((()=>{De(),window.addEventListener("resize",De)}));const Ve=s(0),De=()=>{const e=window.innerHeight||document.documentElement.clientHeight;Ve.value=.75*e};return(t,a)=>(c(),g(d,null,[p(y(e),null,{default:f((()=>[p(y(S),null,{default:f((()=>[p(y(I),{span:1},{default:f((()=>[p(y(C),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:f((()=>[v(k(y(Q)("task.taskName"))+":",1)])),_:1})])),_:1}),p(y(I),{span:5},{default:f((()=>[p(y(j),{modelValue:X.value,"onUpdate:modelValue":a[0]||(a[0]=e=>X.value=e),placeholder:y(Q)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),p(y(I),{span:5,style:{position:"relative",left:"16px"}},{default:f((()=>[p(y(i),{type:"primary",icon:y(q),style:{height:"100%"},onClick:Y},{default:f((()=>[v("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),p(y(S),null,{default:f((()=>[p(y(I),{style:{position:"relative",top:"16px"}},{default:f((()=>[_("div",J,[p(y(m),{type:"primary",onClick:ke},{default:f((()=>[v(k(y(Q)("task.addTask")),1)])),_:1}),p(y(m),{type:"danger",loading:Ie.value,onClick:we},{default:f((()=>[v(k(y(Q)("task.delTask")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),_("div",K,[p(y(z),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:y(ce),"onUpdate:pageSize":a[1]||(a[1]=e=>h(ce)?ce.value=e:null),currentPage:y(ue),"onUpdate:currentPage":a[2]||(a[2]=e=>h(ue)?ue.value=e:null),columns:$,data:y(me),stripe:"",border:!0,loading:y(pe),"max-height":Ve.value,resizable:!0,pagination:{total:y(de),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:y(ie),headerCellStyle:ye,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])])])),_:1}),p(y(R),{modelValue:ve.value,"onUpdate:modelValue":a[3]||(a[3]=e=>ve.value=e),title:y(je),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:f((()=>[p(B,{closeDialog:_e,getList:y(ge),create:y(he),taskid:be.value,schedule:!1,tp:"scan",targetIds:[]},null,8,["getList","create","taskid"])])),_:1},8,["modelValue","title"]),p(y(R),{modelValue:Z.value,"onUpdate:modelValue":a[4]||(a[4]=e=>Z.value=e),title:y(Q)("task.taskProgress"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},width:"70%","max-height":"700"},{default:f((()=>[p(G,{closeDialog:oe,getProgressInfoID:y(ee),getProgressInfotype:"scan",getProgressInforunnerid:""},null,8,["getProgressInfoID"])])),_:1},8,["modelValue","title"])],64))}});export{q as default};
