import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as t,l as i,r as a,o as l,i as r,w as s,e as o,a as p}from"./index-3XfDPlIS.js";import{_ as m}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{a as d}from"./index-DkbkkiFT.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./el-table-column-B5hg3WH6.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tag-DcMbxLLg.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./index-Dz8ZrwBc.js";const j=t({__name:"TableVideoPreview",setup(t){const{t:j}=i(),n=[{field:"title",label:j("tableDemo.title")},{field:"video_uri",label:j("tableDemo.videoPreview")},{field:"author",label:j("tableDemo.author")},{field:"display_time",label:j("tableDemo.displayTime")},{field:"pageviews",label:j("tableDemo.pageviews")}],u=a(!0);let _=a([]);return(async e=>{const t=await d(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{u.value=!1}));t&&(_.value=t.data.list)})(),(t,i)=>(l(),r(p(e),{title:p(j)("router.PicturePreview")},{default:s((()=>[o(p(m),{columns:n,data:p(_),loading:u.value,preview:["image_uri","video_uri"]},null,8,["data","loading"])])),_:1},8,["title"]))}});export{j as default};
