import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,r as a,s as l,e as o,G as i,F as s,y as r,o as n,c as p,w as u,a as m,z as c,A as d,B as g,f,t as v,I as y,J as _,l as j}from"./index-DfJTpRkj.js";import{a as b,E as x}from"./el-col-B4Ik8fnS.js";import{_ as h}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{_ as S}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{u as w}from"./useIcon-CNpM61rT.js";import{u as k}from"./useTable-CtyddZqf.js";import{b as D,e as z}from"./index-CrwqUA4w.js";import{_ as C}from"./PortDetail.vue_vue_type_script_setup_true_lang-B6myK3uk.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tag-CbhrEnto.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";import"./el-form-DsaI0u2w.js";import"./el-divider-0NmzbuNU.js";const A={class:"mb-10px"},P={class:"mb-10px"};function E(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!_(e)}const I=t({__name:"PortDictionary",setup(t){const{t:_}=j(),I=w({icon:"iconoir:search"}),U=a(""),F=()=>{W()},V=l([{field:"selection",type:"selection",width:"55"},{field:"id",hidden:!0},{field:"name",label:_("portDict.name"),minWidth:40},{field:"value",label:_("portDict.value")},{field:"action",label:_("tableDemo.action"),minWidth:40,formatter:(e,t,a)=>{let l,r;return o(s,null,[o(i,{type:"primary",onClick:()=>X(e)},E(l=_("common.edit"))?l:{default:()=>[l]}),o(i,{type:"danger",onClick:()=>Z(e)},E(r=_("common.delete"))?r:{default:()=>[r]})])}}]),{tableRegister:L,tableState:R,tableMethods:T}=k({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=R,a=await z(U.value,e.value,t.value);return{list:a.data.list,total:a.data.total}}}),{loading:H,dataList:M,total:N,currentPage:B,pageSize:O}=R,{getList:W,getElTableExpose:G}=T;function J(){return{background:"var(--el-fill-color-light)"}}const K=a(!1),$=async()=>{Q.id="",Q.value="",Q.name="",K.value=!0},q=()=>{K.value=!1};let Q=l({id:"",name:"",value:""});const X=e=>{Q.id=e.id,Q.value=e.value,Q.name=e.name,K.value=!0},Y=a(!1),Z=async e=>{Y.value=!0;try{await D([e.id]);Y.value=!1,W()}catch(t){Y.value=!1,W()}},ee=a([]),te=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{const e=await G(),t=(null==e?void 0:e.getSelectionRows())||[];ee.value=t.map((e=>e.id)),Y.value=!0;try{await D(ee.value),Y.value=!1,W()}catch(a){Y.value=!1,W()}})()};return(t,a)=>{const l=r("ElText");return n(),p(s,null,[o(m(e),null,{default:u((()=>[o(m(b),{gutter:20,style:{"margin-bottom":"15px"}},{default:u((()=>[o(m(x),{span:1.5},{default:u((()=>[o(l,{class:"mx-1",style:{position:"relative",top:"8px"}},{default:u((()=>[c("Search :")])),_:1})])),_:1}),o(m(x),{span:5},{default:u((()=>[o(m(d),{modelValue:U.value,"onUpdate:modelValue":a[0]||(a[0]=e=>U.value=e),placeholder:m(_)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(m(x),{span:5},{default:u((()=>[o(m(g),{type:"primary",icon:m(I),style:{height:"38px"},onClick:F},{default:u((()=>[c("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(m(b),{gutter:60},{default:u((()=>[o(m(x),{span:1},{default:u((()=>[f("div",A,[o(m(g),{type:"primary",onClick:$},{default:u((()=>[c(v(m(_)("common.new")),1)])),_:1})])])),_:1}),o(m(x),{span:1},{default:u((()=>[f("div",P,[o(m(i),{type:"danger",loading:Y.value,onClick:te},{default:u((()=>[c(v(m(_)("common.delete")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),o(m(h),{pageSize:m(O),"onUpdate:pageSize":a[1]||(a[1]=e=>y(O)?O.value=e:null),currentPage:m(B),"onUpdate:currentPage":a[2]||(a[2]=e=>y(B)?B.value=e:null),columns:V,data:m(M),stripe:"",border:!0,loading:m(H),resizable:!0,pagination:{total:m(N),pageSizes:[10,20,50,100,200,500,1e3]},onRegister:m(L),headerCellStyle:J,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister"])])),_:1}),o(m(S),{modelValue:K.value,"onUpdate:modelValue":a[3]||(a[3]=e=>K.value=e),title:m(_)("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:400},{default:u((()=>[o(C,{closeDialog:q,portDictForm:m(Q),getList:m(W)},null,8,["portDictForm","getList"])])),_:1},8,["modelValue","title"])],64)}}});export{I as default};
