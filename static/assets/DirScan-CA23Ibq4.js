import{d as e,r as t,s as l,e as a,z as i,S as s,v as o,A as r,B as n,H as p,o as u,c as d,a as c,w as m,I as g,F as f,J as j,l as h,Y as v,_ as x}from"./index-DfJTpRkj.js";import{u as b}from"./useTable-CtyddZqf.js";import{E as _}from"./el-card-DyZz6u6e.js";import{E as y}from"./el-pagination-FJcT0ZDj.js";import{E as S}from"./el-tag-CbhrEnto.js";import"./el-select-BkpcrSfo.js";import"./el-popper-D2BmgSQA.js";import{E as w,a as k}from"./el-col-B4Ik8fnS.js";import{E as C}from"./el-text-vKNLRkxx.js";import{E}from"./el-link-Bi4jWYBx.js";import{_ as z}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as A}from"./useCrudSchemas-Cz9y99Kk.js";import{a as H,d as L,k as V}from"./index-D4GvAO2k.js";import R from"./Csearch-C6xIjicy.js";import"./index-DE7jtbbk.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./el-table-column-7FjdLFwR.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./tree-BfZhwLPs.js";import"./index-D1ADinPR.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import"./el-divider-0NmzbuNU.js";import"./el-autocomplete-CyglTUOR.js";/* empty css                */import"./el-tree-select-P6ZpyTcB.js";import"./index-Co3LqSsp.js";import"./el-switch-C5ZBDFmL.js";import"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import"./useIcon-CNpM61rT.js";import"./exportData.vue_vue_type_script_setup_true_lang-CthEe30Y.js";import"./el-tab-pane-BijWf7kq.js";import"./el-form-DsaI0u2w.js";import"./el-radio-group-CTAZlJKV.js";import"./el-space-7M-SGVBd.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BD8eoln6.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./index-KH6atv8j.js";import"./index-B0JD2UFG.js";import"./DetailTemplate-DU3RXrBH.js";import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";import"./index-jyMftxhc.js";function I(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!j(e)}const T=x(e({__name:"DirScan",props:{projectList:{}},setup(e){const{t:j}=h(),x=[{keyword:"url",example:'url="http://example.com"',explain:j("searchHelp.url")},{keyword:"statuscode",example:'statuscode=="200"',explain:j("searchHelp.statuscode")},{keyword:"redirect",example:'redirect="https://example.com"',explain:j("searchHelp.redirect")},{keyword:"project",example:'project="Hackerone"',explain:j("searchHelp.project")},{keyword:"length",example:'length="1234"',explain:j("searchHelp.length")}],T=t(""),O=e=>{T.value=e,Q()},U=l({}),D=l([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:j("tableDemo.index"),type:"index",minWidth:55},{field:"url",label:"URL",minWidth:200,formatter:(e,t,l)=>a(E,{href:l,underline:!1,target:"_blank"},I(l)?l:{default:()=>[l]})},{field:"status",label:j("dirScan.status"),columnKey:"status",minWidth:120,formatter:(e,t,l)=>{if(null==l)return a("div",null,[i("-")]);let o="";return o=l<300?"#2eb98a":"#ff5252",a(k,{gutter:1},{default:()=>[a(w,{span:1},{default:()=>[a(s,{icon:"clarity:circle-solid",color:o,size:10,style:"transform: translateY(8%)"},null)]}),a(w,{span:2},{default:()=>[a(C,null,I(l)?l:{default:()=>[l]})]})]})},filters:[{text:"200",value:200},{text:"201",value:201},{text:"204",value:204},{text:"301",value:301},{text:"302",value:302},{text:"304",value:304},{text:"400",value:400},{text:"401",value:401},{text:"403",value:403},{text:"404",value:404},{text:"500",value:500},{text:"502",value:502},{text:"503",value:503},{text:"504",value:504}]},{field:"length",label:"Length",minWidth:120,sortable:"custom"},{field:"msg",label:"Redirect",minWidth:200},{field:"tags",label:"TAG",fit:"true",formatter:(e,l,a)=>{null==a&&(a=[]),U[e.id]||(U[e.id]={inputVisible:!1,inputValue:"",inputRef:t(null)});const i=U[e.id],s=async()=>{i.inputValue&&(a.push(i.inputValue),H(e.id,P,i.inputValue)),i.inputVisible=!1,i.inputValue=""};return o(k,{},(()=>[...a.map((t=>o(w,{span:24,key:t},(()=>[o("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||ne("tags",t)})(e,t)},[o(S,{closable:!0,onClose:()=>(async t=>{const l=a.indexOf(t);l>-1&&a.splice(l,1),L(e.id,P,t)})(t)},(()=>t))])])))),o(w,{span:24},i.inputVisible?()=>o(r,{ref:i.inputRef,modelValue:i.inputValue,"onUpdate:modelValue":e=>i.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&s()},onBlur:s}):()=>o(n,{class:"button-new-tag",size:"small",onClick:()=>(i.inputVisible=!0,void v((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"}]);let P="DirScanResult";D.forEach((e=>{e.hidden=e.hidden??!1}));let W=t(!1);const F=({field:e,hidden:t})=>{const l=D.findIndex((t=>t.field===e));-1!==l&&(D[l].hidden=t),(()=>{const e=D.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=W.value,localStorage.setItem(`columnConfig_${P}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${P}`)||"{}");D.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),W.value=e.statisticsHidden})();const{allSchemas:N}=A(D),{tableRegister:$,tableState:B,tableMethods:J}=b({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=B,l=await V(T.value,e.value,t.value,le,ie);return{list:l.data.list,total:l.data.total}},immediate:!1}),{loading:K,dataList:M,total:G,currentPage:Y,pageSize:q}=B,{getList:Q,getElTableExpose:Z}=J;function X(){return{background:"var(--el-fill-color-light)"}}q.value=20,p((()=>{te(),window.addEventListener("resize",te)}));const ee=t(0),te=()=>{const e=window.innerHeight||document.documentElement.clientHeight;ee.value=.7*e},le=l({}),ae=async e=>{Object.assign(le,e),Q()},ie=l({}),se=async e=>{const t=e.prop,l=e.order;ie[t]=l,Q()},oe=(e,t)=>{Object.assign(le,t),T.value=e,Q()},re=t([]),ne=(e,t)=>{const l=`${e}=${t}`;re.value=[...re.value,l]},pe=e=>{if(re.value){const[t,l]=e.split("=");t in le&&Array.isArray(le[t])&&(le[t]=le[t].filter((e=>e!==l)),0===le[t].length&&delete le[t]),re.value=re.value.filter((t=>t!==e))}},ue=()=>le;return(e,t)=>(u(),d(f,null,[a(R,{getList:c(Q),handleSearch:O,searchKeywordsData:x,index:c(P),dynamicTags:re.value,handleClose:pe,getElTableExpose:c(Z),handleFilterSearch:oe,projectList:e.$props.projectList,crudSchemas:D,onUpdateColumnVisibility:F,searchResultCount:c(G),getFilter:ue},null,8,["getList","index","dynamicTags","getElTableExpose","projectList","crudSchemas","searchResultCount"]),a(c(k),null,{default:m((()=>[a(c(w),null,{default:m((()=>[a(c(_),null,{default:m((()=>[a(c(z),{pageSize:c(q),"onUpdate:pageSize":t[0]||(t[0]=e=>g(q)?q.value=e:null),currentPage:c(Y),"onUpdate:currentPage":t[1]||(t[1]=e=>g(Y)?Y.value=e:null),columns:c(N).tableColumns,data:c(M),stripe:"",border:!0,loading:c(K),resizable:!0,"max-height":ee.value,onRegister:c($),onFilterChange:ae,onSortChange:se,headerCellStyle:X,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!0,showAfter:0,popperOptions:{},popperClass:"test",placement:"top",hideAfter:0,disabled:!0},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","onRegister"])])),_:1})])),_:1}),a(c(w),{":span":24},{default:m((()=>[a(c(_),null,{default:m((()=>[a(c(y),{pageSize:c(q),"onUpdate:pageSize":t[2]||(t[2]=e=>g(q)?q.value=e:null),currentPage:c(Y),"onUpdate:currentPage":t[3]||(t[3]=e=>g(Y)?Y.value=e:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:c(G)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-238561f3"]]);export{T as default};
