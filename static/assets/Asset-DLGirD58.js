import{d as e,s as t,o as r,i,w as o,e as s,a as l,l as p}from"./index-DfJTpRkj.js";import{E as a,a as m}from"./el-tab-pane-BijWf7kq.js";import j from"./AssetInfo2-BoM2V54n.js";import n from"./Subdomain-CmYZUeaO.js";import u from"./URL-BbZu1pI-.js";import c from"./Crawler-C1bcbZ8g.js";import _ from"./SensitiveInformation-Degk_OHN.js";import d from"./DirScan-CA23Ibq4.js";import b from"./PageMonitoring-DOJQ3SsA.js";import f from"./vul-BVuj6qG1.js";import L from"./SubdomainTakeover-B177Gpxq.js";import{g as v}from"./index-jyMftxhc.js";import g from"./RootDomain-Ev33XRor.js";import x from"./APP-CsKU_nw1.js";import y from"./MP-Ce2lSaat.js";import"./strings-CUyZ1T6U.js";import"./useTable-CtyddZqf.js";import"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import"./refs-DAMUgizk.js";import"./el-popper-D2BmgSQA.js";import"./el-col-B4Ik8fnS.js";import"./el-card-DyZz6u6e.js";import"./el-tag-CbhrEnto.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./index-DE7jtbbk.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./el-link-Bi4jWYBx.js";import"./el-text-vKNLRkxx.js";import"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import"./el-table-column-7FjdLFwR.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./index-CyH6XROR.js";import"./useCrudSchemas-Cz9y99Kk.js";import"./tree-BfZhwLPs.js";import"./index-D4GvAO2k.js";import"./index-D1ADinPR.js";import"./Csearch-C6xIjicy.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import"./el-divider-0NmzbuNU.js";import"./el-autocomplete-CyglTUOR.js";/* empty css                */import"./el-tree-select-P6ZpyTcB.js";import"./index-Co3LqSsp.js";import"./el-switch-C5ZBDFmL.js";import"./useIcon-CNpM61rT.js";import"./exportData.vue_vue_type_script_setup_true_lang-CthEe30Y.js";import"./el-form-DsaI0u2w.js";import"./el-radio-group-CTAZlJKV.js";import"./el-space-7M-SGVBd.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BD8eoln6.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./index-KH6atv8j.js";import"./index-B0JD2UFG.js";import"./DetailTemplate-DU3RXrBH.js";import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";import"./index-CfjbBlcQ.js";import"./AssetDetail2.vue_vue_type_script_setup_true_lang-QZDFaPST.js";import"./index-B-gHSwWD.js";import"./el-drawer-mgC2-Flq.js";import"./MonacoDiffEditor-DS5QEM6N.js";import"./el-descriptions-item-BVaiX-1w.js";import"./Detail.vue_vue_type_script_setup_true_lang-Cp4j-41e.js";const N=e({__name:"Asset",setup(e){const{t:N}=p(),w=t([]);return(async()=>{(await v()).data.list.forEach((e=>{w.push(e)}))})(),(e,t)=>(r(),i(l(m),{type:"border-card"},{default:o((()=>[s(l(a),{label:l(N)("asset.assetName")},{default:o((()=>[s(j,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("rootDomain.rootDomainName")},{default:o((()=>[s(g,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("subdomain.subdomainName")},{default:o((()=>[s(n,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("task.subdomainTakeover")},{default:o((()=>[s(L,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("app.appName")},{default:o((()=>[s(x,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("miniProgram.miniProgramName")},{default:o((()=>[s(y,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("URL.URLName")},{default:o((()=>[s(u,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("crawler.crawlerName")},{default:o((()=>[s(c,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("sensitiveInformation.sensitiveInformationName")},{default:o((()=>[s(_,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("dirScan.dirScanName")},{default:o((()=>[s(d,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("vulnerability.vulnerabilityName")},{default:o((()=>[s(f,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("PageMonitoring.pageMonitoringName")},{default:o((()=>[s(b,{projectList:w},null,8,["projectList"])])),_:1},8,["label"])])),_:1}))}});export{N as default};
