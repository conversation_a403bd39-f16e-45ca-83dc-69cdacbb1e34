import{m as e}from"./MonacoDiffEditor-By1hxVEP.js";import"./index-C6fb_XFi.js";
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.52.0(f6dc0eb8fce67e57f6036f4769d92c1666cdf546)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var t,n,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,s=(e,t,n,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===n||r(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e},u={};s(u,t=e,"default"),n&&s(n,t,"default");var c,d,l,g,f,h,m,p,v,b,_,k,w,y,x,I,E,C,S,L,A,R,T,M,P,j,D,F,N,U,V,O,K,W,H,X,z,$,B,q,Q,G,J,Y,Z,ee,te,ne,re,ie,oe,ae,se,ue,ce,de,le,ge,fe,he,me,pe,ve,be,_e,ke,we,ye,xe,Ie,Ee,Ce,Se,Le,Ae,Re,Te,Me,Pe,je,De,Fe,Ne,Ue,Ve,Oe,Ke,We,He,Xe,ze,$e,Be,qe,Qe,Ge,Je,Ye,Ze,et,tt,nt,rt,it,ot,at,st,ut,ct,dt,lt,gt,ft,ht,mt,pt,vt,bt,_t,kt,wt,yt,xt,It,Et,Ct,St,Lt,At,Rt,Tt,Mt,Pt,jt=class{constructor(e){this._defaults=e,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval((()=>this._checkIfIdle()),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange((()=>this._stopWorker()))}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){if(!this._worker)return;Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=u.editor.createWebWorker({moduleId:"vs/language/css/cssWorker",label:this._defaults.languageId,createData:{options:this._defaults.options,languageId:this._defaults.languageId}}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...e){let t;return this._getClient().then((e=>{t=e})).then((t=>{if(this._worker)return this._worker.withSyncedResources(e)})).then((e=>t))}};(c||(c={})).is=function(e){return"string"==typeof e},(d||(d={})).is=function(e){return"string"==typeof e},(g=l||(l={})).MIN_VALUE=-2147483648,g.MAX_VALUE=2147483647,g.is=function(e){return"number"==typeof e&&g.MIN_VALUE<=e&&e<=g.MAX_VALUE},(h=f||(f={})).MIN_VALUE=0,h.MAX_VALUE=2147483647,h.is=function(e){return"number"==typeof e&&h.MIN_VALUE<=e&&e<=h.MAX_VALUE},(p=m||(m={})).create=function(e,t){return e===Number.MAX_VALUE&&(e=f.MAX_VALUE),t===Number.MAX_VALUE&&(t=f.MAX_VALUE),{line:e,character:t}},p.is=function(e){let t=e;return Dt.objectLiteral(t)&&Dt.uinteger(t.line)&&Dt.uinteger(t.character)},(b=v||(v={})).create=function(e,t,n,r){if(Dt.uinteger(e)&&Dt.uinteger(t)&&Dt.uinteger(n)&&Dt.uinteger(r))return{start:m.create(e,t),end:m.create(n,r)};if(m.is(e)&&m.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)},b.is=function(e){let t=e;return Dt.objectLiteral(t)&&m.is(t.start)&&m.is(t.end)},(k=_||(_={})).create=function(e,t){return{uri:e,range:t}},k.is=function(e){let t=e;return Dt.objectLiteral(t)&&v.is(t.range)&&(Dt.string(t.uri)||Dt.undefined(t.uri))},(y=w||(w={})).create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},y.is=function(e){let t=e;return Dt.objectLiteral(t)&&v.is(t.targetRange)&&Dt.string(t.targetUri)&&v.is(t.targetSelectionRange)&&(v.is(t.originSelectionRange)||Dt.undefined(t.originSelectionRange))},(I=x||(x={})).create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},I.is=function(e){const t=e;return Dt.objectLiteral(t)&&Dt.numberRange(t.red,0,1)&&Dt.numberRange(t.green,0,1)&&Dt.numberRange(t.blue,0,1)&&Dt.numberRange(t.alpha,0,1)},(C=E||(E={})).create=function(e,t){return{range:e,color:t}},C.is=function(e){const t=e;return Dt.objectLiteral(t)&&v.is(t.range)&&x.is(t.color)},(L=S||(S={})).create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},L.is=function(e){const t=e;return Dt.objectLiteral(t)&&Dt.string(t.label)&&(Dt.undefined(t.textEdit)||X.is(t))&&(Dt.undefined(t.additionalTextEdits)||Dt.typedArray(t.additionalTextEdits,X.is))},(R=A||(A={})).Comment="comment",R.Imports="imports",R.Region="region",(M=T||(T={})).create=function(e,t,n,r,i,o){const a={startLine:e,endLine:t};return Dt.defined(n)&&(a.startCharacter=n),Dt.defined(r)&&(a.endCharacter=r),Dt.defined(i)&&(a.kind=i),Dt.defined(o)&&(a.collapsedText=o),a},M.is=function(e){const t=e;return Dt.objectLiteral(t)&&Dt.uinteger(t.startLine)&&Dt.uinteger(t.startLine)&&(Dt.undefined(t.startCharacter)||Dt.uinteger(t.startCharacter))&&(Dt.undefined(t.endCharacter)||Dt.uinteger(t.endCharacter))&&(Dt.undefined(t.kind)||Dt.string(t.kind))},(j=P||(P={})).create=function(e,t){return{location:e,message:t}},j.is=function(e){let t=e;return Dt.defined(t)&&_.is(t.location)&&Dt.string(t.message)},(F=D||(D={})).Error=1,F.Warning=2,F.Information=3,F.Hint=4,(U=N||(N={})).Unnecessary=1,U.Deprecated=2,(V||(V={})).is=function(e){const t=e;return Dt.objectLiteral(t)&&Dt.string(t.href)},(K=O||(O={})).create=function(e,t,n,r,i,o){let a={range:e,message:t};return Dt.defined(n)&&(a.severity=n),Dt.defined(r)&&(a.code=r),Dt.defined(i)&&(a.source=i),Dt.defined(o)&&(a.relatedInformation=o),a},K.is=function(e){var t;let n=e;return Dt.defined(n)&&v.is(n.range)&&Dt.string(n.message)&&(Dt.number(n.severity)||Dt.undefined(n.severity))&&(Dt.integer(n.code)||Dt.string(n.code)||Dt.undefined(n.code))&&(Dt.undefined(n.codeDescription)||Dt.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(Dt.string(n.source)||Dt.undefined(n.source))&&(Dt.undefined(n.relatedInformation)||Dt.typedArray(n.relatedInformation,P.is))},(H=W||(W={})).create=function(e,t,...n){let r={title:e,command:t};return Dt.defined(n)&&n.length>0&&(r.arguments=n),r},H.is=function(e){let t=e;return Dt.defined(t)&&Dt.string(t.title)&&Dt.string(t.command)},(z=X||(X={})).replace=function(e,t){return{range:e,newText:t}},z.insert=function(e,t){return{range:{start:e,end:e},newText:t}},z.del=function(e){return{range:e,newText:""}},z.is=function(e){const t=e;return Dt.objectLiteral(t)&&Dt.string(t.newText)&&v.is(t.range)},(B=$||($={})).create=function(e,t,n){const r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},B.is=function(e){const t=e;return Dt.objectLiteral(t)&&Dt.string(t.label)&&(Dt.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(Dt.string(t.description)||void 0===t.description)},(q||(q={})).is=function(e){const t=e;return Dt.string(t)},(G=Q||(Q={})).replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},G.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},G.del=function(e,t){return{range:e,newText:"",annotationId:t}},G.is=function(e){const t=e;return X.is(t)&&($.is(t.annotationId)||q.is(t.annotationId))},(Y=J||(J={})).create=function(e,t){return{textDocument:e,edits:t}},Y.is=function(e){let t=e;return Dt.defined(t)&&de.is(t.textDocument)&&Array.isArray(t.edits)},(ee=Z||(Z={})).create=function(e,t,n){let r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},ee.is=function(e){let t=e;return t&&"create"===t.kind&&Dt.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Dt.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Dt.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||q.is(t.annotationId))},(ne=te||(te={})).create=function(e,t,n,r){let i={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),void 0!==r&&(i.annotationId=r),i},ne.is=function(e){let t=e;return t&&"rename"===t.kind&&Dt.string(t.oldUri)&&Dt.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Dt.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Dt.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||q.is(t.annotationId))},(ie=re||(re={})).create=function(e,t,n){let r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},ie.is=function(e){let t=e;return t&&"delete"===t.kind&&Dt.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Dt.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Dt.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||q.is(t.annotationId))},(oe||(oe={})).is=function(e){let t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((e=>Dt.string(e.kind)?Z.is(e)||te.is(e)||re.is(e):J.is(e))))},(se=ae||(ae={})).create=function(e){return{uri:e}},se.is=function(e){let t=e;return Dt.defined(t)&&Dt.string(t.uri)},(ce=ue||(ue={})).create=function(e,t){return{uri:e,version:t}},ce.is=function(e){let t=e;return Dt.defined(t)&&Dt.string(t.uri)&&Dt.integer(t.version)},(le=de||(de={})).create=function(e,t){return{uri:e,version:t}},le.is=function(e){let t=e;return Dt.defined(t)&&Dt.string(t.uri)&&(null===t.version||Dt.integer(t.version))},(fe=ge||(ge={})).create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},fe.is=function(e){let t=e;return Dt.defined(t)&&Dt.string(t.uri)&&Dt.string(t.languageId)&&Dt.integer(t.version)&&Dt.string(t.text)},(me=he||(he={})).PlainText="plaintext",me.Markdown="markdown",me.is=function(e){const t=e;return t===me.PlainText||t===me.Markdown},(pe||(pe={})).is=function(e){const t=e;return Dt.objectLiteral(e)&&he.is(t.kind)&&Dt.string(t.value)},(be=ve||(ve={})).Text=1,be.Method=2,be.Function=3,be.Constructor=4,be.Field=5,be.Variable=6,be.Class=7,be.Interface=8,be.Module=9,be.Property=10,be.Unit=11,be.Value=12,be.Enum=13,be.Keyword=14,be.Snippet=15,be.Color=16,be.File=17,be.Reference=18,be.Folder=19,be.EnumMember=20,be.Constant=21,be.Struct=22,be.Event=23,be.Operator=24,be.TypeParameter=25,(ke=_e||(_e={})).PlainText=1,ke.Snippet=2,(we||(we={})).Deprecated=1,(xe=ye||(ye={})).create=function(e,t,n){return{newText:e,insert:t,replace:n}},xe.is=function(e){const t=e;return t&&Dt.string(t.newText)&&v.is(t.insert)&&v.is(t.replace)},(Ee=Ie||(Ie={})).asIs=1,Ee.adjustIndentation=2,(Ce||(Ce={})).is=function(e){const t=e;return t&&(Dt.string(t.detail)||void 0===t.detail)&&(Dt.string(t.description)||void 0===t.description)},(Se||(Se={})).create=function(e){return{label:e}},(Le||(Le={})).create=function(e,t){return{items:e||[],isIncomplete:!!t}},(Re=Ae||(Ae={})).fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},Re.is=function(e){const t=e;return Dt.string(t)||Dt.objectLiteral(t)&&Dt.string(t.language)&&Dt.string(t.value)},(Te||(Te={})).is=function(e){let t=e;return!!t&&Dt.objectLiteral(t)&&(pe.is(t.contents)||Ae.is(t.contents)||Dt.typedArray(t.contents,Ae.is))&&(void 0===e.range||v.is(e.range))},(Me||(Me={})).create=function(e,t){return t?{label:e,documentation:t}:{label:e}},(Pe||(Pe={})).create=function(e,t,...n){let r={label:e};return Dt.defined(t)&&(r.documentation=t),Dt.defined(n)?r.parameters=n:r.parameters=[],r},(De=je||(je={})).Text=1,De.Read=2,De.Write=3,(Fe||(Fe={})).create=function(e,t){let n={range:e};return Dt.number(t)&&(n.kind=t),n},(Ue=Ne||(Ne={})).File=1,Ue.Module=2,Ue.Namespace=3,Ue.Package=4,Ue.Class=5,Ue.Method=6,Ue.Property=7,Ue.Field=8,Ue.Constructor=9,Ue.Enum=10,Ue.Interface=11,Ue.Function=12,Ue.Variable=13,Ue.Constant=14,Ue.String=15,Ue.Number=16,Ue.Boolean=17,Ue.Array=18,Ue.Object=19,Ue.Key=20,Ue.Null=21,Ue.EnumMember=22,Ue.Struct=23,Ue.Event=24,Ue.Operator=25,Ue.TypeParameter=26,(Ve||(Ve={})).Deprecated=1,(Oe||(Oe={})).create=function(e,t,n,r,i){let o={name:e,kind:t,location:{uri:r,range:n}};return i&&(o.containerName=i),o},(Ke||(Ke={})).create=function(e,t,n,r){return void 0!==r?{name:e,kind:t,location:{uri:n,range:r}}:{name:e,kind:t,location:{uri:n}}},(He=We||(We={})).create=function(e,t,n,r,i,o){let a={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==o&&(a.children=o),a},He.is=function(e){let t=e;return t&&Dt.string(t.name)&&Dt.number(t.kind)&&v.is(t.range)&&v.is(t.selectionRange)&&(void 0===t.detail||Dt.string(t.detail))&&(void 0===t.deprecated||Dt.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))},(ze=Xe||(Xe={})).Empty="",ze.QuickFix="quickfix",ze.Refactor="refactor",ze.RefactorExtract="refactor.extract",ze.RefactorInline="refactor.inline",ze.RefactorRewrite="refactor.rewrite",ze.Source="source",ze.SourceOrganizeImports="source.organizeImports",ze.SourceFixAll="source.fixAll",(Be=$e||($e={})).Invoked=1,Be.Automatic=2,(Qe=qe||(qe={})).create=function(e,t,n){let r={diagnostics:e};return null!=t&&(r.only=t),null!=n&&(r.triggerKind=n),r},Qe.is=function(e){let t=e;return Dt.defined(t)&&Dt.typedArray(t.diagnostics,O.is)&&(void 0===t.only||Dt.typedArray(t.only,Dt.string))&&(void 0===t.triggerKind||t.triggerKind===$e.Invoked||t.triggerKind===$e.Automatic)},(Je=Ge||(Ge={})).create=function(e,t,n){let r={title:e},i=!0;return"string"==typeof t?(i=!1,r.kind=t):W.is(t)?r.command=t:r.edit=t,i&&void 0!==n&&(r.kind=n),r},Je.is=function(e){let t=e;return t&&Dt.string(t.title)&&(void 0===t.diagnostics||Dt.typedArray(t.diagnostics,O.is))&&(void 0===t.kind||Dt.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||W.is(t.command))&&(void 0===t.isPreferred||Dt.boolean(t.isPreferred))&&(void 0===t.edit||oe.is(t.edit))},(Ze=Ye||(Ye={})).create=function(e,t){let n={range:e};return Dt.defined(t)&&(n.data=t),n},Ze.is=function(e){let t=e;return Dt.defined(t)&&v.is(t.range)&&(Dt.undefined(t.command)||W.is(t.command))},(tt=et||(et={})).create=function(e,t){return{tabSize:e,insertSpaces:t}},tt.is=function(e){let t=e;return Dt.defined(t)&&Dt.uinteger(t.tabSize)&&Dt.boolean(t.insertSpaces)},(rt=nt||(nt={})).create=function(e,t,n){return{range:e,target:t,data:n}},rt.is=function(e){let t=e;return Dt.defined(t)&&v.is(t.range)&&(Dt.undefined(t.target)||Dt.string(t.target))},(ot=it||(it={})).create=function(e,t){return{range:e,parent:t}},ot.is=function(e){let t=e;return Dt.objectLiteral(t)&&v.is(t.range)&&(void 0===t.parent||ot.is(t.parent))},(st=at||(at={})).namespace="namespace",st.type="type",st.class="class",st.enum="enum",st.interface="interface",st.struct="struct",st.typeParameter="typeParameter",st.parameter="parameter",st.variable="variable",st.property="property",st.enumMember="enumMember",st.event="event",st.function="function",st.method="method",st.macro="macro",st.keyword="keyword",st.modifier="modifier",st.comment="comment",st.string="string",st.number="number",st.regexp="regexp",st.operator="operator",st.decorator="decorator",(ct=ut||(ut={})).declaration="declaration",ct.definition="definition",ct.readonly="readonly",ct.static="static",ct.deprecated="deprecated",ct.abstract="abstract",ct.async="async",ct.modification="modification",ct.documentation="documentation",ct.defaultLibrary="defaultLibrary",(dt||(dt={})).is=function(e){const t=e;return Dt.objectLiteral(t)&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])},(gt=lt||(lt={})).create=function(e,t){return{range:e,text:t}},gt.is=function(e){const t=e;return null!=t&&v.is(t.range)&&Dt.string(t.text)},(ht=ft||(ft={})).create=function(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}},ht.is=function(e){const t=e;return null!=t&&v.is(t.range)&&Dt.boolean(t.caseSensitiveLookup)&&(Dt.string(t.variableName)||void 0===t.variableName)},(pt=mt||(mt={})).create=function(e,t){return{range:e,expression:t}},pt.is=function(e){const t=e;return null!=t&&v.is(t.range)&&(Dt.string(t.expression)||void 0===t.expression)},(bt=vt||(vt={})).create=function(e,t){return{frameId:e,stoppedLocation:t}},bt.is=function(e){const t=e;return Dt.defined(t)&&v.is(e.stoppedLocation)},(kt=_t||(_t={})).Type=1,kt.Parameter=2,kt.is=function(e){return 1===e||2===e},(yt=wt||(wt={})).create=function(e){return{value:e}},yt.is=function(e){const t=e;return Dt.objectLiteral(t)&&(void 0===t.tooltip||Dt.string(t.tooltip)||pe.is(t.tooltip))&&(void 0===t.location||_.is(t.location))&&(void 0===t.command||W.is(t.command))},(It=xt||(xt={})).create=function(e,t,n){const r={position:e,label:t};return void 0!==n&&(r.kind=n),r},It.is=function(e){const t=e;return Dt.objectLiteral(t)&&m.is(t.position)&&(Dt.string(t.label)||Dt.typedArray(t.label,wt.is))&&(void 0===t.kind||_t.is(t.kind))&&void 0===t.textEdits||Dt.typedArray(t.textEdits,X.is)&&(void 0===t.tooltip||Dt.string(t.tooltip)||pe.is(t.tooltip))&&(void 0===t.paddingLeft||Dt.boolean(t.paddingLeft))&&(void 0===t.paddingRight||Dt.boolean(t.paddingRight))},(Et||(Et={})).createSnippet=function(e){return{kind:"snippet",value:e}},(Ct||(Ct={})).create=function(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}},(St||(St={})).create=function(e){return{items:e}},(At=Lt||(Lt={})).Invoked=0,At.Automatic=1,(Rt||(Rt={})).create=function(e,t){return{range:e,text:t}},(Tt||(Tt={})).create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}},(Mt||(Mt={})).is=function(e){const t=e;return Dt.objectLiteral(t)&&d.is(t.uri)&&Dt.string(t.name)},function(e){function t(e,n){if(e.length<=1)return e;const r=e.length/2|0,i=e.slice(0,r),o=e.slice(r);t(i,n),t(o,n);let a=0,s=0,u=0;for(;a<i.length&&s<o.length;){let t=n(i[a],o[s]);e[u++]=t<=0?i[a++]:o[s++]}for(;a<i.length;)e[u++]=i[a++];for(;s<o.length;)e[u++]=o[s++];return e}e.create=function(e,t,n,r){return new Ft(e,t,n,r)},e.is=function(e){let t=e;return!!(Dt.defined(t)&&Dt.string(t.uri)&&(Dt.undefined(t.languageId)||Dt.string(t.languageId))&&Dt.uinteger(t.lineCount)&&Dt.func(t.getText)&&Dt.func(t.positionAt)&&Dt.func(t.offsetAt))},e.applyEdits=function(e,n){let r=e.getText(),i=t(n,((e,t)=>{let n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),o=r.length;for(let t=i.length-1;t>=0;t--){let n=i[t],a=e.offsetAt(n.range.start),s=e.offsetAt(n.range.end);if(!(s<=o))throw new Error("Overlapping edit");r=r.substring(0,a)+n.newText+r.substring(s,r.length),o=a}return r}}(Pt||(Pt={}));var Dt,Ft=class{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&(e.push(r),n=!1);let i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return m.create(0,e);for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return m.create(i,e-t[i])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}};!function(e){const t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(Dt||(Dt={}));var Nt=class{constructor(e,t,n){this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);const r=e=>{let t,n=e.getLanguageId();n===this._languageId&&(this._listener[e.uri.toString()]=e.onDidChangeContent((()=>{window.clearTimeout(t),t=window.setTimeout((()=>this._doValidate(e.uri,n)),500)})),this._doValidate(e.uri,n))},i=e=>{u.editor.setModelMarkers(e,this._languageId,[]);let t=e.uri.toString(),n=this._listener[t];n&&(n.dispose(),delete this._listener[t])};this._disposables.push(u.editor.onDidCreateModel(r)),this._disposables.push(u.editor.onWillDisposeModel(i)),this._disposables.push(u.editor.onDidChangeModelLanguage((e=>{i(e.model),r(e.model)}))),this._disposables.push(n((e=>{u.editor.getModels().forEach((e=>{e.getLanguageId()===this._languageId&&(i(e),r(e))}))}))),this._disposables.push({dispose:()=>{u.editor.getModels().forEach(i);for(let e in this._listener)this._listener[e].dispose()}}),u.editor.getModels().forEach(r)}dispose(){this._disposables.forEach((e=>e&&e.dispose())),this._disposables.length=0}_doValidate(e,t){this._worker(e).then((t=>t.doValidation(e.toString()))).then((n=>{const r=n.map((e=>function(e,t){let n="number"==typeof t.code?String(t.code):t.code;return{severity:Ut(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:n,source:t.source}}(0,e)));let i=u.editor.getModel(e);i&&i.getLanguageId()===t&&u.editor.setModelMarkers(i,t,r)})).then(void 0,(e=>{}))}};function Ut(e){switch(e){case D.Error:return u.MarkerSeverity.Error;case D.Warning:return u.MarkerSeverity.Warning;case D.Information:return u.MarkerSeverity.Info;case D.Hint:return u.MarkerSeverity.Hint;default:return u.MarkerSeverity.Info}}var Vt=class{constructor(e,t){this._worker=e,this._triggerCharacters=t}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(e,t,n,r){const i=e.uri;return this._worker(i).then((e=>e.doComplete(i.toString(),Ot(t)))).then((n=>{if(!n)return;const r=e.getWordUntilPosition(t),i=new u.Range(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn),o=n.items.map((e=>{const t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,command:(n=e.command,n&&"editor.action.triggerSuggest"===n.command?{id:n.command,title:n.title,arguments:n.arguments}:void 0),range:i,kind:Ht(e.kind)};var n,r;return e.textEdit&&(void 0!==(r=e.textEdit).insert&&void 0!==r.replace?t.range={insert:Wt(e.textEdit.insert),replace:Wt(e.textEdit.replace)}:t.range=Wt(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(Xt)),e.insertTextFormat===_e.Snippet&&(t.insertTextRules=u.languages.CompletionItemInsertTextRule.InsertAsSnippet),t}));return{isIncomplete:n.isIncomplete,suggestions:o}}))}};function Ot(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function Kt(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function Wt(e){if(e)return new u.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function Ht(e){const t=u.languages.CompletionItemKind;switch(e){case ve.Text:return t.Text;case ve.Method:return t.Method;case ve.Function:return t.Function;case ve.Constructor:return t.Constructor;case ve.Field:return t.Field;case ve.Variable:return t.Variable;case ve.Class:return t.Class;case ve.Interface:return t.Interface;case ve.Module:return t.Module;case ve.Property:return t.Property;case ve.Unit:return t.Unit;case ve.Value:return t.Value;case ve.Enum:return t.Enum;case ve.Keyword:return t.Keyword;case ve.Snippet:return t.Snippet;case ve.Color:return t.Color;case ve.File:return t.File;case ve.Reference:return t.Reference}return t.Property}function Xt(e){if(e)return{range:Wt(e.range),text:e.newText}}var zt=class{constructor(e){this._worker=e}provideHover(e,t,n){let r=e.uri;return this._worker(r).then((e=>e.doHover(r.toString(),Ot(t)))).then((e=>{if(e)return{range:Wt(e.range),contents:Bt(e.contents)}}))}};function $t(e){return"string"==typeof e?{value:e}:(t=e)&&"object"==typeof t&&"string"==typeof t.kind?"plaintext"===e.kind?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+"\n"+e.value+"\n```\n"};var t}function Bt(e){if(e)return Array.isArray(e)?e.map($t):[$t(e)]}var qt=class{constructor(e){this._worker=e}provideDocumentHighlights(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.findDocumentHighlights(r.toString(),Ot(t)))).then((e=>{if(e)return e.map((e=>({range:Wt(e.range),kind:Qt(e.kind)})))}))}};function Qt(e){switch(e){case je.Read:return u.languages.DocumentHighlightKind.Read;case je.Write:return u.languages.DocumentHighlightKind.Write;case je.Text:return u.languages.DocumentHighlightKind.Text}return u.languages.DocumentHighlightKind.Text}var Gt=class{constructor(e){this._worker=e}provideDefinition(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.findDefinition(r.toString(),Ot(t)))).then((e=>{if(e)return[Jt(e)]}))}};function Jt(e){return{uri:u.Uri.parse(e.uri),range:Wt(e.range)}}var Yt=class{constructor(e){this._worker=e}provideReferences(e,t,n,r){const i=e.uri;return this._worker(i).then((e=>e.findReferences(i.toString(),Ot(t)))).then((e=>{if(e)return e.map(Jt)}))}},Zt=class{constructor(e){this._worker=e}provideRenameEdits(e,t,n,r){const i=e.uri;return this._worker(i).then((e=>e.doRename(i.toString(),Ot(t),n))).then((e=>function(e){if(!e||!e.changes)return;let t=[];for(let n in e.changes){const r=u.Uri.parse(n);for(let i of e.changes[n])t.push({resource:r,versionId:void 0,textEdit:{range:Wt(i.range),text:i.newText}})}return{edits:t}}(e)))}};var en=class{constructor(e){this._worker=e}provideDocumentSymbols(e,t){const n=e.uri;return this._worker(n).then((e=>e.findDocumentSymbols(n.toString()))).then((e=>{if(e)return e.map((e=>"children"in e?tn(e):{name:e.name,detail:"",containerName:e.containerName,kind:nn(e.kind),range:Wt(e.location.range),selectionRange:Wt(e.location.range),tags:[]}))}))}};function tn(e){return{name:e.name,detail:e.detail??"",kind:nn(e.kind),range:Wt(e.range),selectionRange:Wt(e.selectionRange),tags:e.tags??[],children:(e.children??[]).map((e=>tn(e)))}}function nn(e){let t=u.languages.SymbolKind;switch(e){case Ne.File:return t.File;case Ne.Module:return t.Module;case Ne.Namespace:return t.Namespace;case Ne.Package:return t.Package;case Ne.Class:return t.Class;case Ne.Method:return t.Method;case Ne.Property:return t.Property;case Ne.Field:return t.Field;case Ne.Constructor:return t.Constructor;case Ne.Enum:return t.Enum;case Ne.Interface:return t.Interface;case Ne.Function:return t.Function;case Ne.Variable:return t.Variable;case Ne.Constant:return t.Constant;case Ne.String:return t.String;case Ne.Number:return t.Number;case Ne.Boolean:return t.Boolean;case Ne.Array:return t.Array}return t.Function}var rn=class{constructor(e){this._worker=e}provideLinks(e,t){const n=e.uri;return this._worker(n).then((e=>e.findDocumentLinks(n.toString()))).then((e=>{if(e)return{links:e.map((e=>({range:Wt(e.range),url:e.target})))}}))}},on=class{constructor(e){this._worker=e}provideDocumentFormattingEdits(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.format(r.toString(),null,sn(t)).then((e=>{if(e&&0!==e.length)return e.map(Xt)}))))}},an=class{constructor(e){this._worker=e,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(e,t,n,r){const i=e.uri;return this._worker(i).then((e=>e.format(i.toString(),Kt(t),sn(n)).then((e=>{if(e&&0!==e.length)return e.map(Xt)}))))}};function sn(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var un=class{constructor(e){this._worker=e}provideDocumentColors(e,t){const n=e.uri;return this._worker(n).then((e=>e.findDocumentColors(n.toString()))).then((e=>{if(e)return e.map((e=>({color:e.color,range:Wt(e.range)})))}))}provideColorPresentations(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.getColorPresentations(r.toString(),t.color,Kt(t.range)))).then((e=>{if(e)return e.map((e=>{let t={label:e.label};return e.textEdit&&(t.textEdit=Xt(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(Xt)),t}))}))}},cn=class{constructor(e){this._worker=e}provideFoldingRanges(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.getFoldingRanges(r.toString(),t))).then((e=>{if(e)return e.map((e=>{const t={start:e.startLine+1,end:e.endLine+1};return void 0!==e.kind&&(t.kind=function(e){switch(e){case A.Comment:return u.languages.FoldingRangeKind.Comment;case A.Imports:return u.languages.FoldingRangeKind.Imports;case A.Region:return u.languages.FoldingRangeKind.Region}return}(e.kind)),t}))}))}};var dn=class{constructor(e){this._worker=e}provideSelectionRanges(e,t,n){const r=e.uri;return this._worker(r).then((e=>e.getSelectionRanges(r.toString(),t.map(Ot)))).then((e=>{if(e)return e.map((e=>{const t=[];for(;e;)t.push({range:Wt(e.range)}),e=e.parent;return t}))}))}};function ln(e){const t=[],n=[],r=new jt(e);t.push(r);const i=(...e)=>r.getLanguageServiceWorker(...e);return function(){const{languageId:t,modeConfiguration:r}=e;fn(n),r.completionItems&&n.push(u.languages.registerCompletionItemProvider(t,new Vt(i,["/","-",":"]))),r.hovers&&n.push(u.languages.registerHoverProvider(t,new zt(i))),r.documentHighlights&&n.push(u.languages.registerDocumentHighlightProvider(t,new qt(i))),r.definitions&&n.push(u.languages.registerDefinitionProvider(t,new Gt(i))),r.references&&n.push(u.languages.registerReferenceProvider(t,new Yt(i))),r.documentSymbols&&n.push(u.languages.registerDocumentSymbolProvider(t,new en(i))),r.rename&&n.push(u.languages.registerRenameProvider(t,new Zt(i))),r.colors&&n.push(u.languages.registerColorProvider(t,new un(i))),r.foldingRanges&&n.push(u.languages.registerFoldingRangeProvider(t,new cn(i))),r.diagnostics&&n.push(new Nt(t,i,e.onDidChange)),r.selectionRanges&&n.push(u.languages.registerSelectionRangeProvider(t,new dn(i))),r.documentFormattingEdits&&n.push(u.languages.registerDocumentFormattingEditProvider(t,new on(i))),r.documentRangeFormattingEdits&&n.push(u.languages.registerDocumentRangeFormattingEditProvider(t,new an(i)))}(),t.push(gn(n)),gn(t)}function gn(e){return{dispose:()=>fn(e)}}function fn(e){for(;e.length;)e.pop().dispose()}export{Vt as CompletionAdapter,Gt as DefinitionAdapter,Nt as DiagnosticsAdapter,un as DocumentColorAdapter,on as DocumentFormattingEditProvider,qt as DocumentHighlightAdapter,rn as DocumentLinkAdapter,an as DocumentRangeFormattingEditProvider,en as DocumentSymbolAdapter,cn as FoldingRangeAdapter,zt as HoverAdapter,Yt as ReferenceAdapter,Zt as RenameAdapter,dn as SelectionRangeAdapter,jt as WorkerManager,Ot as fromPosition,Kt as fromRange,ln as setupMode,Wt as toRange,Xt as toTextEdit};
