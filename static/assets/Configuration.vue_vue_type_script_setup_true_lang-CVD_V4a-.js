import{d as e,dE as a,r as l,H as t,o,i as s,w as u,e as i,a as n,A as d,B as m,z as r,t as v,l as f}from"./index-DfJTpRkj.js";import{a as c,E as p}from"./el-form-DsaI0u2w.js";import{E as g,a as _}from"./el-col-B4Ik8fnS.js";import{E as x}from"./el-switch-C5ZBDFmL.js";import{E as b}from"./el-divider-0NmzbuNU.js";import{E as h}from"./el-text-vKNLRkxx.js";import{u as j}from"./index-KH6atv8j.js";import{j as w,o as y,T as V}from"./index-B-gHSwWD.js";const C=e({__name:"Configuration",props:{closeDialog:{type:Function},getList:{type:Function},nodeConfForm:{}},setup(e){const C=[w(),y],{t:E}=f(),F=e,{nodeConfForm:M}=a(F),z=l({...M.value}),k=l(!1),U=l(!1),A=l(!1);t((()=>{"1"===z.value.state?(A.value=!0,U.value=!1):"2"===z.value.state?(A.value=!1,U.value=!1):"3"===z.value.state&&(A.value=!1,U.value=!0)}));const D=l(),L=z.value.name;return(e,a)=>(o(),s(n(p),{model:z.value,"label-width":"auto","status-icon":"",ref_key:"ruleFormRef",ref:D},{default:u((()=>[i(n(c),{label:n(E)("node.nodeName"),prop:"name"},{default:u((()=>[i(n(d),{modelValue:z.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>z.value.name=e)},null,8,["modelValue"])])),_:1},8,["label"]),i(n(c),{label:"Module Config"},{default:u((()=>[i(n(V),{modelValue:z.value.ModulesConfig,"onUpdate:modelValue":a[1]||(a[1]=e=>z.value.ModulesConfig=e),extensions:C,autofocus:!0,"indent-with-tab":!0,"tab-size":2,style:{height:"550px",width:"100%"}},null,8,["modelValue"])])),_:1}),i(n(c),{label:n(E)("common.state")},{default:u((()=>[i(n(x),{modelValue:A.value,"onUpdate:modelValue":a[2]||(a[2]=e=>A.value=e),"inline-prompt":"","active-text":n(E)("common.switchAction"),"inactive-text":n(E)("common.switchInactive"),disabled:U.value},null,8,["modelValue","active-text","inactive-text","disabled"])])),_:1},8,["label"]),i(n(_),null,{default:u((()=>[i(n(g),{span:16,offset:8},{default:u((()=>[i(n(c),null,{default:u((()=>[i(n(m),{type:"primary",onClick:a[3]||(a[3]=e=>(async e=>{k.value=!0,e&&await e.validate((async(e,a)=>{if(e){let e;e=await j(L,z.value.name,z.value.ModulesConfig,A.value),200===e.code&&(F.getList(),F.closeDialog()),k.value=!1}else k.value=!1}))})(D.value)),loading:k.value},{default:u((()=>[r(v(n(E)("task.save")),1)])),_:1},8,["loading"]),i(n(b),{direction:"vertical"}),i(n(h),{size:"small",type:"danger"},{default:u((()=>[r(v(n(E)("configuration.threadMsg")),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"]))}});export{C as _};
