import{u as e,F as s}from"./useForm-RHT9mrs1.js";import{d as a,b as t,N as o,q as r,u as l,s as n,e as i,F as u,G as d,r as m,H as c,O as p,a as f,o as g,i as w,J as R,l as v}from"./index-C6fb_XFi.js";import{E as b}from"./el-checkbox-CvJzNe2E.js";import{l as h,g as y,a as x}from"./index-CZl4JiK3.js";import{u as I}from"./useValidator-B7S-lU_d.js";const P=a({__name:"LoginForm",emits:["to-register"],setup(a,{emit:P}){const{required:j}=I(),k=t(),D=o(),E=r(),{currentRoute:F,addRoute:q,push:A}=l(),{t:L}=v(),V={username:[j()],password:[j()]},_=n([{field:"title",colProps:{span:24},formItemProps:{slots:{default:()=>i("h2",{class:"text-2xl font-bold text-center w-[100%]"},[L("login.login")])}}},{field:"username",label:L("login.username"),component:"Input",colProps:{span:24}},{field:"password",label:L("login.password"),component:"InputPassword",colProps:{span:24},componentProps:{style:{width:"100%"}}},{field:"tool",colProps:{span:24},formItemProps:{slots:{default:()=>i(u,null,[i("div",{class:"flex justify-between items-center w-[100%]"},[i(b,{modelValue:M.value,"onUpdate:modelValue":e=>M.value=e,label:L("login.remember"),size:"small"},null)])])}}},{field:"login",colProps:{span:24},formItemProps:{slots:{default:()=>{let e;return i(u,null,[i("div",{class:"w-[100%]"},[i(d,{loading:C.value,type:"primary",class:"w-[100%]",onClick:H},(s=e=L("login.login"),"function"==typeof s||"[object Object]"===Object.prototype.toString.call(s)&&!R(s)?e:{default:()=>[e]}))])]);var s}}}}]),M=m(D.getRememberMe);c((()=>{(()=>{const e=D.getLoginInfo;if(e){const{username:s,password:a}=e;U({username:s,password:a})}})()}));const{formRegister:O,formMethods:S}=e(),{getFormData:z,getElFormExpose:N,setValues:U}=S,C=m(!1),G=m("");p((()=>F.value),(e=>{var s;G.value=null==(s=null==e?void 0:e.query)?void 0:s.redirect}),{immediate:!0});const H=async()=>{const e=await N();await(null==e?void 0:e.validate((async e=>{if(e){C.value=!0;const e=await z();try{const s=await h(e);s&&(f(M)?D.setLoginInfo({username:e.username,password:e.password}):D.setLoginInfo(void 0),D.setRememberMe(f(M)),D.setToken(s.data.access_token),D.setUsername(e.username),k.getDynamicRouter?J():(await E.generateRoutes("static").catch((()=>{})),E.getAddRouters.forEach((e=>{q(e)})),E.setIsAddRouters(!0),A({path:G.value||E.addRouters[0].path})))}finally{C.value=!1}}})))},J=async()=>{const e={roleName:(await z()).username},s=k.getDynamicRouter&&k.getServerDynamicRouter?await y(e):await x(e);if(s){const e=s.data||[];D.setRoleRouters(e),k.getDynamicRouter&&k.getServerDynamicRouter?await E.generateRoutes("server",e).catch((()=>{})):await E.generateRoutes("frontEnd",e).catch((()=>{})),E.getAddRouters.forEach((e=>{q(e)})),E.setIsAddRouters(!0),A({path:G.value||E.addRouters[0].path})}};return(e,a)=>(g(),w(f(s),{schema:_,rules:V,"label-position":"top","hide-required-asterisk":"",size:"large",class:"dark:border-1 dark:border-[var(--el-border-color)] dark:border-solid",onRegister:f(O)},null,8,["schema","onRegister"]))}});export{P as _};
