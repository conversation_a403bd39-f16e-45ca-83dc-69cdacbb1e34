import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,l as i,r as a,o as l,i as r,w as s,e as o,a as p}from"./index-DfJTpRkj.js";import{_ as m}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{a as d}from"./index-E-CbF_DZ.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tag-CbhrEnto.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";const j=t({__name:"TableVideoPreview",setup(t){const{t:j}=i(),n=[{field:"title",label:j("tableDemo.title")},{field:"video_uri",label:j("tableDemo.videoPreview")},{field:"author",label:j("tableDemo.author")},{field:"display_time",label:j("tableDemo.displayTime")},{field:"pageviews",label:j("tableDemo.pageviews")}],u=a(!0);let _=a([]);return(async e=>{const t=await d(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{u.value=!1}));t&&(_.value=t.data.list)})(),(t,i)=>(l(),r(p(e),{title:p(j)("router.PicturePreview")},{default:s((()=>[o(p(m),{columns:n,data:p(_),loading:u.value,preview:["image_uri","video_uri"]},null,8,["data","loading"])])),_:1},8,["title"]))}});export{j as default};
