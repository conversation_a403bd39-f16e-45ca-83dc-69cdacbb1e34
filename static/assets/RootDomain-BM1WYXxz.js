import{d as e,H as t,r as i,s as a,v as l,A as o,B as s,o as n,c as r,e as p,a as m,w as d,I as u,F as c,l as j,Y as g,_ as f}from"./index-3XfDPlIS.js";import{u as x}from"./useTable-BezX3TfM.js";import{E as h}from"./el-card-CuEws33_.js";import{E as _}from"./el-pagination-DwzzZyu4.js";import{E as v}from"./el-tag-DcMbxLLg.js";import"./el-select-DH55-Cab.js";import"./el-popper-DVoWBu_3.js";import{E as y,a as b}from"./el-col-CN1tVfqh.js";import{_ as S}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as w}from"./useCrudSchemas-6tFKup3N.js";import{a as C,d as E,o as k}from"./index-BAb9yQka.js";import z from"./Csearch-CpC9XwHn.js";import"./index-tjM0-mlU.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-table-column-B5hg3WH6.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./tree-BfZhwLPs.js";import"./index-Dz8ZrwBc.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import"./el-text-CLWE0mUm.js";import"./el-divider-D9UCOo44.js";import"./el-autocomplete-DpYoUHkX.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-switch-C-DLgt5X.js";import"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import"./useIcon-k-uSyz6l.js";import"./exportData.vue_vue_type_script_setup_true_lang--cnrLcU_.js";import"./el-tab-pane-xcqYouKU.js";import"./el-form-BY8piFS2.js";import"./el-radio-group-evFfsZkP.js";import"./el-space-BFgHxiAK.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import"./el-virtual-list-Drl4IGmp.js";import"./el-select-v2-CJw7ZO42.js";import"./index-lpN3i-fa.js";import"./index-BuRY9bVn.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-Cyn7ZzlS.js";import"./index-D4TGn7aE.js";const V=f(e({__name:"RootDomain",props:{projectList:{}},setup(e){const{t:f}=j(),V=[{keyword:"domain",example:'domain="example.com"',explain:f("searchHelp.domain")},{keyword:"icp",example:'icp="xxxxx"',explain:f("searchHelp.icp")},{keyword:"company",example:'company="xxxx"',explain:f("searchHelp.company")},{keyword:"project",example:'project="Hackerone"',explain:f("searchHelp.project")}];t((()=>{D(),window.addEventListener("resize",D)}));const H=i(0),D=()=>{const e=window.innerHeight||document.documentElement.clientHeight;H.value=.7*e},I=i(""),L=e=>{I.value=e,G()},A=a({}),R=a([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:f("tableDemo.index"),type:"index",minWidth:"30"},{field:"domain",label:f("rootDomain.rootDomainName"),minWidth:"200"},{field:"icp",label:"ICP",minWidth:"200"},{field:"company",label:f("rootDomain.company"),minWidth:"250"},{field:"project",label:f("project.project"),minWidth:"150"},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,a)=>{null==a&&(a=[]),A[e.id]||(A[e.id]={inputVisible:!1,inputValue:"",inputRef:i(null)});const n=A[e.id],r=async()=>{n.inputValue&&(a.push(n.inputValue),C(e.id,T,n.inputValue)),n.inputVisible=!1,n.inputValue=""};return l(b,{},(()=>[...a.map((t=>l(y,{span:24,key:t},(()=>[l("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||te("tags",t)})(e,t)},[l(v,{closable:!0,onClose:()=>(async t=>{const i=a.indexOf(t);i>-1&&a.splice(i,1),E(e.id,T,t)})(t)},(()=>t))])])))),l(y,{span:24},n.inputVisible?()=>l(o,{ref:n.inputRef,modelValue:n.inputValue,"onUpdate:modelValue":e=>n.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&r()},onBlur:r}):()=>l(s,{class:"button-new-tag",size:"small",onClick:()=>(n.inputVisible=!0,void g((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:f("asset.time"),minWidth:"200"}]);let T="RootDomain";R.forEach((e=>{e.hidden=e.hidden??!1}));let P=i(!1);const U=({field:e,hidden:t})=>{const i=R.findIndex((t=>t.field===e));-1!==i&&(R[i].hidden=t),(()=>{const e=R.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=P.value,localStorage.setItem(`columnConfig_${T}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${T}`)||"{}");R.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),P.value=e.statisticsHidden})();const{allSchemas:W}=w(R),{tableRegister:N,tableState:F,tableMethods:O}=x({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=F,i=await k(I.value,e.value,t.value,Y);return{list:i.data.list,total:i.data.total}},immediate:!1}),{loading:$,dataList:B,total:J,currentPage:K,pageSize:M}=F,{getList:G,getElTableExpose:q}=O;function Q(){return{background:"var(--el-fill-color-light)"}}const Y=a({}),Z=async e=>{Object.assign(Y,e),G()},X=(e,t)=>{Object.assign(Y,t),I.value=e,G()},ee=i([]),te=(e,t)=>{const i=`${e}=${t}`;ee.value=[...ee.value,i]},ie=e=>{if(ee.value){const[t,i]=e.split("=");t in Y&&Array.isArray(Y[t])&&(Y[t]=Y[t].filter((e=>e!==i)),0===Y[t].length&&delete Y[t]),ee.value=ee.value.filter((t=>t!==e))}},ae=()=>Y;return(e,t)=>(n(),r(c,null,[p(z,{getList:m(G),handleSearch:L,searchKeywordsData:V,index:m(T),getElTableExpose:m(q),projectList:e.$props.projectList,handleFilterSearch:X,crudSchemas:R,dynamicTags:ee.value,handleClose:ie,onUpdateColumnVisibility:U,searchResultCount:m(J),getFilter:ae},null,8,["getList","index","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),p(m(b),null,{default:d((()=>[p(m(y),null,{default:d((()=>[p(m(h),{style:{height:"min-content"}},{default:d((()=>[p(m(S),{pageSize:m(M),"onUpdate:pageSize":t[0]||(t[0]=e=>u(M)?M.value=e:null),currentPage:m(K),"onUpdate:currentPage":t[1]||(t[1]=e=>u(K)?K.value=e:null),columns:m(W).tableColumns,data:m(B),stripe:"",border:!0,loading:m($),resizable:!0,onRegister:m(N),onFilterChange:Z,headerCellStyle:Q,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","onRegister"])])),_:1})])),_:1}),p(m(y),{":span":24},{default:d((()=>[p(m(h),null,{default:d((()=>[p(m(_),{pageSize:m(M),"onUpdate:pageSize":t[2]||(t[2]=e=>u(M)?M.value=e:null),currentPage:m(K),"onUpdate:currentPage":t[3]||(t[3]=e=>u(K)?K.value=e:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:m(J)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-96ec85d4"]]);export{V as default};
