import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,l as a,e as i,r as l,o,i as r,w as s,a as m}from"./index-C6fb_XFi.js";import{_ as p}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{a as n}from"./index-Bh6CT4kq.js";import{E as d}from"./el-tag-C_oEQYGz.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";const j=t({__name:"TableImagePreview",setup(t){const{t:j}=a(),u=[{field:"title",label:j("tableDemo.title")},{field:"image_uri",label:j("tableDemo.preview")},{field:"author",label:j("tableDemo.author")},{field:"display_time",label:j("tableDemo.displayTime")},{field:"importance",label:j("tableDemo.importance"),formatter:(e,t,a)=>i(d,{type:1===a?"success":2===a?"warning":"danger"},{default:()=>[j(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")]})},{field:"pageviews",label:j("tableDemo.pageviews")}],c=l(!0);let b=l([]);return(async e=>{const t=await n(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{c.value=!1}));t&&(b.value=t.data.list)})(),(t,a)=>(o(),r(m(e),{title:m(j)("router.PicturePreview")},{default:s((()=>[i(m(p),{columns:u,data:m(b),loading:c.value,preview:["image_uri"]},null,8,["data","loading"])])),_:1},8,["title"]))}});export{j as default};
