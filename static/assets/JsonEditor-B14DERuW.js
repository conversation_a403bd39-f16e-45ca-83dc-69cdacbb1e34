import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{_ as t}from"./JsonEditor.vue_vue_type_script_setup_true_lang-BI1SzYeb.js";import{d as s,l as o,r as a,O as r,o as l,i,w as p,e as u,a as n}from"./index-DfJTpRkj.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";const _=s({__name:"JsonEditor",setup(s){const{t:_}=o(),m=a({title:"标题",content:"内容"});return r((()=>m.value),(e=>{}),{deep:!0}),setTimeout((()=>{m.value={title:"异步标题",content:"异步内容"}}),4e3),(s,o)=>(l(),i(n(e),{title:n(_)("richText.jsonEditor"),message:n(_)("richText.jsonEditorDes")},{default:p((()=>[u(n(t),{modelValue:m.value,"onUpdate:modelValue":o[0]||(o[0]=e=>m.value=e)},null,8,["modelValue"])])),_:1},8,["title","message"]))}});export{_ as default};
