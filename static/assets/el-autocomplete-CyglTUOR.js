import{Z as e,$ as a,aT as t,a1 as l,a4 as s,bs as o,a2 as u,bq as n,d as i,co as r,az as p,ba as d,a7 as v,r as c,aR as f,a8 as g,aG as m,H as b,o as h,i as y,w as $,f as x,n as w,a as S,aH as A,e as k,E as F,c as _,a9 as B,C as I,aP as C,F as E,R,z as K,t as T,A as W,aJ as H,ae as q,af as N,bQ as V,aa as j,a5 as z,bu as O,ah as P}from"./index-DfJTpRkj.js";import{u as U,E as L}from"./el-popper-D2BmgSQA.js";import{d as D}from"./debounce-CmAGCOy_.js";const G=e({valueKey:{type:String,default:"value"},modelValue:{type:[String,Number],default:""},debounce:{type:Number,default:300},placement:{type:a(String),values:["top","top-start","top-end","bottom","bottom-start","bottom-end"],default:"bottom-start"},fetchSuggestions:{type:a([Function,Array]),default:t},popperClass:{type:String,default:""},triggerOnFocus:{type:Boolean,default:!0},selectWhenUnmatched:{type:Boolean,default:!1},hideLoading:{type:Boolean,default:!1},label:{type:String},teleported:U.teleported,highlightFirstItem:{type:Boolean,default:!1},fitInputWidth:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},name:String}),J={[l]:e=>s(e),[o]:e=>s(e),[u]:e=>s(e),focus:e=>e instanceof FocusEvent,blur:e=>e instanceof FocusEvent,clear:()=>!0,select:e=>n(e)},M=["aria-expanded","aria-owns"],Q={key:0},Z=["id","aria-selected","onClick"],X="ElAutocomplete",Y=i({name:X,inheritAttrs:!1});const ee=P(j(i({...Y,props:G,emits:J,setup(e,{expose:a,emit:t}){const s=e,n=r(),i=p(),j=d(),P=v("autocomplete"),U=c(),G=c(),J=c(),Y=c();let ee=!1,ae=!1;const te=c([]),le=c(-1),se=c(""),oe=c(!1),ue=c(!1),ne=c(!1),ie=f(),re=g((()=>i.style)),pe=g((()=>(te.value.length>0||ne.value)&&oe.value)),de=g((()=>!s.hideLoading&&ne.value)),ve=g((()=>U.value?Array.from(U.value.$el.querySelectorAll("input")):[])),ce=()=>{pe.value&&(se.value=`${U.value.$el.offsetWidth}px`)},fe=()=>{le.value=-1},ge=D((async e=>{if(ue.value)return;const a=e=>{ne.value=!1,ue.value||(z(e)?(te.value=e,le.value=s.highlightFirstItem?0:-1):O(X,"autocomplete suggestions must be an array"))};if(ne.value=!0,z(s.fetchSuggestions))a(s.fetchSuggestions);else{const t=await s.fetchSuggestions(e,a);z(t)&&a(t)}}),s.debounce),me=e=>{const a=!!e;if(t(o,e),t(l,e),ue.value=!1,oe.value||(oe.value=a),!s.triggerOnFocus&&!e)return ue.value=!0,void(te.value=[]);ge(e)},be=e=>{var a;j.value||("INPUT"!==(null==(a=e.target)?void 0:a.tagName)||ve.value.includes(document.activeElement))&&(oe.value=!0)},he=e=>{t(u,e)},ye=e=>{ae?ae=!1:(oe.value=!0,t("focus",e),s.triggerOnFocus&&!ee&&ge(String(s.modelValue)))},$e=e=>{setTimeout((()=>{var a;(null==(a=J.value)?void 0:a.isFocusInsideContent())?ae=!0:(oe.value&&Ae(),t("blur",e))}))},xe=()=>{oe.value=!1,t(l,""),t("clear")},we=async()=>{pe.value&&le.value>=0&&le.value<te.value.length?ke(te.value[le.value]):s.selectWhenUnmatched&&(t("select",{value:s.modelValue}),te.value=[],le.value=-1)},Se=e=>{pe.value&&(e.preventDefault(),e.stopPropagation(),Ae())},Ae=()=>{oe.value=!1},ke=async e=>{t(o,e[s.valueKey]),t(l,e[s.valueKey]),t("select",e),te.value=[],le.value=-1},Fe=e=>{if(!pe.value||ne.value)return;if(e<0)return void(le.value=-1);e>=te.value.length&&(e=te.value.length-1);const a=G.value.querySelector(`.${P.be("suggestion","wrap")}`),t=a.querySelectorAll(`.${P.be("suggestion","list")} li`)[e],l=a.scrollTop,{offsetTop:s,scrollHeight:o}=t;s+o>l+a.clientHeight&&(a.scrollTop+=o),s<l&&(a.scrollTop-=o),le.value=e,U.value.ref.setAttribute("aria-activedescendant",`${ie.value}-item-${le.value}`)};return m(Y,(()=>{pe.value&&Ae()})),b((()=>{U.value.ref.setAttribute("role","textbox"),U.value.ref.setAttribute("aria-autocomplete","list"),U.value.ref.setAttribute("aria-controls","id"),U.value.ref.setAttribute("aria-activedescendant",`${ie.value}-item-${le.value}`),ee=U.value.ref.hasAttribute("readonly")})),a({highlightedIndex:le,activated:oe,loading:ne,inputRef:U,popperRef:J,suggestions:te,handleSelect:ke,handleKeyEnter:we,focus:()=>{var e;null==(e=U.value)||e.focus()},blur:()=>{var e;null==(e=U.value)||e.blur()},close:Ae,highlight:Fe}),(e,a)=>(h(),y(S(L),{ref_key:"popperRef",ref:J,visible:S(pe),placement:e.placement,"fallback-placements":["bottom-start","top-start"],"popper-class":[S(P).e("popper"),e.popperClass],teleported:e.teleported,"gpu-acceleration":!1,pure:"","manual-mode":"",effect:"light",trigger:"click",transition:`${S(P).namespace.value}-zoom-in-top`,persistent:"",role:"listbox",onBeforeShow:ce,onHide:fe},{content:$((()=>[x("div",{ref_key:"regionRef",ref:G,class:w([S(P).b("suggestion"),S(P).is("loading",S(de))]),style:A({[e.fitInputWidth?"width":"minWidth"]:se.value,outline:"none"}),role:"region"},[k(S(F),{id:S(ie),tag:"ul","wrap-class":S(P).be("suggestion","wrap"),"view-class":S(P).be("suggestion","list"),role:"listbox"},{default:$((()=>[S(de)?(h(),_("li",Q,[B(e.$slots,"loading",{},(()=>[k(S(I),{class:w(S(P).is("loading"))},{default:$((()=>[k(S(C))])),_:1},8,["class"])]))])):(h(!0),_(E,{key:1},R(te.value,((a,t)=>(h(),_("li",{id:`${S(ie)}-item-${t}`,key:t,class:w({highlighted:le.value===t}),role:"option","aria-selected":le.value===t,onClick:e=>ke(a)},[B(e.$slots,"default",{item:a},(()=>[K(T(a[e.valueKey]),1)]))],10,Z)))),128))])),_:3},8,["id","wrap-class","view-class"])],6)])),default:$((()=>[x("div",{ref_key:"listboxRef",ref:Y,class:w([S(P).b(),e.$attrs.class]),style:A(S(re)),role:"combobox","aria-haspopup":"listbox","aria-expanded":S(pe),"aria-owns":S(ie)},[k(S(W),H({ref_key:"inputRef",ref:U},S(n),{clearable:e.clearable,disabled:S(j),name:e.name,"model-value":e.modelValue,onInput:me,onChange:he,onFocus:ye,onBlur:$e,onClear:xe,onKeydown:[a[0]||(a[0]=q(N((e=>Fe(le.value-1)),["prevent"]),["up"])),a[1]||(a[1]=q(N((e=>Fe(le.value+1)),["prevent"]),["down"])),q(we,["enter"]),q(Ae,["tab"]),q(Se,["esc"])],onMousedown:be}),V({_:2},[e.$slots.prepend?{name:"prepend",fn:$((()=>[B(e.$slots,"prepend")]))}:void 0,e.$slots.append?{name:"append",fn:$((()=>[B(e.$slots,"append")]))}:void 0,e.$slots.prefix?{name:"prefix",fn:$((()=>[B(e.$slots,"prefix")]))}:void 0,e.$slots.suffix?{name:"suffix",fn:$((()=>[B(e.$slots,"suffix")]))}:void 0]),1040,["clearable","disabled","name","model-value","onKeydown"])],14,M)])),_:3},8,["visible","placement","popper-class","teleported","transition"]))}}),[["__file","autocomplete.vue"]]));export{ee as E};
