import{ab as e,a8 as l,bq as t,aQ as a,bK as o,bx as s,O as n,aa as i,d as r,a7 as u,aR as p,a as d,s as c,bv as v,x as f,Y as m,Q as b,ag as h,o as g,c as y,a9 as S,f as x,t as C,n as O,af as w,r as I,H as V,b1 as T,j as R,aH as E,aA as k,bc as B,aB as M,bb as D,a5 as L,b7 as F,ci as $,aF as z,aC as W,aX as _,aN as K,a3 as P,b9 as j,aS as H,cj as N,a1 as A,aK as G,aY as Q,a2 as U,a4 as q,Z as Y,ay as X,$ as Z,bm as J,ax as ee,D as le,A as te,E as ae,C as oe,a6 as se,y as ne,P as ie,e as re,w as ue,F as pe,R as de,i as ce,ae as ve,b2 as fe,aI as me,ck as be,ah as he,ai as ge}from"./index-3XfDPlIS.js";import{u as ye,a as Se,E as xe}from"./el-popper-DVoWBu_3.js";import{t as Ce,E as Oe}from"./el-tag-DcMbxLLg.js";import{e as we}from"./strings-Dm4Pnsdt.js";import{i as Ie,C as Ve}from"./index-tjM0-mlU.js";import{u as Te,f as Re}from"./useInput-SkgDzq11.js";import{d as Ee}from"./debounce-Cb7r1Afr.js";const ke=Symbol("ElSelectGroup"),Be=Symbol("ElSelect");const Me=r({name:"ElOption",componentName:"ElOption",props:{value:{required:!0,type:[String,Number,Boolean,Object]},label:[String,Number],created:Boolean,disabled:Boolean},setup(i){const r=u("select"),b=p(),h=l((()=>[r.be("dropdown","item"),r.is("disabled",d(x)),r.is("selected",d(S)),r.is("hovering",d(V))])),g=c({index:-1,groupDisabled:!1,visible:!0,hover:!1}),{currentLabel:y,itemSelected:S,isDisabled:x,select:C,hoverItem:O,updateOption:w}=function(i,r){const u=e(Be),p=e(ke,{disabled:!1}),d=l((()=>u.props.multiple?h(u.props.modelValue,i.value):h([u.props.modelValue],i.value))),c=l((()=>{if(u.props.multiple){const e=u.props.modelValue||[];return!d.value&&e.length>=u.props.multipleLimit&&u.props.multipleLimit>0}return!1})),v=l((()=>i.label||(t(i.value)?"":i.value))),f=l((()=>i.value||i.label||"")),m=l((()=>i.disabled||r.groupDisabled||c.value)),b=a(),h=(e=[],l)=>{if(t(i.value)){const t=u.props.valueKey;return e&&e.some((e=>o(s(e,t))===s(l,t)))}return e&&e.includes(l)};return n((()=>v.value),(()=>{i.created||u.props.remote||u.setSelected()})),n((()=>i.value),((e,l)=>{const{remote:a,valueKey:o}=u.props;if(Ie(e,l)||(u.onOptionDestroy(l,b.proxy),u.onOptionCreate(b.proxy)),!i.created&&!a){if(o&&t(e)&&t(l)&&e[o]===l[o])return;u.setSelected()}})),n((()=>p.disabled),(()=>{r.groupDisabled=p.disabled}),{immediate:!0}),{select:u,currentLabel:v,currentValue:f,itemSelected:d,isDisabled:m,hoverItem:()=>{i.disabled||p.disabled||(u.states.hoveringIndex=u.optionsArray.indexOf(b.proxy))},updateOption:e=>{const l=new RegExp(we(e),"i");r.visible=l.test(v.value)||i.created}}}(i,g),{visible:I,hover:V}=v(g),T=a().proxy;return C.onOptionCreate(T),f((()=>{const e=T.value,{selected:l}=C.states,t=(C.props.multiple?l:[l]).some((e=>e.value===T.value));m((()=>{C.states.cachedOptions.get(e)!==T||t||C.states.cachedOptions.delete(e)})),C.onOptionDestroy(e,T)})),{ns:r,id:b,containerKls:h,currentLabel:y,itemSelected:S,isDisabled:x,select:C,hoverItem:O,updateOption:w,visible:I,hover:V,selectOptionClick:function(){!0!==i.disabled&&!0!==g.groupDisabled&&C.handleOptionSelect(T)},states:g}}}),De=["id","aria-disabled","aria-selected"];var Le=i(Me,[["render",function(e,l,t,a,o,s){return b((g(),y("li",{id:e.id,class:O(e.containerKls),role:"option","aria-disabled":e.isDisabled||void 0,"aria-selected":e.itemSelected,onMouseenter:l[0]||(l[0]=(...l)=>e.hoverItem&&e.hoverItem(...l)),onClick:l[1]||(l[1]=w(((...l)=>e.selectOptionClick&&e.selectOptionClick(...l)),["stop"]))},[S(e.$slots,"default",{},(()=>[x("span",null,C(e.currentLabel),1)]))],42,De)),[[h,e.visible]])}],["__file","option.vue"]]);var Fe=i(r({name:"ElSelectDropdown",componentName:"ElSelectDropdown",setup(){const t=e(Be),a=u("select"),o=l((()=>t.props.popperClass)),s=l((()=>t.props.multiple)),n=l((()=>t.props.fitInputWidth)),i=I("");function r(){var e;i.value=`${null==(e=t.selectRef)?void 0:e.offsetWidth}px`}return V((()=>{r(),T(t.selectRef,r)})),{ns:a,minWidth:i,popperClass:o,isMultiple:s,isFitInputWidth:n}}}),[["render",function(e,l,t,a,o,s){return g(),y("div",{class:O([e.ns.b("dropdown"),e.ns.is("multiple",e.isMultiple),e.popperClass]),style:E({[e.isFitInputWidth?"width":"minWidth"]:e.minWidth})},[e.$slots.header?(g(),y("div",{key:0,class:O(e.ns.be("dropdown","header"))},[S(e.$slots,"header")],2)):R("v-if",!0),S(e.$slots,"default"),e.$slots.footer?(g(),y("div",{key:1,class:O(e.ns.be("dropdown","footer"))},[S(e.$slots,"footer")],2)):R("v-if",!0)],6)}],["__file","select-dropdown.vue"]]);const $e=(e,a)=>{const{t:i}=k(),r=p(),d=u("select"),v=u("input"),f=c({inputValue:"",options:new Map,cachedOptions:new Map,disabledOptions:new Map,optionValues:[],selected:e.multiple?[]:{},selectionWidth:0,calculatorWidth:0,collapseItemWidth:0,selectedLabel:"",hoveringIndex:-1,previousQuery:null,inputHovering:!1,menuVisibleOnFocus:!1,isBeforeHide:!1}),b=I(null),h=I(null),g=I(null),y=I(null),S=I(null),x=I(null),C=I(null),O=I(null),w=I(null),R=I(null),E=I(null),q=I(null),{wrapperRef:Y,isFocused:X,handleFocus:Z,handleBlur:J}=B(S,{afterFocus(){e.automaticDropdown&&!ee.value&&(ee.value=!0,f.menuVisibleOnFocus=!0)},beforeBlur(e){var l,t;return(null==(l=g.value)?void 0:l.isFocusInsideContent(e))||(null==(t=y.value)?void 0:t.isFocusInsideContent(e))},afterBlur(){ee.value=!1,f.menuVisibleOnFocus=!1}}),ee=I(!1),le=I(),{form:te,formItem:ae}=M(),{inputId:oe}=D(e,{formItemContext:ae}),se=l((()=>e.disabled||(null==te?void 0:te.disabled))),ne=l((()=>be.value.some((e=>""===e.value)))),ie=l((()=>e.multiple?L(e.modelValue)&&e.modelValue.length>0:!F(e.modelValue)&&(""!==e.modelValue||ne.value))),re=l((()=>e.clearable&&!se.value&&f.inputHovering&&ie.value)),ue=l((()=>e.remote&&e.filterable&&!e.remoteShowSuffix?"":e.suffixIcon)),pe=l((()=>d.is("reverse",ue.value&&ee.value))),de=l((()=>(null==ae?void 0:ae.validateState)||"")),ce=l((()=>$[de.value])),ve=l((()=>e.remote?300:0)),fe=l((()=>e.loading?e.loadingText||i("el.select.loading"):!(e.remote&&!f.inputValue&&0===f.options.size)&&(e.filterable&&f.inputValue&&f.options.size>0&&0===me.value?e.noMatchText||i("el.select.noMatch"):0===f.options.size?e.noDataText||i("el.select.noData"):null))),me=l((()=>be.value.filter((e=>e.visible)).length)),be=l((()=>{const e=Array.from(f.options.values()),l=[];return f.optionValues.forEach((t=>{const a=e.findIndex((e=>e.value===t));a>-1&&l.push(e[a])})),l.length>=e.length?l:e})),he=l((()=>Array.from(f.cachedOptions.values()))),ge=l((()=>{const l=be.value.filter((e=>!e.created)).some((e=>e.currentLabel===f.inputValue));return e.filterable&&e.allowCreate&&""!==f.inputValue&&!l})),ye=()=>{e.filterable&&H(e.filterMethod)||e.filterable&&e.remote&&H(e.remoteMethod)||be.value.forEach((e=>{var l;null==(l=e.updateOption)||l.call(e,f.inputValue)}))},Se=z(),xe=l((()=>["small"].includes(Se.value)?"small":"default")),Ce=l({get:()=>ee.value&&!1!==fe.value,set(e){ee.value=e}}),Oe=l((()=>L(e.modelValue)?0===e.modelValue.length&&!f.inputValue:!e.filterable||!f.inputValue)),we=l((()=>{var l;const t=null!=(l=e.placeholder)?l:i("el.select.placeholder");return e.multiple||!ie.value?t:f.selectedLabel}));n((()=>e.modelValue),((l,t)=>{e.multiple&&e.filterable&&!e.reserveKeyword&&(f.inputValue="",Ve("")),Be(),!Ie(l,t)&&e.validateEvent&&(null==ae||ae.validate("change").catch((e=>W())))}),{flush:"post",deep:!0}),n((()=>ee.value),(e=>{e?Ve(f.inputValue):(f.inputValue="",f.previousQuery=null,f.isBeforeHide=!0),a("visible-change",e)})),n((()=>f.options.entries()),(()=>{var l;if(!_)return;const t=(null==(l=b.value)?void 0:l.querySelectorAll("input"))||[];(e.filterable||e.defaultFirstOption||K(e.modelValue))&&Array.from(t).includes(document.activeElement)||Be(),e.defaultFirstOption&&(e.filterable||e.remote)&&me.value&&ke()}),{flush:"post"}),n((()=>f.hoveringIndex),(e=>{P(e)&&e>-1?le.value=be.value[e]||{}:le.value={},be.value.forEach((e=>{e.hover=le.value===e}))})),j((()=>{f.isBeforeHide||ye()}));const Ve=l=>{f.previousQuery!==l&&(f.previousQuery=l,e.filterable&&H(e.filterMethod)?e.filterMethod(l):e.filterable&&e.remote&&H(e.remoteMethod)&&e.remoteMethod(l),e.defaultFirstOption&&(e.filterable||e.remote)&&me.value?m(ke):m(De))},ke=()=>{const e=be.value.filter((e=>e.visible&&!e.disabled&&!e.states.groupDisabled)),l=e.find((e=>e.created)),t=e[0];f.hoveringIndex=He(be.value,l||t)},Be=()=>{if(!e.multiple){const l=Me(e.modelValue);return f.selectedLabel=l.currentLabel,void(f.selected=l)}f.selectedLabel="";const l=[];L(e.modelValue)&&e.modelValue.forEach((e=>{l.push(Me(e))})),f.selected=l},Me=l=>{let t;const a="object"===N(l).toLowerCase(),o="null"===N(l).toLowerCase(),n="undefined"===N(l).toLowerCase();for(let i=f.cachedOptions.size-1;i>=0;i--){const o=he.value[i];if(a?s(o.value,e.valueKey)===s(l,e.valueKey):o.value===l){t={value:l,currentLabel:o.currentLabel,isDisabled:o.isDisabled};break}}if(t)return t;return{value:l,currentLabel:a?l.label:o||n?"":l}},De=()=>{e.multiple?f.hoveringIndex=be.value.findIndex((e=>f.selected.some((l=>Ze(l)===Ze(e))))):f.hoveringIndex=be.value.findIndex((e=>Ze(e)===Ze(f.selected)))},Le=()=>{f.calculatorWidth=x.value.getBoundingClientRect().width},Fe=()=>{var e,l;null==(l=null==(e=g.value)?void 0:e.updatePopper)||l.call(e)},$e=()=>{var e,l;null==(l=null==(e=y.value)?void 0:e.updatePopper)||l.call(e)},ze=()=>{f.inputValue.length>0&&!ee.value&&(ee.value=!0),Ve(f.inputValue)},We=l=>{if(f.inputValue=l.target.value,!e.remote)return ze();_e()},_e=Ee((()=>{ze()}),ve.value),Ke=l=>{Ie(e.modelValue,l)||a(U,l)},Pe=l=>{l.stopPropagation();const t=e.multiple?[]:void 0;if(e.multiple)for(const e of f.selected)e.isDisabled&&t.push(e.value);a(A,t),Ke(t),f.hoveringIndex=-1,ee.value=!1,a("clear"),qe()},je=l=>{if(e.multiple){const t=(e.modelValue||[]).slice(),o=He(t,l.value);o>-1?t.splice(o,1):(e.multipleLimit<=0||t.length<e.multipleLimit)&&t.push(l.value),a(A,t),Ke(t),l.created&&Ve(""),e.filterable&&!e.reserveKeyword&&(f.inputValue="")}else a(A,l.value),Ke(l.value),ee.value=!1;qe(),ee.value||m((()=>{Ne(l)}))},He=(l=[],a)=>{if(!t(a))return l.indexOf(a);const n=e.valueKey;let i=-1;return l.some(((e,l)=>o(s(e,n))===s(a,n)&&(i=l,!0))),i},Ne=e=>{var l,t,a,o,s;const n=L(e)?e[0]:e;let i=null;if(null==n?void 0:n.value){const e=be.value.filter((e=>e.value===n.value));e.length>0&&(i=e[0].$el)}if(g.value&&i){const e=null==(o=null==(a=null==(t=null==(l=g.value)?void 0:l.popperRef)?void 0:t.contentRef)?void 0:a.querySelector)?void 0:o.call(a,`.${d.be("dropdown","wrap")}`);e&&Q(e,i)}null==(s=q.value)||s.handleScroll()},{handleCompositionStart:Ae,handleCompositionUpdate:Ge,handleCompositionEnd:Qe}=Te((e=>We(e))),Ue=l((()=>{var e,l;return null==(l=null==(e=g.value)?void 0:e.popperRef)?void 0:l.contentRef})),qe=()=>{var e;null==(e=S.value)||e.focus()},Ye=e=>{if(ee.value=!1,X.value){const l=new FocusEvent("focus",e);m((()=>J(l)))}},Xe=()=>{se.value||(f.menuVisibleOnFocus?f.menuVisibleOnFocus=!1:ee.value=!ee.value)},Ze=l=>t(l.value)?s(l.value,e.valueKey):l.value,Je=l((()=>be.value.filter((e=>e.visible)).every((e=>e.disabled)))),el=l((()=>e.multiple?e.collapseTags?f.selected.slice(0,e.maxCollapseTags):f.selected:[])),ll=l((()=>e.multiple&&e.collapseTags?f.selected.slice(e.maxCollapseTags):[])),tl=e=>{if(ee.value){if(0!==f.options.size&&0!==me.value&&!Je.value){"next"===e?(f.hoveringIndex++,f.hoveringIndex===f.options.size&&(f.hoveringIndex=0)):"prev"===e&&(f.hoveringIndex--,f.hoveringIndex<0&&(f.hoveringIndex=f.options.size-1));const l=be.value[f.hoveringIndex];!0!==l.disabled&&!0!==l.states.groupDisabled&&l.visible||tl(e),m((()=>Ne(le.value)))}}else ee.value=!0},al=l((()=>{const l=(()=>{if(!h.value)return 0;const e=window.getComputedStyle(h.value);return Number.parseFloat(e.gap||"6px")})();return{maxWidth:`${E.value&&1===e.maxCollapseTags?f.selectionWidth-f.collapseItemWidth-l:f.selectionWidth}px`}})),ol=l((()=>({maxWidth:`${f.selectionWidth}px`}))),sl=l((()=>({width:`${Math.max(f.calculatorWidth,11)}px`})));return e.multiple&&!L(e.modelValue)&&a(A,[]),!e.multiple&&L(e.modelValue)&&a(A,""),T(h,(()=>{f.selectionWidth=h.value.getBoundingClientRect().width})),T(x,Le),T(w,Fe),T(Y,Fe),T(R,$e),T(E,(()=>{f.collapseItemWidth=E.value.getBoundingClientRect().width})),V((()=>{Be()})),{inputId:oe,contentId:r,nsSelect:d,nsInput:v,states:f,isFocused:X,expanded:ee,optionsArray:be,hoverOption:le,selectSize:Se,filteredOptionsCount:me,resetCalculatorWidth:Le,updateTooltip:Fe,updateTagTooltip:$e,debouncedOnInputChange:_e,onInput:We,deletePrevTag:l=>{if(e.multiple&&l.code!==G.delete&&l.target.value.length<=0){const l=e.modelValue.slice(),t=(e=>Re(e,(e=>!f.disabledOptions.has(e))))(l);if(t<0)return;l.splice(t,1),a(A,l),Ke(l)}},deleteTag:(l,t)=>{const o=f.selected.indexOf(t);if(o>-1&&!se.value){const l=e.modelValue.slice();l.splice(o,1),a(A,l),Ke(l),a("remove-tag",t.value)}l.stopPropagation(),qe()},deleteSelected:Pe,handleOptionSelect:je,scrollToOption:Ne,hasModelValue:ie,shouldShowPlaceholder:Oe,currentPlaceholder:we,showClose:re,iconComponent:ue,iconReverse:pe,validateState:de,validateIcon:ce,showNewOption:ge,updateOptions:ye,collapseTagSize:xe,setSelected:Be,selectDisabled:se,emptyText:fe,handleCompositionStart:Ae,handleCompositionUpdate:Ge,handleCompositionEnd:Qe,onOptionCreate:e=>{f.options.set(e.value,e),f.cachedOptions.set(e.value,e),e.disabled&&f.disabledOptions.set(e.value,e)},onOptionDestroy:(e,l)=>{f.options.get(e)===l&&f.options.delete(e)},handleMenuEnter:()=>{m((()=>Ne(f.selected)))},handleFocus:Z,focus:qe,blur:()=>{Ye()},handleBlur:J,handleClearClick:e=>{Pe(e)},handleClickOutside:Ye,handleEsc:()=>{f.inputValue.length>0?f.inputValue="":ee.value=!1},toggleMenu:Xe,selectOption:()=>{ee.value?be.value[f.hoveringIndex]&&je(be.value[f.hoveringIndex]):Xe()},getValueKey:Ze,navigateOptions:tl,dropdownMenuVisible:Ce,showTagList:el,collapseTagList:ll,tagStyle:al,collapseTagStyle:ol,inputStyle:sl,popperRef:Ue,inputRef:S,tooltipRef:g,tagTooltipRef:y,calculatorRef:x,prefixRef:C,suffixRef:O,selectRef:b,wrapperRef:Y,selectionRef:h,scrollbarRef:q,menuRef:w,tagMenuRef:R,collapseItemRef:E}};var ze=r({name:"ElOptions",setup(l,{slots:t}){const a=e(Be);let o=[];return()=>{var e,l;const s=null==(e=t.default)?void 0:e.call(t),n=[];return s.length&&function e(l){L(l)&&l.forEach((l=>{var t,a,o,s;const i=null==(t=(null==l?void 0:l.type)||{})?void 0:t.name;"ElOptionGroup"===i?e(q(l.children)||L(l.children)||!H(null==(a=l.children)?void 0:a.default)?l.children:null==(o=l.children)?void 0:o.default()):"ElOption"===i?n.push(null==(s=l.props)?void 0:s.value):L(l.children)&&e(l.children)}))}(null==(l=s[0])?void 0:l.children),Ie(n,o)||(o=n,a&&(a.states.optionValues=n)),s}}});const We="ElSelect",_e=r({name:We,componentName:We,components:{ElInput:te,ElSelectMenu:Fe,ElOption:Le,ElOptions:ze,ElTag:Oe,ElScrollbar:ae,ElTooltip:xe,ElIcon:oe},directives:{ClickOutside:Ve},props:Y({name:String,id:String,modelValue:{type:[Array,String,Number,Boolean,Object],default:void 0},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:X,effect:{type:Z(String),default:"light"},disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:{type:String,default:""},popperOptions:{type:Z(Object),default:()=>({})},remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},teleported:ye.teleported,persistent:{type:Boolean,default:!0},clearIcon:{type:J,default:ee},fitInputWidth:Boolean,suffixIcon:{type:J,default:le},tagType:{...Ce.type,default:"info"},validateEvent:{type:Boolean,default:!0},remoteShowSuffix:Boolean,placement:{type:Z(String),values:Se,default:"bottom-start"},fallbackPlacements:{type:Z(Array),default:["bottom-start","top-start","right","left"]},ariaLabel:{type:String,default:void 0}}),emits:[A,U,"remove-tag","clear","visible-change","focus","blur"],setup(e,{emit:l}){const t=$e(e,l);return se(Be,c({props:e,states:t.states,optionsArray:t.optionsArray,handleOptionSelect:t.handleOptionSelect,onOptionCreate:t.onOptionCreate,onOptionDestroy:t.onOptionDestroy,selectRef:t.selectRef,setSelected:t.setSelected})),{...t}}}),Ke=["id","disabled","autocomplete","readonly","aria-activedescendant","aria-controls","aria-expanded","aria-label"],Pe=["textContent"];var je=i(_e,[["render",function(e,l,t,a,o,s){const n=ne("el-tag"),i=ne("el-tooltip"),r=ne("el-icon"),u=ne("el-option"),p=ne("el-options"),d=ne("el-scrollbar"),c=ne("el-select-menu"),v=ie("click-outside");return b((g(),y("div",{ref:"selectRef",class:O([e.nsSelect.b(),e.nsSelect.m(e.selectSize)]),onMouseenter:l[16]||(l[16]=l=>e.states.inputHovering=!0),onMouseleave:l[17]||(l[17]=l=>e.states.inputHovering=!1),onClick:l[18]||(l[18]=w(((...l)=>e.toggleMenu&&e.toggleMenu(...l)),["prevent","stop"]))},[re(i,{ref:"tooltipRef",visible:e.dropdownMenuVisible,placement:e.placement,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"popper-options":e.popperOptions,"fallback-placements":e.fallbackPlacements,effect:e.effect,pure:"",trigger:"click",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,persistent:e.persistent,onBeforeShow:e.handleMenuEnter,onHide:l[15]||(l[15]=l=>e.states.isBeforeHide=!1)},{default:ue((()=>{var t;return[x("div",{ref:"wrapperRef",class:O([e.nsSelect.e("wrapper"),e.nsSelect.is("focused",e.isFocused),e.nsSelect.is("hovering",e.states.inputHovering),e.nsSelect.is("filterable",e.filterable),e.nsSelect.is("disabled",e.selectDisabled)])},[e.$slots.prefix?(g(),y("div",{key:0,ref:"prefixRef",class:O(e.nsSelect.e("prefix"))},[S(e.$slots,"prefix")],2)):R("v-if",!0),x("div",{ref:"selectionRef",class:O([e.nsSelect.e("selection"),e.nsSelect.is("near",e.multiple&&!e.$slots.prefix&&!!e.states.selected.length)])},[e.multiple?S(e.$slots,"tag",{key:0},(()=>[(g(!0),y(pe,null,de(e.showTagList,(l=>(g(),y("div",{key:e.getValueKey(l),class:O(e.nsSelect.e("selected-item"))},[re(n,{closable:!e.selectDisabled&&!l.isDisabled,size:e.collapseTagSize,type:e.tagType,"disable-transitions":"",style:E(e.tagStyle),onClose:t=>e.deleteTag(t,l)},{default:ue((()=>[x("span",{class:O(e.nsSelect.e("tags-text"))},C(l.currentLabel),3)])),_:2},1032,["closable","size","type","style","onClose"])],2)))),128)),e.collapseTags&&e.states.selected.length>e.maxCollapseTags?(g(),ce(i,{key:0,ref:"tagTooltipRef",disabled:e.dropdownMenuVisible||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:ue((()=>[x("div",{ref:"collapseItemRef",class:O(e.nsSelect.e("selected-item"))},[re(n,{closable:!1,size:e.collapseTagSize,type:e.tagType,"disable-transitions":"",style:E(e.collapseTagStyle)},{default:ue((()=>[x("span",{class:O(e.nsSelect.e("tags-text"))}," + "+C(e.states.selected.length-e.maxCollapseTags),3)])),_:1},8,["size","type","style"])],2)])),content:ue((()=>[x("div",{ref:"tagMenuRef",class:O(e.nsSelect.e("selection"))},[(g(!0),y(pe,null,de(e.collapseTagList,(l=>(g(),y("div",{key:e.getValueKey(l),class:O(e.nsSelect.e("selected-item"))},[re(n,{class:"in-tooltip",closable:!e.selectDisabled&&!l.isDisabled,size:e.collapseTagSize,type:e.tagType,"disable-transitions":"",onClose:t=>e.deleteTag(t,l)},{default:ue((()=>[x("span",{class:O(e.nsSelect.e("tags-text"))},C(l.currentLabel),3)])),_:2},1032,["closable","size","type","onClose"])],2)))),128))],2)])),_:1},8,["disabled","effect","teleported"])):R("v-if",!0)])):R("v-if",!0),e.selectDisabled?R("v-if",!0):(g(),y("div",{key:1,class:O([e.nsSelect.e("selected-item"),e.nsSelect.e("input-wrapper"),e.nsSelect.is("hidden",!e.filterable)])},[b(x("input",{id:e.inputId,ref:"inputRef","onUpdate:modelValue":l[0]||(l[0]=l=>e.states.inputValue=l),type:"text",class:O([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,autocomplete:e.autocomplete,style:E(e.inputStyle),role:"combobox",readonly:!e.filterable,spellcheck:"false","aria-activedescendant":(null==(t=e.hoverOption)?void 0:t.id)||"","aria-controls":e.contentId,"aria-expanded":e.dropdownMenuVisible,"aria-label":e.ariaLabel,"aria-autocomplete":"none","aria-haspopup":"listbox",onFocus:l[1]||(l[1]=(...l)=>e.handleFocus&&e.handleFocus(...l)),onBlur:l[2]||(l[2]=(...l)=>e.handleBlur&&e.handleBlur(...l)),onKeydown:[l[3]||(l[3]=ve(w((l=>e.navigateOptions("next")),["stop","prevent"]),["down"])),l[4]||(l[4]=ve(w((l=>e.navigateOptions("prev")),["stop","prevent"]),["up"])),l[5]||(l[5]=ve(w(((...l)=>e.handleEsc&&e.handleEsc(...l)),["stop","prevent"]),["esc"])),l[6]||(l[6]=ve(w(((...l)=>e.selectOption&&e.selectOption(...l)),["stop","prevent"]),["enter"])),l[7]||(l[7]=ve(w(((...l)=>e.deletePrevTag&&e.deletePrevTag(...l)),["stop"]),["delete"]))],onCompositionstart:l[8]||(l[8]=(...l)=>e.handleCompositionStart&&e.handleCompositionStart(...l)),onCompositionupdate:l[9]||(l[9]=(...l)=>e.handleCompositionUpdate&&e.handleCompositionUpdate(...l)),onCompositionend:l[10]||(l[10]=(...l)=>e.handleCompositionEnd&&e.handleCompositionEnd(...l)),onInput:l[11]||(l[11]=(...l)=>e.onInput&&e.onInput(...l)),onClick:l[12]||(l[12]=w(((...l)=>e.toggleMenu&&e.toggleMenu(...l)),["stop"]))},null,46,Ke),[[fe,e.states.inputValue]]),e.filterable?(g(),y("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:O(e.nsSelect.e("input-calculator")),textContent:C(e.states.inputValue)},null,10,Pe)):R("v-if",!0)],2)),e.shouldShowPlaceholder?(g(),y("div",{key:2,class:O([e.nsSelect.e("selected-item"),e.nsSelect.e("placeholder"),e.nsSelect.is("transparent",!e.hasModelValue||e.expanded&&!e.states.inputValue)])},[x("span",null,C(e.currentPlaceholder),1)],2)):R("v-if",!0)],2),x("div",{ref:"suffixRef",class:O(e.nsSelect.e("suffix"))},[e.iconComponent&&!e.showClose?(g(),ce(r,{key:0,class:O([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.iconReverse])},{default:ue((()=>[(g(),ce(me(e.iconComponent)))])),_:1},8,["class"])):R("v-if",!0),e.showClose&&e.clearIcon?(g(),ce(r,{key:1,class:O([e.nsSelect.e("caret"),e.nsSelect.e("icon")]),onClick:e.handleClearClick},{default:ue((()=>[(g(),ce(me(e.clearIcon)))])),_:1},8,["class","onClick"])):R("v-if",!0),e.validateState&&e.validateIcon?(g(),ce(r,{key:2,class:O([e.nsInput.e("icon"),e.nsInput.e("validateIcon")])},{default:ue((()=>[(g(),ce(me(e.validateIcon)))])),_:1},8,["class"])):R("v-if",!0)],2)],2)]})),content:ue((()=>[re(c,{ref:"menuRef"},{default:ue((()=>[e.$slots.header?(g(),y("div",{key:0,class:O(e.nsSelect.be("dropdown","header")),onClick:l[13]||(l[13]=w((()=>{}),["stop"]))},[S(e.$slots,"header")],2)):R("v-if",!0),b(re(d,{id:e.contentId,ref:"scrollbarRef",tag:"ul","wrap-class":e.nsSelect.be("dropdown","wrap"),"view-class":e.nsSelect.be("dropdown","list"),class:O([e.nsSelect.is("empty",0===e.filteredOptionsCount)]),role:"listbox","aria-label":e.ariaLabel,"aria-orientation":"vertical"},{default:ue((()=>[e.showNewOption?(g(),ce(u,{key:0,value:e.states.inputValue,created:!0},null,8,["value"])):R("v-if",!0),re(p,null,{default:ue((()=>[S(e.$slots,"default")])),_:3})])),_:3},8,["id","wrap-class","view-class","class","aria-label"]),[[h,e.states.options.size>0&&!e.loading]]),e.$slots.loading&&e.loading?(g(),y("div",{key:1,class:O(e.nsSelect.be("dropdown","loading"))},[S(e.$slots,"loading")],2)):e.loading||0===e.filteredOptionsCount?(g(),y("div",{key:2,class:O(e.nsSelect.be("dropdown","empty"))},[S(e.$slots,"empty",{},(()=>[x("span",null,C(e.emptyText),1)]))],2)):R("v-if",!0),e.$slots.footer?(g(),y("div",{key:3,class:O(e.nsSelect.be("dropdown","footer")),onClick:l[14]||(l[14]=w((()=>{}),["stop"]))},[S(e.$slots,"footer")],2)):R("v-if",!0)])),_:3},512)])),_:3},8,["visible","placement","teleported","popper-class","popper-options","fallback-placements","effect","transition","persistent","onBeforeShow"])],34)),[[v,e.handleClickOutside,e.popperRef]])}],["__file","select.vue"]]);var He=i(r({name:"ElOptionGroup",componentName:"ElOptionGroup",props:{label:String,disabled:Boolean},setup(e){const t=u("select"),o=I(null),s=a(),n=I([]);se(ke,c({...v(e)}));const i=l((()=>n.value.some((e=>!0===e.visible)))),r=e=>{const l=[];return L(e.children)&&e.children.forEach((e=>{var t,a;e.type&&"ElOption"===e.type.name&&e.component&&e.component.proxy?l.push(e.component.proxy):(null==(t=e.children)?void 0:t.length)?l.push(...r(e)):(null==(a=e.component)?void 0:a.subTree)&&l.push(...r(e.component.subTree))})),l},p=()=>{n.value=r(s.subTree)};return V((()=>{p()})),be(o,p,{attributes:!0,subtree:!0,childList:!0}),{groupRef:o,visible:i,ns:t}}}),[["render",function(e,l,t,a,o,s){return b((g(),y("ul",{ref:"groupRef",class:O(e.ns.be("group","wrap"))},[x("li",{class:O(e.ns.be("group","title"))},C(e.label),3),x("li",null,[x("ul",{class:O(e.ns.b("group"))},[S(e.$slots,"default")],2)])],2)),[[h,e.visible]])}],["__file","option-group.vue"]]);const Ne=he(je,{Option:Le,OptionGroup:He}),Ae=ge(Le),Ge=ge(He);export{Ae as E,Ne as a,Ge as b,Be as s};
