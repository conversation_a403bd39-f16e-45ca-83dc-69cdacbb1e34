import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,l as a,e as i,r as l,o,i as r,w as s,a as m}from"./index-DfJTpRkj.js";import{_ as p}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{a as n}from"./index-E-CbF_DZ.js";import{E as d}from"./el-tag-CbhrEnto.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";const j=t({__name:"TableImagePreview",setup(t){const{t:j}=a(),u=[{field:"title",label:j("tableDemo.title")},{field:"image_uri",label:j("tableDemo.preview")},{field:"author",label:j("tableDemo.author")},{field:"display_time",label:j("tableDemo.displayTime")},{field:"importance",label:j("tableDemo.importance"),formatter:(e,t,a)=>i(d,{type:1===a?"success":2===a?"warning":"danger"},{default:()=>[j(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")]})},{field:"pageviews",label:j("tableDemo.pageviews")}],c=l(!0);let b=l([]);return(async e=>{const t=await n(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{c.value=!1}));t&&(b.value=t.data.list)})(),(t,a)=>(o(),r(m(e),{title:m(j)("router.PicturePreview")},{default:s((()=>[i(m(p),{columns:u,data:m(b),loading:c.value,preview:["image_uri"]},null,8,["data","loading"])])),_:1},8,["title"]))}});export{j as default};
