import{Y as e,dy as t,dK as a,d as l,bg as i,a6 as n,r as o,a7 as s,o as c,i as u,w as d,Q as m,f as r,n as p,a as f,C as v,aI as x,j as b,c as _,a8 as g,z as w,t as h,F as y,e as V,af as k,h as S,a9 as U,dL as I,ag as R,s as A,y as N,B as j,l as T,_ as E}from"./index-C6fb_XFi.js";import{E as M,a as B}from"./el-col-Dl4_4Pn5.js";import{a as C,E as D}from"./el-form-C2Y6uNCj.js";import{E as P}from"./el-switch-Bh7JeorW.js";import{E as $}from"./el-input-number-DVs4I2j5.js";import{E as F}from"./el-card-B37ahJ8o.js";import{k as L,l as z}from"./index-Bkh3VFwV.js";import"./castArray-DRqY4cIf.js";import"./index-CnCQNuY4.js";const H=e({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:t(a),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:["light","dark"],default:"light"}}),K={close:e=>e instanceof MouseEvent},O=l({name:"ElAlert"});const J=R(U(l({...O,props:H,emits:K,setup(e,{emit:t}){const l=e,{Close:U}=I,R=i(),A=n("alert"),N=o(!0),j=s((()=>a[l.type])),T=s((()=>[A.e("icon"),{[A.is("big")]:!!l.description||!!R.default}])),E=s((()=>({"with-description":l.description||R.default}))),M=e=>{N.value=!1,t("close",e)};return(e,t)=>(c(),u(S,{name:f(A).b("fade"),persisted:""},{default:d((()=>[m(r("div",{class:p([f(A).b(),f(A).m(e.type),f(A).is("center",e.center),f(A).is(e.effect)]),role:"alert"},[e.showIcon&&f(j)?(c(),u(f(v),{key:0,class:p(f(T))},{default:d((()=>[(c(),u(x(f(j))))])),_:1},8,["class"])):b("v-if",!0),r("div",{class:p(f(A).e("content"))},[e.title||e.$slots.title?(c(),_("span",{key:0,class:p([f(A).e("title"),f(E)])},[g(e.$slots,"title",{},(()=>[w(h(e.title),1)]))],2)):b("v-if",!0),e.$slots.default||e.description?(c(),_("p",{key:1,class:p(f(A).e("description"))},[g(e.$slots,"default",{},(()=>[w(h(e.description),1)]))],2)):b("v-if",!0),e.closable?(c(),_(y,{key:2},[e.closeText?(c(),_("div",{key:0,class:p([f(A).e("close-btn"),f(A).is("customed")]),onClick:M},h(e.closeText),3)):(c(),u(f(v),{key:1,class:p(f(A).e("close-btn")),onClick:M},{default:d((()=>[V(f(U))])),_:1},8,["class"]))],64)):b("v-if",!0)],2)],2),[[k,N.value]])])),_:3},8,["name"]))}}),[["__file","alert.vue"]])),Q={style:{"max-width":"600px",margin:"20px 0 0"}},Y=E(l({__name:"Deduplication",setup(e){const{t:t}=T(),a=A({asset:!1,subdomain:!0,SubdoaminTakerResult:!0,UrlScan:!0,crawler:!0,SensitiveResult:!0,DirScanResult:!0,vulnerability:!1,PageMonitoring:!0,hour:3,flag:!1,runNow:!1});(async()=>{const e=await z();Object.assign(a,e.data),l.value=e.data.next_run_time})();const l=o(""),i=o(!1);return(e,n)=>{const o=N("ElText");return c(),u(f(F),{shadow:"never",class:"mb-20px"},{header:d((()=>[V(f(B),null,{default:d((()=>[V(f(M),{span:3,style:{height:"100%"}},{default:d((()=>[r("span",null,h(f(t)("configuration.duplicationconfiguration")),1)])),_:1})])),_:1})])),default:d((()=>[r("div",Q,[V(f(J),{type:"info",closable:!1},{default:d((()=>[r("p",null,h(f(t)("task.nextTime"))+": "+h(l.value),1)])),_:1})]),V(f(D),{model:a,"label-width":"auto","status-icon":"",ref:"ruleFormRef",style:{position:"relative",top:"1rem"}},{default:d((()=>[V(f(B),null,{default:d((()=>[V(f(M),{span:3},{default:d((()=>[V(f(C),{label:f(t)("configuration.deduplicationFlag")},{default:d((()=>[V(f(P),{modelValue:a.flag,"onUpdate:modelValue":n[0]||(n[0]=e=>a.flag=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),a.flag?(c(),u(f(M),{key:0,span:3},{default:d((()=>[V(f(C),{label:f(t)("configuration.runNowOne")},{default:d((()=>[V(f(P),{modelValue:a.runNow,"onUpdate:modelValue":n[1]||(n[1]=e=>a.runNow=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})):b("",!0),a.flag?(c(),u(f(M),{key:1,span:5},{default:d((()=>[V(f(C),{label:f(t)("configuration.deduplicationHour"),prop:"type"},{default:d((()=>[V(f($),{modelValue:a.hour,"onUpdate:modelValue":n[2]||(n[2]=e=>a.hour=e),min:1,"controls-position":"right",size:"small"},null,8,["modelValue"]),V(o,{style:{position:"relative",left:"16px"}},{default:d((()=>[w("Hour")])),_:1})])),_:1},8,["label"])])),_:1})):b("",!0)])),_:1}),a.flag?(c(),u(f(B),{key:0},{default:d((()=>[V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("asset.assetName")},{default:d((()=>[V(f(P),{modelValue:a.asset,"onUpdate:modelValue":n[3]||(n[3]=e=>a.asset=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("subdomain.subdomainName")},{default:d((()=>[V(f(P),{modelValue:a.subdomain,"onUpdate:modelValue":n[4]||(n[4]=e=>a.subdomain=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("task.subdomainTakeover")},{default:d((()=>[V(f(P),{modelValue:a.SubdoaminTakerResult,"onUpdate:modelValue":n[5]||(n[5]=e=>a.SubdoaminTakerResult=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1})):b("",!0),a.flag?(c(),u(f(B),{key:1},{default:d((()=>[V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("URL.URLName")},{default:d((()=>[V(f(P),{modelValue:a.UrlScan,"onUpdate:modelValue":n[6]||(n[6]=e=>a.UrlScan=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("crawler.crawlerName")},{default:d((()=>[V(f(P),{modelValue:a.crawler,"onUpdate:modelValue":n[7]||(n[7]=e=>a.crawler=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("sensitiveInformation.sensitiveInformationName")},{default:d((()=>[V(f(P),{modelValue:a.SensitiveResult,"onUpdate:modelValue":n[8]||(n[8]=e=>a.SensitiveResult=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1})):b("",!0),a.flag?(c(),u(f(B),{key:2},{default:d((()=>[V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("dirScan.dirScanName")},{default:d((()=>[V(f(P),{modelValue:a.DirScanResult,"onUpdate:modelValue":n[9]||(n[9]=e=>a.DirScanResult=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("vulnerability.vulnerabilityName")},{default:d((()=>[V(f(P),{modelValue:a.vulnerability,"onUpdate:modelValue":n[10]||(n[10]=e=>a.vulnerability=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),V(f(M),{span:5},{default:d((()=>[V(f(C),{label:f(t)("PageMonitoring.pageMonitoringName")},{default:d((()=>[V(f(P),{modelValue:a.PageMonitoring,"onUpdate:modelValue":n[11]||(n[11]=e=>a.PageMonitoring=e),"inline-prompt":"","active-text":f(t)("common.switchAction"),"inactive-text":f(t)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1})):b("",!0),V(f(B),null,{default:d((()=>[V(f(M),{span:2,offset:8},{default:d((()=>[V(f(C),null,{default:d((()=>[V(f(j),{type:"primary",onClick:n[12]||(n[12]=e=>(async()=>{i.value=!0,await L(a.asset,a.subdomain,a.SubdoaminTakerResult,a.UrlScan,a.crawler,a.SensitiveResult,a.DirScanResult,a.vulnerability,a.PageMonitoring,a.hour,a.flag,a.runNow),i.value=!1})()),loading:i.value},{default:d((()=>[w(h(f(t)("common.submit")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})}}}),[["__scopeId","data-v-ef6d4310"]]);export{Y as default};
