import{d as t,r as e,y as s,o as a,c as l,e as r,w as o,f as n,a as c,W as u,X as i,F as p,R as d,z as m,t as h,p as x,m as b,_ as f}from"./index-C6fb_XFi.js";const g=t=>(x("data-v-7c4b7bd1"),t=t(),b(),t),v={class:"about-container p-6"},y={class:"flex items-center"},w=g((()=>n("span",null,"项目介绍",-1))),_=g((()=>n("div",{class:"project-info"},[n("h2",{class:"text-xl font-bold mb-4"},"## 网址"),n("ul",{class:"link-list space-y-3 list-disc pl-6"},[n("li",null,[m(" 官网&安装&文档： "),n("a",{href:"https://www.scope-sentry.top",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://www.scope-sentry.top ")]),n("li",null,[m(" Github： "),n("a",{href:"https://github.com/Autumn-27/ScopeSentry",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://github.com/Autumn-27/ScopeSentry ")]),n("li",null,[m(" 扫描端源码： "),n("a",{href:"https://github.com/Autumn-27/ScopeSentry-Scan",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://github.com/Autumn-27/ScopeSentry-Scan ")]),n("li",null,[m(" 插件市场： "),n("a",{href:"https://plugin.scope-sentry.top/",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://plugin.scope-sentry.top/ ")]),n("li",null,[m(" SecureFlow： "),n("a",{href:"https://plugin.scope-sentry.top/",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://secureflow.scope-sentry.top ")])])],-1))),S={class:"flex items-center"},k=g((()=>n("span",null,"赞助与合作",-1))),j={class:"sponsor-content p-4"},A=g((()=>n("h3",{class:"text-2xl font-bold mb-4 text-center"},"支持这个项目",-1))),F=g((()=>n("p",{class:"text-gray-600 mb-8 text-center max-w-2xl mx-auto"}," 如果你觉得这个项目对你有帮助，可以通过以下方式支持作者的持续开发和维护 ",-1))),z={class:"sponsor-options grid grid-cols-1 md:grid-cols-2 gap-8"},q=g((()=>n("h4",{class:"font-bold text-lg text-center"},"赞助",-1))),P={class:"flex flex-col items-center p-4"},B={class:"qr-codes flex flex-col md:flex-row gap-8 justify-center items-center"},C=["src","alt"],D={class:"text-gray-600 text-lg"},G=g((()=>n("div",{class:"mt-8 text-center"},[n("a",{href:"https://paypal.me/autumn5520",target:"_blank",class:"px-6 py-3 bg-[#0070BA] text-white rounded-lg hover:bg-[#005ea6] transition-colors duration-300"}," 通过 PayPal 赞助 ")],-1))),I=g((()=>n("h4",{class:"font-bold text-lg text-center"},"合作",-1))),L={class:"flex flex-col items-center p-4"},N=g((()=>n("p",{class:"text-gray-600 mb-6 text-center break-all"},"如果有想法可以一起合作",-1))),O=f(t({__name:"about",setup(t){const x=e([{name:"支付宝",img:"/assets/zfb--nwhu3QO.png"},{name:"微信",img:"/assets/wx-D1dLCFFN.jpg"}]);return(t,e)=>{const b=s("el-icon"),f=s("el-card"),g=s("el-button");return a(),l("div",v,[r(f,{class:"mb-6 transform hover:shadow-lg transition-all duration-300"},{header:o((()=>[n("div",y,[r(b,{class:"mr-2"},{default:o((()=>[r(c(u))])),_:1}),w])])),default:o((()=>[_])),_:1}),r(f,{class:"transform hover:shadow-lg transition-all duration-300"},{header:o((()=>[n("div",S,[r(b,{class:"mr-2"},{default:o((()=>[r(c(i))])),_:1}),k])])),default:o((()=>[n("div",j,[A,F,n("div",z,[r(f,{shadow:"hover",class:"sponsor-card"},{header:o((()=>[q])),default:o((()=>[n("div",P,[n("div",B,[(a(!0),l(p,null,d(x.value,(t=>(a(),l("div",{key:t.name,class:"qr-code-item flex flex-col items-center"},[n("img",{src:t.img,alt:`${t.name}收款码`,class:"w-64 h-64 object-cover mb-3 hover:scale-105 transition-transform duration-300"},null,8,C),n("span",D,h(t.name),1)])))),128))]),G])])),_:1}),r(f,{shadow:"hover",class:"sponsor-card"},{header:o((()=>[I])),default:o((()=>[n("div",L,[N,r(g,{type:"primary",size:"large",class:"w-full md:w-auto"},{default:o((()=>[m(" <EMAIL> ")])),_:1})])])),_:1})])])])),_:1})])}}}),[["__scopeId","data-v-7c4b7bd1"]]);export{O as default};
