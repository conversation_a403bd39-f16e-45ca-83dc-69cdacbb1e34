const r={common:{selectCategory:r=>{const{normalize:n}=r;return n(["分类选择"])},about:r=>{const{normalize:n}=r;return n(["关于"])},unprocessed:r=>{const{normalize:n}=r;return n(["未处理"])},processing:r=>{const{normalize:n}=r;return n(["处理中"])},ignored:r=>{const{normalize:n}=r;return n(["忽略"])},suspected:r=>{const{normalize:n}=r;return n(["疑似"])},confirmed:r=>{const{normalize:n}=r;return n(["确认"])},cleanLog:r=>{const{normalize:n}=r;return n(["清除日志"])},log:r=>{const{normalize:n}=r;return n(["日志"])},filesize:r=>{const{normalize:n}=r;return n(["大小"])},quantity:r=>{const{normalize:n}=r;return n(["数量"])},import:r=>{const{normalize:n}=r;return n(["导入"])},category:r=>{const{normalize:n}=r;return n(["分类"])},uploadMsg:r=>{const{normalize:n}=r;return n(["批量导入，只支持nuclei，将yaml文件放入zip中上传，可直接将nuclei官方poc库下载上传会自动提取poc。"])},upload:r=>{const{normalize:n}=r;return n(["上传"])},cover:r=>{const{normalize:n}=r;return n(["覆盖"])},download:r=>{const{normalize:n}=r;return n(["下载"])},operation:r=>{const{normalize:n}=r;return n(["操作"])},selectAll:r=>{const{normalize:n}=r;return n(["全选"])},multipleSelection:r=>{const{normalize:n}=r;return n(["多选"])},name:r=>{const{normalize:n}=r;return n(["名称"])},cversion:r=>{const{normalize:n}=r;return n(["当前版本"])},lversion:r=>{const{normalize:n}=r;return n(["最新版本"])},updateButtonMsg:r=>{const{normalize:n}=r;return n(["目前只支持升级Server"])},switchAction:r=>{const{normalize:n}=r;return n(["开"])},switchInactive:r=>{const{normalize:n}=r;return n(["关"])},inputText:r=>{const{normalize:n}=r;return n(["请输入"])},selectText:r=>{const{normalize:n}=r;return n(["请选择"])},startTimeText:r=>{const{normalize:n}=r;return n(["开始时间"])},endTimeText:r=>{const{normalize:n}=r;return n(["结束时间"])},changePassword:r=>{const{normalize:n}=r;return n(["修改密码"])},submit:r=>{const{normalize:n}=r;return n(["提交"])},updatemsg:r=>{const{normalize:n}=r;return n(["发现新版本"])},update:r=>{const{normalize:n}=r;return n(["一键更新"])},querysyntax:r=>{const{normalize:n}=r;return n(["查询语法"])},true:r=>{const{normalize:n}=r;return n(["是"])},statusStop:r=>{const{normalize:n}=r;return n(["暂停"])},false:r=>{const{normalize:n}=r;return n(["否"])},version:r=>{const{normalize:n}=r;return n(["系统版本信息"])},newPassword:r=>{const{normalize:n}=r;return n(["新密码"])},login:r=>{const{normalize:n}=r;return n(["登录"])},edit:r=>{const{normalize:n}=r;return n(["编辑"])},delete:r=>{const{normalize:n}=r;return n(["删除"])},view:r=>{const{normalize:n}=r;return n(["查看"])},new:r=>{const{normalize:n}=r;return n(["新建"])},state:r=>{const{normalize:n}=r;return n(["状态"])},config:r=>{const{normalize:n}=r;return n(["设置"])},on:r=>{const{normalize:n}=r;return n(["开启"])},off:r=>{const{normalize:n}=r;return n(["关闭"])},save:r=>{const{normalize:n}=r;return n(["保存"])},required:r=>{const{normalize:n}=r;return n(["该项为必填项"])},loginOut:r=>{const{normalize:n}=r;return n(["退出系统"])},document:r=>{const{normalize:n}=r;return n(["项目文档"])},reminder:r=>{const{normalize:n}=r;return n(["温馨提示"])},loginOutMessage:r=>{const{normalize:n}=r;return n(["是否退出本系统？"])},back:r=>{const{normalize:n}=r;return n(["返回"])},ok:r=>{const{normalize:n}=r;return n(["确定"])},cancel:r=>{const{normalize:n}=r;return n(["取消"])},reload:r=>{const{normalize:n}=r;return n(["重新加载"])},closeTab:r=>{const{normalize:n}=r;return n(["关闭标签页"])},closeTheLeftTab:r=>{const{normalize:n}=r;return n(["关闭左侧标签页"])},closeTheRightTab:r=>{const{normalize:n}=r;return n(["关闭右侧标签页"])},closeOther:r=>{const{normalize:n}=r;return n(["关闭其它标签页"])},closeAll:r=>{const{normalize:n}=r;return n(["关闭全部标签页"])},prevLabel:r=>{const{normalize:n}=r;return n(["上一步"])},nextLabel:r=>{const{normalize:n}=r;return n(["下一步"])},skipLabel:r=>{const{normalize:n}=r;return n(["跳过"])},doneLabel:r=>{const{normalize:n}=r;return n(["结束"])},menu:r=>{const{normalize:n}=r;return n(["菜单"])},menuDes:r=>{const{normalize:n}=r;return n(["以路由的结构渲染的菜单栏"])},collapse:r=>{const{normalize:n}=r;return n(["展开缩收"])},collapseDes:r=>{const{normalize:n}=r;return n(["展开和缩放菜单栏"])},tagsView:r=>{const{normalize:n}=r;return n(["标签页"])},tagsViewDes:r=>{const{normalize:n}=r;return n(["用于记录路由历史记录"])},tool:r=>{const{normalize:n}=r;return n(["工具"])},toolDes:r=>{const{normalize:n}=r;return n(["用于设置定制系统"])},query:r=>{const{normalize:n}=r;return n(["查询"])},reset:r=>{const{normalize:n}=r;return n(["重置"])},shrink:r=>{const{normalize:n}=r;return n(["收起"])},expand:r=>{const{normalize:n}=r;return n(["展开"])},delMessage:r=>{const{normalize:n}=r;return n(["是否删除所选中数据？"])},delWarning:r=>{const{normalize:n}=r;return n(["提示"])},delOk:r=>{const{normalize:n}=r;return n(["确定"])},delCancel:r=>{const{normalize:n}=r;return n(["取消"])},delNoData:r=>{const{normalize:n}=r;return n(["请选择需要删除的数据"])},delSuccess:r=>{const{normalize:n}=r;return n(["删除成功"])},refresh:r=>{const{normalize:n}=r;return n(["刷新"])},fullscreen:r=>{const{normalize:n}=r;return n(["全屏"])},size:r=>{const{normalize:n}=r;return n(["尺寸"])},columnSetting:r=>{const{normalize:n}=r;return n(["列设置"])},lengthRange:r=>{const{normalize:n,interpolate:e,named:o}=r;return n(["长度在 ",e(o("min"))," 到 ",e(o("max"))," 个字符"])},notSpace:r=>{const{normalize:n}=r;return n(["不能包含空格"])},notSpecialCharacters:r=>{const{normalize:n}=r;return n(["不能包含特殊字符"])},isEqual:r=>{const{normalize:n}=r;return n(["两次输入不一致"])}},export:{exportType:r=>{const{normalize:n}=r;return n(["导出类型"])},exportTypeAll:r=>{const{normalize:n}=r;return n(["所有数据"])},exportTypeSearch:r=>{const{normalize:n}=r;return n(["当前搜索条件"])},exportQuantity:r=>{const{normalize:n}=r;return n(["导出数据量"])},exportRecords:r=>{const{normalize:n}=r;return n(["导出记录"])},fileName:r=>{const{normalize:n}=r;return n(["文件名"])},createTime:r=>{const{normalize:n}=r;return n(["创建时间"])},endTime:r=>{const{normalize:n}=r;return n(["完成时间"])},fileSize:r=>{const{normalize:n}=r;return n(["文件大小"])},state:r=>{const{normalize:n}=r;return n(["状态"])},run:r=>{const{normalize:n}=r;return n(["进行中"])},success:r=>{const{normalize:n}=r;return n(["成功"])},fail:r=>{const{normalize:n}=r;return n(["失败"])},type:r=>{const{normalize:n}=r;return n(["类型"])},download:r=>{const{normalize:n}=r;return n(["下载"])},field:r=>{const{normalize:n}=r;return n(["字段"])},fileType:r=>{const{normalize:n}=r;return n(["文件类型"])}},searchHelp:{notice:r=>{const{normalize:n}=r;return n(["关键字需要用双引号包裹"])},operator:r=>{const{normalize:n}=r;return n(["运算符"])},meaning:r=>{const{normalize:n}=r;return n(["含义"])},equal:r=>{const{normalize:n}=r;return n(["精准匹配，表示仅查询关键词资产。"])},notIn:r=>{const{normalize:n}=r;return n(["剔除，表示剔除包含关键词资产。"])},like:r=>{const{normalize:n}=r;return n(["匹配，表示查询包含关键词资产（支持正则输入）。"])},brackets:r=>{const{normalize:n}=r;return n(["括号内容优先级最高"])},and:r=>{const{normalize:n}=r;return n(["与条件"])},or:r=>{const{normalize:n}=r;return n(["或条件"])},keywords:r=>{const{normalize:n}=r;return n(["关键字"])},explain:r=>{const{normalize:n}=r;return n(["说明"])},example:r=>{const{normalize:n}=r;return n(["示例"])},app:r=>{const{normalize:n}=r;return n(["检索指定组件"])},body:r=>{const{normalize:n}=r;return n(["检索HTTP响应体"])},header:r=>{const{normalize:n}=r;return n(["检索HTTP请求头"])},title:r=>{const{normalize:n}=r;return n(["检测网站标题"])},statuscode:r=>{const{normalize:n}=r;return n(["检索响应码，不支持模糊查找"])},icon:r=>{const{normalize:n}=r;return n(["根据网站图标hash检索"])},ip:r=>{const{normalize:n}=r;return n(["检索IP"])},port:r=>{const{normalize:n}=r;return n(["检索端口"])},domain:r=>{const{normalize:n}=r;return n(["检索域名"])},protocol:r=>{const{normalize:n}=r;return n(["根据服务检索"])},banner:r=>{const{normalize:n}=r;return n(["检索非HTTP资产的Banner"])},project:r=>{const{normalize:n}=r;return n(["根据项目名称检索"])},length:r=>{const{normalize:n}=r;return n(["根据响应长度检索，不支持模糊查找"])},subdomainType:r=>{const{normalize:n}=r;return n(["检索记录类型"])},subdoaminValue:r=>{const{normalize:n}=r;return n(["检索记录值"])},url:r=>{const{normalize:n}=r;return n(["检索URL"])},inpur:r=>{const{normalize:n}=r;return n(["检索输入源"])},source:r=>{const{normalize:n}=r;return n(["检索URL来源"])},urlType:r=>{const{normalize:n}=r;return n(["检索URL类型"])},method:r=>{const{normalize:n}=r;return n(["检索Method"])},crawlerBody:r=>{const{normalize:n}=r;return n(["检索POST数据"])},sname:r=>{const{normalize:n}=r;return n(["检索敏感信息名称"])},sinfo:r=>{const{normalize:n}=r;return n(["检索敏感信息"])},redirect:r=>{const{normalize:n}=r;return n(["检索跳转链接"])},vulname:r=>{const{normalize:n}=r;return n(["检索漏洞名称"])},matched:r=>{const{normalize:n}=r;return n(["检索匹配内容"])},vulRequest:r=>{const{normalize:n}=r;return n(["检索请求内容"])},response:r=>{const{normalize:n}=r;return n(["检索响应内容"])},hash:r=>{const{normalize:n}=r;return n(["检索响应hash"])},diff:r=>{const{normalize:n}=r;return n(["检索diff内容"])},level:r=>{const{normalize:n}=r;return n(["检索漏洞等级(info、high、medium、critical、low、unknown)"])},sensMd5:r=>{const{normalize:n}=r;return n(["根据响应体MD5检索"])},sensLevel:r=>{const{normalize:n}=r;return n(["根据敏感信息等级搜索（red、green、cyan、yellow、orange、gray、pink）"])},taskName:r=>{const{normalize:n}=r;return n(["根据任务名称检索，仅支持精确查找"])},icp:r=>{const{normalize:n}=r;return n(["检索ICP"])},company:r=>{const{normalize:n}=r;return n(["检索公司名称"])},name:r=>{const{normalize:n}=r;return n(["检索名称"])}},lock:{lockScreen:r=>{const{normalize:n}=r;return n(["锁定屏幕"])},lock:r=>{const{normalize:n}=r;return n(["锁定"])},lockPassword:r=>{const{normalize:n}=r;return n(["锁屏密码"])},unlock:r=>{const{normalize:n}=r;return n(["点击解锁"])},backToLogin:r=>{const{normalize:n}=r;return n(["返回登录"])},entrySystem:r=>{const{normalize:n}=r;return n(["进入系统"])},placeholder:r=>{const{normalize:n}=r;return n(["请输入锁屏密码"])},message:r=>{const{normalize:n}=r;return n(["锁屏密码错误"])}},error:{noPermission:r=>{const{normalize:n}=r;return n(["抱歉，您无权访问此页面。"])},pageError:r=>{const{normalize:n}=r;return n(["抱歉，您访问的页面不存在。"])},networkError:r=>{const{normalize:n}=r;return n(["抱歉，服务器报告错误。"])},returnToHome:r=>{const{normalize:n}=r;return n(["返回首页"])}},setting:{projectSetting:r=>{const{normalize:n}=r;return n(["项目配置"])},theme:r=>{const{normalize:n}=r;return n(["主题"])},layout:r=>{const{normalize:n}=r;return n(["布局"])},systemTheme:r=>{const{normalize:n}=r;return n(["系统主题"])},menuTheme:r=>{const{normalize:n}=r;return n(["菜单主题"])},interfaceDisplay:r=>{const{normalize:n}=r;return n(["界面显示"])},breadcrumb:r=>{const{normalize:n}=r;return n(["面包屑"])},breadcrumbIcon:r=>{const{normalize:n}=r;return n(["面包屑图标"])},collapseMenu:r=>{const{normalize:n}=r;return n(["折叠菜单"])},hamburgerIcon:r=>{const{normalize:n}=r;return n(["折叠图标"])},screenfullIcon:r=>{const{normalize:n}=r;return n(["全屏图标"])},sizeIcon:r=>{const{normalize:n}=r;return n(["尺寸图标"])},localeIcon:r=>{const{normalize:n}=r;return n(["多语言图标"])},tagsView:r=>{const{normalize:n}=r;return n(["标签页"])},logo:r=>{const{normalize:n}=r;return n(["标志"])},greyMode:r=>{const{normalize:n}=r;return n(["灰色模式"])},fixedHeader:r=>{const{normalize:n}=r;return n(["固定头部"])},headerTheme:r=>{const{normalize:n}=r;return n(["头部主题"])},cutMenu:r=>{const{normalize:n}=r;return n(["切割菜单"])},copy:r=>{const{normalize:n}=r;return n(["拷贝"])},clearAndReset:r=>{const{normalize:n}=r;return n(["清除缓存并且重置"])},copySuccess:r=>{const{normalize:n}=r;return n(["拷贝成功"])},copyFailed:r=>{const{normalize:n}=r;return n(["拷贝失败"])},footer:r=>{const{normalize:n}=r;return n(["页脚"])},uniqueOpened:r=>{const{normalize:n}=r;return n(["菜单手风琴"])},tagsViewIcon:r=>{const{normalize:n}=r;return n(["标签页图标"])},dynamicRouter:r=>{const{normalize:n}=r;return n(["开启动态路由"])},serverDynamicRouter:r=>{const{normalize:n}=r;return n(["服务端动态路由"])},reExperienced:r=>{const{normalize:n}=r;return n(["请重新退出登录体验"])},fixedMenu:r=>{const{normalize:n}=r;return n(["固定菜单"])}},size:{default:r=>{const{normalize:n}=r;return n(["默认"])},large:r=>{const{normalize:n}=r;return n(["大"])},small:r=>{const{normalize:n}=r;return n(["小"])}},login:{welcome:r=>{const{normalize:n}=r;return n(["欢迎使用本系统"])},message:r=>{const{normalize:n}=r;return n(["开箱即用的中后台管理系统"])},username:r=>{const{normalize:n}=r;return n(["用户名"])},password:r=>{const{normalize:n}=r;return n(["密码"])},register:r=>{const{normalize:n}=r;return n(["注册"])},checkPassword:r=>{const{normalize:n}=r;return n(["确认密码"])},login:r=>{const{normalize:n}=r;return n(["登录"])},otherLogin:r=>{const{normalize:n}=r;return n(["其它登录方式"])},remember:r=>{const{normalize:n}=r;return n(["记住我"])},hasUser:r=>{const{normalize:n}=r;return n(["已有账号？去登录"])},forgetPassword:r=>{const{normalize:n}=r;return n(["忘记密码"])},usernamePlaceholder:r=>{const{normalize:n}=r;return n(["请输入用户名"])},passwordPlaceholder:r=>{const{normalize:n}=r;return n(["请输入密码"])},code:r=>{const{normalize:n}=r;return n(["验证码"])},codePlaceholder:r=>{const{normalize:n}=r;return n(["请输入验证码"])}},router:{assetinfo:r=>{const{normalize:n}=r;return n(["资产信息"])},taskManagement:r=>{const{normalize:n}=r;return n(["任务管理"])},scanTask:r=>{const{normalize:n}=r;return n(["扫描任务"])},scheduledTask:r=>{const{normalize:n}=r;return n(["计划任务"])},scanTemplate:r=>{const{normalize:n}=r;return n(["扫描模板"])},nodeManagement:r=>{const{normalize:n}=r;return n(["节点管理"])},projectManagement:r=>{const{normalize:n}=r;return n(["项目管理"])},projectDetail:r=>{const{normalize:n}=r;return n(["项目信息"])},vulFingerprint:r=>{const{normalize:n}=r;return n(["漏洞-指纹"])},pocManagement:r=>{const{normalize:n}=r;return n(["POC管理"])},fingerprintManagement:r=>{const{normalize:n}=r;return n(["指纹管理"])},sensitiveInformationRules:r=>{const{normalize:n}=r;return n(["敏感信息规则"])},dictionaryManagement:r=>{const{normalize:n}=r;return n(["字典管理"])},subdomainDictionary:r=>{const{normalize:n}=r;return n(["子域名字典"])},dirDictionary:r=>{const{normalize:n}=r;return n(["目录扫描"])},portDictionary:r=>{const{normalize:n}=r;return n(["端口"])},configuration:r=>{const{normalize:n}=r;return n(["配置"])},subfinder:r=>{const{normalize:n}=r;return n(["subfinder配置"])},rad:r=>{const{normalize:n}=r;return n(["rad配置"])},system:r=>{const{normalize:n}=r;return n(["系统配置"])},login:r=>{const{normalize:n}=r;return n(["登录"])},pluginsManager:r=>{const{normalize:n}=r;return n(["插件管理"])},dashboard:r=>{const{normalize:n}=r;return n(["首页"])},document:r=>{const{normalize:n}=r;return n(["文档"])}},permission:{hasPermission:r=>{const{normalize:n}=r;return n(["请设置操作权限值"])}},dashboard:{totalAssets:r=>{const{normalize:n}=r;return n(["资产总数"])},subDomain:r=>{const{normalize:n}=r;return n(["子域名"])},informationLeakage:r=>{const{normalize:n}=r;return n(["信息泄露"])},URL:r=>{const{normalize:n}=r;return n(["URL"])},nodeInfo:r=>{const{normalize:n}=r;return n(["节点状态信息"])},taskInfo:r=>{const{normalize:n}=r;return n(["任务信息"])}},node:{nodeName:r=>{const{normalize:n}=r;return n(["节点名称"])},nodeStatus:r=>{const{normalize:n}=r;return n(["节点状态"])},taskCount:r=>{const{normalize:n}=r;return n(["任务数量"])},finished:r=>{const{normalize:n}=r;return n(["完成数量"])},statusRun:r=>{const{normalize:n}=r;return n(["运行中"])},statusStop:r=>{const{normalize:n}=r;return n(["暂停"])},statusError:r=>{const{normalize:n}=r;return n(["未连接"])},nodeUsageStatus:r=>{const{normalize:n}=r;return n(["节点使用状态"])},nodeUsageCpu:r=>{const{normalize:n}=r;return n(["CPU"])},nodeUsageMemory:r=>{const{normalize:n}=r;return n(["内存"])},nodeUsageLoad:r=>{const{normalize:n}=r;return n(["负载"])},createTime:r=>{const{normalize:n}=r;return n(["创建时间"])},updateTime:r=>{const{normalize:n}=r;return n(["更新时间"])},log:r=>{const{normalize:n}=r;return n(["日志"])},onlineNodeMsg:r=>{const{normalize:n}=r;return n(["没有连接中的扫描节点"])},plugin:r=>{const{normalize:n}=r;return n(["插件"])},restart:r=>{const{normalize:n}=r;return n(["重启"])},restartMsg:r=>{const{normalize:n}=r;return n(["仅支持docker"])}},task:{taskName:r=>{const{normalize:n}=r;return n(["任务名称"])},taskCount:r=>{const{normalize:n}=r;return n(["任务数量"])},taskProgress:r=>{const{normalize:n}=r;return n(["任务进度"])},taskCycle:r=>{const{normalize:n}=r;return n(["任务周期"])},createTime:r=>{const{normalize:n}=r;return n(["创建时间"])},ignore:r=>{const{normalize:n}=r;return n(["忽略目标"])},endTime:r=>{const{normalize:n}=r;return n(["结束时间"])},addTask:r=>{const{normalize:n}=r;return n(["新建任务"])},addURL:r=>{const{normalize:n}=r;return n(["新建URL"])},delURL:r=>{const{normalize:n}=r;return n(["删除URL"])},retest:r=>{const{normalize:n}=r;return n(["重新测试"])},delTask:r=>{const{normalize:n}=r;return n(["删除任务"])},delAsset:r=>{const{normalize:n}=r;return n(["同时删除资产:"])},typeTask:r=>{const{normalize:n}=r;return n(["任务类型"])},lastTime:r=>{const{normalize:n}=r;return n(["上次运行"])},nextTime:r=>{const{normalize:n}=r;return n(["下次运行"])},msgTaskName:r=>{const{normalize:n}=r;return n(["请输入任务名称"])},taskTarget:r=>{const{normalize:n}=r;return n(["目标"])},subdomainTakeover:r=>{const{normalize:n}=r;return n(["子域名接管"])},assetMapping:r=>{const{normalize:n}=r;return n(["资产测绘"])},msgTarget:r=>{const{normalize:n}=r;return n(["请输入目标，一行一个。\n192.168.1.1-192.168.1.253\n192.168.1.1/24\nexample.com\nCIDR:192.168.0.0/18(这种方式会将网段放在一个节点中扫描，快速扫描存活。)\nCMP:xxx公司（公司名称）\nICP:京ICP证xxxx号(ICP备案号)\nAPP:xxx（APP名称）\nAPP-ID:com.xx.xx（app包名）\n"])},ignoreMsg:r=>{const{normalize:n}=r;return n(["忽略目标，一行一个。\n192.168.1.1-192.168.1.253\n192.168.1.1/24\n*.example.com\n域名格式需要加通配符，否则进行全等判断"])},subdomainScan:r=>{const{normalize:n}=r;return n(["子域名扫描"])},nodeSelect:r=>{const{normalize:n}=r;return n(["节点选择"])},nodeMsg:r=>{const{normalize:n}=r;return n(["请选择扫描节点"])},config:r=>{const{normalize:n}=r;return n(["配置"])},portScan:r=>{const{normalize:n}=r;return n(["端口扫描"])},portSelect:r=>{const{normalize:n}=r;return n(["选择端口"])},url:r=>{const{normalize:n}=r;return n(["URL"])},msgUrl:r=>{const{normalize:n}=r;return n(["获取更多页面入口"])},sensitiveInfoScan:r=>{const{normalize:n}=r;return n(["扫描敏感信息"])},pageMonitoring:r=>{const{normalize:n}=r;return n(["监控页面变动"])},msgPageMonitoringAll:r=>{const{normalize:n}=r;return n(["监控所有页面"])},msgPageMonitoringJs:r=>{const{normalize:n}=r;return n(["仅监控JS页面"])},msgCrawler:r=>{const{normalize:n}=r;return n(["爬虫获取get以及post参数，建议开启URL扫描获取更多网站入口。"])},vulScan:r=>{const{normalize:n}=r;return n(["漏洞扫描"])},vulList:r=>{const{normalize:n}=r;return n(["漏洞列表"])},save:r=>{const{normalize:n}=r;return n(["保存"])},duplicatesSubdomain:r=>{const{normalize:n}=r;return n(["子域名去重"])},data:r=>{const{normalize:n}=r;return n(["数据"])},selectNodeMsg:r=>{const{normalize:n}=r;return n(["当有新节点注册时会自动将任务添加到新节点"])},duplicatesMsg:r=>{const{normalize:n}=r;return n(["历史已经查询到了子域名将会跳过"])},waybackUrlMsg:r=>{const{normalize:n}=r;return n(["从Wayback Machine获取URL"])},addPageMonitTask:r=>{const{normalize:n}=r;return n(["新建页面监控"])},duplicates:r=>{const{normalize:n}=r;return n(["去重"])},duplicatesPort:r=>{const{normalize:n}=r;return n(["端口去重"])},duplicatesPortMsg:r=>{const{normalize:n}=r;return n(["仅扫描未发现的端口"])},runNow:r=>{const{normalize:n}=r;return n(["立即运行"])},templateName:r=>{const{normalize:n}=r;return n(["模板名称"])},addTemplate:r=>{const{normalize:n}=r;return n(["新建模板"])},editTemplate:r=>{const{normalize:n}=r;return n(["编辑模板"])},deleteTemplate:r=>{const{normalize:n}=r;return n(["删除模板"])},autoNode:r=>{const{normalize:n}=r;return n(["自动加入"])},running:r=>{const{normalize:n}=r;return n(["运行中"])},stop:r=>{const{normalize:n}=r;return n(["暂停"])},start:r=>{const{normalize:n}=r;return n(["开始"])},finish:r=>{const{normalize:n}=r;return n(["完成"])},result:r=>{const{normalize:n}=r;return n(["结果"])},select:r=>{const{normalize:n}=r;return n(["选中数据"])},targetNumber:r=>{const{normalize:n}=r;return n(["目标数量"])},targetSource:r=>{const{normalize:n}=r;return n(["目标来源"])},general:r=>{const{normalize:n}=r;return n(["普通"])},fromAsset:r=>{const{normalize:n}=r;return n(["从资产"])},fromSubdomain:r=>{const{normalize:n}=r;return n(["从子域名"])},fromRootDomain:r=>{const{normalize:n}=r;return n(["从根域名"])},fromProject:r=>{const{normalize:n}=r;return n(["从项目"])},targetProject:r=>{const{normalize:n}=r;return n(["筛选项目"])},search:r=>{const{normalize:n}=r;return n(["搜索语句"])},daily:r=>{const{normalize:n}=r;return n(["每天"])},ndays:r=>{const{normalize:n}=r;return n(["N天"])},hourly:r=>{const{normalize:n}=r;return n(["每小时"])},nhours:r=>{const{normalize:n}=r;return n(["N小时"])},nminutes:r=>{const{normalize:n}=r;return n(["N分钟"])},weekly:r=>{const{normalize:n}=r;return n(["每星期"])},monthly:r=>{const{normalize:n}=r;return n(["每月"])},day:r=>{const{normalize:n}=r;return n(["天"])},hour:r=>{const{normalize:n}=r;return n(["小时"])},minute:r=>{const{normalize:n}=r;return n(["分钟"])},monday:r=>{const{normalize:n}=r;return n(["周一"])},tuesday:r=>{const{normalize:n}=r;return n(["周二"])},wednesday:r=>{const{normalize:n}=r;return n(["周三"])},thursday:r=>{const{normalize:n}=r;return n(["周四"])},friday:r=>{const{normalize:n}=r;return n(["周五"])},saturday:r=>{const{normalize:n}=r;return n(["周六"])},sunday:r=>{const{normalize:n}=r;return n(["周日"])},bindProject:r=>{const{normalize:n}=r;return n(["绑定项目"])},addScheduled:r=>{const{normalize:n}=r;return n(["新建计划任务"])},syncToProject:r=>{const{normalize:n}=r;return n(["同步到项目"])},syncToExisting:r=>{const{normalize:n}=r;return n(["已有项目"])},createNewProject:r=>{const{normalize:n}=r;return n(["创建项目"])},selectProject:r=>{const{normalize:n}=r;return n(["选择项目"])}},scanTemplate:{TargetHandler:r=>{const{normalize:n}=r;return n(["目标处理"])},SubdomainScan:r=>{const{normalize:n}=r;return n(["子域名扫描"])},SubdomainSecurity:r=>{const{normalize:n}=r;return n(["域名安全检测"])},PortScanPreparation:r=>{const{normalize:n}=r;return n(["端口扫描预处理"])},PortScan:r=>{const{normalize:n}=r;return n(["端口扫描"])},AssetMapping:r=>{const{normalize:n}=r;return n(["资产测绘"])},URLScan:r=>{const{normalize:n}=r;return n(["URL扫描"])},WebCrawler:r=>{const{normalize:n}=r;return n(["爬虫"])},DirScan:r=>{const{normalize:n}=r;return n(["目录扫描"])},VulnerabilityScan:r=>{const{normalize:n}=r;return n(["漏洞扫描"])},AssetHandle:r=>{const{normalize:n}=r;return n(["资产处理"])},PortFingerprint:r=>{const{normalize:n}=r;return n(["端口指纹识别"])},URLSecurity:r=>{const{normalize:n}=r;return n(["URL安全检测"])},PassiveScan:r=>{const{normalize:n}=r;return n(["被动扫描"])}},asset:{assetName:r=>{const{normalize:n}=r;return n(["资产"])},banner:r=>{const{normalize:n}=r;return n(["Banner"])},products:r=>{const{normalize:n}=r;return n(["应用/组件"])},IP:r=>{const{normalize:n}=r;return n(["IP"])},domain:r=>{const{normalize:n}=r;return n(["域名"])},port:r=>{const{normalize:n}=r;return n(["端口"])},service:r=>{const{normalize:n}=r;return n(["服务"])},title:r=>{const{normalize:n}=r;return n(["标题"])},status:r=>{const{normalize:n}=r;return n(["状态码"])},time:r=>{const{normalize:n}=r;return n(["时间"])},total:r=>{const{normalize:n}=r;return n(["共"])},p:r=>{const{normalize:n}=r;return n(["条"])},result:r=>{const{normalize:n}=r;return n(["条结果"])},detail:r=>{const{normalize:n}=r;return n(["详情"])},assetDetail:r=>{const{normalize:n}=r;return n(["资产详情"])},assetTotalNum:r=>{const{normalize:n}=r;return n(["资产总数"])},responseHeader:r=>{const{normalize:n}=r;return n(["响应头"])},responseBody:r=>{const{normalize:n}=r;return n(["响应体"])},historyDiff:r=>{const{normalize:n}=r;return n(["历史变更"])},export:r=>{const{normalize:n}=r;return n(["导出"])},screenshot:r=>{const{normalize:n}=r;return n(["截图"])},Chart:r=>{const{normalize:n}=r;return n(["统计"])}},subdomain:{subdomainName:r=>{const{normalize:n}=r;return n(["子域名"])},recordType:r=>{const{normalize:n}=r;return n(["记录类型"])},recordValue:r=>{const{normalize:n}=r;return n(["记录值"])}},rootDomain:{rootDomainName:r=>{const{normalize:n}=r;return n(["根域名"])},company:r=>{const{normalize:n}=r;return n(["公司"])}},app:{appName:r=>{const{normalize:n}=r;return n(["APP"])},name:r=>{const{normalize:n}=r;return n(["名称"])},category:r=>{const{normalize:n}=r;return n(["分类"])},description:r=>{const{normalize:n}=r;return n(["描述"])}},miniProgram:{miniProgramName:r=>{const{normalize:n}=r;return n(["小程序"])}},URL:{URLName:r=>{const{normalize:n}=r;return n(["URL"])},source:r=>{const{normalize:n}=r;return n(["来源"])},type:r=>{const{normalize:n}=r;return n(["类型"])},input:r=>{const{normalize:n}=r;return n(["输入"])}},crawler:{crawlerName:r=>{const{normalize:n}=r;return n(["爬虫"])},getParameter:r=>{const{normalize:n}=r;return n(["GET参数"])},postParameter:r=>{const{normalize:n}=r;return n(["POST参数"])}},sensitiveInformation:{sensitiveInformationName:r=>{const{normalize:n}=r;return n(["敏感信息"])},sensitiveName:r=>{const{normalize:n}=r;return n(["名称"])},sensitiveColor:r=>{const{normalize:n}=r;return n(["颜色"])},sensitiveRegular:r=>{const{normalize:n}=r;return n(["正则"])},sensitiveNameMsg:r=>{const{normalize:n}=r;return n(["请输入规则名称"])},sensitiveRegularMsg:r=>{const{normalize:n}=r;return n(["请输入正则表达式"])},sensAggre:r=>{const{normalize:n}=r;return n(["敏感信息名称聚合"])}},dirScan:{dirScanName:r=>{const{normalize:n}=r;return n(["目录扫描"])},title:r=>{const{normalize:n}=r;return n(["标题"])},status:r=>{const{normalize:n}=r;return n(["状态码"])},length:r=>{const{normalize:n}=r;return n(["长度"])}},vulnerability:{vulnerabilityName:r=>{const{normalize:n}=r;return n(["漏洞"])}},PageMonitoring:{pageMonitoringName:r=>{const{normalize:n}=r;return n(["页面监控"])},oldResponseBody:r=>{const{normalize:n}=r;return n(["原始响应体"])},currentResponseBody:r=>{const{normalize:n}=r;return n(["当前响应体"])},statusCode:r=>{const{normalize:n}=r;return n(["响应码"])},hash:r=>{const{normalize:n}=r;return n(["响应hash"])},similarity:r=>{const{normalize:n}=r;return n(["相似度"])}},project:{project:r=>{const{normalize:n}=r;return n(["项目"])},addProject:r=>{const{normalize:n}=r;return n(["新建项目"])},totalAssets:r=>{const{normalize:n}=r;return n(["总资产"])},projectName:r=>{const{normalize:n}=r;return n(["项目名称"])},msgProject:r=>{const{normalize:n}=r;return n(["请输入项目名称"])},projectScope:r=>{const{normalize:n}=r;return n(["项目范围"])},msgProjectScope:r=>{const{normalize:n}=r;return n(["输入项目的根域名，一行一个。"])},msgScheduledTasks:r=>{const{normalize:n}=r;return n(["定时扫描"])},scheduledTasks:r=>{const{normalize:n}=r;return n(["定时任务"])},cycle:r=>{const{normalize:n}=r;return n(["监控周期"])},projectDetail:r=>{const{normalize:n}=r;return n(["项目信息"])},CreatTime:r=>{const{normalize:n}=r;return n(["创建时间"])},msgProjectTag:r=>{const{normalize:n}=r;return n(["请输入项目TAG"])},aggregation:r=>{const{normalize:n}=r;return n(["聚合"])},overview:r=>{const{normalize:n}=r;return n(["概况"])}},poc:{pocName:r=>{const{normalize:n}=r;return n(["POC名称"])},level:r=>{const{normalize:n}=r;return n(["风险等级"])},content:r=>{const{normalize:n}=r;return n(["POC内容"])},critical:r=>{const{normalize:n}=r;return n(["严重"])},high:r=>{const{normalize:n}=r;return n(["高危"])},medium:r=>{const{normalize:n}=r;return n(["中等"])},low:r=>{const{normalize:n}=r;return n(["低"])},info:r=>{const{normalize:n}=r;return n(["信息"])},unknown:r=>{const{normalize:n}=r;return n(["未知"])},nameMsg:r=>{const{normalize:n}=r;return n(["请输入POC名称,POC名称和POC内容中的name应保持一致。"])},contentMsg:r=>{const{normalize:n}=r;return n(["请输入poc内容"])}},configuration:{subfinder:r=>{const{normalize:n}=r;return n(["subfinder配置"])},rad:r=>{const{normalize:n}=r;return n(["rad配置"])},system:r=>{const{normalize:n}=r;return n(["系统配置"])},timezone:r=>{const{normalize:n}=r;return n(["时区"])},maxTaskNum:r=>{const{normalize:n}=r;return n(["最大任务数量"])},dirScanThread:r=>{const{normalize:n}=r;return n(["目录扫描并发数"])},portScanThread:r=>{const{normalize:n}=r;return n(["端口扫描并发数"])},crawlerThread:r=>{const{normalize:n}=r;return n(["爬虫并行数"])},urlThread:r=>{const{normalize:n}=r;return n(["URL爬取线程数"])},maxUrlNum:r=>{const{normalize:n}=r;return n(["最大URL获取数量"])},threadMsg:r=>{const{normalize:n}=r;return n(["请根据系统内存自行配置并发数量(参考文档)"])},noticeConfig:r=>{const{normalize:n}=r;return n(["通知配置"])},newWebhookConfig:r=>{const{normalize:n}=r;return n(["新建配置"])},noticeHelp:r=>{const{normalize:n}=r;return n(['*msg*为消息参数位置。eg:http://example.com?msg=*msg*  or POST "msg":"*msg*"'])},duplicationconfiguration:r=>{const{normalize:n}=r;return n(["去重配置"])},deduplicationHour:r=>{const{normalize:n}=r;return n(["去重周期"])},deduplicationFlag:r=>{const{normalize:n}=r;return n(["去重开关"])},runNowOne:r=>{const{normalize:n}=r;return n(["立即运行一次"])}},form:{input:r=>{const{normalize:n}=r;return n(["搜索"])}},portDict:{name:r=>{const{normalize:n}=r;return n(["名称"])},value:r=>{const{normalize:n}=r;return n(["值"])},nameMsg:r=>{const{normalize:n}=r;return n(["请输入名称"])},valueMsg:r=>{const{normalize:n}=r;return n(["请输入值"])}},fingerprint:{name:r=>{const{normalize:n}=r;return n(["名称"])},rule:r=>{const{normalize:n}=r;return n(["规则"])},category:r=>{const{normalize:n}=r;return n(["类型"])},nameMsg:r=>{const{normalize:n}=r;return n(["请输入指纹名称"])},ruleMsg:r=>{const{normalize:n}=r;return n(["请输入规则内容"])},parentCategory:r=>{const{normalize:n}=r;return n(["归类"])},amount:r=>{const{normalize:n}=r;return n(["资产数量"])}},plugin:{name:r=>{const{normalize:n}=r;return n(["插件名称"])},version:r=>{const{normalize:n}=r;return n(["版本"])},parameter:r=>{const{normalize:n}=r;return n(["参数"])},introduction:r=>{const{normalize:n}=r;return n(["简介"])},new:r=>{const{normalize:n}=r;return n(["新建插件"])},delete:r=>{const{normalize:n}=r;return n(["删除插件"])},module:r=>{const{normalize:n}=r;return n(["模块"])},help:r=>{const{normalize:n}=r;return n(["参数说明"])},source:r=>{const{normalize:n}=r;return n(["源码"])},isSystem:r=>{const{normalize:n}=r;return n(["内置"])},market:r=>{const{normalize:n}=r;return n(["插件市场"])},import:r=>{const{normalize:n}=r;return n(["导入"])},key:r=>{const{normalize:n}=r;return n(["插件Key"])},keyMsg:r=>{const{normalize:n}=r;return n(["输入插件密钥，查看server运行日志，或者在项目运行根目录PLUGINKEY文件查看。"])},reInstall:r=>{const{normalize:n}=r;return n(["重新install"])},reCheck:r=>{const{normalize:n}=r;return n(["重新check"])},uninstall:r=>{const{normalize:n}=r;return n(["卸载"])}},workplace:{goodMorning:r=>{const{normalize:n}=r;return n(["早安"])},happyDay:r=>{const{normalize:n}=r;return n(["祝你开心每一天!"])},toady:r=>{const{normalize:n}=r;return n(["今日晴"])},project:r=>{const{normalize:n}=r;return n(["项目数"])},access:r=>{const{normalize:n}=r;return n(["项目访问"])},toDo:r=>{const{normalize:n}=r;return n(["待办"])},introduction:r=>{const{normalize:n}=r;return n(["一个正经的简介"])},more:r=>{const{normalize:n}=r;return n(["更多"])},shortcutOperation:r=>{const{normalize:n}=r;return n(["快捷操作"])},operation:r=>{const{normalize:n}=r;return n(["操作"])},index:r=>{const{normalize:n}=r;return n(["指数"])},personal:r=>{const{normalize:n}=r;return n(["个人"])},team:r=>{const{normalize:n}=r;return n(["团队"])},quote:r=>{const{normalize:n}=r;return n(["引用"])},contribution:r=>{const{normalize:n}=r;return n(["贡献"])},hot:r=>{const{normalize:n}=r;return n(["热度"])},yield:r=>{const{normalize:n}=r;return n(["产量"])},dynamic:r=>{const{normalize:n}=r;return n(["动态"])},push:r=>{const{normalize:n}=r;return n(["推送"])},pushCode:r=>{const{normalize:n}=r;return n(["Archer 推送 代码到 Github"])},follow:r=>{const{normalize:n}=r;return n(["关注"])}},formDemo:{input:r=>{const{normalize:n}=r;return n(["输入框"])},inputNumber:r=>{const{normalize:n}=r;return n(["数字输入框"])},default:r=>{const{normalize:n}=r;return n(["默认"])},icon:r=>{const{normalize:n}=r;return n(["图标"])},mixed:r=>{const{normalize:n}=r;return n(["复合型"])},password:r=>{const{normalize:n}=r;return n(["密码框"])},textarea:r=>{const{normalize:n}=r;return n(["多行文本"])},remoteSearch:r=>{const{normalize:n}=r;return n(["远程搜索"])},slot:r=>{const{normalize:n}=r;return n(["插槽"])},position:r=>{const{normalize:n}=r;return n(["位置"])},autocomplete:r=>{const{normalize:n}=r;return n(["自动补全"])},select:r=>{const{normalize:n}=r;return n(["选择器"])},optionSlot:r=>{const{normalize:n}=r;return n(["选项插槽"])},selectGroup:r=>{const{normalize:n}=r;return n(["选项分组"])},selectV2:r=>{const{normalize:n}=r;return n(["虚拟列表选择器"])},cascader:r=>{const{normalize:n}=r;return n(["级联选择器"])},switch:r=>{const{normalize:n}=r;return n(["开关"])},rate:r=>{const{normalize:n}=r;return n(["评分"])},colorPicker:r=>{const{normalize:n}=r;return n(["颜色选择器"])},transfer:r=>{const{normalize:n}=r;return n(["穿梭框"])},render:r=>{const{normalize:n}=r;return n(["渲染器"])},radio:r=>{const{normalize:n}=r;return n(["单选框"])},radioGroup:r=>{const{normalize:n}=r;return n(["单选框组"])},button:r=>{const{normalize:n}=r;return n(["按钮"])},checkbox:r=>{const{normalize:n}=r;return n(["多选框"])},checkboxButton:r=>{const{normalize:n}=r;return n(["多选框按钮"])},checkboxGroup:r=>{const{normalize:n}=r;return n(["多选框组"])},slider:r=>{const{normalize:n}=r;return n(["滑块"])},datePicker:r=>{const{normalize:n}=r;return n(["日期选择器"])},shortcuts:r=>{const{normalize:n}=r;return n(["快捷选项"])},today:r=>{const{normalize:n}=r;return n(["今天"])},yesterday:r=>{const{normalize:n}=r;return n(["昨天"])},aWeekAgo:r=>{const{normalize:n}=r;return n(["一周前"])},week:r=>{const{normalize:n}=r;return n(["周"])},year:r=>{const{normalize:n}=r;return n(["年"])},month:r=>{const{normalize:n}=r;return n(["月"])},dates:r=>{const{normalize:n}=r;return n(["日期"])},daterange:r=>{const{normalize:n}=r;return n(["日期范围"])},monthrange:r=>{const{normalize:n}=r;return n(["月份范围"])},dateTimePicker:r=>{const{normalize:n}=r;return n(["日期时间选择器"])},dateTimerange:r=>{const{normalize:n}=r;return n(["日期时间范围"])},timePicker:r=>{const{normalize:n}=r;return n(["时间选择器"])},timeSelect:r=>{const{normalize:n}=r;return n(["时间选择"])},inputPassword:r=>{const{normalize:n}=r;return n(["密码输入框"])},passwordStrength:r=>{const{normalize:n}=r;return n(["密码强度"])},defaultForm:r=>{const{normalize:n}=r;return n(["全部示例"])},formDes:r=>{const{normalize:n}=r;return n(["基于 ElementPlus 的 Form 组件二次封装，实现数据驱动，支持所有 Form 参数"])},example:r=>{const{normalize:n}=r;return n(["示例"])},operate:r=>{const{normalize:n}=r;return n(["操作"])},change:r=>{const{normalize:n}=r;return n(["更改"])},restore:r=>{const{normalize:n}=r;return n(["还原"])},disabled:r=>{const{normalize:n}=r;return n(["禁用"])},disablement:r=>{const{normalize:n}=r;return n(["解除禁用"])},delete:r=>{const{normalize:n}=r;return n(["删除"])},add:r=>{const{normalize:n}=r;return n(["添加"])},setValue:r=>{const{normalize:n}=r;return n(["设置值"])},resetValue:r=>{const{normalize:n}=r;return n(["重置值"])},set:r=>{const{normalize:n}=r;return n(["设置"])},subitem:r=>{const{normalize:n}=r;return n(["子项"])},formValidation:r=>{const{normalize:n}=r;return n(["表单验证"])},verifyReset:r=>{const{normalize:n}=r;return n(["验证重置"])},richText:r=>{const{normalize:n}=r;return n(["富文本编辑器"])},jsonEditor:r=>{const{normalize:n}=r;return n(["JSON编辑器"])},form:r=>{const{normalize:n}=r;return n(["表单"])},remoteLoading:r=>{const{normalize:n}=r;return n(["远程加载"])},focus:r=>{const{normalize:n}=r;return n(["聚焦"])},treeSelect:r=>{const{normalize:n}=r;return n(["树形选择器"])},showCheckbox:r=>{const{normalize:n}=r;return n(["显示复选框"])},selectAnyLevel:r=>{const{normalize:n}=r;return n(["选择任意级别"])},multiple:r=>{const{normalize:n}=r;return n(["多选"])},filterable:r=>{const{normalize:n}=r;return n(["可筛选"])},customContent:r=>{const{normalize:n}=r;return n(["自定义内容"])},lazyLoad:r=>{const{normalize:n}=r;return n(["懒加载"])},upload:r=>{const{normalize:n}=r;return n(["上传"])},userAvatar:r=>{const{normalize:n}=r;return n(["用户头像"])},iconPicker:r=>{const{normalize:n}=r;return n(["图标选择器"])}},guideDemo:{guide:r=>{const{normalize:n}=r;return n(["引导页"])},start:r=>{const{normalize:n}=r;return n(["开始"])},message:r=>{const{normalize:n}=r;return n(["引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。引导页基于 driver.js"])}},iconDemo:{icon:r=>{const{normalize:n}=r;return n(["图标"])},localIcon:r=>{const{normalize:n}=r;return n(["本地图标"])},iconify:r=>{const{normalize:n}=r;return n(["Iconify组件"])},recommendedUse:r=>{const{normalize:n}=r;return n(["推荐使用"])},recommendeDes:r=>{const{normalize:n}=r;return n(["Iconify组件基本包含所有的图标，你可以查询到你想要的任何图标。并且打包只会打包所用到的图标。"])},accessAddress:r=>{const{normalize:n}=r;return n(["访问地址"])}},echartDemo:{echart:r=>{const{normalize:n}=r;return n(["图表"])},echartDes:r=>{const{normalize:n}=r;return n(["基于 echarts 二次封装组件，自适应宽度，只需传入 options 与 height 属性即可展示对应的图表。"])}},countToDemo:{countTo:r=>{const{normalize:n}=r;return n(["数字动画"])},countToDes:r=>{const{normalize:n}=r;return n(["基于 vue-count-to 进行改造，支持所有 vue-count-to 参数。"])},suffix:r=>{const{normalize:n}=r;return n(["后缀"])},prefix:r=>{const{normalize:n}=r;return n(["前缀"])},separator:r=>{const{normalize:n}=r;return n(["分割符号"])},duration:r=>{const{normalize:n}=r;return n(["持续时间"])},endVal:r=>{const{normalize:n}=r;return n(["结束值"])},startVal:r=>{const{normalize:n}=r;return n(["开始值"])},start:r=>{const{normalize:n}=r;return n(["开始"])},pause:r=>{const{normalize:n}=r;return n(["暂停"])},resume:r=>{const{normalize:n}=r;return n(["继续"])}},watermarkDemo:{watermark:r=>{const{normalize:n}=r;return n(["水印"])},createdWatermark:r=>{const{normalize:n}=r;return n(["创建水印"])},clearWatermark:r=>{const{normalize:n}=r;return n(["清除水印"])},resetWatermark:r=>{const{normalize:n}=r;return n(["重置水印"])}},qrcodeDemo:{qrcode:r=>{const{normalize:n}=r;return n(["二维码"])},qrcodeDes:r=>{const{normalize:n}=r;return n(["基于 qrcode 二次封装"])},basicUsage:r=>{const{normalize:n}=r;return n(["基础用法"])},imgTag:r=>{const{normalize:n}=r;return n(["img标签"])},style:r=>{const{normalize:n}=r;return n(["样式配置"])},click:r=>{const{normalize:n}=r;return n(["点击事件"])},asynchronousContent:r=>{const{normalize:n}=r;return n(["异步内容"])},invalid:r=>{const{normalize:n}=r;return n(["失效"])},logoConfig:r=>{const{normalize:n}=r;return n(["logo配置"])},logoStyle:r=>{const{normalize:n}=r;return n(["logo样式"])},size:r=>{const{normalize:n}=r;return n(["大小配置"])}},highlightDemo:{highlight:r=>{const{normalize:n}=r;return n(["高亮"])},message:r=>{const{normalize:n}=r;return n(["种一棵树最好的时间是十年前，其次就是现在。"])},keys1:r=>{const{normalize:n}=r;return n(["十年前"])},keys2:r=>{const{normalize:n}=r;return n(["现在"])}},infotipDemo:{infotip:r=>{const{normalize:n}=r;return n(["信息提示"])},infotipDes:r=>{const{normalize:n}=r;return n(["基于 Highlight 组件二次封装"])},title:r=>{const{normalize:n}=r;return n(["注意事项"])}},levelDemo:{menu:r=>{const{normalize:n}=r;return n(["多级菜单缓存"])}},searchDemo:{search:r=>{const{normalize:n}=r;return n(["查询"])},searchDes:r=>{const{normalize:n}=r;return n(["基于 Form 组件二次封装，实现查询、重置功能"])},operate:r=>{const{normalize:n}=r;return n(["操作"])},change:r=>{const{normalize:n}=r;return n(["更改"])},grid:r=>{const{normalize:n}=r;return n(["栅格"])},button:r=>{const{normalize:n}=r;return n(["按钮"])},restore:r=>{const{normalize:n}=r;return n(["还原"])},inline:r=>{const{normalize:n}=r;return n(["内联"])},bottom:r=>{const{normalize:n}=r;return n(["底部"])},position:r=>{const{normalize:n}=r;return n(["位置"])},left:r=>{const{normalize:n}=r;return n(["左"])},center:r=>{const{normalize:n}=r;return n(["中"])},right:r=>{const{normalize:n}=r;return n(["右"])},dynamicOptions:r=>{const{normalize:n}=r;return n(["动态选项"])},deleteRadio:r=>{const{normalize:n}=r;return n(["删除单选框"])},restoreRadio:r=>{const{normalize:n}=r;return n(["还原单选框"])},loading:r=>{const{normalize:n}=r;return n(["加载中"])},reset:r=>{const{normalize:n}=r;return n(["重置"])}},stickyDemo:{sticky:r=>{const{normalize:n}=r;return n(["黏性"])}},tableDemo:{table:r=>{const{normalize:n}=r;return n(["表格"])},tableDes:r=>{const{normalize:n}=r;return n(["基于 ElementPlus 的 Table 组件二次封装"])},index:r=>{const{normalize:n}=r;return n(["序号"])},title:r=>{const{normalize:n}=r;return n(["标题"])},author:r=>{const{normalize:n}=r;return n(["作者"])},displayTime:r=>{const{normalize:n}=r;return n(["创建时间"])},importance:r=>{const{normalize:n}=r;return n(["重要性"])},pageviews:r=>{const{normalize:n}=r;return n(["阅读数"])},action:r=>{const{normalize:n}=r;return n(["操作"])},important:r=>{const{normalize:n}=r;return n(["重要"])},good:r=>{const{normalize:n}=r;return n(["良好"])},commonly:r=>{const{normalize:n}=r;return n(["一般"])},operate:r=>{const{normalize:n}=r;return n(["操作"])},example:r=>{const{normalize:n}=r;return n(["示例"])},show:r=>{const{normalize:n}=r;return n(["显示"])},hidden:r=>{const{normalize:n}=r;return n(["隐藏"])},pagination:r=>{const{normalize:n}=r;return n(["分页"])},reserveIndex:r=>{const{normalize:n}=r;return n(["叠加序号"])},restoreIndex:r=>{const{normalize:n}=r;return n(["还原序号"])},showSelections:r=>{const{normalize:n}=r;return n(["显示多选"])},hiddenSelections:r=>{const{normalize:n}=r;return n(["隐藏多选"])},showExpandedRows:r=>{const{normalize:n}=r;return n(["显示展开行"])},hiddenExpandedRows:r=>{const{normalize:n}=r;return n(["隐藏展开行"])},changeTitle:r=>{const{normalize:n}=r;return n(["修改标题"])},header:r=>{const{normalize:n}=r;return n(["头部"])},selectAllNone:r=>{const{normalize:n}=r;return n(["全选/全不选"])},delOrAddAction:r=>{const{normalize:n}=r;return n(["删除/添加操作列"])},showOrHiddenStripe:r=>{const{normalize:n}=r;return n(["显示/隐藏斑马纹"])},showOrHiddenBorder:r=>{const{normalize:n}=r;return n(["显示/隐藏边框"])},fixedHeaderOrAuto:r=>{const{normalize:n}=r;return n(["固定头部/自动"])},getSelections:r=>{const{normalize:n}=r;return n(["获取多选数据"])},preview:r=>{const{normalize:n}=r;return n(["封面"])},showOrHiddenSortable:r=>{const{normalize:n}=r;return n(["显示/隐藏排序"])},videoPreview:r=>{const{normalize:n}=r;return n(["视频预览"])},cardTable:r=>{const{normalize:n}=r;return n(["卡片表格"])}}};export{r as default};
