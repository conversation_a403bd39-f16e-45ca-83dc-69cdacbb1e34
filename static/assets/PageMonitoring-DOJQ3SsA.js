import{ak as t,d as e,r as i,s as n,e as _,z as s,F as a,v as r,A as o,B as u,G as p,H as h,o as l,c,a as d,w as f,I as g,J as m,l as b,Y as k,_ as y}from"./index-DfJTpRkj.js";import{u as w}from"./useTable-CtyddZqf.js";import{E as x}from"./el-card-DyZz6u6e.js";import{E as v}from"./el-pagination-FJcT0ZDj.js";import{E}from"./el-tag-CbhrEnto.js";import"./el-select-BkpcrSfo.js";import"./el-popper-D2BmgSQA.js";import{E as O,a as T}from"./el-col-B4Ik8fnS.js";import{E as R}from"./el-link-Bi4jWYBx.js";import{_ as N}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{_ as A}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as S}from"./useCrudSchemas-Cz9y99Kk.js";import{m as L,a as C,d as j,n as P}from"./index-D4GvAO2k.js";import D from"./Csearch-C6xIjicy.js";import{M as W}from"./MonacoDiffEditor-DS5QEM6N.js";import"./index-DE7jtbbk.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./refs-DAMUgizk.js";import"./el-table-column-7FjdLFwR.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./index-CyH6XROR.js";import"./tree-BfZhwLPs.js";import"./index-D1ADinPR.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import"./el-text-vKNLRkxx.js";import"./el-divider-0NmzbuNU.js";import"./el-autocomplete-CyglTUOR.js";/* empty css                */import"./el-tree-select-P6ZpyTcB.js";import"./index-Co3LqSsp.js";import"./el-switch-C5ZBDFmL.js";import"./useIcon-CNpM61rT.js";import"./exportData.vue_vue_type_script_setup_true_lang-CthEe30Y.js";import"./el-tab-pane-BijWf7kq.js";import"./el-form-DsaI0u2w.js";import"./el-radio-group-CTAZlJKV.js";import"./el-space-7M-SGVBd.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BD8eoln6.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./index-KH6atv8j.js";import"./index-B0JD2UFG.js";import"./DetailTemplate-DU3RXrBH.js";import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";import"./index-jyMftxhc.js";var M,K={exports:{}},I={},B={exports:{}},U={},z={};function V(){if(M)return z;function t(t){this.__parent=t,this.__character_count=0,this.__indent_count=-1,this.__alignment_count=0,this.__wrap_point_index=0,this.__wrap_point_character_count=0,this.__wrap_point_indent_count=-1,this.__wrap_point_alignment_count=0,this.__items=[]}function e(t,e){this.__cache=[""],this.__indent_size=t.indent_size,this.__indent_string=t.indent_char,t.indent_with_tabs||(this.__indent_string=new Array(t.indent_size+1).join(t.indent_char)),e=e||"",t.indent_level>0&&(e=new Array(t.indent_level+1).join(this.__indent_string)),this.__base_string=e,this.__base_string_length=e.length}function i(i,n){this.__indent_cache=new e(i,n),this.raw=!1,this._end_with_newline=i.end_with_newline,this.indent_size=i.indent_size,this.wrap_line_length=i.wrap_line_length,this.indent_empty_lines=i.indent_empty_lines,this.__lines=[],this.previous_line=null,this.current_line=null,this.next_line=new t(this),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1,this.__add_outputline()}return M=1,t.prototype.clone_empty=function(){var e=new t(this.__parent);return e.set_indent(this.__indent_count,this.__alignment_count),e},t.prototype.item=function(t){return t<0?this.__items[this.__items.length+t]:this.__items[t]},t.prototype.has_match=function(t){for(var e=this.__items.length-1;e>=0;e--)if(this.__items[e].match(t))return!0;return!1},t.prototype.set_indent=function(t,e){this.is_empty()&&(this.__indent_count=t||0,this.__alignment_count=e||0,this.__character_count=this.__parent.get_indent_size(this.__indent_count,this.__alignment_count))},t.prototype._set_wrap_point=function(){this.__parent.wrap_line_length&&(this.__wrap_point_index=this.__items.length,this.__wrap_point_character_count=this.__character_count,this.__wrap_point_indent_count=this.__parent.next_line.__indent_count,this.__wrap_point_alignment_count=this.__parent.next_line.__alignment_count)},t.prototype._should_wrap=function(){return this.__wrap_point_index&&this.__character_count>this.__parent.wrap_line_length&&this.__wrap_point_character_count>this.__parent.next_line.__character_count},t.prototype._allow_wrap=function(){if(this._should_wrap()){this.__parent.add_new_line();var t=this.__parent.current_line;return t.set_indent(this.__wrap_point_indent_count,this.__wrap_point_alignment_count),t.__items=this.__items.slice(this.__wrap_point_index),this.__items=this.__items.slice(0,this.__wrap_point_index),t.__character_count+=this.__character_count-this.__wrap_point_character_count,this.__character_count=this.__wrap_point_character_count," "===t.__items[0]&&(t.__items.splice(0,1),t.__character_count-=1),!0}return!1},t.prototype.is_empty=function(){return 0===this.__items.length},t.prototype.last=function(){return this.is_empty()?null:this.__items[this.__items.length-1]},t.prototype.push=function(t){this.__items.push(t);var e=t.lastIndexOf("\n");-1!==e?this.__character_count=t.length-e:this.__character_count+=t.length},t.prototype.pop=function(){var t=null;return this.is_empty()||(t=this.__items.pop(),this.__character_count-=t.length),t},t.prototype._remove_indent=function(){this.__indent_count>0&&(this.__indent_count-=1,this.__character_count-=this.__parent.indent_size)},t.prototype._remove_wrap_indent=function(){this.__wrap_point_indent_count>0&&(this.__wrap_point_indent_count-=1)},t.prototype.trim=function(){for(;" "===this.last();)this.__items.pop(),this.__character_count-=1},t.prototype.toString=function(){var t="";return this.is_empty()?this.__parent.indent_empty_lines&&(t=this.__parent.get_indent_string(this.__indent_count)):(t=this.__parent.get_indent_string(this.__indent_count,this.__alignment_count),t+=this.__items.join("")),t},e.prototype.get_indent_size=function(t,e){var i=this.__base_string_length;return e=e||0,t<0&&(i=0),i+=t*this.__indent_size,i+=e},e.prototype.get_indent_string=function(t,e){var i=this.__base_string;return e=e||0,t<0&&(t=0,i=""),e+=t*this.__indent_size,this.__ensure_cache(e),i+=this.__cache[e]},e.prototype.__ensure_cache=function(t){for(;t>=this.__cache.length;)this.__add_column()},e.prototype.__add_column=function(){var t=this.__cache.length,e=0,i="";this.__indent_size&&t>=this.__indent_size&&(t-=(e=Math.floor(t/this.__indent_size))*this.__indent_size,i=new Array(e+1).join(this.__indent_string)),t&&(i+=new Array(t+1).join(" ")),this.__cache.push(i)},i.prototype.__add_outputline=function(){this.previous_line=this.current_line,this.current_line=this.next_line.clone_empty(),this.__lines.push(this.current_line)},i.prototype.get_line_number=function(){return this.__lines.length},i.prototype.get_indent_string=function(t,e){return this.__indent_cache.get_indent_string(t,e)},i.prototype.get_indent_size=function(t,e){return this.__indent_cache.get_indent_size(t,e)},i.prototype.is_empty=function(){return!this.previous_line&&this.current_line.is_empty()},i.prototype.add_new_line=function(t){return!(this.is_empty()||!t&&this.just_added_newline())&&(this.raw||this.__add_outputline(),!0)},i.prototype.get_code=function(t){this.trim(!0);var e=this.current_line.pop();e&&("\n"===e[e.length-1]&&(e=e.replace(/\n+$/g,"")),this.current_line.push(e)),this._end_with_newline&&this.__add_outputline();var i=this.__lines.join("\n");return"\n"!==t&&(i=i.replace(/[\n]/g,t)),i},i.prototype.set_wrap_point=function(){this.current_line._set_wrap_point()},i.prototype.set_indent=function(t,e){return t=t||0,e=e||0,this.next_line.set_indent(t,e),this.__lines.length>1?(this.current_line.set_indent(t,e),!0):(this.current_line.set_indent(),!1)},i.prototype.add_raw_token=function(t){for(var e=0;e<t.newlines;e++)this.__add_outputline();this.current_line.set_indent(-1),this.current_line.push(t.whitespace_before),this.current_line.push(t.text),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1},i.prototype.add_token=function(t){this.__add_space_before_token(),this.current_line.push(t),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=this.current_line._allow_wrap()},i.prototype.__add_space_before_token=function(){this.space_before_token&&!this.just_added_newline()&&(this.non_breaking_space||this.set_wrap_point(),this.current_line.push(" "))},i.prototype.remove_indent=function(t){for(var e=this.__lines.length;t<e;)this.__lines[t]._remove_indent(),t++;this.current_line._remove_wrap_indent()},i.prototype.trim=function(t){for(t=void 0!==t&&t,this.current_line.trim();t&&this.__lines.length>1&&this.current_line.is_empty();)this.__lines.pop(),this.current_line=this.__lines[this.__lines.length-1],this.current_line.trim();this.previous_line=this.__lines.length>1?this.__lines[this.__lines.length-2]:null},i.prototype.just_added_newline=function(){return this.current_line.is_empty()},i.prototype.just_added_blankline=function(){return this.is_empty()||this.current_line.is_empty()&&this.previous_line.is_empty()},i.prototype.ensure_empty_line_above=function(e,i){for(var n=this.__lines.length-2;n>=0;){var _=this.__lines[n];if(_.is_empty())break;if(0!==_.item(0).indexOf(e)&&_.item(-1)!==i){this.__lines.splice(n+1,0,new t(this)),this.previous_line=this.__lines[this.__lines.length-2];break}n--}},z.Output=i,z}var X,F={};function G(){if(X)return F;return X=1,F.Token=function(t,e,i,n){this.type=t,this.text=e,this.comments_before=null,this.newlines=i||0,this.whitespace_before=n||"",this.parent=null,this.next=null,this.previous=null,this.opened=null,this.closed=null,this.directives=null},F}var $,Q={};function q(){return $||($=1,s="(?:"+(_="\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]+\\}")+"|[\\x23\\x24\\x40\\x41-\\x5a\\x5f\\x61-\\x7a"+(i="\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u0527\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05d0-\\u05ea\\u05f0-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u08a0\\u08a2-\\u08ac\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0977\\u0979-\\u097f\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c33\\u0c35-\\u0c39\\u0c3d\\u0c58\\u0c59\\u0c60\\u0c61\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d60\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e87\\u0e88\\u0e8a\\u0e8d\\u0e94-\\u0e97\\u0e99-\\u0e9f\\u0ea1-\\u0ea3\\u0ea5\\u0ea7\\u0eaa\\u0eab\\u0ead-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f4\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f0\\u1700-\\u170c\\u170e-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1877\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191c\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19c1-\\u19c7\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4b\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1ce9-\\u1cec\\u1cee-\\u1cf1\\u1cf5\\u1cf6\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2119-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u212d\\u212f-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cee\\u2cf2\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u2e2f\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309d-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312d\\u3131-\\u318e\\u31a0-\\u31ba\\u31f0-\\u31ff\\u3400-\\u4db5\\u4e00-\\u9fcc\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua697\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua78e\\ua790-\\ua793\\ua7a0-\\ua7aa\\ua7f8-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa80-\\uaaaf\\uaab1\\uaab5\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uabc0-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc")+"])",a="(?:"+_+"|["+(e="\\x24\\x30-\\x39\\x41-\\x5a\\x5f\\x61-\\x7a")+i+(n="\\u0300-\\u036f\\u0483-\\u0487\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u0620-\\u0649\\u0672-\\u06d3\\u06e7-\\u06e8\\u06fb-\\u06fc\\u0730-\\u074a\\u0800-\\u0814\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0840-\\u0857\\u08e4-\\u08fe\\u0900-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962-\\u0963\\u0966-\\u096f\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09d7\\u09df-\\u09e0\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a66-\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2-\\u0ae3\\u0ae6-\\u0aef\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b56\\u0b57\\u0b5f-\\u0b60\\u0b66-\\u0b6f\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0be6-\\u0bef\\u0c01-\\u0c03\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62-\\u0c63\\u0c66-\\u0c6f\\u0c82\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2-\\u0ce3\\u0ce6-\\u0cef\\u0d02\\u0d03\\u0d46-\\u0d48\\u0d57\\u0d62-\\u0d63\\u0d66-\\u0d6f\\u0d82\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0df2\\u0df3\\u0e34-\\u0e3a\\u0e40-\\u0e45\\u0e50-\\u0e59\\u0eb4-\\u0eb9\\u0ec8-\\u0ecd\\u0ed0-\\u0ed9\\u0f18\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f41-\\u0f47\\u0f71-\\u0f84\\u0f86-\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u1000-\\u1029\\u1040-\\u1049\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f-\\u109d\\u135d-\\u135f\\u170e-\\u1710\\u1720-\\u1730\\u1740-\\u1750\\u1772\\u1773\\u1780-\\u17b2\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u1810-\\u1819\\u1920-\\u192b\\u1930-\\u193b\\u1951-\\u196d\\u19b0-\\u19c0\\u19c8-\\u19c9\\u19d0-\\u19d9\\u1a00-\\u1a15\\u1a20-\\u1a53\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1b46-\\u1b4b\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1bb0-\\u1bb9\\u1be6-\\u1bf3\\u1c00-\\u1c22\\u1c40-\\u1c49\\u1c5b-\\u1c7d\\u1cd0-\\u1cd2\\u1d00-\\u1dbe\\u1e01-\\u1f15\\u200c\\u200d\\u203f\\u2040\\u2054\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2d81-\\u2d96\\u2de0-\\u2dff\\u3021-\\u3028\\u3099\\u309a\\ua640-\\ua66d\\ua674-\\ua67d\\ua69f\\ua6f0-\\ua6f1\\ua7f8-\\ua800\\ua806\\ua80b\\ua823-\\ua827\\ua880-\\ua881\\ua8b4-\\ua8c4\\ua8d0-\\ua8d9\\ua8f3-\\ua8f7\\ua900-\\ua909\\ua926-\\ua92d\\ua930-\\ua945\\ua980-\\ua983\\ua9b3-\\ua9c0\\uaa00-\\uaa27\\uaa40-\\uaa41\\uaa4c-\\uaa4d\\uaa50-\\uaa59\\uaa7b\\uaae0-\\uaae9\\uaaf2-\\uaaf3\\uabc0-\\uabe1\\uabec\\uabed\\uabf0-\\uabf9\\ufb20-\\ufb28\\ufe00-\\ufe0f\\ufe20-\\ufe26\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff10-\\uff19\\uff3f")+"])*",(t=Q).identifier=new RegExp(s+a,"g"),t.identifierStart=new RegExp(s),t.identifierMatch=new RegExp("(?:"+_+"|["+e+i+n+"])+"),t.newline=/[\n\r\u2028\u2029]/,t.lineBreak=new RegExp("\r\n|"+t.newline.source),t.allLineBreaks=new RegExp(t.lineBreak.source,"g")),Q;var t,e,i,n,_,s,a}var H,Y,Z={},J={};function tt(){if(H)return J;function t(t,i){this.raw_options=e(t,i),this.disabled=this._get_boolean("disabled"),this.eol=this._get_characters("eol","auto"),this.end_with_newline=this._get_boolean("end_with_newline"),this.indent_size=this._get_number("indent_size",4),this.indent_char=this._get_characters("indent_char"," "),this.indent_level=this._get_number("indent_level"),this.preserve_newlines=this._get_boolean("preserve_newlines",!0),this.max_preserve_newlines=this._get_number("max_preserve_newlines",32786),this.preserve_newlines||(this.max_preserve_newlines=0),this.indent_with_tabs=this._get_boolean("indent_with_tabs","\t"===this.indent_char),this.indent_with_tabs&&(this.indent_char="\t",1===this.indent_size&&(this.indent_size=4)),this.wrap_line_length=this._get_number("wrap_line_length",this._get_number("max_char")),this.indent_empty_lines=this._get_boolean("indent_empty_lines"),this.templating=this._get_selection_list("templating",["auto","none","angular","django","erb","handlebars","php","smarty"],["auto"])}function e(t,e){var n,_={};for(n in t=i(t))n!==e&&(_[n]=t[n]);if(e&&t[e])for(n in t[e])_[n]=t[e][n];return _}function i(t){var e,i={};for(e in t){i[e.replace(/-/g,"_")]=t[e]}return i}return H=1,t.prototype._get_array=function(t,e){var i=this.raw_options[t],n=e||[];return"object"==typeof i?null!==i&&"function"==typeof i.concat&&(n=i.concat()):"string"==typeof i&&(n=i.split(/[^a-zA-Z0-9_\/\-]+/)),n},t.prototype._get_boolean=function(t,e){var i=this.raw_options[t];return void 0===i?!!e:!!i},t.prototype._get_characters=function(t,e){var i=this.raw_options[t],n=e||"";return"string"==typeof i&&(n=i.replace(/\\r/,"\r").replace(/\\n/,"\n").replace(/\\t/,"\t")),n},t.prototype._get_number=function(t,e){var i=this.raw_options[t];e=parseInt(e,10),isNaN(e)&&(e=0);var n=parseInt(i,10);return isNaN(n)&&(n=e),n},t.prototype._get_selection=function(t,e,i){var n=this._get_selection_list(t,e,i);if(1!==n.length)throw new Error("Invalid Option Value: The option '"+t+"' can only be one of the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return n[0]},t.prototype._get_selection_list=function(t,e,i){if(!e||0===e.length)throw new Error("Selection list cannot be empty.");if(i=i||[e[0]],!this._is_valid_selection(i,e))throw new Error("Invalid Default Value!");var n=this._get_array(t,i);if(!this._is_valid_selection(n,e))throw new Error("Invalid Option Value: The option '"+t+"' can contain only the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return n},t.prototype._is_valid_selection=function(t,e){return t.length&&e.length&&!t.some((function(t){return-1===e.indexOf(t)}))},J.Options=t,J.normalizeOpts=i,J.mergeOpts=e,J}function et(){if(Y)return Z;Y=1;var t=tt().Options,e=["before-newline","after-newline","preserve-newline"];function i(i){t.call(this,i,"js");var n=this.raw_options.brace_style||null;"expand-strict"===n?this.raw_options.brace_style="expand":"collapse-preserve-inline"===n?this.raw_options.brace_style="collapse,preserve-inline":void 0!==this.raw_options.braces_on_own_line&&(this.raw_options.brace_style=this.raw_options.braces_on_own_line?"expand":"collapse");var _=this._get_selection_list("brace_style",["collapse","expand","end-expand","none","preserve-inline"]);this.brace_preserve_inline=!1,this.brace_style="collapse";for(var s=0;s<_.length;s++)"preserve-inline"===_[s]?this.brace_preserve_inline=!0:this.brace_style=_[s];this.unindent_chained_methods=this._get_boolean("unindent_chained_methods"),this.break_chained_methods=this._get_boolean("break_chained_methods"),this.space_in_paren=this._get_boolean("space_in_paren"),this.space_in_empty_paren=this._get_boolean("space_in_empty_paren"),this.jslint_happy=this._get_boolean("jslint_happy"),this.space_after_anon_function=this._get_boolean("space_after_anon_function"),this.space_after_named_function=this._get_boolean("space_after_named_function"),this.keep_array_indentation=this._get_boolean("keep_array_indentation"),this.space_before_conditional=this._get_boolean("space_before_conditional",!0),this.unescape_strings=this._get_boolean("unescape_strings"),this.e4x=this._get_boolean("e4x"),this.comma_first=this._get_boolean("comma_first"),this.operator_position=this._get_selection("operator_position",e),this.test_output_raw=this._get_boolean("test_output_raw"),this.jslint_happy&&(this.space_after_anon_function=!0)}return i.prototype=new t,Z.Options=i,Z}var it,nt={},_t={};function st(){if(it)return _t;it=1;var t=RegExp.prototype.hasOwnProperty("sticky");function e(t){this.__input=t||"",this.__input_length=this.__input.length,this.__position=0}return e.prototype.restart=function(){this.__position=0},e.prototype.back=function(){this.__position>0&&(this.__position-=1)},e.prototype.hasNext=function(){return this.__position<this.__input_length},e.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__input.charAt(this.__position),this.__position+=1),t},e.prototype.peek=function(t){var e=null;return t=t||0,(t+=this.__position)>=0&&t<this.__input_length&&(e=this.__input.charAt(t)),e},e.prototype.__match=function(e,i){e.lastIndex=i;var n=e.exec(this.__input);return!n||t&&e.sticky||n.index!==i&&(n=null),n},e.prototype.test=function(t,e){return e=e||0,(e+=this.__position)>=0&&e<this.__input_length&&!!this.__match(t,e)},e.prototype.testChar=function(t,e){var i=this.peek(e);return t.lastIndex=0,null!==i&&t.test(i)},e.prototype.match=function(t){var e=this.__match(t,this.__position);return e?this.__position+=e[0].length:e=null,e},e.prototype.read=function(t,e,i){var n,_="";return t&&(n=this.match(t))&&(_+=n[0]),!e||!n&&t||(_+=this.readUntil(e,i)),_},e.prototype.readUntil=function(t,e){var i,n=this.__position;t.lastIndex=this.__position;var _=t.exec(this.__input);return _?(n=_.index,e&&(n+=_[0].length)):n=this.__input_length,i=this.__input.substring(this.__position,n),this.__position=n,i},e.prototype.readUntilAfter=function(t){return this.readUntil(t,!0)},e.prototype.get_regexp=function(e,i){var n=null,_="g";return i&&t&&(_="y"),"string"==typeof e&&""!==e?n=new RegExp(e,_):e&&(n=new RegExp(e.source,_)),n},e.prototype.get_literal_regexp=function(t){return RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"))},e.prototype.peekUntilAfter=function(t){var e=this.__position,i=this.readUntilAfter(t);return this.__position=e,i},e.prototype.lookBack=function(t){var e=this.__position-1;return e>=t.length&&this.__input.substring(e-t.length,e).toLowerCase()===t},_t.InputScanner=e,_t}var at,rt={},ot={};var ut,pt,ht,lt={},ct={};function dt(){if(ut)return ct;function t(t,e){this._input=t,this._starting_pattern=null,this._match_pattern=null,this._until_pattern=null,this._until_after=!1,e&&(this._starting_pattern=this._input.get_regexp(e._starting_pattern,!0),this._match_pattern=this._input.get_regexp(e._match_pattern,!0),this._until_pattern=this._input.get_regexp(e._until_pattern),this._until_after=e._until_after)}return ut=1,t.prototype.read=function(){var t=this._input.read(this._starting_pattern);return this._starting_pattern&&!t||(t+=this._input.read(this._match_pattern,this._until_pattern,this._until_after)),t},t.prototype.read_match=function(){return this._input.match(this._match_pattern)},t.prototype.until_after=function(t){var e=this._create();return e._until_after=!0,e._until_pattern=this._input.get_regexp(t),e._update(),e},t.prototype.until=function(t){var e=this._create();return e._until_after=!1,e._until_pattern=this._input.get_regexp(t),e._update(),e},t.prototype.starting_with=function(t){var e=this._create();return e._starting_pattern=this._input.get_regexp(t,!0),e._update(),e},t.prototype.matching=function(t){var e=this._create();return e._match_pattern=this._input.get_regexp(t,!0),e._update(),e},t.prototype._create=function(){return new t(this._input,this)},t.prototype._update=function(){},ct.Pattern=t,ct}function ft(){if(ht)return rt;ht=1;var t=st().InputScanner,e=G().Token,i=function(){if(at)return ot;function t(t){this.__tokens=[],this.__tokens_length=this.__tokens.length,this.__position=0,this.__parent_token=t}return at=1,t.prototype.restart=function(){this.__position=0},t.prototype.isEmpty=function(){return 0===this.__tokens_length},t.prototype.hasNext=function(){return this.__position<this.__tokens_length},t.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__tokens[this.__position],this.__position+=1),t},t.prototype.peek=function(t){var e=null;return t=t||0,(t+=this.__position)>=0&&t<this.__tokens_length&&(e=this.__tokens[t]),e},t.prototype.add=function(t){this.__parent_token&&(t.parent=this.__parent_token),this.__tokens.push(t),this.__tokens_length+=1},ot.TokenStream=t,ot}().TokenStream,n=function(){if(pt)return lt;pt=1;var t=dt().Pattern;function e(e,i){t.call(this,e,i),i?this._line_regexp=this._input.get_regexp(i._line_regexp):this.__set_whitespace_patterns("",""),this.newline_count=0,this.whitespace_before_token=""}return e.prototype=new t,e.prototype.__set_whitespace_patterns=function(t,e){t+="\\t ",e+="\\n\\r",this._match_pattern=this._input.get_regexp("["+t+e+"]+",!0),this._newline_regexp=this._input.get_regexp("\\r\\n|["+e+"]")},e.prototype.read=function(){this.newline_count=0,this.whitespace_before_token="";var t=this._input.read(this._match_pattern);if(" "===t)this.whitespace_before_token=" ";else if(t){var e=this.__split(this._newline_regexp,t);this.newline_count=e.length-1,this.whitespace_before_token=e[this.newline_count]}return t},e.prototype.matching=function(t,e){var i=this._create();return i.__set_whitespace_patterns(t,e),i._update(),i},e.prototype._create=function(){return new e(this._input,this)},e.prototype.__split=function(t,e){t.lastIndex=0;for(var i=0,n=[],_=t.exec(e);_;)n.push(e.substring(i,_.index)),i=_.index+_[0].length,_=t.exec(e);return i<e.length?n.push(e.substring(i,e.length)):n.push(""),n},lt.WhitespacePattern=e,lt}().WhitespacePattern,_={START:"TK_START",RAW:"TK_RAW",EOF:"TK_EOF"},s=function(e,i){this._input=new t(e),this._options=i||{},this.__tokens=null,this._patterns={},this._patterns.whitespace=new n(this._input)};return s.prototype.tokenize=function(){var t;this._input.restart(),this.__tokens=new i,this._reset();for(var n=new e(_.START,""),s=null,a=[],r=new i;n.type!==_.EOF;){for(t=this._get_next_token(n,s);this._is_comment(t);)r.add(t),t=this._get_next_token(n,s);r.isEmpty()||(t.comments_before=r,r=new i),t.parent=s,this._is_opening(t)?(a.push(s),s=t):s&&this._is_closing(t,s)&&(t.opened=s,s.closed=t,s=a.pop(),t.parent=s),t.previous=n,n.next=t,this.__tokens.add(t),n=t}return this.__tokens},s.prototype._is_first_token=function(){return this.__tokens.isEmpty()},s.prototype._reset=function(){},s.prototype._get_next_token=function(t,e){this._readWhitespace();var i=this._input.read(/.+/g);return i?this._create_token(_.RAW,i):this._create_token(_.EOF,"")},s.prototype._is_comment=function(t){return!1},s.prototype._is_opening=function(t){return!1},s.prototype._is_closing=function(t,e){return!1},s.prototype._create_token=function(t,i){return new e(t,i,this._patterns.whitespace.newline_count,this._patterns.whitespace.whitespace_before_token)},s.prototype._readWhitespace=function(){return this._patterns.whitespace.read()},rt.Tokenizer=s,rt.TOKEN=_,rt}var gt,mt={};function bt(){if(gt)return mt;function t(t,e){t="string"==typeof t?t:t.source,e="string"==typeof e?e:e.source,this.__directives_block_pattern=new RegExp(t+/ beautify( \w+[:]\w+)+ /.source+e,"g"),this.__directive_pattern=/ (\w+)[:](\w+)/g,this.__directives_end_ignore_pattern=new RegExp(t+/\sbeautify\signore:end\s/.source+e,"g")}return gt=1,t.prototype.get_directives=function(t){if(!t.match(this.__directives_block_pattern))return null;var e={};this.__directive_pattern.lastIndex=0;for(var i=this.__directive_pattern.exec(t);i;)e[i[1]]=i[2],i=this.__directive_pattern.exec(t);return e},t.prototype.readIgnored=function(t){return t.readUntilAfter(this.__directives_end_ignore_pattern)},mt.Directives=t,mt}var kt,yt,wt,xt,vt={};function Et(){if(kt)return vt;kt=1;var t=dt().Pattern,e={django:!1,erb:!1,handlebars:!1,php:!1,smarty:!1,angular:!1};function i(i,n){t.call(this,i,n),this.__template_pattern=null,this._disabled=Object.assign({},e),this._excluded=Object.assign({},e),n&&(this.__template_pattern=this._input.get_regexp(n.__template_pattern),this._excluded=Object.assign(this._excluded,n._excluded),this._disabled=Object.assign(this._disabled,n._disabled));var _=new t(i);this.__patterns={handlebars_comment:_.starting_with(/{{!--/).until_after(/--}}/),handlebars_unescaped:_.starting_with(/{{{/).until_after(/}}}/),handlebars:_.starting_with(/{{/).until_after(/}}/),php:_.starting_with(/<\?(?:[= ]|php)/).until_after(/\?>/),erb:_.starting_with(/<%[^%]/).until_after(/[^%]%>/),django:_.starting_with(/{%/).until_after(/%}/),django_value:_.starting_with(/{{/).until_after(/}}/),django_comment:_.starting_with(/{#/).until_after(/#}/),smarty:_.starting_with(/{(?=[^}{\s\n])/).until_after(/[^\s\n]}/),smarty_comment:_.starting_with(/{\*/).until_after(/\*}/),smarty_literal:_.starting_with(/{literal}/).until_after(/{\/literal}/)}}return i.prototype=new t,i.prototype._create=function(){return new i(this._input,this)},i.prototype._update=function(){this.__set_templated_pattern()},i.prototype.disable=function(t){var e=this._create();return e._disabled[t]=!0,e._update(),e},i.prototype.read_options=function(t){var i=this._create();for(var n in e)i._disabled[n]=-1===t.templating.indexOf(n);return i._update(),i},i.prototype.exclude=function(t){var e=this._create();return e._excluded[t]=!0,e._update(),e},i.prototype.read=function(){var t="";t=this._match_pattern?this._input.read(this._starting_pattern):this._input.read(this._starting_pattern,this.__template_pattern);for(var e=this._read_template();e;)this._match_pattern?e+=this._input.read(this._match_pattern):e+=this._input.readUntil(this.__template_pattern),t+=e,e=this._read_template();return this._until_after&&(t+=this._input.readUntilAfter(this._until_pattern)),t},i.prototype.__set_templated_pattern=function(){var t=[];this._disabled.php||t.push(this.__patterns.php._starting_pattern.source),this._disabled.handlebars||t.push(this.__patterns.handlebars._starting_pattern.source),this._disabled.erb||t.push(this.__patterns.erb._starting_pattern.source),this._disabled.django||(t.push(this.__patterns.django._starting_pattern.source),t.push(this.__patterns.django_value._starting_pattern.source),t.push(this.__patterns.django_comment._starting_pattern.source)),this._disabled.smarty||t.push(this.__patterns.smarty._starting_pattern.source),this._until_pattern&&t.push(this._until_pattern.source),this.__template_pattern=this._input.get_regexp("(?:"+t.join("|")+")")},i.prototype._read_template=function(){var t="",e=this._input.peek();if("<"===e){var i=this._input.peek(1);this._disabled.php||this._excluded.php||"?"!==i||(t=t||this.__patterns.php.read()),this._disabled.erb||this._excluded.erb||"%"!==i||(t=t||this.__patterns.erb.read())}else"{"===e&&(this._disabled.handlebars||this._excluded.handlebars||(t=(t=(t=t||this.__patterns.handlebars_comment.read())||this.__patterns.handlebars_unescaped.read())||this.__patterns.handlebars.read()),this._disabled.django||(this._excluded.django||this._excluded.handlebars||(t=t||this.__patterns.django_value.read()),this._excluded.django||(t=(t=t||this.__patterns.django_comment.read())||this.__patterns.django.read())),this._disabled.smarty||this._disabled.django&&this._disabled.handlebars&&(t=(t=(t=t||this.__patterns.smarty_comment.read())||this.__patterns.smarty_literal.read())||this.__patterns.smarty.read()));return t},vt.TemplatablePattern=i,vt}function Ot(){if(yt)return nt;yt=1;var t=st().InputScanner,e=ft().Tokenizer,i=ft().TOKEN,n=bt().Directives,_=q(),s=dt().Pattern,a=Et().TemplatablePattern;function r(t,e){return-1!==e.indexOf(t)}var o={START_EXPR:"TK_START_EXPR",END_EXPR:"TK_END_EXPR",START_BLOCK:"TK_START_BLOCK",END_BLOCK:"TK_END_BLOCK",WORD:"TK_WORD",RESERVED:"TK_RESERVED",SEMICOLON:"TK_SEMICOLON",STRING:"TK_STRING",EQUALS:"TK_EQUALS",OPERATOR:"TK_OPERATOR",COMMA:"TK_COMMA",BLOCK_COMMENT:"TK_BLOCK_COMMENT",COMMENT:"TK_COMMENT",DOT:"TK_DOT",UNKNOWN:"TK_UNKNOWN",START:i.START,RAW:i.RAW,EOF:i.EOF},u=new n(/\/\*/,/\*\//),p=/0[xX][0123456789abcdefABCDEF_]*n?|0[oO][01234567_]*n?|0[bB][01_]*n?|\d[\d_]*n|(?:\.\d[\d_]*|\d[\d_]*\.?[\d_]*)(?:[eE][+-]?[\d_]+)?/,h=/[0-9]/,l=/[^\d\.]/,c=">>> === !== &&= ??= ||= << && >= ** != == <= >> || ?? |> < / - + > : & % ? ^ | *".split(" "),d=">>>= ... >>= <<= === >>> !== **= &&= ??= ||= => ^= :: /= << <= == && -= >= >> != -- += ** || ?? ++ %= &= *= |= |> = ! ? > < : / ^ - + * & % ~ |";d=(d="\\?\\.(?!\\d) "+(d=d.replace(/[-[\]{}()*+?.,\\^$|#]/g,"\\$&"))).replace(/ /g,"|");var f,g=new RegExp(d),m="continue,try,throw,return,var,let,const,if,switch,case,default,for,while,break,function,import,export".split(","),b=m.concat(["do","in","of","else","get","set","new","catch","finally","typeof","yield","async","await","from","as","class","extends"]),k=new RegExp("^(?:"+b.join("|")+")$"),y=function(t,i){e.call(this,t,i),this._patterns.whitespace=this._patterns.whitespace.matching(/\u00A0\u1680\u180e\u2000-\u200a\u202f\u205f\u3000\ufeff/.source,/\u2028\u2029/.source);var n=new s(this._input),r=new a(this._input).read_options(this._options);this.__patterns={template:r,identifier:r.starting_with(_.identifier).matching(_.identifierMatch),number:n.matching(p),punct:n.matching(g),comment:n.starting_with(/\/\//).until(/[\n\r\u2028\u2029]/),block_comment:n.starting_with(/\/\*/).until_after(/\*\//),html_comment_start:n.matching(/<!--/),html_comment_end:n.matching(/-->/),include:n.starting_with(/#include/).until_after(_.lineBreak),shebang:n.starting_with(/#!/).until_after(_.lineBreak),xml:n.matching(/[\s\S]*?<(\/?)([-a-zA-Z:0-9_.]+|{[^}]+?}|!\[CDATA\[[^\]]*?\]\]|)(\s*{[^}]+?}|\s+[-a-zA-Z:0-9_.]+|\s+[-a-zA-Z:0-9_.]+\s*=\s*('[^']*'|"[^"]*"|{([^{}]|{[^}]+?})+?}))*\s*(\/?)\s*>/),single_quote:r.until(/['\\\n\r\u2028\u2029]/),double_quote:r.until(/["\\\n\r\u2028\u2029]/),template_text:r.until(/[`\\$]/),template_expression:r.until(/[`}\\]/)}};return(y.prototype=new e)._is_comment=function(t){return t.type===o.COMMENT||t.type===o.BLOCK_COMMENT||t.type===o.UNKNOWN},y.prototype._is_opening=function(t){return t.type===o.START_BLOCK||t.type===o.START_EXPR},y.prototype._is_closing=function(t,e){return(t.type===o.END_BLOCK||t.type===o.END_EXPR)&&e&&("]"===t.text&&"["===e.text||")"===t.text&&"("===e.text||"}"===t.text&&"{"===e.text)},y.prototype._reset=function(){f=!1},y.prototype._get_next_token=function(t,e){var i=null;this._readWhitespace();var n=this._input.peek();return null===n?this._create_token(o.EOF,""):i=(i=(i=(i=(i=(i=(i=(i=(i=(i=i||this._read_non_javascript(n))||this._read_string(n))||this._read_pair(n,this._input.peek(1)))||this._read_word(t))||this._read_singles(n))||this._read_comment(n))||this._read_regexp(n,t))||this._read_xml(n,t))||this._read_punctuation())||this._create_token(o.UNKNOWN,this._input.next())},y.prototype._read_word=function(t){var e;return""!==(e=this.__patterns.identifier.read())?(e=e.replace(_.allLineBreaks,"\n"),t.type!==o.DOT&&(t.type!==o.RESERVED||"set"!==t.text&&"get"!==t.text)&&k.test(e)?"in"!==e&&"of"!==e||t.type!==o.WORD&&t.type!==o.STRING?this._create_token(o.RESERVED,e):this._create_token(o.OPERATOR,e):this._create_token(o.WORD,e)):""!==(e=this.__patterns.number.read())?this._create_token(o.WORD,e):void 0},y.prototype._read_singles=function(t){var e=null;return"("===t||"["===t?e=this._create_token(o.START_EXPR,t):")"===t||"]"===t?e=this._create_token(o.END_EXPR,t):"{"===t?e=this._create_token(o.START_BLOCK,t):"}"===t?e=this._create_token(o.END_BLOCK,t):";"===t?e=this._create_token(o.SEMICOLON,t):"."===t&&l.test(this._input.peek(1))?e=this._create_token(o.DOT,t):","===t&&(e=this._create_token(o.COMMA,t)),e&&this._input.next(),e},y.prototype._read_pair=function(t,e){var i=null;return"#"===t&&"{"===e&&(i=this._create_token(o.START_BLOCK,t+e)),i&&(this._input.next(),this._input.next()),i},y.prototype._read_punctuation=function(){var t=this.__patterns.punct.read();if(""!==t)return"="===t?this._create_token(o.EQUALS,t):"?."===t?this._create_token(o.DOT,t):this._create_token(o.OPERATOR,t)},y.prototype._read_non_javascript=function(t){var e="";if("#"===t){if(this._is_first_token()&&(e=this.__patterns.shebang.read()))return this._create_token(o.UNKNOWN,e.trim()+"\n");if(e=this.__patterns.include.read())return this._create_token(o.UNKNOWN,e.trim()+"\n");t=this._input.next();var i="#";if(this._input.hasNext()&&this._input.testChar(h)){do{i+=t=this._input.next()}while(this._input.hasNext()&&"#"!==t&&"="!==t);return"#"===t||("["===this._input.peek()&&"]"===this._input.peek(1)?(i+="[]",this._input.next(),this._input.next()):"{"===this._input.peek()&&"}"===this._input.peek(1)&&(i+="{}",this._input.next(),this._input.next())),this._create_token(o.WORD,i)}this._input.back()}else if("<"===t&&this._is_first_token()){if(e=this.__patterns.html_comment_start.read()){for(;this._input.hasNext()&&!this._input.testChar(_.newline);)e+=this._input.next();return f=!0,this._create_token(o.COMMENT,e)}}else if(f&&"-"===t&&(e=this.__patterns.html_comment_end.read()))return f=!1,this._create_token(o.COMMENT,e);return null},y.prototype._read_comment=function(t){var e=null;if("/"===t){var i="";if("*"===this._input.peek(1)){i=this.__patterns.block_comment.read();var n=u.get_directives(i);n&&"start"===n.ignore&&(i+=u.readIgnored(this._input)),i=i.replace(_.allLineBreaks,"\n"),(e=this._create_token(o.BLOCK_COMMENT,i)).directives=n}else"/"===this._input.peek(1)&&(i=this.__patterns.comment.read(),e=this._create_token(o.COMMENT,i))}return e},y.prototype._read_string=function(e){if("`"===e||"'"===e||'"'===e){var i=this._input.next();return this.has_char_escapes=!1,i+="`"===e?this._read_string_recursive("`",!0,"${"):this._read_string_recursive(e),this.has_char_escapes&&this._options.unescape_strings&&(i=function(e){var i="",n=0,_=new t(e),s=null;for(;_.hasNext();)if((s=_.match(/([\s]|[^\\]|\\\\)+/g))&&(i+=s[0]),"\\"===_.peek()){if(_.next(),"x"===_.peek())s=_.match(/x([0-9A-Fa-f]{2})/g);else{if("u"!==_.peek()){i+="\\",_.hasNext()&&(i+=_.next());continue}(s=_.match(/u([0-9A-Fa-f]{4})/g))||(s=_.match(/u\{([0-9A-Fa-f]+)\}/g))}if(!s)return e;if((n=parseInt(s[1],16))>126&&n<=255&&0===s[0].indexOf("x"))return e;i+=n>=0&&n<32||n>1114111?"\\"+s[0]:34===n||39===n||92===n?"\\"+String.fromCharCode(n):String.fromCharCode(n)}return i}(i)),this._input.peek()===e&&(i+=this._input.next()),i=i.replace(_.allLineBreaks,"\n"),this._create_token(o.STRING,i)}return null},y.prototype._allow_regexp_or_xml=function(t){return t.type===o.RESERVED&&r(t.text,["return","case","throw","else","do","typeof","yield"])||t.type===o.END_EXPR&&")"===t.text&&t.opened.previous.type===o.RESERVED&&r(t.opened.previous.text,["if","while","for"])||r(t.type,[o.COMMENT,o.START_EXPR,o.START_BLOCK,o.START,o.END_BLOCK,o.OPERATOR,o.EQUALS,o.EOF,o.SEMICOLON,o.COMMA])},y.prototype._read_regexp=function(t,e){if("/"===t&&this._allow_regexp_or_xml(e)){for(var i=this._input.next(),n=!1,s=!1;this._input.hasNext()&&(n||s||this._input.peek()!==t)&&!this._input.testChar(_.newline);)i+=this._input.peek(),n?n=!1:(n="\\"===this._input.peek(),"["===this._input.peek()?s=!0:"]"===this._input.peek()&&(s=!1)),this._input.next();return this._input.peek()===t&&(i+=this._input.next(),i+=this._input.read(_.identifier)),this._create_token(o.STRING,i)}return null},y.prototype._read_xml=function(t,e){if(this._options.e4x&&"<"===t&&this._allow_regexp_or_xml(e)){var i="",n=this.__patterns.xml.read_match();if(n){for(var s=n[2].replace(/^{\s+/,"{").replace(/\s+}$/,"}"),a=0===s.indexOf("{"),r=0;n;){var u=!!n[1],p=n[2];if(!(!!n[n.length-1]||"![CDATA["===p.slice(0,8))&&(p===s||a&&p.replace(/^{\s+/,"{").replace(/\s+}$/,"}"))&&(u?--r:++r),i+=n[0],r<=0)break;n=this.__patterns.xml.read_match()}return n||(i+=this._input.match(/[\s\S]*/g)[0]),i=i.replace(_.allLineBreaks,"\n"),this._create_token(o.STRING,i)}}return null},y.prototype._read_string_recursive=function(t,e,i){var n,s;"'"===t?s=this.__patterns.single_quote:'"'===t?s=this.__patterns.double_quote:"`"===t?s=this.__patterns.template_text:"}"===t&&(s=this.__patterns.template_expression);for(var a=s.read(),r="";this._input.hasNext();){if((r=this._input.next())===t||!e&&_.newline.test(r)){this._input.back();break}"\\"===r&&this._input.hasNext()?("x"===(n=this._input.peek())||"u"===n?this.has_char_escapes=!0:"\r"===n&&"\n"===this._input.peek(1)&&this._input.next(),r+=this._input.next()):i&&("${"===i&&"$"===r&&"{"===this._input.peek()&&(r+=this._input.next()),i===r&&(r+="`"===t?this._read_string_recursive("}",e,"`"):this._read_string_recursive("`",e,"${"),this._input.hasNext()&&(r+=this._input.next()))),a+=r+=s.read()}return a},nt.Tokenizer=y,nt.TOKEN=o,nt.positionable_operators=c.slice(),nt.line_starters=m.slice(),nt}function Tt(){if(wt)return U;wt=1;var t=V().Output,e=G().Token,i=q(),n=et().Options,_=Ot().Tokenizer,s=Ot().line_starters,a=Ot().positionable_operators,r=Ot().TOKEN;function o(t,e){return-1!==e.indexOf(t)}function u(t,e){return t&&t.type===r.RESERVED&&t.text===e}function p(t,e){return t&&t.type===r.RESERVED&&o(t.text,e)}var h=["case","return","do","if","throw","else","await","break","continue","async"],l=function(t){for(var e={},i=0;i<t.length;i++)e[t[i].replace(/-/g,"_")]=t[i];return e}(["before-newline","after-newline","preserve-newline"]),c=[l.before_newline,l.preserve_newline],d="BlockStatement",f="Statement",g="ObjectLiteral",m="ArrayLiteral",b="ForInitializer",k="Conditional",y="Expression";function w(t,e){e.multiline_frame||e.mode===b||e.mode===k||t.remove_indent(e.start_line_index)}function x(t){return t===m}function v(t){return o(t,[y,b,k])}function E(t,e){e=e||{},this._source_text=t||"",this._output=null,this._tokens=null,this._last_last_text=null,this._flags=null,this._previous_flags=null,this._flag_store=null,this._options=new n(e)}E.prototype.create_flags=function(t,i){var n=0;return t&&(n=t.indentation_level,!this._output.just_added_newline()&&t.line_indent_level>n&&(n=t.line_indent_level)),{mode:i,parent:t,last_token:t?t.last_token:new e(r.START_BLOCK,""),last_word:t?t.last_word:"",declaration_statement:!1,declaration_assignment:!1,multiline_frame:!1,inline_frame:!1,if_block:!1,else_block:!1,class_start_block:!1,do_block:!1,do_while:!1,import_block:!1,in_case_statement:!1,in_case:!1,case_body:!1,case_block:!1,indentation_level:n,alignment:0,line_indent_level:t?t.line_indent_level:n,start_line_index:this._output.get_line_number(),ternary_depth:0}},E.prototype._reset=function(e){var i=e.match(/^[\t ]*/)[0];this._last_last_text="",this._output=new t(this._options,i),this._output.raw=this._options.test_output_raw,this._flag_store=[],this.set_mode(d);var n=new _(e,this._options);return this._tokens=n.tokenize(),e},E.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var t=this._reset(this._source_text),e=this._options.eol;"auto"===this._options.eol&&(e="\n",t&&i.lineBreak.test(t||"")&&(e=t.match(i.lineBreak)[0]));for(var n=this._tokens.next();n;)this.handle_token(n),this._last_last_text=this._flags.last_token.text,this._flags.last_token=n,n=this._tokens.next();return this._output.get_code(e)},E.prototype.handle_token=function(t,e){t.type===r.START_EXPR?this.handle_start_expr(t):t.type===r.END_EXPR?this.handle_end_expr(t):t.type===r.START_BLOCK?this.handle_start_block(t):t.type===r.END_BLOCK?this.handle_end_block(t):t.type===r.WORD||t.type===r.RESERVED?this.handle_word(t):t.type===r.SEMICOLON?this.handle_semicolon(t):t.type===r.STRING?this.handle_string(t):t.type===r.EQUALS?this.handle_equals(t):t.type===r.OPERATOR?this.handle_operator(t):t.type===r.COMMA?this.handle_comma(t):t.type===r.BLOCK_COMMENT?this.handle_block_comment(t,e):t.type===r.COMMENT?this.handle_comment(t,e):t.type===r.DOT?this.handle_dot(t):t.type===r.EOF?this.handle_eof(t):(t.type,r.UNKNOWN,this.handle_unknown(t,e))},E.prototype.handle_whitespace_and_comments=function(t,e){var i=t.newlines,n=this._options.keep_array_indentation&&x(this._flags.mode);if(t.comments_before)for(var _=t.comments_before.next();_;)this.handle_whitespace_and_comments(_,e),this.handle_token(_,e),_=t.comments_before.next();if(n)for(var s=0;s<i;s+=1)this.print_newline(s>0,e);else if(this._options.max_preserve_newlines&&i>this._options.max_preserve_newlines&&(i=this._options.max_preserve_newlines),this._options.preserve_newlines&&i>1){this.print_newline(!1,e);for(var a=1;a<i;a+=1)this.print_newline(!0,e)}};var O=["async","break","continue","return","throw","yield"];return E.prototype.allow_wrap_or_preserved_newline=function(t,e){if(e=void 0!==e&&e,!this._output.just_added_newline()){var i=this._options.preserve_newlines&&t.newlines||e;if(o(this._flags.last_token.text,a)||o(t.text,a)){var n=o(this._flags.last_token.text,a)&&o(this._options.operator_position,c)||o(t.text,a);i=i&&n}if(i)this.print_newline(!1,!0);else if(this._options.wrap_line_length){if(p(this._flags.last_token,O))return;this._output.set_wrap_point()}}},E.prototype.print_newline=function(t,e){if(!e&&";"!==this._flags.last_token.text&&","!==this._flags.last_token.text&&"="!==this._flags.last_token.text&&(this._flags.last_token.type!==r.OPERATOR||"--"===this._flags.last_token.text||"++"===this._flags.last_token.text))for(var i=this._tokens.peek();!(this._flags.mode!==f||this._flags.if_block&&u(i,"else")||this._flags.do_block);)this.restore_mode();this._output.add_new_line(t)&&(this._flags.multiline_frame=!0)},E.prototype.print_token_line_indentation=function(t){this._output.just_added_newline()&&(this._options.keep_array_indentation&&t.newlines&&("["===t.text||x(this._flags.mode))?(this._output.current_line.set_indent(-1),this._output.current_line.push(t.whitespace_before),this._output.space_before_token=!1):this._output.set_indent(this._flags.indentation_level,this._flags.alignment)&&(this._flags.line_indent_level=this._flags.indentation_level))},E.prototype.print_token=function(t){if(this._output.raw)this._output.add_raw_token(t);else{if(this._options.comma_first&&t.previous&&t.previous.type===r.COMMA&&this._output.just_added_newline()&&","===this._output.previous_line.last()){var e=this._output.previous_line.pop();this._output.previous_line.is_empty()&&(this._output.previous_line.push(e),this._output.trim(!0),this._output.current_line.pop(),this._output.trim()),this.print_token_line_indentation(t),this._output.add_token(","),this._output.space_before_token=!0}this.print_token_line_indentation(t),this._output.non_breaking_space=!0,this._output.add_token(t.text),this._output.previous_token_wrapped&&(this._flags.multiline_frame=!0)}},E.prototype.indent=function(){this._flags.indentation_level+=1,this._output.set_indent(this._flags.indentation_level,this._flags.alignment)},E.prototype.deindent=function(){this._flags.indentation_level>0&&(!this._flags.parent||this._flags.indentation_level>this._flags.parent.indentation_level)&&(this._flags.indentation_level-=1,this._output.set_indent(this._flags.indentation_level,this._flags.alignment))},E.prototype.set_mode=function(t){this._flags?(this._flag_store.push(this._flags),this._previous_flags=this._flags):this._previous_flags=this.create_flags(null,t),this._flags=this.create_flags(this._previous_flags,t),this._output.set_indent(this._flags.indentation_level,this._flags.alignment)},E.prototype.restore_mode=function(){this._flag_store.length>0&&(this._previous_flags=this._flags,this._flags=this._flag_store.pop(),this._previous_flags.mode===f&&w(this._output,this._previous_flags),this._output.set_indent(this._flags.indentation_level,this._flags.alignment))},E.prototype.start_of_object_property=function(){return this._flags.parent.mode===g&&this._flags.mode===f&&(":"===this._flags.last_token.text&&0===this._flags.ternary_depth||p(this._flags.last_token,["get","set"]))},E.prototype.start_of_statement=function(t){var e=!1;return!!(e=(e=(e=(e=(e=(e=(e=e||p(this._flags.last_token,["var","let","const"])&&t.type===r.WORD)||u(this._flags.last_token,"do"))||!(this._flags.parent.mode===g&&this._flags.mode===f)&&p(this._flags.last_token,O)&&!t.newlines)||u(this._flags.last_token,"else")&&!(u(t,"if")&&!t.comments_before))||this._flags.last_token.type===r.END_EXPR&&(this._previous_flags.mode===b||this._previous_flags.mode===k))||this._flags.last_token.type===r.WORD&&this._flags.mode===d&&!this._flags.in_case&&!("--"===t.text||"++"===t.text)&&"function"!==this._last_last_text&&t.type!==r.WORD&&t.type!==r.RESERVED)||this._flags.mode===g&&(":"===this._flags.last_token.text&&0===this._flags.ternary_depth||p(this._flags.last_token,["get","set"])))&&(this.set_mode(f),this.indent(),this.handle_whitespace_and_comments(t,!0),this.start_of_object_property()||this.allow_wrap_or_preserved_newline(t,p(t,["do","for","if","while"])),!0)},E.prototype.handle_start_expr=function(t){this.start_of_statement(t)||this.handle_whitespace_and_comments(t);var e=y;if("["===t.text){if(this._flags.last_token.type===r.WORD||")"===this._flags.last_token.text)return p(this._flags.last_token,s)&&(this._output.space_before_token=!0),this.print_token(t),this.set_mode(e),this.indent(),void(this._options.space_in_paren&&(this._output.space_before_token=!0));e=m,x(this._flags.mode)&&("["!==this._flags.last_token.text&&(","!==this._flags.last_token.text||"]"!==this._last_last_text&&"}"!==this._last_last_text)||this._options.keep_array_indentation||this.print_newline()),o(this._flags.last_token.type,[r.START_EXPR,r.END_EXPR,r.WORD,r.OPERATOR,r.DOT])||(this._output.space_before_token=!0)}else{if(this._flags.last_token.type===r.RESERVED)"for"===this._flags.last_token.text?(this._output.space_before_token=this._options.space_before_conditional,e=b):o(this._flags.last_token.text,["if","while","switch"])?(this._output.space_before_token=this._options.space_before_conditional,e=k):o(this._flags.last_word,["await","async"])?this._output.space_before_token=!0:"import"===this._flags.last_token.text&&""===t.whitespace_before?this._output.space_before_token=!1:(o(this._flags.last_token.text,s)||"catch"===this._flags.last_token.text)&&(this._output.space_before_token=!0);else if(this._flags.last_token.type===r.EQUALS||this._flags.last_token.type===r.OPERATOR)this.start_of_object_property()||this.allow_wrap_or_preserved_newline(t);else if(this._flags.last_token.type===r.WORD){this._output.space_before_token=!1;var i=this._tokens.peek(-3);if(this._options.space_after_named_function&&i){var n=this._tokens.peek(-4);p(i,["async","function"])||"*"===i.text&&p(n,["async","function"])?this._output.space_before_token=!0:this._flags.mode===g?"{"!==i.text&&","!==i.text&&("*"!==i.text||"{"!==n.text&&","!==n.text)||(this._output.space_before_token=!0):this._flags.parent&&this._flags.parent.class_start_block&&(this._output.space_before_token=!0)}}else this.allow_wrap_or_preserved_newline(t);(this._flags.last_token.type===r.RESERVED&&("function"===this._flags.last_word||"typeof"===this._flags.last_word)||"*"===this._flags.last_token.text&&(o(this._last_last_text,["function","yield"])||this._flags.mode===g&&o(this._last_last_text,["{",","])))&&(this._output.space_before_token=this._options.space_after_anon_function)}";"===this._flags.last_token.text||this._flags.last_token.type===r.START_BLOCK?this.print_newline():this._flags.last_token.type!==r.END_EXPR&&this._flags.last_token.type!==r.START_EXPR&&this._flags.last_token.type!==r.END_BLOCK&&"."!==this._flags.last_token.text&&this._flags.last_token.type!==r.COMMA||this.allow_wrap_or_preserved_newline(t,t.newlines),this.print_token(t),this.set_mode(e),this._options.space_in_paren&&(this._output.space_before_token=!0),this.indent()},E.prototype.handle_end_expr=function(t){for(;this._flags.mode===f;)this.restore_mode();this.handle_whitespace_and_comments(t),this._flags.multiline_frame&&this.allow_wrap_or_preserved_newline(t,"]"===t.text&&x(this._flags.mode)&&!this._options.keep_array_indentation),this._options.space_in_paren&&(this._flags.last_token.type!==r.START_EXPR||this._options.space_in_empty_paren?this._output.space_before_token=!0:(this._output.trim(),this._output.space_before_token=!1)),this.deindent(),this.print_token(t),this.restore_mode(),w(this._output,this._previous_flags),this._flags.do_while&&this._previous_flags.mode===k&&(this._previous_flags.mode=y,this._flags.do_block=!1,this._flags.do_while=!1)},E.prototype.handle_start_block=function(t){this.handle_whitespace_and_comments(t);var e=this._tokens.peek(),i=this._tokens.peek(1);"switch"===this._flags.last_word&&this._flags.last_token.type===r.END_EXPR?(this.set_mode(d),this._flags.in_case_statement=!0):this._flags.case_body?this.set_mode(d):i&&(o(i.text,[":",","])&&o(e.type,[r.STRING,r.WORD,r.RESERVED])||o(e.text,["get","set","..."])&&o(i.type,[r.WORD,r.RESERVED]))?o(this._last_last_text,["class","interface"])&&!o(i.text,[":",","])?this.set_mode(d):this.set_mode(g):this._flags.last_token.type===r.OPERATOR&&"=>"===this._flags.last_token.text?this.set_mode(d):o(this._flags.last_token.type,[r.EQUALS,r.START_EXPR,r.COMMA,r.OPERATOR])||p(this._flags.last_token,["return","throw","import","default"])?this.set_mode(g):this.set_mode(d),this._flags.last_token&&p(this._flags.last_token.previous,["class","extends"])&&(this._flags.class_start_block=!0);var n=!e.comments_before&&"}"===e.text,_=n&&"function"===this._flags.last_word&&this._flags.last_token.type===r.END_EXPR;if(this._options.brace_preserve_inline){var s=0,a=null;this._flags.inline_frame=!0;do{if(s+=1,(a=this._tokens.peek(s-1)).newlines){this._flags.inline_frame=!1;break}}while(a.type!==r.EOF&&(a.type!==r.END_BLOCK||a.opened!==t))}("expand"===this._options.brace_style||"none"===this._options.brace_style&&t.newlines)&&!this._flags.inline_frame?this._flags.last_token.type!==r.OPERATOR&&(_||this._flags.last_token.type===r.EQUALS||p(this._flags.last_token,h)&&"else"!==this._flags.last_token.text)?this._output.space_before_token=!0:this.print_newline(!1,!0):(!x(this._previous_flags.mode)||this._flags.last_token.type!==r.START_EXPR&&this._flags.last_token.type!==r.COMMA||((this._flags.last_token.type===r.COMMA||this._options.space_in_paren)&&(this._output.space_before_token=!0),(this._flags.last_token.type===r.COMMA||this._flags.last_token.type===r.START_EXPR&&this._flags.inline_frame)&&(this.allow_wrap_or_preserved_newline(t),this._previous_flags.multiline_frame=this._previous_flags.multiline_frame||this._flags.multiline_frame,this._flags.multiline_frame=!1)),this._flags.last_token.type!==r.OPERATOR&&this._flags.last_token.type!==r.START_EXPR&&(o(this._flags.last_token.type,[r.START_BLOCK,r.SEMICOLON])&&!this._flags.inline_frame?this.print_newline():this._output.space_before_token=!0)),this.print_token(t),this.indent(),n||this._options.brace_preserve_inline&&this._flags.inline_frame||this.print_newline()},E.prototype.handle_end_block=function(t){for(this.handle_whitespace_and_comments(t);this._flags.mode===f;)this.restore_mode();var e=this._flags.last_token.type===r.START_BLOCK;this._flags.inline_frame&&!e?this._output.space_before_token=!0:"expand"===this._options.brace_style?e||this.print_newline():e||(x(this._flags.mode)&&this._options.keep_array_indentation?(this._options.keep_array_indentation=!1,this.print_newline(),this._options.keep_array_indentation=!0):this.print_newline()),this.restore_mode(),this.print_token(t)},E.prototype.handle_word=function(t){if(t.type===r.RESERVED)if(o(t.text,["set","get"])&&this._flags.mode!==g)t.type=r.WORD;else if("import"===t.text&&o(this._tokens.peek().text,["(","."]))t.type=r.WORD;else if(o(t.text,["as","from"])&&!this._flags.import_block)t.type=r.WORD;else if(this._flags.mode===g){":"===this._tokens.peek().text&&(t.type=r.WORD)}if(this.start_of_statement(t)?p(this._flags.last_token,["var","let","const"])&&t.type===r.WORD&&(this._flags.declaration_statement=!0):!t.newlines||v(this._flags.mode)||this._flags.last_token.type===r.OPERATOR&&"--"!==this._flags.last_token.text&&"++"!==this._flags.last_token.text||this._flags.last_token.type===r.EQUALS||!this._options.preserve_newlines&&p(this._flags.last_token,["var","let","const","set","get"])?this.handle_whitespace_and_comments(t):(this.handle_whitespace_and_comments(t),this.print_newline()),this._flags.do_block&&!this._flags.do_while){if(u(t,"while"))return this._output.space_before_token=!0,this.print_token(t),this._output.space_before_token=!0,void(this._flags.do_while=!0);this.print_newline(),this._flags.do_block=!1}if(this._flags.if_block)if(!this._flags.else_block&&u(t,"else"))this._flags.else_block=!0;else{for(;this._flags.mode===f;)this.restore_mode();this._flags.if_block=!1,this._flags.else_block=!1}if(this._flags.in_case_statement&&p(t,["case","default"]))return this.print_newline(),this._flags.case_block||!this._flags.case_body&&!this._options.jslint_happy||this.deindent(),this._flags.case_body=!1,this.print_token(t),void(this._flags.in_case=!0);if(this._flags.last_token.type!==r.COMMA&&this._flags.last_token.type!==r.START_EXPR&&this._flags.last_token.type!==r.EQUALS&&this._flags.last_token.type!==r.OPERATOR||this.start_of_object_property()||o(this._flags.last_token.text,["+","-"])&&":"===this._last_last_text&&this._flags.parent.mode===g||this.allow_wrap_or_preserved_newline(t),u(t,"function"))return(o(this._flags.last_token.text,["}",";"])||this._output.just_added_newline()&&!o(this._flags.last_token.text,["(","[","{",":","=",","])&&this._flags.last_token.type!==r.OPERATOR)&&(this._output.just_added_blankline()||t.comments_before||(this.print_newline(),this.print_newline(!0))),this._flags.last_token.type===r.RESERVED||this._flags.last_token.type===r.WORD?p(this._flags.last_token,["get","set","new","export"])||p(this._flags.last_token,O)||u(this._flags.last_token,"default")&&"export"===this._last_last_text||"declare"===this._flags.last_token.text?this._output.space_before_token=!0:this.print_newline():this._flags.last_token.type===r.OPERATOR||"="===this._flags.last_token.text?this._output.space_before_token=!0:(this._flags.multiline_frame||!v(this._flags.mode)&&!x(this._flags.mode))&&this.print_newline(),this.print_token(t),void(this._flags.last_word=t.text);var e="NONE";(this._flags.last_token.type===r.END_BLOCK?this._previous_flags.inline_frame?e="SPACE":p(t,["else","catch","finally","from"])?"expand"===this._options.brace_style||"end-expand"===this._options.brace_style||"none"===this._options.brace_style&&t.newlines?e="NEWLINE":(e="SPACE",this._output.space_before_token=!0):e="NEWLINE":this._flags.last_token.type===r.SEMICOLON&&this._flags.mode===d?e="NEWLINE":this._flags.last_token.type===r.SEMICOLON&&v(this._flags.mode)?e="SPACE":this._flags.last_token.type===r.STRING?e="NEWLINE":this._flags.last_token.type===r.RESERVED||this._flags.last_token.type===r.WORD||"*"===this._flags.last_token.text&&(o(this._last_last_text,["function","yield"])||this._flags.mode===g&&o(this._last_last_text,["{",","]))?e="SPACE":this._flags.last_token.type===r.START_BLOCK?e=this._flags.inline_frame?"SPACE":"NEWLINE":this._flags.last_token.type===r.END_EXPR&&(this._output.space_before_token=!0,e="NEWLINE"),p(t,s)&&")"!==this._flags.last_token.text&&(e=this._flags.inline_frame||"else"===this._flags.last_token.text||"export"===this._flags.last_token.text?"SPACE":"NEWLINE"),p(t,["else","catch","finally"]))?(this._flags.last_token.type!==r.END_BLOCK||this._previous_flags.mode!==d||"expand"===this._options.brace_style||"end-expand"===this._options.brace_style||"none"===this._options.brace_style&&t.newlines)&&!this._flags.inline_frame?this.print_newline():(this._output.trim(!0),"}"!==this._output.current_line.last()&&this.print_newline(),this._output.space_before_token=!0):"NEWLINE"===e?p(this._flags.last_token,h)||"declare"===this._flags.last_token.text&&p(t,["var","let","const"])?this._output.space_before_token=!0:this._flags.last_token.type!==r.END_EXPR?this._flags.last_token.type===r.START_EXPR&&p(t,["var","let","const"])||":"===this._flags.last_token.text||(u(t,"if")&&u(t.previous,"else")?this._output.space_before_token=!0:this.print_newline()):p(t,s)&&")"!==this._flags.last_token.text&&this.print_newline():this._flags.multiline_frame&&x(this._flags.mode)&&","===this._flags.last_token.text&&"}"===this._last_last_text?this.print_newline():"SPACE"===e&&(this._output.space_before_token=!0);!t.previous||t.previous.type!==r.WORD&&t.previous.type!==r.RESERVED||(this._output.space_before_token=!0),this.print_token(t),this._flags.last_word=t.text,t.type===r.RESERVED&&("do"===t.text?this._flags.do_block=!0:"if"===t.text?this._flags.if_block=!0:"import"===t.text?this._flags.import_block=!0:this._flags.import_block&&u(t,"from")&&(this._flags.import_block=!1))},E.prototype.handle_semicolon=function(t){this.start_of_statement(t)?this._output.space_before_token=!1:this.handle_whitespace_and_comments(t);for(var e=this._tokens.peek();!(this._flags.mode!==f||this._flags.if_block&&u(e,"else")||this._flags.do_block);)this.restore_mode();this._flags.import_block&&(this._flags.import_block=!1),this.print_token(t)},E.prototype.handle_string=function(t){(!t.text.startsWith("`")||0!==t.newlines||""!==t.whitespace_before||")"!==t.previous.text&&this._flags.last_token.type!==r.WORD)&&(this.start_of_statement(t)?this._output.space_before_token=!0:(this.handle_whitespace_and_comments(t),this._flags.last_token.type===r.RESERVED||this._flags.last_token.type===r.WORD||this._flags.inline_frame?this._output.space_before_token=!0:this._flags.last_token.type===r.COMMA||this._flags.last_token.type===r.START_EXPR||this._flags.last_token.type===r.EQUALS||this._flags.last_token.type===r.OPERATOR?this.start_of_object_property()||this.allow_wrap_or_preserved_newline(t):!t.text.startsWith("`")||this._flags.last_token.type!==r.END_EXPR||"]"!==t.previous.text&&")"!==t.previous.text||0!==t.newlines?this.print_newline():this._output.space_before_token=!0)),this.print_token(t)},E.prototype.handle_equals=function(t){this.start_of_statement(t)||this.handle_whitespace_and_comments(t),this._flags.declaration_statement&&(this._flags.declaration_assignment=!0),this._output.space_before_token=!0,this.print_token(t),this._output.space_before_token=!0},E.prototype.handle_comma=function(t){this.handle_whitespace_and_comments(t,!0),this.print_token(t),this._output.space_before_token=!0,this._flags.declaration_statement?(v(this._flags.parent.mode)&&(this._flags.declaration_assignment=!1),this._flags.declaration_assignment?(this._flags.declaration_assignment=!1,this.print_newline(!1,!0)):this._options.comma_first&&this.allow_wrap_or_preserved_newline(t)):this._flags.mode===g||this._flags.mode===f&&this._flags.parent.mode===g?(this._flags.mode===f&&this.restore_mode(),this._flags.inline_frame||this.print_newline()):this._options.comma_first&&this.allow_wrap_or_preserved_newline(t)},E.prototype.handle_operator=function(t){var e="*"===t.text&&(p(this._flags.last_token,["function","yield"])||o(this._flags.last_token.type,[r.START_BLOCK,r.COMMA,r.END_BLOCK,r.SEMICOLON])),i=o(t.text,["-","+"])&&(o(this._flags.last_token.type,[r.START_BLOCK,r.START_EXPR,r.EQUALS,r.OPERATOR])||o(this._flags.last_token.text,s)||","===this._flags.last_token.text);if(this.start_of_statement(t));else{var n=!e;this.handle_whitespace_and_comments(t,n)}if("*"!==t.text||this._flags.last_token.type!==r.DOT)if("::"!==t.text)if(o(t.text,["-","+"])&&this.start_of_object_property())this.print_token(t);else{if(this._flags.last_token.type===r.OPERATOR&&o(this._options.operator_position,c)&&this.allow_wrap_or_preserved_newline(t),":"===t.text&&this._flags.in_case)return this.print_token(t),this._flags.in_case=!1,this._flags.case_body=!0,void(this._tokens.peek().type!==r.START_BLOCK?(this.indent(),this.print_newline(),this._flags.case_block=!1):(this._flags.case_block=!0,this._output.space_before_token=!0));var _=!0,u=!0,g=!1;if(":"===t.text?0===this._flags.ternary_depth?_=!1:(this._flags.ternary_depth-=1,g=!0):"?"===t.text&&(this._flags.ternary_depth+=1),!i&&!e&&this._options.preserve_newlines&&o(t.text,a)){var m=":"===t.text,b=m&&g,k=m&&!g;switch(this._options.operator_position){case l.before_newline:return this._output.space_before_token=!k,this.print_token(t),m&&!b||this.allow_wrap_or_preserved_newline(t),void(this._output.space_before_token=!0);case l.after_newline:return this._output.space_before_token=!0,!m||b?this._tokens.peek().newlines?this.print_newline(!1,!0):this.allow_wrap_or_preserved_newline(t):this._output.space_before_token=!1,this.print_token(t),void(this._output.space_before_token=!0);case l.preserve_newline:return k||this.allow_wrap_or_preserved_newline(t),_=!(this._output.just_added_newline()||k),this._output.space_before_token=_,this.print_token(t),void(this._output.space_before_token=!0)}}if(e){this.allow_wrap_or_preserved_newline(t),_=!1;var y=this._tokens.peek();u=y&&o(y.type,[r.WORD,r.RESERVED])}else if("..."===t.text)this.allow_wrap_or_preserved_newline(t),_=this._flags.last_token.type===r.START_BLOCK,u=!1;else if(o(t.text,["--","++","!","~"])||i){if(this._flags.last_token.type!==r.COMMA&&this._flags.last_token.type!==r.START_EXPR||this.allow_wrap_or_preserved_newline(t),_=!1,u=!1,t.newlines&&("--"===t.text||"++"===t.text||"~"===t.text)){var w=p(this._flags.last_token,h)&&t.newlines;w&&(this._previous_flags.if_block||this._previous_flags.else_block)&&this.restore_mode(),this.print_newline(w,!0)}";"===this._flags.last_token.text&&v(this._flags.mode)&&(_=!0),this._flags.last_token.type===r.RESERVED?_=!0:this._flags.last_token.type===r.END_EXPR?_=!("]"===this._flags.last_token.text&&("--"===t.text||"++"===t.text)):this._flags.last_token.type===r.OPERATOR&&(_=o(t.text,["--","-","++","+"])&&o(this._flags.last_token.text,["--","-","++","+"]),o(t.text,["+","-"])&&o(this._flags.last_token.text,["--","++"])&&(u=!0)),(this._flags.mode!==d||this._flags.inline_frame)&&this._flags.mode!==f||"{"!==this._flags.last_token.text&&";"!==this._flags.last_token.text||this.print_newline()}this._output.space_before_token=this._output.space_before_token||_,this.print_token(t),this._output.space_before_token=u}else this.print_token(t);else this.print_token(t)},E.prototype.handle_block_comment=function(t,e){return this._output.raw?(this._output.add_raw_token(t),void(t.directives&&"end"===t.directives.preserve&&(this._output.raw=this._options.test_output_raw))):t.directives?(this.print_newline(!1,e),this.print_token(t),"start"===t.directives.preserve&&(this._output.raw=!0),void this.print_newline(!1,!0)):i.newline.test(t.text)||t.newlines?void this.print_block_commment(t,e):(this._output.space_before_token=!0,this.print_token(t),void(this._output.space_before_token=!0))},E.prototype.print_block_commment=function(t,e){var n,_=function(t){for(var e=[],n=(t=t.replace(i.allLineBreaks,"\n")).indexOf("\n");-1!==n;)e.push(t.substring(0,n)),n=(t=t.substring(n+1)).indexOf("\n");return t.length&&e.push(t),e}(t.text),s=!1,a=!1,r=t.whitespace_before,o=r.length;if(this.print_newline(!1,e),this.print_token_line_indentation(t),this._output.add_token(_[0]),this.print_newline(!1,e),_.length>1){for(s=function(t,e){for(var i=0;i<t.length;i++)if(t[i].trim().charAt(0)!==e)return!1;return!0}(_=_.slice(1),"*"),a=function(t,e){for(var i,n=0,_=t.length;n<_;n++)if((i=t[n])&&0!==i.indexOf(e))return!1;return!0}(_,r),s&&(this._flags.alignment=1),n=0;n<_.length;n++)s?(this.print_token_line_indentation(t),this._output.add_token(_[n].replace(/^\s+/g,""))):a&&_[n]?(this.print_token_line_indentation(t),this._output.add_token(_[n].substring(o))):(this._output.current_line.set_indent(-1),this._output.add_token(_[n])),this.print_newline(!1,e);this._flags.alignment=0}},E.prototype.handle_comment=function(t,e){t.newlines?this.print_newline(!1,e):this._output.trim(!0),this._output.space_before_token=!0,this.print_token(t),this.print_newline(!1,e)},E.prototype.handle_dot=function(t){this.start_of_statement(t)||this.handle_whitespace_and_comments(t,!0),this._flags.last_token.text.match("^[0-9]+$")&&(this._output.space_before_token=!0),p(this._flags.last_token,h)?this._output.space_before_token=!1:this.allow_wrap_or_preserved_newline(t,")"===this._flags.last_token.text&&this._options.break_chained_methods),this._options.unindent_chained_methods&&this._output.just_added_newline()&&this.deindent(),this.print_token(t)},E.prototype.handle_unknown=function(t,e){this.print_token(t),"\n"===t.text[t.text.length-1]&&this.print_newline(!1,e)},E.prototype.handle_eof=function(t){for(;this._flags.mode===f;)this.restore_mode();this.handle_whitespace_and_comments(t)},U.Beautifier=E,U}var Rt,Nt,At,St={exports:{}},Lt={},Ct={};function jt(){if(Rt)return Ct;Rt=1;var t=tt().Options;function e(e){t.call(this,e,"css"),this.selector_separator_newline=this._get_boolean("selector_separator_newline",!0),this.newline_between_rules=this._get_boolean("newline_between_rules",!0);var i=this._get_boolean("space_around_selector_separator");this.space_around_combinator=this._get_boolean("space_around_combinator")||i;var n=this._get_selection_list("brace_style",["collapse","expand","end-expand","none","preserve-inline"]);this.brace_style="collapse";for(var _=0;_<n.length;_++)"expand"!==n[_]?this.brace_style="collapse":this.brace_style=n[_]}return e.prototype=new t,Ct.Options=e,Ct}function Pt(){if(At)return St.exports;At=1;var t=function(){if(Nt)return Lt;Nt=1;var t=jt().Options,e=V().Output,i=st().InputScanner,n=new(0,bt().Directives)(/\/\*/,/\*\//),_=/\r\n|[\r\n]/,s=/\r\n|[\r\n]/g,a=/\s/,r=/(?:\s|\n)+/g,o=/\/\*(?:[\s\S]*?)((?:\*\/)|$)/g,u=/\/\/(?:[^\n\r\u2028\u2029]*)/g;function p(e,i){this._source_text=e||"",this._options=new t(i),this._ch=null,this._input=null,this.NESTED_AT_RULE={page:!0,"font-face":!0,keyframes:!0,media:!0,supports:!0,document:!0},this.CONDITIONAL_GROUP_RULE={media:!0,supports:!0,document:!0},this.NON_SEMICOLON_NEWLINE_PROPERTY=["grid-template-areas","grid-template"]}return p.prototype.eatString=function(t){var e="";for(this._ch=this._input.next();this._ch;){if(e+=this._ch,"\\"===this._ch)e+=this._input.next();else if(-1!==t.indexOf(this._ch)||"\n"===this._ch)break;this._ch=this._input.next()}return e},p.prototype.eatWhitespace=function(t){for(var e=a.test(this._input.peek()),i=0;a.test(this._input.peek());)this._ch=this._input.next(),t&&"\n"===this._ch&&(0===i||i<this._options.max_preserve_newlines)&&(i++,this._output.add_new_line(!0));return e},p.prototype.foundNestedPseudoClass=function(){for(var t=0,e=1,i=this._input.peek(e);i;){if("{"===i)return!0;if("("===i)t+=1;else if(")"===i){if(0===t)return!1;t-=1}else if(";"===i||"}"===i)return!1;e++,i=this._input.peek(e)}return!1},p.prototype.print_string=function(t){this._output.set_indent(this._indentLevel),this._output.non_breaking_space=!0,this._output.add_token(t)},p.prototype.preserveSingleSpace=function(t){t&&(this._output.space_before_token=!0)},p.prototype.indent=function(){this._indentLevel++},p.prototype.outdent=function(){this._indentLevel>0&&this._indentLevel--},p.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var t=this._source_text,p=this._options.eol;"auto"===p&&(p="\n",t&&_.test(t||"")&&(p=t.match(_)[0]));var h=(t=t.replace(s,"\n")).match(/^[\t ]*/)[0];this._output=new e(this._options,h),this._input=new i(t),this._indentLevel=0,this._nestedLevel=0,this._ch=null;for(var l,c,d=0,f=!1,g=!1,m=!1,b=!1,k=!1,y=this._ch,w=!1;l=""!==this._input.read(r),c=y,this._ch=this._input.next(),"\\"===this._ch&&this._input.hasNext()&&(this._ch+=this._input.next()),y=this._ch,this._ch;)if("/"===this._ch&&"*"===this._input.peek()){this._output.add_new_line(),this._input.back();var x=this._input.read(o),v=n.get_directives(x);v&&"start"===v.ignore&&(x+=n.readIgnored(this._input)),this.print_string(x),this.eatWhitespace(!0),this._output.add_new_line()}else if("/"===this._ch&&"/"===this._input.peek())this._output.space_before_token=!0,this._input.back(),this.print_string(this._input.read(u)),this.eatWhitespace(!0);else if("$"===this._ch){this.preserveSingleSpace(l),this.print_string(this._ch);var E=this._input.peekUntilAfter(/[: ,;{}()[\]\/='"]/g);E.match(/[ :]$/)&&(E=this.eatString(": ").replace(/\s+$/,""),this.print_string(E),this._output.space_before_token=!0),0===d&&-1!==E.indexOf(":")&&(g=!0,this.indent())}else if("@"===this._ch)if(this.preserveSingleSpace(l),"{"===this._input.peek())this.print_string(this._ch+this.eatString("}"));else{this.print_string(this._ch);var O=this._input.peekUntilAfter(/[: ,;{}()[\]\/='"]/g);O.match(/[ :]$/)&&(O=this.eatString(": ").replace(/\s+$/,""),this.print_string(O),this._output.space_before_token=!0),0===d&&-1!==O.indexOf(":")?(g=!0,this.indent()):O in this.NESTED_AT_RULE?(this._nestedLevel+=1,O in this.CONDITIONAL_GROUP_RULE&&(m=!0)):0!==d||g||(b=!0)}else if("#"===this._ch&&"{"===this._input.peek())this.preserveSingleSpace(l),this.print_string(this._ch+this.eatString("}"));else if("{"===this._ch)g&&(g=!1,this.outdent()),b=!1,m?(m=!1,f=this._indentLevel>=this._nestedLevel):f=this._indentLevel>=this._nestedLevel-1,this._options.newline_between_rules&&f&&this._output.previous_line&&"{"!==this._output.previous_line.item(-1)&&this._output.ensure_empty_line_above("/",","),this._output.space_before_token=!0,"expand"===this._options.brace_style?(this._output.add_new_line(),this.print_string(this._ch),this.indent(),this._output.set_indent(this._indentLevel)):("("===c?this._output.space_before_token=!1:","!==c&&this.indent(),this.print_string(this._ch)),this.eatWhitespace(!0),this._output.add_new_line();else if("}"===this._ch)this.outdent(),this._output.add_new_line(),"{"===c&&this._output.trim(!0),g&&(this.outdent(),g=!1),this.print_string(this._ch),f=!1,this._nestedLevel&&this._nestedLevel--,this.eatWhitespace(!0),this._output.add_new_line(),this._options.newline_between_rules&&!this._output.just_added_blankline()&&"}"!==this._input.peek()&&this._output.add_new_line(!0),")"===this._input.peek()&&(this._output.trim(!0),"expand"===this._options.brace_style&&this._output.add_new_line(!0));else if(":"===this._ch){for(var T=0;T<this.NON_SEMICOLON_NEWLINE_PROPERTY.length;T++)if(this._input.lookBack(this.NON_SEMICOLON_NEWLINE_PROPERTY[T])){w=!0;break}!f&&!m||this._input.lookBack("&")||this.foundNestedPseudoClass()||this._input.lookBack("(")||b||0!==d?(this._input.lookBack(" ")&&(this._output.space_before_token=!0),":"===this._input.peek()?(this._ch=this._input.next(),this.print_string("::")):this.print_string(":")):(this.print_string(":"),g||(g=!0,this._output.space_before_token=!0,this.eatWhitespace(!0),this.indent()))}else if('"'===this._ch||"'"===this._ch){var R='"'===c||"'"===c;this.preserveSingleSpace(R||l),this.print_string(this._ch+this.eatString(this._ch)),this.eatWhitespace(!0)}else if(";"===this._ch)w=!1,0===d?(g&&(this.outdent(),g=!1),b=!1,this.print_string(this._ch),this.eatWhitespace(!0),"/"!==this._input.peek()&&this._output.add_new_line()):(this.print_string(this._ch),this.eatWhitespace(!0),this._output.space_before_token=!0);else if("("===this._ch)if(this._input.lookBack("url"))this.print_string(this._ch),this.eatWhitespace(),d++,this.indent(),this._ch=this._input.next(),")"===this._ch||'"'===this._ch||"'"===this._ch?this._input.back():this._ch&&(this.print_string(this._ch+this.eatString(")")),d&&(d--,this.outdent()));else{var N=!1;this._input.lookBack("with")&&(N=!0),this.preserveSingleSpace(l||N),this.print_string(this._ch),g&&"$"===c&&this._options.selector_separator_newline?(this._output.add_new_line(),k=!0):(this.eatWhitespace(),d++,this.indent())}else if(")"===this._ch)d&&(d--,this.outdent()),k&&";"===this._input.peek()&&this._options.selector_separator_newline&&(k=!1,this.outdent(),this._output.add_new_line()),this.print_string(this._ch);else if(","===this._ch)this.print_string(this._ch),this.eatWhitespace(!0),!this._options.selector_separator_newline||g&&!k||0!==d||b?this._output.space_before_token=!0:this._output.add_new_line();else if(">"!==this._ch&&"+"!==this._ch&&"~"!==this._ch||g||0!==d)if("]"===this._ch)this.print_string(this._ch);else if("["===this._ch)this.preserveSingleSpace(l),this.print_string(this._ch);else if("="===this._ch)this.eatWhitespace(),this.print_string("="),a.test(this._ch)&&(this._ch="");else if("!"!==this._ch||this._input.lookBack("\\")){var A='"'===c||"'"===c;this.preserveSingleSpace(A||l),this.print_string(this._ch),!this._output.just_added_newline()&&"\n"===this._input.peek()&&w&&this._output.add_new_line()}else this._output.space_before_token=!0,this.print_string(this._ch);else this._options.space_around_combinator?(this._output.space_before_token=!0,this.print_string(this._ch),this._output.space_before_token=!0):(this.print_string(this._ch),this.eatWhitespace(),this._ch&&a.test(this._ch)&&(this._ch=""));return this._output.get_code(p)},Lt.Beautifier=p,Lt}().Beautifier,e=jt().Options;return St.exports=function(e,i){return new t(e,i).beautify()},St.exports.defaultOptions=function(){return new e},St.exports}var Dt,Wt={exports:{}},Mt={},Kt={};function It(){if(Dt)return Kt;Dt=1;var t=tt().Options;function e(e){t.call(this,e,"html"),1===this.templating.length&&"auto"===this.templating[0]&&(this.templating=["django","erb","handlebars","php"]),this.indent_inner_html=this._get_boolean("indent_inner_html"),this.indent_body_inner_html=this._get_boolean("indent_body_inner_html",!0),this.indent_head_inner_html=this._get_boolean("indent_head_inner_html",!0),this.indent_handlebars=this._get_boolean("indent_handlebars",!0),this.wrap_attributes=this._get_selection("wrap_attributes",["auto","force","force-aligned","force-expand-multiline","aligned-multiple","preserve","preserve-aligned"]),this.wrap_attributes_min_attrs=this._get_number("wrap_attributes_min_attrs",2),this.wrap_attributes_indent_size=this._get_number("wrap_attributes_indent_size",this.indent_size),this.extra_liners=this._get_array("extra_liners",["head","body","/html"]),this.inline=this._get_array("inline",["a","abbr","area","audio","b","bdi","bdo","br","button","canvas","cite","code","data","datalist","del","dfn","em","embed","i","iframe","img","input","ins","kbd","keygen","label","map","mark","math","meter","noscript","object","output","progress","q","ruby","s","samp","select","small","span","strong","sub","sup","svg","template","textarea","time","u","var","video","wbr","text","acronym","big","strike","tt"]),this.inline_custom_elements=this._get_boolean("inline_custom_elements",!0),this.void_elements=this._get_array("void_elements",["area","base","br","col","embed","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr","!doctype","?xml","basefont","isindex"]),this.unformatted=this._get_array("unformatted",[]),this.content_unformatted=this._get_array("content_unformatted",["pre","textarea"]),this.unformatted_content_delimiter=this._get_characters("unformatted_content_delimiter"),this.indent_scripts=this._get_selection("indent_scripts",["normal","keep","separate"])}return e.prototype=new t,Kt.Options=e,Kt}var Bt,Ut,zt,Vt,Xt,Ft,Gt,$t,Qt,qt,Ht={};function Yt(){if(Bt)return Ht;Bt=1;var t=ft().Tokenizer,e=ft().TOKEN,i=bt().Directives,n=Et().TemplatablePattern,_=dt().Pattern,s={TAG_OPEN:"TK_TAG_OPEN",TAG_CLOSE:"TK_TAG_CLOSE",CONTROL_FLOW_OPEN:"TK_CONTROL_FLOW_OPEN",CONTROL_FLOW_CLOSE:"TK_CONTROL_FLOW_CLOSE",ATTRIBUTE:"TK_ATTRIBUTE",EQUALS:"TK_EQUALS",VALUE:"TK_VALUE",COMMENT:"TK_COMMENT",TEXT:"TK_TEXT",UNKNOWN:"TK_UNKNOWN",START:e.START,RAW:e.RAW,EOF:e.EOF},a=new i(/<\!--/,/-->/),r=function(e,i){t.call(this,e,i),this._current_tag_name="";var s=new n(this._input).read_options(this._options),a=new _(this._input);if(this.__patterns={word:s.until(/[\n\r\t <]/),word_control_flow_close_excluded:s.until(/[\n\r\t <}]/),single_quote:s.until_after(/'/),double_quote:s.until_after(/"/),attribute:s.until(/[\n\r\t =>]|\/>/),element_name:s.until(/[\n\r\t >\/]/),angular_control_flow_start:a.matching(/\@[a-zA-Z]+[^({]*[({]/),handlebars_comment:a.starting_with(/{{!--/).until_after(/--}}/),handlebars:a.starting_with(/{{/).until_after(/}}/),handlebars_open:a.until(/[\n\r\t }]/),handlebars_raw_close:a.until(/}}/),comment:a.starting_with(/<!--/).until_after(/-->/),cdata:a.starting_with(/<!\[CDATA\[/).until_after(/]]>/),conditional_comment:a.starting_with(/<!\[/).until_after(/]>/),processing:a.starting_with(/<\?/).until_after(/\?>/)},this._options.indent_handlebars&&(this.__patterns.word=this.__patterns.word.exclude("handlebars"),this.__patterns.word_control_flow_close_excluded=this.__patterns.word_control_flow_close_excluded.exclude("handlebars")),this._unformatted_content_delimiter=null,this._options.unformatted_content_delimiter){var r=this._input.get_literal_regexp(this._options.unformatted_content_delimiter);this.__patterns.unformatted_content_delimiter=a.matching(r).until_after(r)}};return(r.prototype=new t)._is_comment=function(t){return!1},r.prototype._is_opening=function(t){return t.type===s.TAG_OPEN||t.type===s.CONTROL_FLOW_OPEN},r.prototype._is_closing=function(t,e){return t.type===s.TAG_CLOSE&&e&&((">"===t.text||"/>"===t.text)&&"<"===e.text[0]||"}}"===t.text&&"{"===e.text[0]&&"{"===e.text[1])||t.type===s.CONTROL_FLOW_CLOSE&&"}"===t.text&&e.text.endsWith("{")},r.prototype._reset=function(){this._current_tag_name=""},r.prototype._get_next_token=function(t,e){var i=null;this._readWhitespace();var n=this._input.peek();return null===n?this._create_token(s.EOF,""):i=(i=(i=(i=(i=(i=(i=(i=(i=(i=i||this._read_open_handlebars(n,e))||this._read_attribute(n,t,e))||this._read_close(n,e))||this._read_control_flows(n,e))||this._read_raw_content(n,t,e))||this._read_content_word(n,e))||this._read_comment_or_cdata(n))||this._read_processing(n))||this._read_open(n,e))||this._create_token(s.UNKNOWN,this._input.next())},r.prototype._read_comment_or_cdata=function(t){var e=null,i=null,n=null;"<"===t&&("!"===this._input.peek(1)&&((i=this.__patterns.comment.read())?(n=a.get_directives(i))&&"start"===n.ignore&&(i+=a.readIgnored(this._input)):i=this.__patterns.cdata.read()),i&&((e=this._create_token(s.COMMENT,i)).directives=n));return e},r.prototype._read_processing=function(t){var e=null,i=null;if("<"===t){var n=this._input.peek(1);"!"!==n&&"?"!==n||(i=(i=this.__patterns.conditional_comment.read())||this.__patterns.processing.read()),i&&((e=this._create_token(s.COMMENT,i)).directives=null)}return e},r.prototype._read_open=function(t,e){var i=null,n=null;return e&&e.type!==s.CONTROL_FLOW_OPEN||"<"===t&&(i=this._input.next(),"/"===this._input.peek()&&(i+=this._input.next()),i+=this.__patterns.element_name.read(),n=this._create_token(s.TAG_OPEN,i)),n},r.prototype._read_open_handlebars=function(t,e){var i=null,n=null;return e&&e.type!==s.CONTROL_FLOW_OPEN||this._options.indent_handlebars&&"{"===t&&"{"===this._input.peek(1)&&("!"===this._input.peek(2)?(i=(i=this.__patterns.handlebars_comment.read())||this.__patterns.handlebars.read(),n=this._create_token(s.COMMENT,i)):(i=this.__patterns.handlebars_open.read(),n=this._create_token(s.TAG_OPEN,i))),n},r.prototype._read_control_flows=function(t,e){var i="",n=null;if(!this._options.templating.includes("angular")||!this._options.indent_handlebars)return n;if("@"===t){if(""===(i=this.__patterns.angular_control_flow_start.read()))return n;for(var _=i.endsWith("(")?1:0,a=0;!i.endsWith("{")||_!==a;){var r=this._input.next();if(null===r)break;"("===r?_++:")"===r&&a++,i+=r}n=this._create_token(s.CONTROL_FLOW_OPEN,i)}else"}"===t&&e&&e.type===s.CONTROL_FLOW_OPEN&&(i=this._input.next(),n=this._create_token(s.CONTROL_FLOW_CLOSE,i));return n},r.prototype._read_close=function(t,e){var i=null,n=null;return e&&e.type===s.TAG_OPEN&&("<"===e.text[0]&&(">"===t||"/"===t&&">"===this._input.peek(1))?(i=this._input.next(),"/"===t&&(i+=this._input.next()),n=this._create_token(s.TAG_CLOSE,i)):"{"===e.text[0]&&"}"===t&&"}"===this._input.peek(1)&&(this._input.next(),this._input.next(),n=this._create_token(s.TAG_CLOSE,"}}"))),n},r.prototype._read_attribute=function(t,e,i){var n=null,_="";if(i&&"<"===i.text[0])if("="===t)n=this._create_token(s.EQUALS,this._input.next());else if('"'===t||"'"===t){var a=this._input.next();a+='"'===t?this.__patterns.double_quote.read():this.__patterns.single_quote.read(),n=this._create_token(s.VALUE,a)}else(_=this.__patterns.attribute.read())&&(n=e.type===s.EQUALS?this._create_token(s.VALUE,_):this._create_token(s.ATTRIBUTE,_));return n},r.prototype._is_content_unformatted=function(t){return-1===this._options.void_elements.indexOf(t)&&(-1!==this._options.content_unformatted.indexOf(t)||-1!==this._options.unformatted.indexOf(t))},r.prototype._read_raw_content=function(t,e,i){var n="";if(i&&"{"===i.text[0])n=this.__patterns.handlebars_raw_close.read();else if(e.type===s.TAG_CLOSE&&"<"===e.opened.text[0]&&"/"!==e.text[0]){var _=e.opened.text.substr(1).toLowerCase();if("script"===_||"style"===_){var a=this._read_comment_or_cdata(t);if(a)return a.type=s.TEXT,a;n=this._input.readUntil(new RegExp("</"+_+"[\\n\\r\\t ]*?>","ig"))}else this._is_content_unformatted(_)&&(n=this._input.readUntil(new RegExp("</"+_+"[\\n\\r\\t ]*?>","ig")))}return n?this._create_token(s.TEXT,n):null},r.prototype._read_content_word=function(t,e){var i="";if(this._options.unformatted_content_delimiter&&t===this._options.unformatted_content_delimiter[0]&&(i=this.__patterns.unformatted_content_delimiter.read()),i||(i=e&&e.type===s.CONTROL_FLOW_OPEN?this.__patterns.word_control_flow_close_excluded.read():this.__patterns.word.read()),i)return this._create_token(s.TEXT,i)},Ht.Tokenizer=r,Ht.TOKEN=s,Ht}function Zt(){if(Ut)return Mt;Ut=1;var t=It().Options,e=V().Output,i=Yt().Tokenizer,n=Yt().TOKEN,_=/\r\n|[\r\n]/,s=/\r\n|[\r\n]/g,a=function(t,i){this.indent_level=0,this.alignment_size=0,this.max_preserve_newlines=t.max_preserve_newlines,this.preserve_newlines=t.preserve_newlines,this._output=new e(t,i)};a.prototype.current_line_has_match=function(t){return this._output.current_line.has_match(t)},a.prototype.set_space_before_token=function(t,e){this._output.space_before_token=t,this._output.non_breaking_space=e},a.prototype.set_wrap_point=function(){this._output.set_indent(this.indent_level,this.alignment_size),this._output.set_wrap_point()},a.prototype.add_raw_token=function(t){this._output.add_raw_token(t)},a.prototype.print_preserved_newlines=function(t){var e=0;t.type!==n.TEXT&&t.previous.type!==n.TEXT&&(e=t.newlines?1:0),this.preserve_newlines&&(e=t.newlines<this.max_preserve_newlines+1?t.newlines:this.max_preserve_newlines+1);for(var i=0;i<e;i++)this.print_newline(i>0);return 0!==e},a.prototype.traverse_whitespace=function(t){return!(!t.whitespace_before&&!t.newlines)&&(this.print_preserved_newlines(t)||(this._output.space_before_token=!0),!0)},a.prototype.previous_token_wrapped=function(){return this._output.previous_token_wrapped},a.prototype.print_newline=function(t){this._output.add_new_line(t)},a.prototype.print_token=function(t){t.text&&(this._output.set_indent(this.indent_level,this.alignment_size),this._output.add_token(t.text))},a.prototype.indent=function(){this.indent_level++},a.prototype.deindent=function(){this.indent_level>0&&(this.indent_level--,this._output.set_indent(this.indent_level,this.alignment_size))},a.prototype.get_full_indent=function(t){return(t=this.indent_level+(t||0))<1?"":this._output.get_indent_string(t)};var r=function(t,e){var i=null,_=null;return e.closed?("script"===t?i="text/javascript":"style"===t&&(i="text/css"),i=function(t){for(var e=null,i=t.next;i.type!==n.EOF&&t.closed!==i;){if(i.type===n.ATTRIBUTE&&"type"===i.text){i.next&&i.next.type===n.EQUALS&&i.next.next&&i.next.next.type===n.VALUE&&(e=i.next.next.text);break}i=i.next}return e}(e)||i,i.search("text/css")>-1?_="css":i.search(/module|((text|application|dojo)\/(x-)?(javascript|ecmascript|jscript|livescript|(ld\+)?json|method|aspect))/)>-1?_="javascript":i.search(/(text|application|dojo)\/(x-)?(html)/)>-1?_="html":i.search(/test\/null/)>-1&&(_="null"),_):null};function o(t,e){return-1!==e.indexOf(t)}function u(t,e,i){this.parent=t||null,this.tag=e?e.tag_name:"",this.indent_level=i||0,this.parser_token=e||null}function p(t){this._printer=t,this._current_frame=null}function h(e,i,n,_){this._source_text=e||"",i=i||{},this._js_beautify=n,this._css_beautify=_,this._tag_stack=null;var s=new t(i,"html");this._options=s,this._is_wrap_attributes_force="force"===this._options.wrap_attributes.substr(0,5),this._is_wrap_attributes_force_expand_multiline="force-expand-multiline"===this._options.wrap_attributes,this._is_wrap_attributes_force_aligned="force-aligned"===this._options.wrap_attributes,this._is_wrap_attributes_aligned_multiple="aligned-multiple"===this._options.wrap_attributes,this._is_wrap_attributes_preserve="preserve"===this._options.wrap_attributes.substr(0,8),this._is_wrap_attributes_preserve_aligned="preserve-aligned"===this._options.wrap_attributes}p.prototype.get_parser_token=function(){return this._current_frame?this._current_frame.parser_token:null},p.prototype.record_tag=function(t){var e=new u(this._current_frame,t,this._printer.indent_level);this._current_frame=e},p.prototype._try_pop_frame=function(t){var e=null;return t&&(e=t.parser_token,this._printer.indent_level=t.indent_level,this._current_frame=t.parent),e},p.prototype._get_frame=function(t,e){for(var i=this._current_frame;i&&-1===t.indexOf(i.tag);){if(e&&-1!==e.indexOf(i.tag)){i=null;break}i=i.parent}return i},p.prototype.try_pop=function(t,e){var i=this._get_frame([t],e);return this._try_pop_frame(i)},p.prototype.indent_to_tag=function(t){var e=this._get_frame(t);e&&(this._printer.indent_level=e.indent_level)},h.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var t=this._source_text,e=this._options.eol;"auto"===this._options.eol&&(e="\n",t&&_.test(t)&&(e=t.match(_)[0]));var r=(t=t.replace(s,"\n")).match(/^[\t ]*/)[0],o={text:"",type:""},u=new l,h=new a(this._options,r),c=new i(t,this._options).tokenize();this._tag_stack=new p(h);for(var d=null,f=c.next();f.type!==n.EOF;)f.type===n.TAG_OPEN||f.type===n.COMMENT?u=d=this._handle_tag_open(h,f,u,o,c):f.type===n.ATTRIBUTE||f.type===n.EQUALS||f.type===n.VALUE||f.type===n.TEXT&&!u.tag_complete?d=this._handle_inside_tag(h,f,u,o):f.type===n.TAG_CLOSE?d=this._handle_tag_close(h,f,u):f.type===n.TEXT?d=this._handle_text(h,f,u):f.type===n.CONTROL_FLOW_OPEN?d=this._handle_control_flow_open(h,f):f.type===n.CONTROL_FLOW_CLOSE?d=this._handle_control_flow_close(h,f):h.add_raw_token(f),o=d,f=c.next();return h._output.get_code(e)},h.prototype._handle_control_flow_open=function(t,e){var i={text:e.text,type:e.type};return t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),e.newlines?t.print_preserved_newlines(e):t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),t.print_token(e),t.indent(),i},h.prototype._handle_control_flow_close=function(t,e){var i={text:e.text,type:e.type};return t.deindent(),e.newlines?t.print_preserved_newlines(e):t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),t.print_token(e),i},h.prototype._handle_tag_close=function(t,e,i){var n={text:e.text,type:e.type};return t.alignment_size=0,i.tag_complete=!0,t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),i.is_unformatted?t.add_raw_token(e):("<"===i.tag_start_char&&(t.set_space_before_token("/"===e.text[0],!0),this._is_wrap_attributes_force_expand_multiline&&i.has_wrapped_attrs&&t.print_newline(!1)),t.print_token(e)),!i.indent_content||i.is_unformatted||i.is_content_unformatted||(t.indent(),i.indent_content=!1),i.is_inline_element||i.is_unformatted||i.is_content_unformatted||t.set_wrap_point(),n},h.prototype._handle_inside_tag=function(t,e,i,_){var s=i.has_wrapped_attrs,a={text:e.text,type:e.type};return t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),i.is_unformatted?t.add_raw_token(e):"{"===i.tag_start_char&&e.type===n.TEXT?t.print_preserved_newlines(e)?(e.newlines=0,t.add_raw_token(e)):t.print_token(e):(e.type===n.ATTRIBUTE?t.set_space_before_token(!0):(e.type===n.EQUALS||e.type===n.VALUE&&e.previous.type===n.EQUALS)&&t.set_space_before_token(!1),e.type===n.ATTRIBUTE&&"<"===i.tag_start_char&&((this._is_wrap_attributes_preserve||this._is_wrap_attributes_preserve_aligned)&&(t.traverse_whitespace(e),s=s||0!==e.newlines),this._is_wrap_attributes_force&&i.attr_count>=this._options.wrap_attributes_min_attrs&&(_.type!==n.TAG_OPEN||this._is_wrap_attributes_force_expand_multiline)&&(t.print_newline(!1),s=!0)),t.print_token(e),s=s||t.previous_token_wrapped(),i.has_wrapped_attrs=s),a},h.prototype._handle_text=function(t,e,i){var n={text:e.text,type:"TK_CONTENT"};return i.custom_beautifier_name?this._print_custom_beatifier_text(t,e,i):i.is_unformatted||i.is_content_unformatted?t.add_raw_token(e):(t.traverse_whitespace(e),t.print_token(e)),n},h.prototype._print_custom_beatifier_text=function(t,e,i){var n=this;if(""!==e.text){var _,s=e.text,a=1,r="",o="";"javascript"===i.custom_beautifier_name&&"function"==typeof this._js_beautify?_=this._js_beautify:"css"===i.custom_beautifier_name&&"function"==typeof this._css_beautify?_=this._css_beautify:"html"===i.custom_beautifier_name&&(_=function(t,e){return new h(t,e,n._js_beautify,n._css_beautify).beautify()}),"keep"===this._options.indent_scripts?a=0:"separate"===this._options.indent_scripts&&(a=-t.indent_level);var u=t.get_full_indent(a);if(s=s.replace(/\n[ \t]*$/,""),"html"!==i.custom_beautifier_name&&"<"===s[0]&&s.match(/^(<!--|<!\[CDATA\[)/)){var p=/^(<!--[^\n]*|<!\[CDATA\[)(\n?)([ \t\n]*)([\s\S]*)(-->|]]>)$/.exec(s);if(!p)return void t.add_raw_token(e);r=u+p[1]+"\n",s=p[4],p[5]&&(o=u+p[5]),s=s.replace(/\n[ \t]*$/,""),(p[2]||-1!==p[3].indexOf("\n"))&&(p=p[3].match(/[ \t]+$/))&&(e.whitespace_before=p[0])}if(s)if(_){var l=function(){this.eol="\n"};l.prototype=this._options.raw_options,s=_(u+s,new l)}else{var c=e.whitespace_before;c&&(s=s.replace(new RegExp("\n("+c+")?","g"),"\n")),s=u+s.replace(/\n/g,"\n"+u)}r&&(s=s?r+s+"\n"+o:r+o),t.print_newline(!1),s&&(e.text=s,e.whitespace_before="",e.newlines=0,t.add_raw_token(e),t.print_newline(!0))}},h.prototype._handle_tag_open=function(t,e,i,_,s){var a=this._get_tag_open_token(e);if(!i.is_unformatted&&!i.is_content_unformatted||i.is_empty_element||e.type!==n.TAG_OPEN||a.is_start_tag?(t.traverse_whitespace(e),this._set_tag_position(t,e,a,i,_),a.is_inline_element||t.set_wrap_point(),t.print_token(e)):(t.add_raw_token(e),a.start_tag_token=this._tag_stack.try_pop(a.tag_name)),a.is_start_tag&&this._is_wrap_attributes_force){var r,o=0;do{(r=s.peek(o)).type===n.ATTRIBUTE&&(a.attr_count+=1),o+=1}while(r.type!==n.EOF&&r.type!==n.TAG_CLOSE)}return(this._is_wrap_attributes_force_aligned||this._is_wrap_attributes_aligned_multiple||this._is_wrap_attributes_preserve_aligned)&&(a.alignment_size=e.text.length+1),a.tag_complete||a.is_unformatted||(t.alignment_size=a.alignment_size),a};var l=function(t,e){if(this.parent=t||null,this.text="",this.type="TK_TAG_OPEN",this.tag_name="",this.is_inline_element=!1,this.is_unformatted=!1,this.is_content_unformatted=!1,this.is_empty_element=!1,this.is_start_tag=!1,this.is_end_tag=!1,this.indent_content=!1,this.multiline_content=!1,this.custom_beautifier_name=null,this.start_tag_token=null,this.attr_count=0,this.has_wrapped_attrs=!1,this.alignment_size=0,this.tag_complete=!1,this.tag_start_char="",this.tag_check="",e){var i;this.tag_start_char=e.text[0],this.text=e.text,"<"===this.tag_start_char?(i=e.text.match(/^<([^\s>]*)/),this.tag_check=i?i[1]:""):(i=e.text.match(/^{{~?(?:[\^]|#\*?)?([^\s}]+)/),this.tag_check=i?i[1]:"",(e.text.startsWith("{{#>")||e.text.startsWith("{{~#>"))&&">"===this.tag_check[0]&&(">"===this.tag_check&&null!==e.next?this.tag_check=e.next.text.split(" ")[0]:this.tag_check=e.text.split(">")[1])),this.tag_check=this.tag_check.toLowerCase(),e.type===n.COMMENT&&(this.tag_complete=!0),this.is_start_tag="/"!==this.tag_check.charAt(0),this.tag_name=this.is_start_tag?this.tag_check:this.tag_check.substr(1),this.is_end_tag=!this.is_start_tag||e.closed&&"/>"===e.closed.text;var _=2;"{"===this.tag_start_char&&this.text.length>=3&&"~"===this.text.charAt(2)&&(_=3),this.is_end_tag=this.is_end_tag||"{"===this.tag_start_char&&(this.text.length<3||/[^#\^]/.test(this.text.charAt(_)))}else this.tag_complete=!0};h.prototype._get_tag_open_token=function(t){var e=new l(this._tag_stack.get_parser_token(),t);return e.alignment_size=this._options.wrap_attributes_indent_size,e.is_end_tag=e.is_end_tag||o(e.tag_check,this._options.void_elements),e.is_empty_element=e.tag_complete||e.is_start_tag&&e.is_end_tag,e.is_unformatted=!e.tag_complete&&o(e.tag_check,this._options.unformatted),e.is_content_unformatted=!e.is_empty_element&&o(e.tag_check,this._options.content_unformatted),e.is_inline_element=o(e.tag_name,this._options.inline)||this._options.inline_custom_elements&&e.tag_name.includes("-")||"{"===e.tag_start_char,e},h.prototype._set_tag_position=function(t,e,i,_,s){if(i.is_empty_element||(i.is_end_tag?i.start_tag_token=this._tag_stack.try_pop(i.tag_name):(this._do_optional_end_element(i)&&(i.is_inline_element||t.print_newline(!1)),this._tag_stack.record_tag(i),"script"!==i.tag_name&&"style"!==i.tag_name||i.is_unformatted||i.is_content_unformatted||(i.custom_beautifier_name=r(i.tag_check,e)))),o(i.tag_check,this._options.extra_liners)&&(t.print_newline(!1),t._output.just_added_blankline()||t.print_newline(!0)),i.is_empty_element){if("{"===i.tag_start_char&&"else"===i.tag_check)this._tag_stack.indent_to_tag(["if","unless","each"]),i.indent_content=!0,t.current_line_has_match(/{{#if/)||t.print_newline(!1);"!--"===i.tag_name&&s.type===n.TAG_CLOSE&&_.is_end_tag&&-1===i.text.indexOf("\n")||(i.is_inline_element||i.is_unformatted||t.print_newline(!1),this._calcluate_parent_multiline(t,i))}else if(i.is_end_tag){var a=!1;a=(a=i.start_tag_token&&i.start_tag_token.multiline_content)||!i.is_inline_element&&!(_.is_inline_element||_.is_unformatted)&&!(s.type===n.TAG_CLOSE&&i.start_tag_token===_)&&"TK_CONTENT"!==s.type,(i.is_content_unformatted||i.is_unformatted)&&(a=!1),a&&t.print_newline(!1)}else i.indent_content=!i.custom_beautifier_name,"<"===i.tag_start_char&&("html"===i.tag_name?i.indent_content=this._options.indent_inner_html:"head"===i.tag_name?i.indent_content=this._options.indent_head_inner_html:"body"===i.tag_name&&(i.indent_content=this._options.indent_body_inner_html)),i.is_inline_element||i.is_unformatted||"TK_CONTENT"===s.type&&!i.is_content_unformatted||t.print_newline(!1),this._calcluate_parent_multiline(t,i)},h.prototype._calcluate_parent_multiline=function(t,e){!e.parent||!t._output.just_added_newline()||(e.is_inline_element||e.is_unformatted)&&e.parent.is_inline_element||(e.parent.multiline_content=!0)};var c=["address","article","aside","blockquote","details","div","dl","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hr","main","menu","nav","ol","p","pre","section","table","ul"],d=["a","audio","del","ins","map","noscript","video"];return h.prototype._do_optional_end_element=function(t){var e=null;if(!t.is_empty_element&&t.is_start_tag&&t.parent){if("body"===t.tag_name)e=e||this._tag_stack.try_pop("head");else if("li"===t.tag_name)e=e||this._tag_stack.try_pop("li",["ol","ul","menu"]);else if("dd"===t.tag_name||"dt"===t.tag_name)e=(e=e||this._tag_stack.try_pop("dt",["dl"]))||this._tag_stack.try_pop("dd",["dl"]);else if("p"===t.parent.tag_name&&-1!==c.indexOf(t.tag_name)){var i=t.parent.parent;i&&-1!==d.indexOf(i.tag_name)||(e=e||this._tag_stack.try_pop("p"))}else"rp"===t.tag_name||"rt"===t.tag_name?e=(e=e||this._tag_stack.try_pop("rt",["ruby","rtc"]))||this._tag_stack.try_pop("rp",["ruby","rtc"]):"optgroup"===t.tag_name?e=e||this._tag_stack.try_pop("optgroup",["select"]):"option"===t.tag_name?e=e||this._tag_stack.try_pop("option",["select","datalist","optgroup"]):"colgroup"===t.tag_name?e=e||this._tag_stack.try_pop("caption",["table"]):"thead"===t.tag_name?e=(e=e||this._tag_stack.try_pop("caption",["table"]))||this._tag_stack.try_pop("colgroup",["table"]):"tbody"===t.tag_name||"tfoot"===t.tag_name?e=(e=(e=(e=e||this._tag_stack.try_pop("caption",["table"]))||this._tag_stack.try_pop("colgroup",["table"]))||this._tag_stack.try_pop("thead",["table"]))||this._tag_stack.try_pop("tbody",["table"]):"tr"===t.tag_name?e=(e=(e=e||this._tag_stack.try_pop("caption",["table"]))||this._tag_stack.try_pop("colgroup",["table"]))||this._tag_stack.try_pop("tr",["table","thead","tbody","tfoot"]):"th"!==t.tag_name&&"td"!==t.tag_name||(e=(e=e||this._tag_stack.try_pop("td",["table","thead","tbody","tfoot","tr"]))||this._tag_stack.try_pop("th",["table","thead","tbody","tfoot","tr"]));return t.parent=this._tag_stack.get_parser_token(),e}},Mt.Beautifier=h,Mt}function Jt(){if(Vt)return I;Vt=1;var t=function(){if(xt)return B.exports;xt=1;var t=Tt().Beautifier,e=et().Options;return B.exports=function(e,i){return new t(e,i).beautify()},B.exports.defaultOptions=function(){return new e},B.exports}(),e=Pt(),i=function(){if(zt)return Wt.exports;zt=1;var t=Zt().Beautifier,e=It().Options;return Wt.exports=function(e,i,n,_){return new t(e,i,n,_).beautify()},Wt.exports.defaultOptions=function(){return new e},Wt.exports}();function n(n,_,s,a){return i(n,_,s=s||t,a=a||e)}return n.defaultOptions=i.defaultOptions,I.js=t,I.css=e,I.html=n,I}Xt=K,(qt=Jt()).js_beautify=qt.js,qt.css_beautify=qt.css,qt.html_beautify=qt.html,Xt.exports=(Gt=qt,$t=qt,(Qt=function(t,e){return Ft.js_beautify(t,e)}).js=(Ft=qt).js_beautify,Qt.css=Gt.css_beautify,Qt.html=$t.html_beautify,Qt.js_beautify=Ft.js_beautify,Qt.css_beautify=Gt.css_beautify,Qt.html_beautify=$t.html_beautify,Qt);const te=t(K.exports);const ee=y(e({__name:"PageMonitoring",props:{projectList:{}},setup(t){const{t:e}=b(),y=[{keyword:"url",example:'url="http://example.com"',explain:e("searchHelp.url")},{keyword:"hash",example:'hash="234658675623543"',explain:e("searchHelp.hash")},{keyword:"project",example:'project="Hackerone"',explain:e("searchHelp.project")}],M=i(""),K=t=>{M.value=t,nt()},I=n({}),B=n([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:e("tableDemo.index"),type:"index",minWidth:55},{field:"url",label:"url",minWidth:200,formatter:(t,e,i)=>{return _(R,{href:i,underline:!1},"function"==typeof(n=i)||"[object Object]"===Object.prototype.toString.call(n)&&!m(n)?i:{default:()=>[i]});var n}},{field:"statusCode",label:e("PageMonitoring.statusCode"),minWidth:100,formatter:(t,e,i)=>2==i.length?`${i[0]} => ${i[1]}`:i[0]},{field:"hash",label:e("PageMonitoring.hash"),minWidth:100,formatter:(t,e,i)=>_(a,null,[i[0],s(" "),_("br",null,null),s(" "),i[1]])},{field:"similarity",label:e("PageMonitoring.similarity"),minWidth:200,formatter:(t,e,i)=>`${i}%`},{field:"tags",label:"TAG",fit:"true",formatter:(t,e,n)=>{null==n&&(n=[]),I[t.id]||(I[t.id]={inputVisible:!1,inputValue:"",inputRef:i(null)});const _=I[t.id],s=async()=>{_.inputValue&&(n.push(_.inputValue),C(t.id,U,_.inputValue)),_.inputVisible=!1,_.inputValue=""};return r(T,{},(()=>[...n.map((e=>r(O,{span:24,key:e},(()=>[r("div",{onClick:t=>((t,e)=>{t.target.classList.contains("el-tag__close")||ht("tags",e)})(t,e)},[r(E,{closable:!0,onClose:()=>(async e=>{const i=n.indexOf(e);i>-1&&n.splice(i,1),j(t.id,U,e)})(e)},(()=>e))])])))),r(O,{span:24},_.inputVisible?()=>r(o,{ref:_.inputRef,modelValue:_.inputValue,"onUpdate:modelValue":t=>_.inputValue=t,class:"w-20",size:"small",onKeyup:t=>{"Enter"===t.key&&s()},onBlur:s}):()=>r(u,{class:"button-new-tag",size:"small",onClick:()=>(_.inputVisible=!0,void k((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:e("asset.time"),minWidth:200},{field:"action",label:e("tableDemo.action"),minWidth:100,formatter:(t,e,i)=>_(a,null,[_(p,{type:"success",onClick:()=>X(t.md5)},{default:()=>[s("Diff")]})])}]);let U="PageMonitoring";B.forEach((t=>{t.hidden=t.hidden??!1}));let z=i(!1);const V=({field:t,hidden:e})=>{const i=B.findIndex((e=>e.field===t));-1!==i&&(B[i].hidden=e),(()=>{const t=B.reduce(((t,e)=>(t[e.field]=e.hidden,t)),{});t.statisticsHidden=z.value,localStorage.setItem(`columnConfig_${U}`,JSON.stringify(t))})()};(()=>{const t=JSON.parse(localStorage.getItem(`columnConfig_${U}`)||"{}");B.forEach((e=>{void 0!==t[e.field]&&"select"!=e.field&&(e.hidden=t[e.field])})),z.value=t.statisticsHidden})();const X=async t=>{const e=await L(t);$(e.data.diff)},F=i(!1),G=i([]),$=t=>{G.value=t,0==G.value.length&&(G.value=["",""]),1==G.value.length&&G.value.push(""),G.value[0]=te.js(G.value[0],{indent_size:2}),G.value[1]=te.js(G.value[1],{indent_size:2}),F.value=!0},{allSchemas:Q}=S(B),{tableRegister:q,tableState:H,tableMethods:Y}=w({fetchDataApi:async()=>{const{currentPage:t,pageSize:e}=H,i=await P(M.value,t.value,e.value,ot);return{list:i.data.list,total:i.data.total}},immediate:!1}),{loading:Z,dataList:J,total:tt,currentPage:et,pageSize:it}=H,{getList:nt,getElTableExpose:_t}=Y;function st(){return{background:"var(--el-fill-color-light)"}}h((()=>{rt(),window.addEventListener("resize",rt)}));const at=i(0),rt=()=>{const t=window.innerHeight||document.documentElement.clientHeight;at.value=.7*t},ot=n({}),ut=(t,e)=>{Object.assign(ot,e),M.value=t,nt()},pt=i([]),ht=(t,e)=>{const i=`${t}=${e}`;pt.value=[...pt.value,i]},lt=t=>{if(pt.value){const[e,i]=t.split("=");e in ot&&Array.isArray(ot[e])&&(ot[e]=ot[e].filter((t=>t!==i)),0===ot[e].length&&delete ot[e]),pt.value=pt.value.filter((e=>e!==t))}},ct=()=>ot;return(t,e)=>(l(),c(a,null,[_(D,{getList:d(nt),handleSearch:K,searchKeywordsData:y,index:d(U),dynamicTags:pt.value,handleClose:lt,getElTableExpose:d(_t),handleFilterSearch:ut,projectList:t.$props.projectList,crudSchemas:B,onUpdateColumnVisibility:V,searchResultCount:d(tt),getFilter:ct},null,8,["getList","index","dynamicTags","getElTableExpose","projectList","crudSchemas","searchResultCount"]),_(d(T),null,{default:f((()=>[_(d(O),null,{default:f((()=>[_(d(x),null,{default:f((()=>[_(d(A),{pageSize:d(it),"onUpdate:pageSize":e[0]||(e[0]=t=>g(it)?it.value=t:null),currentPage:d(et),"onUpdate:currentPage":e[1]||(e[1]=t=>g(et)?et.value=t:null),columns:d(Q).tableColumns,data:d(J),"max-height":at.value,stripe:"",border:!0,loading:d(Z),resizable:!0,onRegister:d(q),headerCellStyle:st,tooltipOptions:{disabled:!0,showArrow:!1,effect:"dark",enterable:!1,offset:0,placement:"top",popperClass:"",popperOptions:{},showAfter:0,hideAfter:0},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),_(d(O),{":span":24},{default:f((()=>[_(d(x),null,{default:f((()=>[_(d(v),{pageSize:d(it),"onUpdate:pageSize":e[2]||(e[2]=t=>g(it)?it.value=t:null),currentPage:d(et),"onUpdate:currentPage":e[3]||(e[3]=t=>g(et)?et.value=t:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:d(tt)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1}),_(d(N),{modelValue:F.value,"onUpdate:modelValue":e[4]||(e[4]=t=>F.value=t),title:"Body Diff  (Last -> Current)",center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},width:"80%","max-height":at.value},{default:f((()=>[_(W,{original:G.value[0],modified:G.value[1]},null,8,["original","modified"])])),_:1},8,["modelValue","max-height"])],64))}}),[["__scopeId","data-v-57499753"]]);export{ee as default};
