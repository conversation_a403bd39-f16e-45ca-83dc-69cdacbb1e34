import{d as s,aq as e,y as a,o as t,i as l,bQ as o,w as r,n,a as i,k as p,f as c,t as f,e as d,j as m,a9 as x}from"./index-DfJTpRkj.js";import{E as g}from"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import{E as u}from"./el-popper-D2BmgSQA.js";const v={class:"flex items-center"},j={class:"text-16px font-700"},w={class:"max-w-200px"},_={class:"flex pl-20px flex-grow"},h=s({__name:"ContentWrap",props:{title:e.string.def(""),message:e.string.def("")},setup(s){const{getPrefixCls:e}=p(),h=e("content-wrap");return(e,p)=>{const k=a("Icon");return t(),l(i(g),{class:n([i(h)]),shadow:"never"},o({default:r((()=>[c("div",null,[x(e.$slots,"default")])])),_:2},[s.title?{name:"header",fn:r((()=>[c("div",v,[c("span",j,f(s.title),1),s.message?(t(),l(i(u),{key:0,effect:"dark",placement:"right"},{content:r((()=>[c("div",w,f(s.message),1)])),default:r((()=>[d(k,{class:"ml-5px",icon:"bi:question-circle-fill",size:14})])),_:1})):m("",!0),c("div",_,[x(e.$slots,"header")])])])),key:"0"}:void 0]),1032,["class"])}}});export{h as _};
