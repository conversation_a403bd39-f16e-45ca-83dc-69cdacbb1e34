import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{c as t}from"./index-CfjbBlcQ.js";import{d as s,l as a,y as i,o as m,i as o,w as p,e as r,z as g,t as c,a as l}from"./index-DfJTpRkj.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-image-viewer-C8GqWGTk.js";import"./debounce-CmAGCOy_.js";const b=s({__name:"ImageViewer",setup(s){const{t:b}=a(),u=()=>{t({urlList:["https://images6.alphacoders.com/657/thumbbig-657194.webp","https://images3.alphacoders.com/677/thumbbig-677688.webp","https://images4.alphacoders.com/200/thumbbig-200966.webp","https://images5.alphacoders.com/657/thumbbig-657248.webp","https://images3.alphacoders.com/679/thumbbig-679917.webp","https://images3.alphacoders.com/737/thumbbig-73785.webp"]})};return(t,s)=>{const a=i("BaseButton");return m(),o(l(e),{title:l(b)("imageViewerDemo.imageViewer"),message:l(b)("imageViewerDemo.imageViewerDes")},{default:p((()=>[r(a,{type:"primary",onClick:u},{default:p((()=>[g(c(l(b)("imageViewerDemo.open")),1)])),_:1})])),_:1},8,["title","message"])}}});export{b as default};
