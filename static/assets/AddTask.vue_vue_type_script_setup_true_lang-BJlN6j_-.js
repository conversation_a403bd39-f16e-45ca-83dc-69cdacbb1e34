import{d as e,s as a,r as l,H as t,O as s,o,c as d,e as u,w as n,a as r,A as i,i as p,j as m,z as c,t as f,B as g,ae as h,F as k,R as v,l as _,M as b,f as y}from"./index-C6fb_XFi.js";import{E as V}from"./el-checkbox-CvJzNe2E.js";import{E as x}from"./el-divider-Bw95UAdD.js";import{a as w,E as T}from"./el-form-C2Y6uNCj.js";import{a as j,E as N}from"./el-col-Dl4_4Pn5.js";import{E}from"./el-switch-Bh7JeorW.js";import"./el-tooltip-l0sNRNKZ.js";import{E as U}from"./el-popper-CeVwVUf9.js";import{E as F,a as C}from"./el-radio-group-hI5DSxSU.js";import"./el-tag-C_oEQYGz.js";import"./el-virtual-list-D7NvYvyu.js";import{E as M}from"./el-select-v2-CaMVABoW.js";import{a as P,E as z}from"./el-select-vbM8Rxr1.js";import{E as A}from"./el-input-number-DVs4I2j5.js";import{E as D}from"./el-text-BnUG9HvL.js";import{a as I}from"./index-CBLGyxDn.js";import{h as S,i as q,f as B,j as L,k as H}from"./index-B40b3p-m.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import G from"./DetailTemplate-Dao-XeZd.js";const J={style:{float:"left"}},K=e({__name:"AddTask",props:{closeDialog:{type:Function},getList:{type:Function},create:{type:Boolean},schedule:{type:Boolean},taskid:{},tp:{},targetIds:{},getFilter:{type:Function},searchParams:{}},setup(e){const{t:K}=_(),O=e,Q=!!O.tp.includes("Source"),W=a({name:[{required:!0,message:K("task.msgTaskName"),trigger:"blur"}],target:[{required:!Q,message:K("task.msgTarget"),trigger:"blur"}],node:[{required:!0,message:K("task.nodeMsg"),trigger:"blur"}],template:[{required:!0,message:"Please select template",trigger:"blur"}]}),X=l(!1),Y=l(),Z=a([]),$=a([]),ee=async()=>{$.length=0;const e=await B("",1,1e3);e.data.list.length>0&&e.data.list.forEach((e=>{$.push({value:e.id,label:e.name})}))};t((()=>{(async()=>{const e=await I();e.data.list.length>0?(le.value=!1,e.data.list.forEach((e=>{Z.push({value:e,label:e})}))):(le.value=!0,b.warning(K("node.onlineNodeMsg")))})(),ee()}));const ae=l(!1),le=l(!1),te=a({name:"",target:"",ignore:"",node:[],allNode:!0,scheduledTasks:!1,hour:24,duplicates:"None",template:""}),se=e=>{ae.value=!1,e?(te.node=[],Z.forEach((e=>te.node.push(e.value)))):te.node=[]},oe=l(""),de=l(!1);let ue=K("task.addTemplate");const ne=async e=>{oe.value=e,""!=e&&(ue=K("task.editTemplate")),de.value=!0},re=()=>{de.value=!1};s((()=>O.taskid),(async e=>{e?O.schedule?await(async e=>{const a=await H(e);te.name=a.data.name,te.target=a.data.target,te.ignore=a.data.ignore,te.node=a.data.node,te.allNode=a.data.allNode,te.scheduledTasks=a.data.scheduledTasks,te.hour=a.data.hour,te.duplicates=a.data.duplicates,te.template=a.data.template})(e):await(async e=>{const a=await L(e);te.name=a.data.name,te.target=a.data.target,te.ignore=a.data.ignore,te.node=a.data.node,te.allNode=a.data.allNode,te.scheduledTasks=a.data.scheduledTasks,te.hour=a.data.hour,te.duplicates=a.data.duplicates,te.template=a.data.template})(e):(te.name="",te.target="",te.ignore="",te.node=[],te.allNode=!0,te.scheduledTasks=!1,te.hour=24,te.duplicates="None",te.template="")}),{immediate:!0});const ie=l("select"),pe=l(0);return(e,l)=>(o(),d(k,null,[u(r(T),{model:te,"label-width":"auto",rules:W,"status-icon":"",ref_key:"ruleFormRef",ref:Y,disabled:!e.create},{default:n((()=>[u(r(w),{label:r(K)("task.taskName"),prop:"name"},{default:n((()=>[u(r(i),{modelValue:te.name,"onUpdate:modelValue":l[0]||(l[0]=e=>te.name=e),placeholder:r(K)("task.msgTaskName")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),u(r(w),{label:r(K)("task.taskTarget"),prop:"target"},{default:n((()=>[r(Q)?m("",!0):(o(),p(r(i),{key:0,modelValue:te.target,"onUpdate:modelValue":l[1]||(l[1]=e=>te.target=e),placeholder:r(K)("task.msgTarget"),type:"textarea",rows:"10"},null,8,["modelValue","placeholder"])),r(Q)?(o(),p(r(C),{key:1,modelValue:ie.value,"onUpdate:modelValue":l[2]||(l[2]=e=>ie.value=e)},{default:n((()=>[u(r(F),{value:"select"},{default:n((()=>[c(f(r(K)("task.select")),1)])),_:1}),u(r(F),{value:"search"},{default:n((()=>[c(f(r(K)("export.exportTypeSearch")),1)])),_:1})])),_:1},8,["modelValue"])):m("",!0)])),_:1},8,["label"]),"search"==ie.value&&r(Q)?(o(),p(r(w),{key:0,label:r(K)("task.targetNumber")},{default:n((()=>[u(r(i),{modelValue:pe.value,"onUpdate:modelValue":l[3]||(l[3]=e=>pe.value=e)},null,8,["modelValue"])])),_:1},8,["label"])):m("",!0),u(r(w),{label:r(K)("task.ignore"),prop:"ignore"},{default:n((()=>[u(r(i),{modelValue:te.ignore,"onUpdate:modelValue":l[4]||(l[4]=e=>te.ignore=e),placeholder:r(K)("task.ignoreMsg"),type:"textarea",rows:"10"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),u(r(j),null,{default:n((()=>[u(r(N),{span:12},{default:n((()=>[u(r(w),{label:r(K)("task.nodeSelect"),prop:"node"},{default:n((()=>[u(r(M),{modelValue:te.node,"onUpdate:modelValue":l[5]||(l[5]=e=>te.node=e),filterable:"",options:Z,placeholder:"Please select node",style:{width:"80%"},multiple:"","tag-type":"success","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":7},{header:n((()=>[u(r(V),{disabled:le.value,indeterminate:ae.value,onChange:se},{default:n((()=>[c(" All ")])),_:1},8,["disabled","indeterminate"])])),_:1},8,["modelValue","options"])])),_:1},8,["label"])])),_:1}),u(r(N),{span:12},{default:n((()=>[u(r(w),{label:r(K)("task.autoNode")},{default:n((()=>[u(r(U),{effect:"dark",content:r(K)("task.selectNodeMsg"),placement:"top"},{default:n((()=>[u(r(E),{modelValue:te.allNode,"onUpdate:modelValue":l[6]||(l[6]=e=>te.allNode=e),"inline-prompt":"","active-text":r(K)("common.switchAction"),"inactive-text":r(K)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["content"])])),_:1},8,["label"])])),_:1})])),_:1}),u(r(j),null,{default:n((()=>[u(r(N),{span:12},{default:n((()=>[u(r(w),{label:r(K)("project.scheduledTasks")},{default:n((()=>[u(r(U),{effect:"dark",content:r(K)("project.msgScheduledTasks"),placement:"top"},{default:n((()=>[u(r(E),{modelValue:te.scheduledTasks,"onUpdate:modelValue":l[7]||(l[7]=e=>te.scheduledTasks=e),"inline-prompt":"","active-text":r(K)("common.switchAction"),"inactive-text":r(K)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["content"])])),_:1},8,["label"])])),_:1}),te.scheduledTasks?(o(),p(r(N),{key:0,span:12},{default:n((()=>[u(r(w),{label:r(K)("project.cycle"),prop:"type"},{default:n((()=>[u(r(A),{modelValue:te.hour,"onUpdate:modelValue":l[8]||(l[8]=e=>te.hour=e),min:1,"controls-position":"right",size:"small"},null,8,["modelValue"]),u(r(D),{style:{position:"relative",left:"16px"}},{default:n((()=>[c("Hour")])),_:1})])),_:1},8,["label"])])),_:1})):m("",!0)])),_:1}),u(r(x),{"content-position":"center",style:{width:"60%",left:"20%"}},{default:n((()=>[c(f(r(K)("task.duplicates")),1)])),_:1}),u(r(j),null,{default:n((()=>[u(r(N),{span:24},{default:n((()=>[u(r(w),{label:r(K)("task.duplicates"),prop:"type"},{default:n((()=>[u(r(C),{modelValue:te.duplicates,"onUpdate:modelValue":l[9]||(l[9]=e=>te.duplicates=e)},{default:n((()=>[u(r(F),{label:"None",name:"duplicates",checked:!0,value:"None"}),u(r(U),{effect:"dark",content:r(K)("task.duplicatesMsg"),placement:"top"},{default:n((()=>[u(r(F),{label:r(K)("task.duplicatesSubdomain"),name:"duplicates",value:"subdomain"},null,8,["label"])])),_:1},8,["content"])])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),u(r(x),{"content-position":"center",style:{width:"60%",left:"20%"}},{default:n((()=>[c(f(r(K)("router.scanTemplate")),1)])),_:1}),u(r(w),{label:r(K)("router.scanTemplate"),prop:"template"},{default:n((()=>[u(r(P),{modelValue:te.template,"onUpdate:modelValue":l[11]||(l[11]=e=>te.template=e),placeholder:"Please select template",style:{width:"30%"}},{footer:n((()=>[u(r(g),{type:"success",size:"small",plain:"",style:{"margin-left":"15px"},onClick:l[10]||(l[10]=h((e=>ne("")),["stop"]))},{default:n((()=>[c(f(r(K)("common.new")),1)])),_:1})])),default:n((()=>[(o(!0),d(k,null,v($,(e=>(o(),p(r(z),{key:e.value,label:e.label,value:e.value},{default:n((()=>[u(r(j),null,{default:n((()=>[u(r(N),{span:16},{default:n((()=>[y("span",J,f(e.label),1)])),_:2},1024),u(r(N),{span:8},{default:n((()=>[u(r(g),{type:"primary",size:"small",style:{"margin-left":"15px"},onClick:h((a=>ne(e.value)),["stop"])},{default:n((()=>[c(f(r(K)("common.edit")),1)])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),u(r(x)),u(r(j),null,{default:n((()=>[u(r(N),{span:2,offset:10},{default:n((()=>[u(r(w),null,{default:n((()=>[u(r(g),{type:"primary",onClick:l[12]||(l[12]=e=>(async e=>{X.value=!0;try{if(!e)return;if(await e.validate()){let e;if(O.taskid)e=await S(O.taskid,te.name,te.target,te.ignore,te.node,te.allNode,te.duplicates,te.scheduledTasks,te.hour,te.template);else{let l=a({}),t="";"search"==ie.value&&(O.getFilter&&(l=O.getFilter()),O.searchParams&&(t=O.searchParams)),e=await q(te.name,te.target,te.ignore,te.node,te.allNode,te.duplicates,te.scheduledTasks,te.hour,te.template,O.tp,ie.value,t,l,pe.value,O.targetIds)}200===e.code&&(O.closeDialog(),O.getList())}}catch(l){}finally{X.value=!1}})(Y.value)),loading:X.value},{default:n((()=>[c(f(r(K)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules","disabled"]),u(r(R),{modelValue:de.value,"onUpdate:modelValue":l[13]||(l[13]=e=>de.value=e),title:r(ue),center:"",fullscreen:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:n((()=>[u(G,{closeDialog:re,getList:ee,id:oe.value},null,8,["id"])])),_:1},8,["modelValue","title"])],64))}});export{K as _};
