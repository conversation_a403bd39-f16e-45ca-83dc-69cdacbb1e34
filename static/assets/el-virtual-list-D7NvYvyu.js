import{aQ as e,a7 as t,cl as a,bS as r,Z as l,Y as n,$ as o,d as s,a6 as i,r as u,s as c,cm as d,a as v,O as m,x as p,v as f,ae as g}from"./index-C6fb_XFi.js";import{c as h,r as y}from"./raf-DGOAeO92.js";var b=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function S(e,t){if(e.length!==t.length)return!1;for(var a=0;a<e.length;a++)if(r=e[a],l=t[a],!(r===l||b(r)&&b(l)))return!1;var r,l;return!0}const w=()=>{const r=e().proxy.$props;return t((()=>{const e=(e,t,a)=>({});return r.perfMode?a(e):function(e,t){void 0===t&&(t=S);var a=null;function r(){for(var r=[],l=0;l<arguments.length;l++)r[l]=arguments[l];if(a&&a.lastThis===this&&t(r,a.lastArgs))return a.lastResult;var n=e.apply(this,r);return a={lastResult:n,lastArgs:r,lastThis:this},n}return r.clear=function(){a=null},r}(e)}))},x=50,N="itemRendered",z="scroll",E="forward",M="backward",L="auto",C="smart",I="start",B="center",R="end",T="horizontal",O="vertical",$="rtl",q="negative",D="positive-ascending",F="positive-descending",G={[T]:"left",[O]:"top"},j=r({type:l([Number,Function]),required:!0}),A=r({type:Number}),H=r({type:Number,default:2}),k=r({type:String,values:["ltr","rtl"],default:"ltr"}),P=r({type:Number,default:0}),V=r({type:Number,required:!0}),K=r({type:String,values:["horizontal","vertical"],default:O}),W=n({className:{type:String,default:""},containerElement:{type:l([String,Object]),default:"div"},data:{type:l(Array),default:()=>o([])},direction:k,height:{type:[String,Number],required:!0},innerElement:{type:[String,Object],default:"div"},style:{type:l([Object,String,Array])},useIsScrolling:{type:Boolean,default:!1},width:{type:[Number,String],required:!1},perfMode:{type:Boolean,default:!0},scrollbarAlwaysOn:{type:Boolean,default:!1}}),Y=n({cache:H,estimatedItemSize:A,layout:K,initScrollOffset:P,total:V,itemSize:j,...W}),Q={type:Number,default:6},Z={type:Number,default:0},_={type:Number,default:2},J=n({columnCache:H,columnWidth:j,estimatedColumnWidth:A,estimatedRowHeight:A,initScrollLeft:P,initScrollTop:P,itemKey:{type:l(Function),default:({columnIndex:e,rowIndex:t})=>`${t}:${e}`},rowCache:H,rowHeight:j,totalColumn:V,totalRow:V,hScrollbarSize:Q,vScrollbarSize:Q,scrollbarStartGap:Z,scrollbarEndGap:_,role:String,...W}),U=n({alwaysOn:Boolean,class:String,layout:K,total:V,ratio:{type:Number,required:!0},clientSize:{type:Number,required:!0},scrollFrom:{type:Number,required:!0},scrollbarSize:Q,startGap:Z,endGap:_,visible:Boolean}),X=(e,t)=>e<t?E:M,ee=e=>"ltr"===e||e===$||e===T,te=e=>e===$;let ae=null;function re(e=!1){if(null===ae||e){const e=document.createElement("div"),t=e.style;t.width="50px",t.height="50px",t.overflow="scroll",t.direction="rtl";const a=document.createElement("div"),r=a.style;return r.width="100px",r.height="100px",e.appendChild(a),document.body.appendChild(e),e.scrollLeft>0?ae=F:(e.scrollLeft=1,ae=0===e.scrollLeft?q:D),document.body.removeChild(e),ae}return ae}const le=s({name:"ElVirtualScrollBar",props:U,emits:["scroll","start-move","stop-move"],setup(e,{emit:a}){const r=t((()=>e.startGap+e.endGap)),l=i("virtual-scrollbar"),n=i("scrollbar"),o=u(),s=u();let b=null,S=null;const w=c({isDragging:!1,traveled:0}),x=t((()=>d[e.layout])),N=t((()=>e.clientSize-v(r))),z=t((()=>({position:"absolute",width:`${T===e.layout?N.value:e.scrollbarSize}px`,height:`${T===e.layout?e.scrollbarSize:N.value}px`,[G[e.layout]]:"2px",right:"2px",bottom:"2px",borderRadius:"4px"}))),E=t((()=>{const t=e.ratio,a=e.clientSize;if(t>=100)return Number.POSITIVE_INFINITY;if(t>=50)return t*a/100;const r=a/3;return Math.floor(Math.min(Math.max(t*a,20),r))})),M=t((()=>{if(!Number.isFinite(E.value))return{display:"none"};const t=`${E.value}px`,a=function({move:e,size:t,bar:a},r){const l={},n=`translate${a.axis}(${e}px)`;return l[a.size]=t,l.transform=n,l.msTransform=n,l.webkitTransform=n,"horizontal"===r?l.height="100%":l.width="100%",l}({bar:x.value,size:t,move:w.traveled},e.layout);return a})),L=t((()=>Math.floor(e.clientSize-E.value-v(r)))),C=()=>{window.removeEventListener("mousemove",R),window.removeEventListener("mouseup",B),document.onselectstart=S,S=null;const e=v(s);e&&(e.removeEventListener("touchmove",R),e.removeEventListener("touchend",B))},I=e=>{e.stopImmediatePropagation(),e.ctrlKey||[1,2].includes(e.button)||(w.isDragging=!0,w[x.value.axis]=e.currentTarget[x.value.offset]-(e[x.value.client]-e.currentTarget.getBoundingClientRect()[x.value.direction]),a("start-move"),(()=>{window.addEventListener("mousemove",R),window.addEventListener("mouseup",B);const e=v(s);e&&(S=document.onselectstart,document.onselectstart=()=>!1,e.addEventListener("touchmove",R),e.addEventListener("touchend",B))})())},B=()=>{w.isDragging=!1,w[x.value.axis]=0,a("stop-move"),C()},R=t=>{const{isDragging:r}=w;if(!r)return;if(!s.value||!o.value)return;const l=w[x.value.axis];if(!l)return;h(b);const n=-1*(o.value.getBoundingClientRect()[x.value.direction]-t[x.value.client])-(s.value[x.value.offset]-l);b=y((()=>{w.traveled=Math.max(e.startGap,Math.min(n,L.value)),a("scroll",n,L.value)}))},O=e=>{const t=Math.abs(e.target.getBoundingClientRect()[x.value.direction]-e[x.value.client])-s.value[x.value.offset]/2;w.traveled=Math.max(0,Math.min(t,L.value)),a("scroll",t,L.value)};return m((()=>e.scrollFrom),(e=>{w.isDragging||(w.traveled=Math.ceil(e*L.value))})),p((()=>{C()})),()=>f("div",{role:"presentation",ref:o,class:[l.b(),e.class,(e.alwaysOn||w.isDragging)&&"always-on"],style:z.value,onMousedown:g(O,["stop","prevent"]),onTouchstartPrevent:I},f("div",{ref:s,class:n.e("thumb"),style:M.value,onMousedown:I},[]))}});export{L as A,M as B,B as C,x as D,R as E,E as F,T as H,N as I,$ as R,z as S,O as V,D as a,q as b,le as c,X as d,F as e,I as f,re as g,C as h,ee as i,J as j,te as k,U as l,w as u,Y as v};
