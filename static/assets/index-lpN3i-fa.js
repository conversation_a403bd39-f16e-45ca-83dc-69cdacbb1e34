import{r as a}from"./index-Dz8ZrwBc.js";const e=()=>a.get({url:"/api/node/data"}),t=e=>a.post({url:"/api/node/restart",data:{name:e}}),o=()=>a.get({url:"/api/node/data/online"}),d=(e,t,o,d)=>a.post({url:"/api/node/config/update",data:{oldName:e,name:t,ModulesConfig:o,state:d}}),n=e=>a.post({url:"/api/node/delete",data:{names:e}}),s=e=>a.post({url:"/api/node/log/data",data:{name:e}}),p=e=>a.post({url:"/api/node/plugin",data:{name:e}});export{o as a,s as b,p as c,n as d,e as g,t as r,d as u};
