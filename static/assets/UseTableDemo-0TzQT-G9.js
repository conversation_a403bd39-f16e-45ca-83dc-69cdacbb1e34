import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,a,l,s as o,e as i,z as n,G as s,r,o as d,c as m,w as p,t as u,I as c,F as b,J as f,_ as g}from"./index-C6fb_XFi.js";import{_ as D}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{a as _}from"./index-Bh6CT4kq.js";import{E as j}from"./el-tag-C_oEQYGz.js";import{u as h}from"./useTable-CijeIiBB.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";function v(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!f(e)}const x=g(t({__name:"UseTableDemo",setup(t){const{tableRegister:f,tableMethods:g,tableState:x}=h({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=x,l=await _({pageIndex:a(e),pageSize:a(t)});return{list:l.data.list,total:l.data.total}}}),{loading:y,dataList:C,total:w,currentPage:k,pageSize:S}=x,{setProps:A,setColumn:T,getElTableExpose:R,addColumn:z,delColumn:I,refresh:E}=g,{t:O}=l(),P=o([{field:"expand",type:"expand",slots:{default:e=>{const{row:t}=e;return i("div",{class:"ml-30px"},[i("div",null,[O("tableDemo.title"),n("："),t.title]),i("div",null,[O("tableDemo.author"),n("："),t.author]),i("div",null,[O("tableDemo.displayTime"),n("："),t.display_time])])}}},{field:"selection",type:"selection"},{field:"index",label:O("tableDemo.index"),type:"index"},{field:"content",label:O("tableDemo.header"),children:[{field:"title",label:O("tableDemo.title")},{field:"author",label:O("tableDemo.author")},{field:"display_time",label:O("tableDemo.displayTime")},{field:"importance",label:O("tableDemo.importance"),formatter:(e,t,a)=>i(j,{type:1===a?"success":2===a?"warning":"danger"},{default:()=>[O(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")]})},{field:"pageviews",label:O("tableDemo.pageviews")}]},{field:"action",label:O("tableDemo.action"),slots:{default:e=>{let t;return i(s,{type:"primary",onClick:()=>U(e)},v(t=O("tableDemo.action"))?t:{default:()=>[t]})}}}]),U=e=>{},H=r(!0),$=e=>{H.value=e},F=e=>{A({reserveIndex:e})},J=e=>{T([{field:"selection",path:"hidden",value:!e}])},L=r(1),W=()=>{T([{field:"title",path:"label",value:`${O("tableDemo.title")}${a(L)}`}]),L.value++},G=e=>{T([{field:"expand",path:"hidden",value:!e}])},M=async()=>{const e=await R();null==e||e.toggleAllSelection()},N=r(!0),X=()=>{a(N)?(I("action"),N.value=!1):(z({field:"action",label:O("tableDemo.action"),slots:{default:e=>{let t;return i(s,{type:"primary",onClick:()=>U(e)},v(t=O("tableDemo.action"))?t:{default:()=>[t]})}}}),N.value=!0)},Y=r(!1),q=()=>{A({stripe:!a(Y)}),Y.value=!a(Y)},B=r("auto"),K=()=>{"auto"===a(B)?(A({height:300}),B.value=300):(A({height:"auto"}),B.value="auto")},Q=async()=>{const e=await R();null==e||e.getSelectionRows()};return(t,l)=>(d(),m(b,null,[i(a(e),{title:`UseTable ${a(O)("tableDemo.operate")}`,style:{"margin-bottom":"20px"}},{default:p((()=>[i(a(s),{onClick:l[0]||(l[0]=e=>$(!0))},{default:p((()=>[n(u(a(O)("tableDemo.show"))+" "+u(a(O)("tableDemo.pagination")),1)])),_:1}),i(a(s),{onClick:l[1]||(l[1]=e=>$(!1))},{default:p((()=>[n(u(a(O)("tableDemo.hidden"))+" "+u(a(O)("tableDemo.pagination")),1)])),_:1}),i(a(s),{onClick:l[2]||(l[2]=e=>F(!0))},{default:p((()=>[n(u(a(O)("tableDemo.reserveIndex")),1)])),_:1}),i(a(s),{onClick:l[3]||(l[3]=e=>F(!1))},{default:p((()=>[n(u(a(O)("tableDemo.restoreIndex")),1)])),_:1}),i(a(s),{onClick:l[4]||(l[4]=e=>J(!0))},{default:p((()=>[n(u(a(O)("tableDemo.showSelections")),1)])),_:1}),i(a(s),{onClick:l[5]||(l[5]=e=>J(!1))},{default:p((()=>[n(u(a(O)("tableDemo.hiddenSelections")),1)])),_:1}),i(a(s),{onClick:W},{default:p((()=>[n(u(a(O)("tableDemo.changeTitle")),1)])),_:1}),i(a(s),{onClick:l[6]||(l[6]=e=>G(!0))},{default:p((()=>[n(u(a(O)("tableDemo.showExpandedRows")),1)])),_:1}),i(a(s),{onClick:l[7]||(l[7]=e=>G(!1))},{default:p((()=>[n(u(a(O)("tableDemo.hiddenExpandedRows")),1)])),_:1}),i(a(s),{onClick:M},{default:p((()=>[n(u(a(O)("tableDemo.selectAllNone")),1)])),_:1}),i(a(s),{onClick:X},{default:p((()=>[n(u(a(O)("tableDemo.delOrAddAction")),1)])),_:1}),i(a(s),{onClick:q},{default:p((()=>[n(u(a(O)("tableDemo.showOrHiddenStripe")),1)])),_:1}),i(a(s),{onClick:K},{default:p((()=>[n(u(a(O)("tableDemo.fixedHeaderOrAuto")),1)])),_:1}),i(a(s),{onClick:Q},{default:p((()=>[n(u(a(O)("tableDemo.getSelections")),1)])),_:1})])),_:1},8,["title"]),i(a(e),{title:`UseTable ${a(O)("tableDemo.example")}`},{default:p((()=>[i(a(D),{pageSize:a(S),"onUpdate:pageSize":l[8]||(l[8]=e=>c(S)?S.value=e:null),currentPage:a(k),"onUpdate:currentPage":l[9]||(l[9]=e=>c(k)?k.value=e:null),showAction:"",columns:P,data:a(C),loading:a(y),pagination:H.value?{total:a(w)}:void 0,onRegister:a(f),onRefresh:a(E)},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister","onRefresh"])])),_:1},8,["title"])],64))}}),[["__scopeId","data-v-daa5600d"]]);export{x as default};
