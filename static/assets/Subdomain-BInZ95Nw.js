import{d as e,H as t,r as a,s as i,v as l,A as s,B as o,o as r,c as n,e as p,a as u,w as m,I as d,F as c,l as j,Y as g,_ as f}from"./index-3XfDPlIS.js";import{u as v}from"./useTable-BezX3TfM.js";import{E as h}from"./el-card-CuEws33_.js";import{E as b}from"./el-pagination-DwzzZyu4.js";import{E as x}from"./el-tag-DcMbxLLg.js";import"./el-select-DH55-Cab.js";import"./el-popper-DVoWBu_3.js";import{E as y,a as _}from"./el-col-CN1tVfqh.js";import{_ as S}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as E}from"./useCrudSchemas-6tFKup3N.js";import{a as C,d as w,v as T}from"./index-BAb9yQka.js";import k from"./Csearch-CpC9XwHn.js";import"./index-tjM0-mlU.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-table-column-B5hg3WH6.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./tree-BfZhwLPs.js";import"./index-Dz8ZrwBc.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import"./el-text-CLWE0mUm.js";import"./el-divider-D9UCOo44.js";import"./el-autocomplete-DpYoUHkX.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-switch-C-DLgt5X.js";import"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import"./useIcon-k-uSyz6l.js";import"./exportData.vue_vue_type_script_setup_true_lang--cnrLcU_.js";import"./el-tab-pane-xcqYouKU.js";import"./el-form-BY8piFS2.js";import"./el-radio-group-evFfsZkP.js";import"./el-space-BFgHxiAK.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import"./el-virtual-list-Drl4IGmp.js";import"./el-select-v2-CJw7ZO42.js";import"./index-lpN3i-fa.js";import"./index-BuRY9bVn.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-Cyn7ZzlS.js";import"./index-D4TGn7aE.js";const A=f(e({__name:"Subdomain",props:{projectList:{}},setup(e){const{t:f}=j(),A=[{keyword:"ip",example:'ip="***********"',explain:f("searchHelp.ip")},{keyword:"domain",example:'domain="example.com"',explain:f("searchHelp.domain")},{keyword:"type",example:'type="CNAME"',explain:f("searchHelp.subdomainType")},{keyword:"value",example:'value="allcdn.example.com"',explain:f("searchHelp.subdoaminValue")},{keyword:"project",example:'project="Hackerone"',explain:f("searchHelp.project")}];t((()=>{z(),window.addEventListener("resize",z)}));const V=a(0),z=()=>{const e=window.innerHeight||document.documentElement.clientHeight;V.value=.7*e},H=a(""),N=e=>{H.value=e,G()},I=i({}),L=i([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:f("tableDemo.index"),type:"index",minWidth:"30"},{field:"host",label:f("subdomain.subdomainName"),minWidth:"200"},{field:"type",label:f("subdomain.recordType"),minWidth:"200",columnKey:"type",filters:[{text:"A",value:"A"},{text:"NS",value:"NS"},{text:"CNAME",value:"CNAME"},{text:"PTR",value:"PTR"},{text:"TXT",value:"TXT"}]},{field:"value",label:f("subdomain.recordValue"),minWidth:"250",formatter:(e,t,a)=>{let i="";return a.forEach(((e,t)=>{i+=`${e}\r\n`})),i}},{field:"ip",label:"IP",minWidth:"150",formatter:(e,t,a)=>{let i="";return a.forEach(((e,t)=>{i+=`${e}\r\n`})),i}},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,i)=>{null==i&&(i=[]),I[e.id]||(I[e.id]={inputVisible:!1,inputValue:"",inputRef:a(null)});const r=I[e.id],n=async()=>{r.inputValue&&(i.push(r.inputValue),C(e.id,P,r.inputValue)),r.inputVisible=!1,r.inputValue=""};return l(_,{},(()=>[...i.map((t=>l(y,{span:24,key:t},(()=>[l("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||te("tags",t)})(e,t)},[l(x,{closable:!0,onClose:()=>(async t=>{const a=i.indexOf(t);a>-1&&i.splice(a,1),w(e.id,P,t)})(t)},(()=>t))])])))),l(y,{span:24},r.inputVisible?()=>l(s,{ref:r.inputRef,modelValue:r.inputValue,"onUpdate:modelValue":e=>r.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&n()},onBlur:n}):()=>l(o,{class:"button-new-tag",size:"small",onClick:()=>(r.inputVisible=!0,void g((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:f("asset.time"),minWidth:"200"}]);let P="subdomain";L.forEach((e=>{e.hidden=e.hidden??!1}));let R=a(!1);const U=({field:e,hidden:t})=>{const a=L.findIndex((t=>t.field===e));-1!==a&&(L[a].hidden=t),(()=>{const e=L.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=R.value,localStorage.setItem(`columnConfig_${P}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${P}`)||"{}");L.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),R.value=e.statisticsHidden})();const{allSchemas:W}=E(L),{tableRegister:$,tableState:D,tableMethods:F}=v({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=D,a=await T(H.value,e.value,t.value,Q);return{list:a.data.list,total:a.data.total}},immediate:!1}),{loading:O,dataList:M,total:B,currentPage:K,pageSize:J}=D,{getList:G,getElTableExpose:X}=F;function q(){return{background:"var(--el-fill-color-light)"}}const Q=i({}),Y=async e=>{Object.assign(Q,e),G()},Z=(e,t)=>{Object.assign(Q,t),H.value=e,G()},ee=a([]),te=(e,t)=>{const a=`${e}=${t}`;ee.value=[...ee.value,a]},ae=e=>{if(ee.value){const[t,a]=e.split("=");t in Q&&Array.isArray(Q[t])&&(Q[t]=Q[t].filter((e=>e!==a)),0===Q[t].length&&delete Q[t]),ee.value=ee.value.filter((t=>t!==e))}},ie=()=>Q;return(e,t)=>(r(),n(c,null,[p(k,{getList:u(G),handleSearch:N,searchKeywordsData:A,index:u(P),getElTableExpose:u(X),projectList:e.$props.projectList,handleFilterSearch:Z,crudSchemas:L,dynamicTags:ee.value,handleClose:ae,onUpdateColumnVisibility:U,searchResultCount:u(B),getFilter:ie},null,8,["getList","index","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),p(u(_),null,{default:m((()=>[p(u(y),null,{default:m((()=>[p(u(h),{style:{height:"min-content"}},{default:m((()=>[p(u(S),{pageSize:u(J),"onUpdate:pageSize":t[0]||(t[0]=e=>d(J)?J.value=e:null),currentPage:u(K),"onUpdate:currentPage":t[1]||(t[1]=e=>d(K)?K.value=e:null),columns:u(W).tableColumns,data:u(M),stripe:"",border:!0,loading:u(O),resizable:!0,onRegister:u($),onFilterChange:Y,headerCellStyle:q,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","onRegister"])])),_:1})])),_:1}),p(u(y),{":span":24},{default:m((()=>[p(u(h),null,{default:m((()=>[p(u(b),{pageSize:u(J),"onUpdate:pageSize":t[2]||(t[2]=e=>d(J)?J.value=e:null),currentPage:u(K),"onUpdate:currentPage":t[3]||(t[3]=e=>d(K)?K.value=e:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:u(B)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-afb0277c"]]);export{A as default};
