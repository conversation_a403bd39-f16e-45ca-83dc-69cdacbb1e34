import{d as e,o as t,i as r,w as s,e as o,a as i,l as p}from"./index-DfJTpRkj.js";import{E as m,a}from"./el-tab-pane-BijWf7kq.js";import l from"./Dashboard-C0kkeTth.js";import j from"./Subdomain-CF_IqU20.js";import d from"./Port-CBDbOSEx.js";import n from"./Service-DNVq4dnK.js";import"./strings-CUyZ1T6U.js";import"./debounce-CmAGCOy_.js";import"./el-col-B4Ik8fnS.js";import"./el-card-DyZz6u6e.js";import"./el-descriptions-item-BVaiX-1w.js";import"./el-tag-CbhrEnto.js";import"./el-divider-0NmzbuNU.js";import"./el-skeleton-item-BCvv4WDW.js";import"./el-table-column-7FjdLFwR.js";import"./el-popper-D2BmgSQA.js";import"./index-DE7jtbbk.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-text-vKNLRkxx.js";import"./el-space-7M-SGVBd.js";import"./useIcon-CNpM61rT.js";import"./index-Bz_w54LI.js";import"./index-D1ADinPR.js";import"./index-D4GvAO2k.js";import"./useTable-CtyddZqf.js";import"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./useCrudSchemas-Cz9y99Kk.js";import"./tree-BfZhwLPs.js";const b=e({__name:"ProjectDetail",setup(e){const{t:b}=p();return(e,p)=>(t(),r(i(a),{type:"border-card"},{default:s((()=>[o(i(m),{label:i(b)("project.overview")},{default:s((()=>[o(l)])),_:1},8,["label"]),o(i(m),{label:i(b)("subdomain.subdomainName")},{default:s((()=>[o(j)])),_:1},8,["label"]),o(i(m),{label:i(b)("asset.port")},{default:s((()=>[o(d)])),_:1},8,["label"]),o(i(m),{label:i(b)("asset.service")},{default:s((()=>[o(n)])),_:1},8,["label"])])),_:1}))}});export{b as default};
