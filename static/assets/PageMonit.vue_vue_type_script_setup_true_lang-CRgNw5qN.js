import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as a,r as t,s as l,e as o,G as s,F as i,H as n,o as r,c as u,w as d,a as p,z as c,A as m,B as f,f as g,t as v,I as y,J as _,l as h}from"./index-3XfDPlIS.js";import{a as b,E as x}from"./el-col-CN1tVfqh.js";import{E as w}from"./el-text-CLWE0mUm.js";import{E as S,a as j}from"./el-form-BY8piFS2.js";import{_ as U}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{_ as k}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as R}from"./useTable-BezX3TfM.js";import{u as E}from"./useIcon-k-uSyz6l.js";import{q as C,t as z,v as A}from"./index-BuRY9bVn.js";const L={class:"mb-10px"},V={style:{position:"relative",top:"12px"}};const P=a({__name:"PageMonit",setup(a){const P=E({icon:"iconoir:search"}),{t:I}=h(),H=t(""),T=()=>{J()},F=l([{field:"selection",type:"selection",width:"55"},{field:"url",label:"URL"},{field:"action",label:I("tableDemo.action"),minWidth:20,formatter:(e,a,t)=>{let l;return o(i,null,[o(s,{type:"danger",onClick:()=>Y(e)},(n=l=I("common.delete"),"function"==typeof n||"[object Object]"===Object.prototype.toString.call(n)&&!_(n)?l:{default:()=>[l]}))]);var n}}]),{tableRegister:M,tableState:D,tableMethods:N}=R({fetchDataApi:async()=>{const{currentPage:e,pageSize:a}=D,t=await z(H.value,e.value,a.value);return{list:t.data.list,total:t.data.total}},immediate:!0}),{loading:O,dataList:B,total:W,currentPage:q,pageSize:G}=D;G.value=20;const{getList:J,getElTableExpose:K}=N;function Q(){return{background:"var(--el-fill-color-light)"}}const X=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await ae()},Y=async e=>{window.confirm("Are you sure you want to delete the selected data?")&&await $(e)},Z=t(!1),$=async e=>{Z.value=!0;try{await C([e.id]);Z.value=!1,J()}catch(a){Z.value=!1,J()}},ee=t([]),ae=async()=>{const e=await K(),a=(null==e?void 0:e.getSelectionRows())||[];ee.value=a.map((e=>e.id)),Z.value=!0;try{await C(ee.value);Z.value=!1,J()}catch(t){Z.value=!1,J()}};n((()=>{le(),window.addEventListener("resize",le)}));const te=t(0),le=()=>{const e=window.innerHeight||document.documentElement.clientHeight;te.value=.75*e},oe=t(!1),se=async()=>{oe.value=!0},ie=l({url:""}),ne=t(!1);return(a,t)=>(r(),u(i,null,[o(p(e),null,{default:d((()=>[o(p(b),null,{default:d((()=>[o(p(x),{span:1},{default:d((()=>[o(p(w),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:d((()=>[c("URL:")])),_:1})])),_:1}),o(p(x),{span:5},{default:d((()=>[o(p(m),{modelValue:H.value,"onUpdate:modelValue":t[0]||(t[0]=e=>H.value=e),placeholder:p(I)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(p(x),{span:5,style:{position:"relative",left:"16px"}},{default:d((()=>[o(p(f),{type:"primary",icon:p(P),style:{height:"100%"},onClick:T},{default:d((()=>[c("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(p(b),null,{default:d((()=>[o(p(x),{style:{position:"relative",top:"16px"}},{default:d((()=>[g("div",L,[o(p(s),{type:"primary",onClick:se},{default:d((()=>[c(v(p(I)("task.addURL")),1)])),_:1}),o(p(s),{type:"danger",loading:Z.value,onClick:X},{default:d((()=>[c(v(p(I)("task.delURL")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),g("div",V,[o(p(k),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:p(G),"onUpdate:pageSize":t[1]||(t[1]=e=>y(G)?G.value=e:null),currentPage:p(q),"onUpdate:currentPage":t[2]||(t[2]=e=>y(q)?q.value=e:null),columns:F,data:p(B),stripe:"",border:!0,loading:p(O),"max-height":te.value,resizable:!0,pagination:{total:p(W),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:p(M),headerCellStyle:Q,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])])])),_:1}),o(p(U),{modelValue:oe.value,"onUpdate:modelValue":t[5]||(t[5]=e=>oe.value=e),title:p(I)("task.addPageMonitTask"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:"100"},{default:d((()=>[o(p(S),{model:ie,"label-width":"auto","status-icon":"",ref:"ruleFormRef"},{default:d((()=>[o(p(j),{label:"URL",prop:"url"},{default:d((()=>[o(p(m),{modelValue:ie.url,"onUpdate:modelValue":t[3]||(t[3]=e=>ie.url=e),placeholder:"Input URL. Eg: http(s)://xxx.com"},null,8,["modelValue"])])),_:1}),o(p(b),null,{default:d((()=>[o(p(x),{span:2,offset:8},{default:d((()=>[o(p(j),null,{default:d((()=>[o(p(f),{type:"primary",onClick:t[4]||(t[4]=e=>(async()=>{ne.value=!0,await A(ie.url),ne.value=!1,oe.value=!1})()),loading:ne.value},{default:d((()=>[c(v(p(I)("common.submit")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])],64))}});export{P as _};
