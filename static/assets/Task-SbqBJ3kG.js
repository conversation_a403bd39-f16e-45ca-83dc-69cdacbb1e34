import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as a,u as t,r as l,s,v as o,B as i,C as n,D as r,e as u,G as d,F as p,H as c,o as m,c as g,w as v,a as f,z as y,t as k,A as h,f as j,I as b,J as x,l as w,K as _}from"./index-DfJTpRkj.js";import{a as S,E as T}from"./el-col-B4Ik8fnS.js";import{E as V}from"./el-text-vKNLRkxx.js";import{E as C}from"./el-progress-Wzr98AjO.js";import{E as P}from"./el-tag-CbhrEnto.js";import{E}from"./el-switch-C5ZBDFmL.js";import{E as I,a as U,b as A}from"./el-dropdown-item-nnpzYk3y.js";import"./el-popper-D2BmgSQA.js";import{E as D,a as z}from"./el-radio-group-CTAZlJKV.js";/* empty css                        */import"./el-select-BkpcrSfo.js";/* empty css                */import"./el-checkbox-DU4wMKRd.js";import{E as R}from"./el-tree-select-P6ZpyTcB.js";import{_ as L}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as W}from"./useTable-CtyddZqf.js";import{u as N}from"./useIcon-CNpM61rT.js";import{s as H,a as F,g as O,b as B,d as M,r as $}from"./index-B0JD2UFG.js";import{_ as G}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{_ as J}from"./AddTask.vue_vue_type_script_setup_true_lang-BD8eoln6.js";import{_ as K}from"./ProgressInfo.vue_vue_type_script_setup_true_lang-PvoKMsLj.js";import{g as Q}from"./index-jyMftxhc.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-pagination-FJcT0ZDj.js";import"./index-DE7jtbbk.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./index-Co3LqSsp.js";import"./el-table-column-7FjdLFwR.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";import"./el-divider-0NmzbuNU.js";import"./el-form-DsaI0u2w.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./index-KH6atv8j.js";import"./DetailTemplate-DU3RXrBH.js";import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";const q={class:"mb-10px"},X={style:{position:"relative",top:"12px"}};function Y(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!x(e)}const Z=a({__name:"Task",setup(a){const{push:x}=t(),Z=N({icon:"iconoir:search"}),{t:ee}=w(),ae=l(""),te=()=>{he()},le=s([{field:"selection",type:"selection",minWidth:55},{field:"name",label:ee("task.taskName"),minWidth:100},{field:"taskNum",label:ee("task.taskCount"),minWidth:70,formatter:(e,a,t)=>o(P,{type:"info"},(()=>t))},{field:"progress",label:ee("task.taskProgress"),minWidth:200,formatter:(e,a,t)=>o(C,{percentage:t,type:"line",striped:!0,status:t<100?"":"success",stripedFlow:t<100})},{field:"status",label:ee("common.state"),minWidth:200,formatter:(e,a,t)=>{let l,s;switch(t){case 1:l="info",s=ee("task.running");break;case 2:l="warning",s=ee("task.stop");break;case 3:l="success",s=ee("task.finish");break;default:l="default",s=""}return o(P,{type:l},(()=>s))}},{field:"creatTime",minWidth:200,label:ee("task.createTime")},{field:"endTime",label:ee("task.endTime"),minWidth:200,formatter:(e,a,t)=>""==t?"-":t},{field:"action",label:ee("tableDemo.action"),minWidth:"420",fixed:"right",formatter:(e,a,t)=>{let l,s,c;const m=o(A,{onCommand:a=>{switch(a){case"retest":Fe(e);break;case"delete":Re(e);break;case"stop":Ne.value.push(e.id),re(Ne.value);break;case"start":Ne.value.push(e.id),ue(Ne.value)}}},{default:()=>o(i,{style:{outline:"none",boxShadow:"none"}},(()=>[ee("common.operation"),o(n,{},(()=>o(r)))])),dropdown:()=>o(I,null,(()=>3===e.status?[o(U,{command:"retest"},(()=>ee("task.retest"))),o(U,{command:"delete"},(()=>ee("common.delete")))]:[o(U,{command:"start"},(()=>ee("task.start"))),o(U,{command:"stop"},(()=>ee("task.stop"))),o(U,{command:"retest"},(()=>ee("task.retest"))),o(U,{command:"delete"},(()=>ee("common.delete")))]))});return u(p,null,[m,u(d,{type:"primary",onClick:()=>ne(e.name),style:{marginLeft:"10px"}},Y(l=ee("task.result"))?l:{default:()=>[l]}),u(d,{type:"success",onClick:()=>Ce(e),style:{marginLeft:"10px"}},Y(s=ee("common.view"))?s:{default:()=>[s]}),u(i,{type:"warning",onClick:()=>ie(e.id)},Y(c=ee("task.taskProgress"))?c:{default:()=>[c]})])}}]),se=l(!1);let oe="";const ie=async e=>{oe=e,se.value=!0},ne=async e=>{x(`/asset-information/index?task=${e}`)},re=async e=>{await H(e)},ue=async e=>{await F(e)},de=()=>{se.value=!1},{tableRegister:pe,tableState:ce,tableMethods:me}=W({fetchDataApi:async()=>{const{currentPage:e,pageSize:a}=ce,t=await O(ae.value,e.value,a.value);return{list:t.data.list,total:t.data.total}},immediate:!0}),{loading:ge,dataList:ve,total:fe,currentPage:ye,pageSize:ke}=ce;ke.value=20;const{getList:he,getElTableExpose:je}=me;function be(){return{background:"var(--el-fill-color-light)"}}const xe=l(!1),we=async()=>{Ve.value="",_e=ee("task.addTask"),Te.value=!0,xe.value=!0};let _e=ee("task.addTask");const Se=()=>{xe.value=!1};let Te=l(!0);const Ve=l(""),Ce=async e=>{Ve.value=e.id,xe.value=!0,Te.value=!1,_e=ee("common.view")},Pe=async()=>{const e=l(!1);_({title:"Delete",draggable:!0,message:()=>o("div",{style:{display:"flex",alignItems:"center"}},[o("p",{style:{margin:"0 10px 0 0"}},ee("task.delAsset")),o(E,{modelValue:e.value,"onUpdate:modelValue":a=>{e.value=a}})])}).then((async()=>{await He(e.value)}))},Ee=async()=>{_({title:"Stop Task",draggable:!0}).then((async()=>{await Ie()}))},Ie=async()=>{const e=await je(),a=(null==e?void 0:e.getSelectionRows())||[];Ne.value=a.map((e=>e.id)),Le.value=!0;try{await re(Ne.value),Le.value=!1,he()}catch(t){Le.value=!1,he()}},Ue=async()=>{_({title:"Start Task",draggable:!0}).then((async()=>{await Ae()}))},Ae=async()=>{const e=await je(),a=(null==e?void 0:e.getSelectionRows())||[];Ne.value=a.map((e=>e.id)),Le.value=!0;try{await ue(Ne.value),Le.value=!1,he()}catch(t){Le.value=!1,he()}},De=s([]),ze=async()=>{const e=l("existing"),a=l(""),t=l(""),s=l("");await(async()=>{(await Q()).data.list.forEach((e=>{De.push({label:e.label,value:e.value||`parent-${e.label}`,children:e.children||[]})}))})(),_({title:ee("task.syncToProject"),draggable:!0,message:()=>o("div",{style:{display:"flex",flexDirection:"column",gap:"10px"}},[o("div",[o("label",{style:{marginRight:"8px"}}),o(D,{modelValue:e.value,"onUpdate:modelValue":a=>{e.value=a}},{default:()=>[o(z,{label:"existing"},(()=>ee("task.syncToExisting"))),o(z,{label:"new"},(()=>ee("task.createNewProject")))]})]),"existing"===e.value?o(R,{modelValue:a.value,"onUpdate:modelValue":e=>{a.value=e},data:De,showCheckbox:!0,placeholder:ee("project.project"),filterable:!0,style:{width:"100%"}}):null,"new"===e.value?o("div",{style:{display:"flex",flexDirection:"column",gap:"8px"}},[o(h,{modelValue:t.value,placeholder:ee("project.msgProject"),"onUpdate:modelValue":e=>t.value=e}),o(h,{modelValue:s.value,placeholder:ee("project.msgProjectTag"),"onUpdate:modelValue":e=>s.value=e})]):null])}).then((async()=>{e.value;const l=await je(),o=(null==l?void 0:l.getSelectionRows())||[];Ne.value=o.map((e=>e.id)),await B(Ne.value,e.value,a.value,s.value,t.value)}))},Re=async e=>{const a=l(!1);_({title:"Delete",draggable:!0,message:()=>o("div",{style:{display:"flex",alignItems:"center"}},[o("p",{style:{margin:"0 10px 0 0"}},ee("task.delAsset")),o(E,{modelValue:a.value,"onUpdate:modelValue":e=>{a.value=e}})])}).then((async()=>{await We(e,a.value)}))},Le=l(!1),We=async(e,a)=>{Le.value=!0;try{await M([e.id],a);Le.value=!1,he()}catch(t){Le.value=!1,he()}},Ne=l([]),He=async e=>{const a=await je(),t=(null==a?void 0:a.getSelectionRows())||[];Ne.value=t.map((e=>e.id)),Le.value=!0;try{await M(Ne.value,e);Le.value=!1,he()}catch(l){Le.value=!1,he()}},Fe=async e=>{window.confirm("Are you sure you want to retest?")&&await Oe(e)},Oe=async e=>{try{await $(e.id),he()}catch(a){he()}};c((()=>{Me(),window.addEventListener("resize",Me)}));const Be=l(0),Me=()=>{const e=window.innerHeight||document.documentElement.clientHeight;Be.value=.75*e};return(a,t)=>(m(),g(p,null,[u(f(e),null,{default:v((()=>[u(f(S),null,{default:v((()=>[u(f(T),{span:1},{default:v((()=>[u(f(V),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:v((()=>[y(k(f(ee)("task.taskName"))+":",1)])),_:1})])),_:1}),u(f(T),{span:5},{default:v((()=>[u(f(h),{modelValue:ae.value,"onUpdate:modelValue":t[0]||(t[0]=e=>ae.value=e),placeholder:f(ee)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),u(f(T),{span:5,style:{position:"relative",left:"16px"}},{default:v((()=>[u(f(i),{type:"primary",icon:f(Z),style:{height:"100%"},onClick:te},{default:v((()=>[y("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),u(f(S),null,{default:v((()=>[u(f(T),{style:{position:"relative",top:"16px"}},{default:v((()=>[j("div",q,[u(f(d),{type:"primary",onClick:we},{default:v((()=>[y(k(f(ee)("task.addTask")),1)])),_:1}),u(f(d),{type:"danger",loading:Le.value,onClick:Pe},{default:v((()=>[y(k(f(ee)("task.delTask")),1)])),_:1},8,["loading"]),u(f(d),{type:"warning",loading:Le.value,onClick:Ee},{default:v((()=>[y(k(f(ee)("task.stop")),1)])),_:1},8,["loading"]),u(f(d),{type:"success",loading:Le.value,onClick:Ue},{default:v((()=>[y(k(f(ee)("task.start")),1)])),_:1},8,["loading"]),u(f(d),{type:"info",loading:Le.value,onClick:ze},{default:v((()=>[y(k(f(ee)("task.syncToProject")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),j("div",X,[u(f(L),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:f(ke),"onUpdate:pageSize":t[1]||(t[1]=e=>b(ke)?ke.value=e:null),currentPage:f(ye),"onUpdate:currentPage":t[2]||(t[2]=e=>b(ye)?ye.value=e:null),columns:le,data:f(ve),stripe:"",border:!0,loading:f(ge),"max-height":Be.value,resizable:!0,pagination:{total:f(fe),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:f(pe),headerCellStyle:be,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])])])),_:1}),u(f(G),{modelValue:xe.value,"onUpdate:modelValue":t[3]||(t[3]=e=>xe.value=e),title:f(_e),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:v((()=>[u(J,{closeDialog:Se,getList:f(he),create:f(Te),taskid:Ve.value,schedule:!1,tp:"scan",targetIds:[]},null,8,["getList","create","taskid"])])),_:1},8,["modelValue","title"]),u(f(G),{modelValue:se.value,"onUpdate:modelValue":t[4]||(t[4]=e=>se.value=e),title:f(ee)("task.taskProgress"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},width:"70%","max-height":"700"},{default:v((()=>[u(K,{closeDialog:de,getProgressInfoID:f(oe),getProgressInfotype:"scan",getProgressInforunnerid:""},null,8,["getProgressInfoID"])])),_:1},8,["modelValue","title"])],64))}});export{Z as default};
