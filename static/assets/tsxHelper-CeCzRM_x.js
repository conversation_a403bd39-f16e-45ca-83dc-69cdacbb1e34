import{aX as t,bz as e}from"./index-C6fb_XFi.js";const n=(e,n)=>{if(!t||!e||!n)return!1;const i=e.getBoundingClientRect();let o;return o=n instanceof Element?n.getBoundingClientRect():{top:0,right:window.innerWidth,bottom:window.innerHeight,left:0},i.top<o.bottom&&i.bottom>o.top&&i.right>o.left&&i.left<o.right},i=t=>{let e,n;return"touchend"===t.type?(n=t.changedTouches[0].clientY,e=t.changedTouches[0].clientX):t.type.startsWith("touch")?(n=t.touches[0].clientY,e=t.touches[0].clientX):(n=t.clientY,e=t.clientX),{clientX:e,clientY:n}},o=(t,n="default",i)=>{if(!t||!Reflect.has(t,n))return null;if(!e(t[n]))return null;const o=t[n];return o?o(i):null};export{o as a,i as g,n as i};
