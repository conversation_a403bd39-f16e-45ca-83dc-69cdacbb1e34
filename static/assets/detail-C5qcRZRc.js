import{d as e,r as a,V as l,M as u,O as o,o as t,i as r,w as n,e as i,a as s,A as d,c as p,R as m,F as v,B as c,z as b,l as g,_ as f}from"./index-DfJTpRkj.js";import{a as _,E as y}from"./el-form-DsaI0u2w.js";import"./el-tag-CbhrEnto.js";import{a as S,E as V}from"./el-select-BkpcrSfo.js";import"./el-popper-D2BmgSQA.js";import{E as h,a as j}from"./el-col-B4Ik8fnS.js";import{j as w,o as x,T as U}from"./index-B-gHSwWD.js";import{h as P,s as A}from"./index-Dt8htmKv.js";import"./castArray-CvwAI87l.js";import"./strings-CUyZ1T6U.js";import"./index-DE7jtbbk.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./index-D1ADinPR.js";const F=f(e({__name:"detail",props:{closeDialog:{type:Function},getList:{type:Function},id:{}},setup(e){const{t:f}=g(),F=e,L=a({name:"",version:"",module:"",parameter:"",help:"",introduction:"",source:""}),H=()=>{L.value={name:"",version:"",module:"",parameter:"",help:"",introduction:"",source:""},k.value=""},I=a({name:[{required:!0,message:"",trigger:"blur"}],module:[{required:!0,message:"",trigger:"change"}],source:[{required:!0,message:"",trigger:"blur"}]}),R=a([{label:"TargetHandler",value:"TargetHandler"},{label:"SubdomainScan",value:"SubdomainScan"},{label:"SubdomainSecurity",value:"SubdomainSecurity"},{label:"PortScanPreparation",value:"PortScanPreparation"},{label:"PortScan",value:"PortScan"},{label:"AssetMapping",value:"AssetMapping"},{label:"URLScan",value:"URLScan"},{label:"WebCrawler",value:"WebCrawler"},{label:"DirScan",value:"DirScan"},{label:"VulnerabilityScan",value:"VulnerabilityScan"},{label:"AssetHandle",value:"AssetHandle"},{label:"PortFingerprint",value:"PortFingerprint"},{label:"URLSecurity",value:"URLSecurity"},{label:"PassiveScan",value:"PassiveScan"}]),k=a(""),z=[w(),x];l((async()=>{H(),F.id&&await C(F.id)}));const D=a("");(()=>{const e=localStorage.getItem("plugin_key");D.value=e})();const q=a(!1),C=async e=>{try{const a=await P(e);if(200===a.code){const e=a.data;L.value.name=e.name,L.value.version=e.version,L.value.module=e.module,L.value.parameter=e.parameter,L.value.help=e.help,L.value.introduction=e.introduction,k.value=e.source,q.value=e.isSystem}else u.error(`数据加载失败：${a.message}`)}catch(a){}},E=a(!1);o((()=>F.id),(async e=>{""===e?H():await C(e)}));const M=async()=>{if(E.value=!0,""==L.value.name)return u.error("name 不能为空"),void(E.value=!1);if(""==L.value.module)return u.error("module 不能为空"),void(E.value=!1);if(!q.value&&""==k.value)return u.error("源码 不能为空"),void(E.value=!1);try{505==(await A(F.id,L.value.name,L.value.version,L.value.module,L.value.parameter,L.value.help,L.value.introduction,k.value,D.value)).code&&localStorage.removeItem("plugin_key"),F.closeDialog(),F.getList()}catch(e){u.error("保存失败，请稍后再试。")}finally{E.value=!1}};return(e,a)=>(t(),r(s(y),{model:L.value,rules:I.value,"label-width":"100px"},{default:n((()=>[i(s(j),{gutter:20},{default:n((()=>[i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.name"),prop:"name"},{default:n((()=>[i(s(d),{modelValue:L.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>L.value.name=e),disabled:q.value},null,8,["modelValue","disabled"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.module"),prop:"module"},{default:n((()=>[i(s(S),{modelValue:L.value.module,"onUpdate:modelValue":a[1]||(a[1]=e=>L.value.module=e),disabled:q.value},{default:n((()=>[(t(!0),p(v,null,m(R.value,(e=>(t(),r(s(V),{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.parameter"),prop:"parameter"},{default:n((()=>[i(s(d),{modelValue:L.value.parameter,"onUpdate:modelValue":a[2]||(a[2]=e=>L.value.parameter=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.help"),prop:"help"},{default:n((()=>[i(s(d),{modelValue:L.value.help,"onUpdate:modelValue":a[3]||(a[3]=e=>L.value.help=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:12},{default:n((()=>[i(s(_),{label:s(f)("plugin.version"),prop:"version"},{default:n((()=>[i(s(d),{modelValue:L.value.version,"onUpdate:modelValue":a[4]||(a[4]=e=>L.value.version=e),disabled:q.value},null,8,["modelValue","disabled"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:24},{default:n((()=>[i(s(_),{label:s(f)("plugin.introduction"),prop:"introduction"},{default:n((()=>[i(s(d),{modelValue:L.value.introduction,"onUpdate:modelValue":a[5]||(a[5]=e=>L.value.introduction=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),i(s(h),{span:24},{default:n((()=>[i(s(_),{label:"源码",prop:"source"},{default:n((()=>[i(s(U),{modelValue:k.value,"onUpdate:modelValue":a[6]||(a[6]=e=>k.value=e),style:{height:"400px",width:"90%"},autofocus:!0,"indent-with-tab":!0,"tab-size":2,extensions:z,disabled:q.value},null,8,["modelValue","disabled"])])),_:1})])),_:1})])),_:1}),i(s(j),null,{default:n((()=>[i(s(h),{span:12,style:{"text-align":"right"}},{default:n((()=>[i(s(c),{type:"primary",onClick:M,loading:E.value},{default:n((()=>[b(" 保存 ")])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1},8,["model","rules"]))}}),[["__scopeId","data-v-13e80fb0"]]);export{F as default};
