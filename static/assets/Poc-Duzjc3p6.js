import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,r as a,s as l,e as o,S as i,G as s,F as n,N as r,o as p,c as u,w as c,a as m,z as d,t as g,A as f,B as v,f as y,I as _,J as j,l as h,M as x}from"./index-C6fb_XFi.js";import{E as b,a as w}from"./el-col-Dl4_4Pn5.js";import{E as S}from"./el-text-BnUG9HvL.js";import{E as k}from"./el-upload-DFauS7op.js";import"./el-progress-sY5OgffI.js";import"./el-tooltip-l0sNRNKZ.js";import{E as C}from"./el-popper-CeVwVUf9.js";import{E}from"./el-tag-C_oEQYGz.js";import{_ as z}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as A}from"./useTable-CijeIiBB.js";import{u as F}from"./useIcon-BxqaCND-.js";import{g as I,d as U,a as N}from"./index-fOwuVARc.js";import{_ as P}from"./Detail.vue_vue_type_script_setup_true_lang-CYU_gebw.js";import{_ as T}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import"./el-card-B37ahJ8o.js";import"./index-BWEJ0epC.js";import"./el-table-column-C9CkC7I1.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";import"./el-form-C2Y6uNCj.js";import"./el-divider-Bw95UAdD.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./index-CZoUTVkP.js";const W={class:"mb-10px"},D={class:"mb-10px"};function V(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!j(e)}const L=t({__name:"Poc",setup(t){const j=F({icon:"iconoir:search"}),{t:L}=h(),R=a(!1),H=a(""),M=()=>{q()};l({});const O=l([{field:"selection",type:"selection",width:"55"},{field:"name",label:L("poc.pocName"),minWidth:70},{field:"level",label:L("poc.level"),minWidth:50,columnKey:"level",formatter:(e,t,a)=>{if(null==a)return o("div",null,null);let l="",s="";return"critical"===a?(l="red",s=L("poc.critical")):"high"===a?(l="orange",s=L("poc.high")):"medium"===a?(l="yellow",s=L("poc.medium")):"low"===a?(l="blue",s=L("poc.low")):"info"===a?(l="green",s=L("poc.info")):"unknown"===a&&(l="gray",s=L("poc.unknown")),o(w,{gutter:20,style:"width: 80%"},{default:()=>[o(b,{span:1},{default:()=>[o(i,{icon:"clarity:circle-solid",color:l,size:10},null)]}),o(b,{span:5},{default:()=>[o(S,{type:"info"},V(s)?s:{default:()=>[s]})]})]})},filters:[{text:L("poc.critical"),value:"critical"},{text:L("poc.high"),value:"high"},{text:L("poc.medium"),value:"medium"},{text:L("poc.low"),value:"low"},{text:L("poc.info"),value:"info"},{text:L("poc.unknown"),value:"unknown"}]},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,a)=>{if(0!=a.length){let e;return o(w,{style:{flexWrap:"wrap"}},V(e=a.map((e=>o(b,{span:24,key:e},{default:()=>[o("div",{onClick:()=>te("app",e),style:"display: inline-block; cursor: pointer"},[o(E,{type:"success"},V(e)?e:{default:()=>[e]})])]}))))?e:{default:()=>[e]})}}},{field:"time",label:L("node.createTime"),minWidth:50},{field:"action",label:L("tableDemo.action"),minWidth:30,formatter:(e,t,a)=>{let l,i;return o(n,null,[o(s,{type:"primary",onClick:()=>oe(e)},V(l=L("common.edit"))?l:{default:()=>[l]}),o(s,{type:"danger",onClick:()=>ne(e)},V(i=L("common.delete"))?i:{default:()=>[i]})])}}]),{tableRegister:G,tableState:$,tableMethods:B}=A({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=$,a=await N(H.value,e.value,t.value,ve);return{list:a.data.list,total:a.data.total}}}),{loading:J,dataList:K,total:Q,currentPage:X,pageSize:Y}=$,{getList:q,getElTableExpose:Z}=B;function ee(){return{background:"var(--el-fill-color-light)"}}const te=(e,t)=>{};let ae=l({id:"",name:"",level:"",content:"",tags:[]});const le=async()=>{ae.id="",ae.name="",ae.level="",ae.content="",ae.tags=[],R.value=!0},oe=async e=>{ae.id=e.id,ae.name=e.name,ae.level=e.level,ae.tags=e.tags;const t=await I(ae.id);ae.content=t.data.content,R.value=!0},ie=()=>{R.value=!1},se=a(!1),ne=async e=>{se.value=!0;try{await U([e.id]);se.value=!1,q()}catch(t){se.value=!1,q()}},re=a([]),pe=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{const e=await Z(),t=(null==e?void 0:e.getSelectionRows())||[];re.value=t.map((e=>e.id)),se.value=!0;try{await U(re.value),se.value=!1,q()}catch(a){se.value=!1,q()}})()},ue=r(),ce=a({Authorization:`${ue.getToken}`}),me=a(),de=e=>{me.value.clearFiles();const t=e[0];me.value.handleStart(t)},ge=e=>{var t;200===e.code?x.success("Upload succes"):x.error(e.message),505==e.code&&localStorage.removeItem("plugin_key"),q(),null==(t=me.value)||t.clearFiles()},fe=(e,t)=>{t.length>0&&me.value.submit()},ve=l({}),ye=async e=>{Object.assign(ve,e),q()};return(t,a)=>(p(),u(n,null,[o(m(e),null,{default:c((()=>[o(m(w),{gutter:20,style:{"margin-bottom":"15px"}},{default:c((()=>[o(m(b),{span:1.5},{default:c((()=>[o(m(S),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:c((()=>[d(g(m(L)("poc.pocName"))+":",1)])),_:1})])),_:1}),o(m(b),{span:5},{default:c((()=>[o(m(f),{modelValue:H.value,"onUpdate:modelValue":a[0]||(a[0]=e=>H.value=e),placeholder:m(L)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(m(b),{span:5,style:{position:"relative",left:"16px"}},{default:c((()=>[o(m(v),{type:"primary",icon:m(j),style:{height:"100%"},onClick:M},{default:c((()=>[d("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(m(w),{gutter:60},{default:c((()=>[o(m(b),{span:1},{default:c((()=>[y("div",W,[o(m(v),{type:"primary",onClick:le},{default:c((()=>[d(g(m(L)("common.new")),1)])),_:1})])])),_:1}),o(m(b),{span:1},{default:c((()=>[y("div",D,[o(m(s),{type:"danger",loading:se.value,onClick:pe},{default:c((()=>[d(g(m(L)("common.delete")),1)])),_:1},8,["loading"])])])),_:1}),o(m(b),{span:3},{default:c((()=>[o(m(C),{content:m(L)("common.uploadMsg"),placement:"top"},{default:c((()=>[o(m(k),{ref_key:"upload",ref:me,class:"flex items-center",action:"/api/poc/data/import",headers:ce.value,"on-success":ge,limit:1,"on-exceed":de,"auto-upload":!1,onChange:fe},{trigger:c((()=>[o(m(s),null,{icon:c((()=>[o(m(i),{icon:"iconoir:upload"})])),default:c((()=>[d(" "+g(m(L)("plugin.import")),1)])),_:1})])),_:1},8,["headers"])])),_:1},8,["content"])])),_:1})])),_:1}),o(m(z),{pageSize:m(Y),"onUpdate:pageSize":a[1]||(a[1]=e=>_(Y)?Y.value=e:null),currentPage:m(X),"onUpdate:currentPage":a[2]||(a[2]=e=>_(X)?X.value=e:null),onFilterChange:ye,columns:O,data:m(K),stripe:"",border:!0,loading:m(J),resizable:!0,pagination:{total:m(Q),pageSizes:[10,20,50,100,200,500,1e3]},onRegister:m(G),headerCellStyle:ee,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister"])])),_:1}),o(m(T),{modelValue:R.value,"onUpdate:modelValue":a[3]||(a[3]=e=>R.value=e),title:m(ae).id?t.$t("common.edit"):t.$t("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:800},{default:c((()=>[o(P,{closeDialog:ie,pocForm:m(ae),getList:m(q)},null,8,["pocForm","getList"])])),_:1},8,["modelValue","title"])],64))}});export{L as default};
