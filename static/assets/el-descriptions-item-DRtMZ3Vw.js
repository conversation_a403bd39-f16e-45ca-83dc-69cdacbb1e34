import{d as e,aa as l,dD as t,b6 as a,a6 as s,Q as n,v as r,b7 as i,Y as o,Z as c,a as d,o as p,c as u,f as y,F as b,R as v,i as g,e as h,a9 as f,ay as m,aF as S,bg as w,a5 as k,a7 as $,n as x,a8 as D,z as N,t as E,j as z,cV as A,ag as _,ah as j}from"./index-C6fb_XFi.js";const C=Symbol("elDescriptions");var I=e({name:"ElDescriptionsCell",props:{cell:{type:Object},tag:{type:String,default:"td"},type:{type:String}},setup:()=>({descriptions:l(C,{})}),render(){var e,l,o,c,d,p,u;const y=t(this.cell),b=((null==(e=this.cell)?void 0:e.dirs)||[]).map((e=>{const{dir:l,arg:t,modifiers:a,value:s}=e;return[l,s,t,a]})),{border:v,direction:g}=this.descriptions,h="vertical"===g,f=(null==(c=null==(o=null==(l=this.cell)?void 0:l.children)?void 0:o.label)?void 0:c.call(o))||y.label,m=null==(u=null==(p=null==(d=this.cell)?void 0:d.children)?void 0:p.default)?void 0:u.call(p),S=y.span,w=y.align?`is-${y.align}`:"",k=y.labelAlign?`is-${y.labelAlign}`:w,$=y.className,x=y.labelClassName,D={width:a(y.width),minWidth:a(y.minWidth)},N=s("descriptions");switch(this.type){case"label":return n(r(this.tag,{style:D,class:[N.e("cell"),N.e("label"),N.is("bordered-label",v),N.is("vertical-label",h),k,x],colSpan:h?S:1},f),b);case"content":return n(r(this.tag,{style:D,class:[N.e("cell"),N.e("content"),N.is("bordered-content",v),N.is("vertical-content",h),w,$],colSpan:h?S:2*S-1},m),b);default:return n(r("td",{style:D,class:[N.e("cell"),w],colSpan:S},[i(f)?void 0:r("span",{class:[N.e("label"),x]},f),r("span",{class:[N.e("content"),$]},m)]),b)}}});const W=o({row:{type:c(Array),default:()=>[]}}),F={key:1},R=e({name:"ElDescriptionsRow"});var B=f(e({...R,props:W,setup(e){const t=l(C,{});return(e,l)=>"vertical"===d(t).direction?(p(),u(b,{key:0},[y("tr",null,[(p(!0),u(b,null,v(e.row,((e,l)=>(p(),g(d(I),{key:`tr1-${l}`,cell:e,tag:"th",type:"label"},null,8,["cell"])))),128))]),y("tr",null,[(p(!0),u(b,null,v(e.row,((e,l)=>(p(),g(d(I),{key:`tr2-${l}`,cell:e,tag:"td",type:"content"},null,8,["cell"])))),128))])],64)):(p(),u("tr",F,[(p(!0),u(b,null,v(e.row,((e,l)=>(p(),u(b,{key:`tr3-${l}`},[d(t).border?(p(),u(b,{key:0},[h(d(I),{cell:e,tag:"td",type:"label"},null,8,["cell"]),h(d(I),{cell:e,tag:"td",type:"content"},null,8,["cell"])],64)):(p(),g(d(I),{key:1,cell:e,tag:"td",type:"both"},null,8,["cell"]))],64)))),128))]))}}),[["__file","descriptions-row.vue"]]);const O=o({border:{type:Boolean,default:!1},column:{type:Number,default:3},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},size:m,title:{type:String,default:""},extra:{type:String,default:""}}),Q=e({name:"ElDescriptions"});var V=f(e({...Q,props:O,setup(e){const l=e,t=s("descriptions"),a=S(),n=w();k(C,l);const r=$((()=>[t.b(),t.m(a.value)])),i=(e,l,t,a=!1)=>(e.props||(e.props={}),l>t&&(e.props.span=t),a&&(e.props.span=l),e),o=()=>{if(!n.default)return[];const e=A(n.default()).filter((e=>{var l;return"ElDescriptionsItem"===(null==(l=null==e?void 0:e.type)?void 0:l.name)})),t=[];let a=[],s=l.column,r=0;return e.forEach(((n,o)=>{var c;const d=(null==(c=n.props)?void 0:c.span)||1;if(o<e.length-1&&(r+=d>s?s:d),o===e.length-1){const e=l.column-r%l.column;return a.push(i(n,e,s,!0)),void t.push(a)}d<s?(s-=d,a.push(n)):(a.push(i(n,d,s)),t.push(a),s=l.column,a=[])})),t};return(e,l)=>(p(),u("div",{class:x(d(r))},[e.title||e.extra||e.$slots.title||e.$slots.extra?(p(),u("div",{key:0,class:x(d(t).e("header"))},[y("div",{class:x(d(t).e("title"))},[D(e.$slots,"title",{},(()=>[N(E(e.title),1)]))],2),y("div",{class:x(d(t).e("extra"))},[D(e.$slots,"extra",{},(()=>[N(E(e.extra),1)]))],2)],2)):z("v-if",!0),y("div",{class:x(d(t).e("body"))},[y("table",{class:x([d(t).e("table"),d(t).is("bordered",e.border)])},[y("tbody",null,[(p(!0),u(b,null,v(o(),((e,l)=>(p(),g(B,{key:l,row:e},null,8,["row"])))),128))])],2)],2)],2))}}),[["__file","description.vue"]]);const Y=e({name:"ElDescriptionsItem",props:o({label:{type:String,default:""},span:{type:Number,default:1},width:{type:[String,Number],default:""},minWidth:{type:[String,Number],default:""},align:{type:String,default:"left"},labelAlign:{type:String,default:""},className:{type:String,default:""},labelClassName:{type:String,default:""}})}),Z=_(V,{DescriptionsItem:Y}),q=j(Y);export{Z as E,q as a};
