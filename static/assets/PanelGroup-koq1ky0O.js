import{d as t,r as e,s as a,y as s,o as l,i,w as n,e as o,a as d,f as u,n as r,t as x,l as c,k as _,_ as v}from"./index-DfJTpRkj.js";import{E as f,a as p}from"./el-col-B4Ik8fnS.js";import{E as m}from"./el-card-DyZz6u6e.js";import{E as b}from"./el-skeleton-item-BCvv4WDW.js";import{_ as g}from"./CountTo.vue_vue_type_script_setup_true_lang-F2X0Lxsw.js";import{r as y}from"./index-D1ADinPR.js";const h=()=>y.get({url:"/api/system/version"}),w=(t,e,a)=>y.post({url:"/api/system/update",data:{server:t,scan:e,key:a}}),C={class:"flex flex-col justify-between"},$={class:"flex flex-col justify-between"},j={class:"flex flex-col justify-between"},k={class:"flex flex-col justify-between"},z={class:"flex flex-col justify-between"},P=v(t({__name:"PanelGroup",setup(t){const{t:v}=c(),{getPrefixCls:h}=_(),w=h("panel"),P=e(!0);let E=a({asetCount:0,subdomainCount:0,sensitiveCount:0,urlCount:0,vulnerabilityCount:0});return(async()=>{const t=await y.get({url:"/api/asset/statistics/data"}).catch((()=>{})).finally((()=>{P.value=!1}));E.asetCount=t.data.assetCount,E.subdomainCount=t.data.subdomainCount,E.sensitiveCount=t.data.sensitiveCount,E.urlCount=t.data.urlCount,E.vulnerabilityCount=t.data.vulnerabilityCount})(),(t,e)=>{const a=s("Icon");return l(),i(d(p),{justify:"space-between",class:r(d(w)),gutter:20},{default:n((()=>[o(d(f),{span:5},{default:n((()=>[o(d(m),{shadow:"hover",class:"mb-20px"},{default:n((()=>[o(d(b),{loading:P.value,animated:"",rows:2},{default:n((()=>[u("div",{class:r(`${d(w)}__item flex justify-between`)},[u("div",null,[u("div",{class:r(`${d(w)}__item--icon ${d(w)}__item--peoples p-16px inline-block rounded-6px`)},[o(a,{icon:"zondicons:network",size:40})],2)]),u("div",C,[u("div",{class:r(`${d(w)}__item--text text-16px text-gray-500 text-right`)},x(d(v)("dashboard.totalAssets")),3),o(d(g),{class:"text-20px font-700 text-right","start-val":0,"end-val":d(E).asetCount||0,duration:2600},null,8,["end-val"])])],2)])),_:1},8,["loading"])])),_:1})])),_:1}),o(d(f),{span:5},{default:n((()=>[o(d(m),{shadow:"hover",class:"mb-20px"},{default:n((()=>[o(d(b),{loading:P.value,animated:"",rows:2},{default:n((()=>[u("div",{class:r(`${d(w)}__item flex justify-between`)},[u("div",null,[u("div",{class:r(`${d(w)}__item--icon ${d(w)}__item--message p-16px inline-block rounded-6px`)},[o(a,{icon:"formkit:url",size:40})],2)]),u("div",$,[u("div",{class:r(`${d(w)}__item--text text-16px text-gray-500 text-right`)},x(d(v)("dashboard.subDomain")),3),o(d(g),{class:"text-20px font-700 text-right","start-val":0,"end-val":d(E).subdomainCount||0,duration:2600},null,8,["end-val"])])],2)])),_:1},8,["loading"])])),_:1})])),_:1}),o(d(f),{span:5},{default:n((()=>[o(d(m),{shadow:"hover",class:"mb-20px"},{default:n((()=>[o(d(b),{loading:P.value,animated:"",rows:2},{default:n((()=>[u("div",{class:r(`${d(w)}__item flex justify-between`)},[u("div",null,[u("div",{class:r(`${d(w)}__item--icon ${d(w)}__item--money p-16px inline-block rounded-6px`)},[o(a,{icon:"lucide:info",size:40})],2)]),u("div",j,[u("div",{class:r(`${d(w)}__item--text text-16px text-gray-500 text-right`)},x(d(v)("dashboard.informationLeakage")),3),o(d(g),{class:"text-20px font-700 text-right","start-val":0,"end-val":d(E).sensitiveCount||0,duration:2600},null,8,["end-val"])])],2)])),_:1},8,["loading"])])),_:1})])),_:1}),o(d(f),{span:5},{default:n((()=>[o(d(m),{shadow:"hover",class:"mb-20px"},{default:n((()=>[o(d(b),{loading:P.value,animated:"",rows:2},{default:n((()=>[u("div",{class:r(`${d(w)}__item flex justify-between`)},[u("div",null,[u("div",{class:r(`${d(w)}__item--icon ${d(w)}__item--shopping p-16px inline-block rounded-6px`)},[o(a,{icon:"carbon:url",size:40})],2)]),u("div",k,[u("div",{class:r(`${d(w)}__item--text text-16px text-gray-500 text-right`)},x(d(v)("dashboard.URL")),3),o(d(g),{class:"text-20px font-700 text-right","start-val":0,"end-val":d(E).urlCount||0,duration:2600},null,8,["end-val"])])],2)])),_:1},8,["loading"])])),_:1})])),_:1}),o(d(f),{span:4},{default:n((()=>[o(d(m),{shadow:"hover",class:"mb-20px"},{default:n((()=>[o(d(b),{loading:P.value,animated:"",rows:2},{default:n((()=>[u("div",{class:r(`${d(w)}__item flex justify-between`)},[u("div",null,[u("div",{class:r(`${d(w)}__item--icon ${d(w)}__item--shopping p-16px inline-block rounded-6px`)},[o(a,{icon:"ant-design:bug-filled",size:40})],2)]),u("div",z,[u("div",{class:r(`${d(w)}__item--text text-16px text-gray-500 text-right`)},x(d(v)("vulnerability.vulnerabilityName")),3),o(d(g),{class:"text-20px font-700 text-right","start-val":0,"end-val":d(E).vulnerabilityCount||0,duration:2600},null,8,["end-val"])])],2)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1},8,["class"])}}}),[["__scopeId","data-v-0f4bfd01"]]),E=Object.freeze(Object.defineProperty({__proto__:null,default:P},Symbol.toStringTag,{value:"Module"}));export{P,w as U,E as a,h as g};
