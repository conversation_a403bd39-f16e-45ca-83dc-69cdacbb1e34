import{d as e,dE as l,r as a,s as o,o as u,i as s,w as t,e as n,a as i,A as r,f as v,c as d,R as m,F as p,ad as c,B as f,z as g,t as b,l as _,ai as w}from"./index-C6fb_XFi.js";import{a as h,E as y}from"./el-form-C2Y6uNCj.js";import{E as V,a as j}from"./el-col-Dl4_4Pn5.js";import{E as k}from"./el-divider-Bw95UAdD.js";import{E as x}from"./el-tag-C_oEQYGz.js";import"./el-popper-CeVwVUf9.js";import"./el-virtual-list-D7NvYvyu.js";import{E}from"./el-select-v2-CaMVABoW.js";import{j as F,o as z,T as C}from"./index-CZoUTVkP.js";import{u as U,c as D}from"./index-fOwuVARc.js";const M={class:"flex gap-2"},N=e({__name:"Detail",props:{closeDialog:{type:Function},getList:{type:Function},pocForm:{}},setup(e){const{t:N}=_(),R=[F(),z],q=e,{pocForm:A}=l(q),B=a({...A.value}),G=o({name:[{required:!0,message:N("poc.nameMsg"),trigger:"blur"}],level:[{required:!0,message:N("poc.contentMsg"),trigger:"blur"}]}),I=[{value:"critical",label:"critical"},{value:"high",label:"high"},{value:"medium",label:"medium"},{value:"low",label:"low"},{value:"info",label:"info"},{value:"unknown",label:"unknown"}],L=a(!1),T=a(),K=a([...A.value.tags]),O=a(""),P=a(!1),H=a(),J=()=>{P.value=!0,w((()=>{H.value.input.focus()}))},Q=()=>{O.value&&K.value.push(O.value),P.value=!1,O.value=""};return(e,l)=>(u(),s(i(y),{model:B.value,"label-width":"120px",rules:G,"status-icon":"",ref_key:"ruleFormRef",ref:T},{default:t((()=>[n(i(h),{label:i(N)("poc.pocName"),prop:"name"},{default:t((()=>[n(i(r),{modelValue:B.value.name,"onUpdate:modelValue":l[0]||(l[0]=e=>B.value.name=e),placeholder:i(N)("poc.nameMsg")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),n(i(h),{label:i(N)("poc.content"),prop:"content"},{default:t((()=>[n(i(C),{modelValue:B.value.content,"onUpdate:modelValue":l[1]||(l[1]=e=>B.value.content=e),style:{height:"600px",width:"100%"},autofocus:!0,"indent-with-tab":!0,"tab-size":2,extensions:R},null,8,["modelValue"])])),_:1},8,["label"]),n(i(h),{label:i(N)("poc.level")},{default:t((()=>[n(i(E),{modelValue:B.value.level,"onUpdate:modelValue":l[2]||(l[2]=e=>B.value.level=e),placeholder:"Please select level",options:I},null,8,["modelValue"])])),_:1},8,["label"]),n(i(h),{label:"TAG"},{default:t((()=>[v("div",M,[(u(!0),d(p,null,m(K.value,(e=>(u(),s(i(x),{key:e,closable:"","disable-transitions":!1,onClose:l=>(e=>{K.value.splice(K.value.indexOf(e),1)})(e)},{default:t((()=>[g(b(e),1)])),_:2},1032,["onClose"])))),128)),P.value?(u(),s(i(r),{key:0,ref_key:"InputRef",ref:H,modelValue:O.value,"onUpdate:modelValue":l[3]||(l[3]=e=>O.value=e),class:"w-20",size:"small",onKeyup:c(Q,["enter"]),onBlur:Q},null,8,["modelValue"])):(u(),s(i(f),{key:1,class:"button-new-tag",size:"small",onClick:J},{default:t((()=>[g(" + New Tag ")])),_:1}))])])),_:1}),n(i(k)),n(i(j),null,{default:t((()=>[n(i(V),{span:2,offset:8},{default:t((()=>[n(i(h),null,{default:t((()=>[n(i(f),{type:"primary",onClick:l[4]||(l[4]=e=>(async e=>{L.value=!0,e&&await e.validate((async(e,l)=>{if(e){let e;e=""!=B.value.id?await U(B.value.id,B.value.name,B.value.content,B.value.level,K.value):await D(B.value.name,B.value.content,B.value.level,K.value),200===e.code&&(q.getList(),q.closeDialog()),L.value=!1}else L.value=!1}))})(T.value)),loading:L.value},{default:t((()=>[g(b(i(N)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"]))}});export{N as _};
