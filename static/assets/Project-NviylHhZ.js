import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,s as l,r as a,y as s,P as o,o as i,c as r,e as p,w as u,a as n,z as m,t as d,A as c,B as j,f as v,i as _,j as f,Q as g,F as y,R as x,l as b,K as w,v as h}from"./index-C6fb_XFi.js";import{E as D,a as V}from"./el-tab-pane-DDoZFwPS.js";import{a as k,E as A}from"./el-col-Dl4_4Pn5.js";import{E as P}from"./el-switch-Bh7JeorW.js";import{E}from"./el-text-BnUG9HvL.js";import{E as R,a as L}from"./el-form-C2Y6uNCj.js";import{b as O,a as U}from"./el-dropdown-item-DpH7Woj3.js";import"./el-popper-CeVwVUf9.js";import{_ as C}from"./ProjectList.vue_vue_type_style_index_0_lang-9tk9MVtu.js";import{_ as I}from"./AddProject.vue_vue_type_script_setup_true_lang-B7RhpJiR.js";import{_ as S}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{a as T,d as z}from"./index-CkmA3mDG.js";import{u as $}from"./useIcon-BxqaCND-.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./strings-BiUeKphX.js";import"./castArray-DRqY4cIf.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./el-tag-C_oEQYGz.js";import"./index-BWEJ0epC.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./refs-3HtnmaOD.js";import"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./index-ghAu5K8t.js";/* empty css                          */import"./el-divider-Bw95UAdD.js";import"./el-radio-group-hI5DSxSU.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-CnCQNuY4.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";/* empty css                */import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const B={class:"mb-10px"},F=t({__name:"Project",setup(t){const{t:F}=b();let H=l({}),K=a([]),N=l({});const W=a(!1),Y=async(e,t)=>{0===e?(e=q.value,t=Z.value):(q.value=e,Z.value=t);try{const l=await T(Q.value,e,t);Object.assign(H,l.data.result),K.value=Object.keys(l.data.tag),Object.assign(N,l.data.tag);const a=K.value.indexOf("All");-1!==a&&K.value.splice(a,1)}catch(l){}},G=a(!1),J=async()=>{G.value=!0},M=()=>{G.value=!1},Q=a(""),X=$({icon:"iconoir:search"}),q=a(1),Z=a(50),ee=a(!1),te=async()=>{ee.value=!0,W.value=!0,await Y(q.value,Z.value),ee.value=!1,W.value=!1};te();const le=a(!1),ae=$({icon:"openmoji:delete"}),se=async()=>{const e=a(!1);w({title:"Delete",draggable:!0,message:()=>h("div",{style:{display:"flex",alignItems:"center"}},[h("p",{style:{margin:"0 10px 0 0"}},F("task.delAsset")),h(P,{modelValue:e.value,"onUpdate:modelValue":t=>{e.value=t}})])}).then((async()=>{await z(ie.value,e.value),Y(q.value,Z.value)}))},oe=$({icon:"ri:arrow-drop-down-line"}),ie=a([]);return(t,l)=>{const a=s("ElIcon"),b=s("ElDropdownMenu"),w=o("loading");return i(),r(y,null,[p(n(e),null,{default:u((()=>[p(n(k),{style:{"margin-bottom":"20px"},gutter:20},{default:u((()=>[p(n(A),{span:.5},{default:u((()=>[p(n(E),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:u((()=>[m(d(n(F)("form.input"))+":",1)])),_:1})])),_:1}),p(n(A),{span:5},{default:u((()=>[p(n(c),{modelValue:Q.value,"onUpdate:modelValue":l[0]||(l[0]=e=>Q.value=e),placeholder:n(F)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),p(n(A),{span:5,style:{position:"relative",left:"16px"}},{default:u((()=>[p(n(j),{loading:ee.value,type:"primary",icon:n(X),size:"large",style:{height:"100%"},onClick:te},null,8,["loading","icon"])])),_:1})])),_:1}),p(n(k),{style:{"margin-bottom":"0%"}},{default:u((()=>[p(n(A),{span:2},{default:u((()=>[v("div",B,[p(n(j),{type:"primary",onClick:J},{default:u((()=>[m(d(n(F)("project.addProject")),1)])),_:1})])])),_:1}),p(n(A),{span:2},{default:u((()=>[p(n(R),null,{default:u((()=>[p(n(L),{label:n(F)("common.multipleSelection")},{default:u((()=>[p(n(P),{modelValue:le.value,"onUpdate:modelValue":l[1]||(l[1]=e=>le.value=e),class:"mb-2","inline-prompt":"","active-text":"Yes","inactive-text":"No"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),le.value?(i(),_(n(A),{key:0,span:1},{default:u((()=>[p(n(O),{trigger:"click"},{dropdown:u((()=>[p(b,null,{default:u((()=>[p(n(U),{icon:n(ae),onClick:se},{default:u((()=>[m(d(n(F)("common.delete")),1)])),_:1},8,["icon"])])),_:1})])),default:u((()=>[p(n(j),{plain:"",class:"custom-button align-bottom"},{default:u((()=>[m(d(n(F)("common.operation"))+" ",1),p(a,{class:"el-icon--right"},{default:u((()=>[p(n(oe))])),_:1})])),_:1})])),_:1})])),_:1})):f("",!0)])),_:1}),g((i(),_(n(V),{class:"demo-tabs"},{default:u((()=>[p(n(D),{label:`All (${n(N).All})`},{default:u((()=>[p(C,{tableDataList:n(H).All,getProjectTag:Y,total:n(N).All,multipleSelection:le.value,selectedRows:ie.value,"onUpdate:selectedRows":l[2]||(l[2]=e=>ie.value=e)},null,8,["tableDataList","total","multipleSelection","selectedRows"])])),_:1},8,["label"]),(i(!0),r(y,null,x(n(K),(e=>(i(),_(n(D),{label:`${e} (${n(N)[e]})`,key:e},{default:u((()=>[p(C,{tableDataList:n(H)[e],getProjectTag:Y,total:n(N)[e],multipleSelection:le.value,selectedRows:ie.value,"onUpdate:selectedRows":l[3]||(l[3]=e=>ie.value=e)},null,8,["tableDataList","total","multipleSelection","selectedRows"])])),_:2},1032,["label"])))),128))])),_:1})),[[w,W.value]])])),_:1}),p(n(S),{modelValue:G.value,"onUpdate:modelValue":l[4]||(l[4]=e=>G.value=e),title:n(F)("project.addProject"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:u((()=>[p(I,{closeDialog:M,projectid:"",getProjectData:Y,schedule:!1})])),_:1},8,["modelValue","title"])],64)}}});export{F as default};
