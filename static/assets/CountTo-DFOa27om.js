import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as l,l as a,r as t,y as s,o,i as u,w as m,f as n,e as r,a as p,t as d,A as i,z as c}from"./index-C6fb_XFi.js";import{_ as x}from"./CountTo.vue_vue_type_script_setup_true_lang-B0Gjq2kE.js";import{a as v,E as f}from"./el-col-Dl4_4Pn5.js";import{E as _}from"./el-input-number-DVs4I2j5.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";const V={class:"text-center mb-40px"},g={class:"flex mb-20px items-center"},T={class:"min-w-90px text-right"},D={class:"flex mb-20px items-center"},b={class:"min-w-90px text-right"},y={class:"flex mb-20px items-center"},j={class:"min-w-90px text-right"},w={class:"flex mb-20px items-center"},h={class:"min-w-90px text-right"},U={class:"flex mb-20px items-center"},C={class:"min-w-90px text-right"},k={class:"flex mb-20px items-center"},B={class:"min-w-90px text-right"},E={class:"text-center"},R=l({__name:"CountTo",setup(l){const{t:R}=a(),z=t(),A=t(0),J=t(1314512),W=t(3e3),q=t(0),F=t(","),G=t("¥ "),H=t(" rmb"),I=t(!1),K=()=>{var e;null==(e=p(z))||e.start()},L=()=>{var e;null==(e=p(z))||e.pauseResume()};return(l,a)=>{const t=s("BaseButton");return o(),u(p(e),{title:p(R)("countToDemo.countTo"),message:p(R)("countToDemo.countToDes")},{default:m((()=>[n("div",V,[r(p(x),{ref_key:"countRef",ref:z,"start-val":A.value,"end-val":J.value,duration:W.value,decimals:q.value,separator:F.value,prefix:G.value,suffix:H.value,autoplay:I.value,class:"text-30px font-bold text-[var(--el-color-primary)]"},null,8,["start-val","end-val","duration","decimals","separator","prefix","suffix","autoplay"])]),r(p(v),{gutter:20,justify:"space-between"},{default:m((()=>[r(p(f),{xl:8,lg:8,md:12,sm:24,xs:24},{default:m((()=>[n("div",g,[n("span",T,d(p(R)("countToDemo.startVal"))+"：",1),r(p(_),{modelValue:A.value,"onUpdate:modelValue":a[0]||(a[0]=e=>A.value=e),min:0},null,8,["modelValue"])])])),_:1}),r(p(f),{xl:8,lg:8,md:12,sm:24,xs:24},{default:m((()=>[n("div",D,[n("span",b,d(p(R)("countToDemo.endVal"))+"：",1),r(p(_),{modelValue:J.value,"onUpdate:modelValue":a[1]||(a[1]=e=>J.value=e),min:1},null,8,["modelValue"])])])),_:1}),r(p(f),{xl:8,lg:8,md:12,sm:24,xs:24},{default:m((()=>[n("div",y,[n("span",j,d(p(R)("countToDemo.duration"))+"：",1),r(p(_),{modelValue:W.value,"onUpdate:modelValue":a[2]||(a[2]=e=>W.value=e),min:1e3},null,8,["modelValue"])])])),_:1}),r(p(f),{xl:8,lg:8,md:12,sm:24,xs:24},{default:m((()=>[n("div",w,[n("span",h,d(p(R)("countToDemo.separator"))+"：",1),r(p(i),{modelValue:F.value,"onUpdate:modelValue":a[3]||(a[3]=e=>F.value=e)},null,8,["modelValue"])])])),_:1}),r(p(f),{xl:8,lg:8,md:12,sm:24,xs:24},{default:m((()=>[n("div",U,[n("span",C,d(p(R)("countToDemo.prefix"))+"：",1),r(p(i),{modelValue:G.value,"onUpdate:modelValue":a[4]||(a[4]=e=>G.value=e)},null,8,["modelValue"])])])),_:1}),r(p(f),{xl:8,lg:8,md:12,sm:24,xs:24},{default:m((()=>[n("div",k,[n("span",B,d(p(R)("countToDemo.suffix"))+"：",1),r(p(i),{modelValue:H.value,"onUpdate:modelValue":a[5]||(a[5]=e=>H.value=e)},null,8,["modelValue"])])])),_:1}),r(p(f),{span:24},{default:m((()=>[n("div",E,[r(t,{type:"primary",onClick:K},{default:m((()=>[c(d(p(R)("countToDemo.start")),1)])),_:1}),r(t,{onClick:L},{default:m((()=>[c(d(p(R)("countToDemo.pause"))+"/"+d(p(R)("countToDemo.resume")),1)])),_:1})])])),_:1})])),_:1})])),_:1},8,["title","message"])}}});export{R as default};
