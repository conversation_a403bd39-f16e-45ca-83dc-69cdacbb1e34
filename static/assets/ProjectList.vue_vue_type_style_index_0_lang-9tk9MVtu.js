import{_ as e}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{Y as a,ca as t,a2 as l,bm as s,Z as o,d as r,a6 as i,r as n,a7 as u,a3 as c,b6 as d,O as p,o as m,c as g,aH as v,a as y,i as f,w as _,aI as j,C as b,a8 as h,n as x,a9 as S,ag as w,u as k,z,t as V,j as A,e as D,f as C,F as P,l as E,K as R,v as L}from"./index-C6fb_XFi.js";import{b as U,E as N,a as T}from"./el-dropdown-item-DpH7Woj3.js";import"./el-popper-CeVwVUf9.js";import{E as B}from"./el-pagination-FWx5cl5J.js";import"./el-tag-C_oEQYGz.js";import"./el-select-vbM8Rxr1.js";import{E as F,a as I}from"./el-checkbox-CvJzNe2E.js";import{E as O}from"./el-switch-Bh7JeorW.js";/* empty css                          */import{_ as $}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{d as q}from"./index-CkmA3mDG.js";import{_ as J}from"./AddProject.vue_vue_type_script_setup_true_lang-B7RhpJiR.js";import{u as K}from"./useIcon-BxqaCND-.js";const H=a({size:{type:[Number,String],values:t,default:"",validator:e=>l(e)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:s},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:o(String),default:"cover"}}),W={error:e=>e instanceof Event},Y=["src","alt","srcset"],Z=r({name:"ElAvatar"});const G=w(S(r({...Z,props:H,emits:W,setup(e,{emit:a}){const t=e,s=i("avatar"),o=n(!1),r=u((()=>{const{size:e,icon:a,shape:l}=t,o=[s.b()];return c(e)&&o.push(s.m(e)),a&&o.push(s.m("icon")),l&&o.push(s.m(l)),o})),S=u((()=>{const{size:e}=t;return l(e)?s.cssVarBlock({size:d(e)||""}):void 0})),w=u((()=>({objectFit:t.fit})));function k(e){o.value=!0,a("error",e)}return p((()=>t.src),(()=>o.value=!1)),(e,a)=>(m(),g("span",{class:x(y(r)),style:v(y(S))},[!e.src&&!e.srcSet||o.value?e.icon?(m(),f(y(b),{key:1},{default:_((()=>[(m(),f(j(e.icon)))])),_:1})):h(e.$slots,"default",{key:2}):(m(),g("img",{key:0,src:e.src,alt:e.alt,srcset:e.srcSet,style:v(y(w)),onError:k},null,44,Y))],6))}}),[["__file","avatar.vue"]])),M={class:"flex cursor-pointer"},Q={class:"pr-16px"},X={class:"name"},ee={class:"assets-info"},ae=r({__name:"ProjectList",props:{tableDataList:{type:Array,default:()=>[]},getProjectTag:{type:Function,required:!0},total:{type:Number,default:0},multipleSelection:{type:Boolean},selectedRows:{type:Array,default:()=>[]}},emits:["update:selectedRows"],setup(a,{emit:t}){const{t:l}=E(),{push:s}=k(),o=a,r=n(!1);let i="";const u=n(!1),c=()=>{u.value=!1},d=K({icon:"uil:edit"}),v=K({icon:"material-symbols:delete-outline"}),j=K({icon:"carbon:data-vis-1"}),b=e=>{"edit"==e.type?(async e=>{i=e,u.value=!0})(e.id):"del"==e.type?(e=>{const a=n(!1);R({title:"Delete",draggable:!0,message:()=>L("div",{style:{display:"flex",alignItems:"center"}},[L("p",{style:{margin:"0 10px 0 0"}},l("task.delAsset")),L(O,{modelValue:a.value,"onUpdate:modelValue":e=>{a.value=e}})])}).then((async()=>{await q([e],a.value)}))})(e.id):le(e.id)},h=()=>{o.getProjectTag(x.value,S.value)},x=n(1),S=n(50),w=n(!1),H=n(!1),W=n(!1),Y=t,Z=n([...o.selectedRows]);p(Z,(e=>{JSON.stringify(e)!==JSON.stringify(o.selectedRows)&&Y("update:selectedRows",e),ae.value=e.length===o.tableDataList.length})),p((()=>o.selectedRows),(e=>{Z.value=[...e]})),p((()=>o.tableDataList),(e=>{ae.value=Z.value.length===e.length}));const ae=n(!1),te=()=>{ae.value?Z.value=o.tableDataList.map((e=>e.id)):Z.value=[]},le=e=>{s(`/project-management/project-detail?id=${e}`)};return(t,s)=>(m(),g(P,null,[a.multipleSelection?(m(),f(y(F),{key:0,modelValue:ae.value,"onUpdate:modelValue":s[0]||(s[0]=e=>ae.value=e),onChange:te},{default:_((()=>[z(V(y(l)("common.selectAll")),1)])),_:1},8,["modelValue"])):A("",!0),D(y(I),{modelValue:Z.value,"onUpdate:modelValue":s[1]||(s[1]=e=>Z.value=e)},{default:_((()=>[D(y(e),{columns:[],data:a.tableDataList,loading:r.value,"custom-content":"","card-wrap-style":{width:"210px",marginBottom:"20px",marginRight:"20px"}},{content:_((e=>[D(y(U),{trigger:"contextmenu",onCommand:b},{dropdown:_((()=>[D(y(N),null,{default:_((()=>[D(y(T),{icon:y(d),command:{type:"edit",id:e.id}},{default:_((()=>[z(V(y(l)("common.edit")),1)])),_:2},1032,["icon","command"]),D(y(T),{icon:y(v),command:{type:"del",id:e.id}},{default:_((()=>[z(V(y(l)("common.delete")),1)])),_:2},1032,["icon","command"]),D(y(T),{icon:y(j),command:{type:"aggregation",id:e.id}},{default:_((()=>[z(V(y(l)("project.aggregation")),1)])),_:2},1032,["icon","command"])])),_:2},1024)])),default:_((()=>[C("div",M,[a.multipleSelection?(m(),f(y(F),{key:0,value:e.id,class:"pr-16px"},null,8,["value"])):A("",!0),C("div",Q,[""!=e.logo?(m(),f(y(G),{key:0,src:e.logo,class:"avatar",fit:"cover"},null,8,["src"])):(m(),f(y(G),{key:1,class:"avatar avatar-placeholder"},{default:_((()=>[z(V(e.name.charAt(0)),1)])),_:2},1024))]),C("div",null,[C("div",X,V(e.name),1),C("div",ee,V(y(l)("project.totalAssets"))+" : "+V(e.AssetCount),1)])])])),_:2},1024)])),_:1},8,["data","loading"])])),_:1},8,["modelValue"]),D(y(B),{"current-page":x.value,"onUpdate:currentPage":s[2]||(s[2]=e=>x.value=e),"page-size":S.value,"onUpdate:pageSize":s[3]||(s[3]=e=>S.value=e),"page-sizes":[50,70,100,200,400],small:w.value,disabled:W.value,background:H.value,layout:"total, sizes, prev, pager, next, jumper",total:a.total,onSizeChange:h,onCurrentChange:h},null,8,["current-page","page-size","small","disabled","background","total"]),D(y($),{modelValue:u.value,"onUpdate:modelValue":s[4]||(s[4]=e=>u.value=e),title:y(l)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:_((()=>[D(J,{closeDialog:c,projectid:y(i),getProjectData:t.$props.getProjectTag,schedule:!1},null,8,["projectid","getProjectData"])])),_:1},8,["modelValue","title"])],64))}});export{ae as _};
