import{d as e,s as t,o as r,i,w as s,e as o,a as p,l}from"./index-C6fb_XFi.js";import{E as a,a as m}from"./el-tab-pane-DDoZFwPS.js";import j from"./AssetInfo2-BLTBHw60.js";import n from"./Subdomain-CMLZn6X8.js";import u from"./URL-DFdGHgZn.js";import c from"./Crawler-BbKxNI-W.js";import _ from"./SensitiveInformation-skHX2WCV.js";import d from"./DirScan-BNVJ9EHw.js";import b from"./PageMonitoring-BHV3uxIb.js";import f from"./vul-vCMgdSF9.js";import v from"./SubdomainTakeover-DMXlFkSr.js";import{g as L}from"./index-CkmA3mDG.js";import"./strings-BiUeKphX.js";import"./useTable-CijeIiBB.js";import"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import"./refs-3HtnmaOD.js";import"./el-popper-CeVwVUf9.js";import"./el-col-Dl4_4Pn5.js";import"./el-card-B37ahJ8o.js";import"./el-tag-C_oEQYGz.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./index-BWEJ0epC.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./el-link-B7nRzwUO.js";import"./el-text-BnUG9HvL.js";import"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./index-ghAu5K8t.js";import"./useCrudSchemas-CEXr0LRM.js";import"./tree-BfZhwLPs.js";import"./index-BBupWySc.js";import"./index-CnCQNuY4.js";import"./Csearch-B51tl_vU.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import"./el-divider-Bw95UAdD.js";import"./el-tree-select-D8i5b8X5.js";import"./index-w1A_Rrgh.js";/* empty css                */import"./el-switch-Bh7JeorW.js";import"./useIcon-BxqaCND-.js";import"./exportData.vue_vue_type_script_setup_true_lang-C3cw41xZ.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-space-CdSK6Ce1.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";import"./index-BW6HF2gU.js";import"./AssetDetail2.vue_vue_type_script_setup_true_lang-BJYs5MHA.js";import"./index-CZoUTVkP.js";import"./el-drawer-DSWQeoTQ.js";import"./MonacoDiffEditor-By1hxVEP.js";import"./el-descriptions-item-DRtMZ3Vw.js";import"./Detail.vue_vue_type_script_setup_true_lang-CYU_gebw.js";const g=e({__name:"Asset",setup(e){const{t:g}=l(),x=t([]);return(async()=>{(await L()).data.list.forEach((e=>{x.push(e)}))})(),(e,t)=>(r(),i(p(m),{type:"border-card"},{default:s((()=>[o(p(a),{label:p(g)("asset.assetName")},{default:s((()=>[o(j,{projectList:x},null,8,["projectList"])])),_:1},8,["label"]),o(p(a),{label:p(g)("subdomain.subdomainName")},{default:s((()=>[o(n,{projectList:x},null,8,["projectList"])])),_:1},8,["label"]),o(p(a),{label:p(g)("task.subdomainTakeover")},{default:s((()=>[o(v,{projectList:x},null,8,["projectList"])])),_:1},8,["label"]),o(p(a),{label:p(g)("URL.URLName")},{default:s((()=>[o(u,{projectList:x},null,8,["projectList"])])),_:1},8,["label"]),o(p(a),{label:p(g)("crawler.crawlerName")},{default:s((()=>[o(c,{projectList:x},null,8,["projectList"])])),_:1},8,["label"]),o(p(a),{label:p(g)("sensitiveInformation.sensitiveInformationName")},{default:s((()=>[o(_,{projectList:x},null,8,["projectList"])])),_:1},8,["label"]),o(p(a),{label:p(g)("dirScan.dirScanName")},{default:s((()=>[o(d,{projectList:x},null,8,["projectList"])])),_:1},8,["label"]),o(p(a),{label:p(g)("vulnerability.vulnerabilityName")},{default:s((()=>[o(f,{projectList:x},null,8,["projectList"])])),_:1},8,["label"]),o(p(a),{label:p(g)("PageMonitoring.pageMonitoringName")},{default:s((()=>[o(b,{projectList:x},null,8,["projectList"])])),_:1},8,["label"])])),_:1}))}});export{g as default};
