import{d as a,r as e,V as s,o as t,i as o,w as n,e as l,a as d,f as i,t as u,B as r,z as c,l as m,_ as f}from"./index-DfJTpRkj.js";import{E as p,a as v}from"./el-col-B4Ik8fnS.js";import{E as _}from"./el-card-DyZz6u6e.js";import{j as h,o as w,T as x}from"./index-B-gHSwWD.js";import{a as y,b as j}from"./index-D9_Z6cKh.js";import"./index-D1ADinPR.js";const b=f(a({__name:"Subfinder",setup(a){const{t:f}=m(),b=e(""),g=[h(),w];s((async()=>{try{const a=await y();200===a.code&&(b.value=a.data.content)}catch(a){}}));const V=async()=>{window.confirm("Do you want to save the data?")&&await z()},z=async()=>{E.value=!0;200==(await j(b.value)).code&&(E.value=!1)},E=e(!1);return(a,e)=>(t(),o(d(_),{shadow:"never",class:"mb-20px"},{header:n((()=>[l(d(v),null,{default:n((()=>[l(d(p),{span:3,style:{height:"100%"}},{default:n((()=>[i("span",null,u(d(f)("configuration.subfinder")),1)])),_:1}),l(d(p),{span:2,offset:19},{default:n((()=>[l(d(r),{type:"primary",onClick:V,onLoading:E.value},{default:n((()=>[c(u(d(f)("common.save")),1)])),_:1},8,["onLoading"])])),_:1})])),_:1})])),default:n((()=>[l(d(x),{modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=a=>b.value=a),style:{height:"800px"},autofocus:!0,"indent-with-tab":!0,"tab-size":2,extensions:g},null,8,["modelValue"])])),_:1}))}}),[["__scopeId","data-v-3e181418"]]);export{b as default};
