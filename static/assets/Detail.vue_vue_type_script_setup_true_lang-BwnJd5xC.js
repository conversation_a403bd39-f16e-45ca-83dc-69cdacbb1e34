import{d as e,dE as a,r as l,s as t,o as s,i,w as o,e as r,a as u,A as n,B as v,z as d,t as m,l as p}from"./index-3XfDPlIS.js";import{a as c,E as g}from"./el-form-BY8piFS2.js";import{E as f,a as b}from"./el-col-CN1tVfqh.js";import{E as y}from"./el-divider-D9UCOo44.js";import"./el-tag-DcMbxLLg.js";import"./el-popper-DVoWBu_3.js";import"./el-virtual-list-Drl4IGmp.js";import{E as _}from"./el-select-v2-CJw7ZO42.js";import{E as V}from"./el-switch-C-DLgt5X.js";import{r as h}from"./index-Dz8ZrwBc.js";const j=(e,a,l)=>h.post({url:"/api/sensitive/data",data:{search:e,pageIndex:a,pageSize:l}}),w=e=>h.post({url:"/api/sensitive/delete",data:{ids:e}}),x=(e,a)=>h.post({url:"/api/sensitive/update/state",data:{ids:e,state:a}}),I=e({__name:"Detail",props:{closeDialog:{type:Function},getList:{type:Function},sensitiveForm:{}},setup(e){const{t:j}=p(),w=e,{sensitiveForm:x}=a(w),I=l({...x.value}),E=t({name:[{required:!0,message:j("sensitiveInformation.sensitiveNameMsg"),trigger:"blur"}],regular:[{required:!0,message:j("sensitiveInformation.sensitiveRegularMsg"),trigger:"blur"}]}),k=[{value:"null",label:"null"},{value:"green",label:"green"},{value:"red",label:"red"},{value:"cyan",label:"cyan"},{value:"yellow",label:"yellow"},{value:"orange",label:"orange"},{value:"gray",label:"gray"},{value:"pink",label:"pink"}],F=l(!1),M=l(),N=async e=>{F.value=!0,e&&await e.validate((async(e,a)=>{if(e){let e;e=""!=I.value.id?await(l=I.value.id,t=I.value.name,s=I.value.regular,i=I.value.color,o=I.value.state,h.post({url:"/api/sensitive/update",data:{id:l,name:t,regular:s,color:i,state:o}})):await((e,a,l,t)=>h.post({url:"/api/sensitive/add",data:{name:e,regular:a,color:l,state:t}}))(I.value.name,I.value.regular,I.value.color,I.value.state),200===e.code&&(w.getList(),w.closeDialog()),F.value=!1}else F.value=!1;var l,t,s,i,o}))};return(e,a)=>(s(),i(u(g),{model:I.value,"label-width":"auto",rules:E,"status-icon":"",ref_key:"ruleFormRef",ref:M},{default:o((()=>[r(u(c),{label:u(j)("sensitiveInformation.sensitiveName"),prop:"name"},{default:o((()=>[r(u(n),{modelValue:I.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>I.value.name=e),placeholder:u(j)("sensitiveInformation.sensitiveNameMsg")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),r(u(c),{label:u(j)("sensitiveInformation.sensitiveRegular"),prop:"regular"},{default:o((()=>[r(u(n),{modelValue:I.value.regular,"onUpdate:modelValue":a[1]||(a[1]=e=>I.value.regular=e),placeholder:u(j)("sensitiveInformation.sensitiveRegularMsg")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),r(u(c),{label:u(j)("sensitiveInformation.sensitiveColor")},{default:o((()=>[r(u(_),{modelValue:I.value.color,"onUpdate:modelValue":a[2]||(a[2]=e=>I.value.color=e),placeholder:"Please select color",options:k},null,8,["modelValue"])])),_:1},8,["label"]),r(u(c),{label:u(j)("common.state")},{default:o((()=>[r(u(V),{modelValue:I.value.state,"onUpdate:modelValue":a[3]||(a[3]=e=>I.value.state=e),"inline-prompt":"","active-text":u(j)("common.switchAction"),"inactive-text":u(j)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),r(u(y)),r(u(b),null,{default:o((()=>[r(u(f),{span:2,offset:8},{default:o((()=>[r(u(c),null,{default:o((()=>[r(u(v),{type:"primary",onClick:a[4]||(a[4]=e=>N(M.value)),loading:F.value},{default:o((()=>[d(m(u(j)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"]))}});export{I as _,w as d,j as g,x as u};
