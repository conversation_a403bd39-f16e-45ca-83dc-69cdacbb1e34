import{d as e,H as t,r as a,s as i,e as l,E as s,v as o,A as r,B as n,o as p,c as d,a as u,w as m,I as c,F as g,l as j,Y as f,_ as h}from"./index-3XfDPlIS.js";import{u as v}from"./useTable-BezX3TfM.js";import{E as b}from"./el-card-CuEws33_.js";import{E as x}from"./el-pagination-DwzzZyu4.js";import{E as y}from"./el-tag-DcMbxLLg.js";import"./el-select-DH55-Cab.js";import"./el-popper-DVoWBu_3.js";import{E as _,a as S}from"./el-col-CN1tVfqh.js";import{_ as w}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as k}from"./useCrudSchemas-6tFKup3N.js";import{a as E,d as C,x as V}from"./index-BAb9yQka.js";import z from"./Csearch-CpC9XwHn.js";import"./index-tjM0-mlU.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-table-column-B5hg3WH6.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./tree-BfZhwLPs.js";import"./index-Dz8ZrwBc.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import"./el-text-CLWE0mUm.js";import"./el-divider-D9UCOo44.js";import"./el-autocomplete-DpYoUHkX.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-switch-C-DLgt5X.js";import"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import"./useIcon-k-uSyz6l.js";import"./exportData.vue_vue_type_script_setup_true_lang--cnrLcU_.js";import"./el-tab-pane-xcqYouKU.js";import"./el-form-BY8piFS2.js";import"./el-radio-group-evFfsZkP.js";import"./el-space-BFgHxiAK.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import"./el-virtual-list-Drl4IGmp.js";import"./el-select-v2-CJw7ZO42.js";import"./index-lpN3i-fa.js";import"./index-BuRY9bVn.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-Cyn7ZzlS.js";import"./index-D4TGn7aE.js";const T=h(e({__name:"SubdomainTakeover",props:{projectList:{}},setup(e){const{t:h}=j(),T=[{keyword:"domain",example:'domain="example.com"',explain:h("searchHelp.domain")},{keyword:"type",example:'type="github"',explain:h("searchHelp.subdomainType")},{keyword:"value",example:'value="exapmle.github.com"',explain:h("searchHelp.subdoaminValue")},{keyword:"response",example:'response="404 Not Found"',explain:h("searchHelp.body")},{keyword:"project",example:'project="Hackerone"',explain:h("searchHelp.project")}];t((()=>{H(),window.addEventListener("resize",H)}));const A=a(0),H=()=>{const e=window.innerHeight||document.documentElement.clientHeight;A.value=.7*e},L=a(""),I=e=>{L.value=e,q()},R=i({}),P=i([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:h("tableDemo.index"),type:"index",minWidth:"30"},{field:"host",label:"Domain",minWidth:"300"},{field:"value",label:h("subdomain.recordValue"),minWidth:"400"},{field:"type",label:"Type",minWidth:"200"},{field:"response",label:"Response",minWidth:"300",formatter:(e,t,a)=>l(s,{"max-height":"100"},{default:()=>[l("div",{style:"whiteSpace: 'pre-line'"},[a])]})},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,i)=>{null==i&&(i=[]),R[e.id]||(R[e.id]={inputVisible:!1,inputValue:"",inputRef:a(null)});const l=R[e.id],s=async()=>{l.inputValue&&(i.push(l.inputValue),E(e.id,U,l.inputValue)),l.inputVisible=!1,l.inputValue=""};return o(S,{},(()=>[...i.map((t=>o(_,{span:24,key:t},(()=>[o("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||te("tags",t)})(e,t)},[o(y,{closable:!0,onClose:()=>(async t=>{const a=i.indexOf(t);a>-1&&i.splice(a,1),C(e.id,U,t)})(t)},(()=>t))])])))),o(_,{span:24},l.inputVisible?()=>o(r,{ref:l.inputRef,modelValue:l.inputValue,"onUpdate:modelValue":e=>l.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&s()},onBlur:s}):()=>o(n,{class:"button-new-tag",size:"small",onClick:()=>(l.inputVisible=!0,void f((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"}]);let U="SubdoaminTakerResult";P.forEach((e=>{e.hidden=e.hidden??!1}));let D=a(!1);const N=({field:e,hidden:t})=>{const a=P.findIndex((t=>t.field===e));-1!==a&&(P[a].hidden=t),(()=>{const e=P.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=D.value,localStorage.setItem(`columnConfig_${U}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${U}`)||"{}");P.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),D.value=e.statisticsHidden})();const{allSchemas:W}=k(P),{tableRegister:F,tableState:O,tableMethods:$}=v({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=O,a=await V(L.value,e.value,t.value,Z);return{list:a.data.list,total:a.data.total}},immediate:!1}),{loading:B,dataList:J,total:K,currentPage:M,pageSize:G}=O;G.value=20;const{getList:q,getElTableExpose:Q}=$;function Y(){return{background:"var(--el-fill-color-light)"}}const Z=i({}),X=(e,t)=>{Object.assign(Z,t),L.value=e,q()},ee=a([]),te=(e,t)=>{const a=`${e}=${t}`;ee.value=[...ee.value,a]},ae=e=>{if(ee.value){const[t,a]=e.split("=");t in Z&&Array.isArray(Z[t])&&(Z[t]=Z[t].filter((e=>e!==a)),0===Z[t].length&&delete Z[t]),ee.value=ee.value.filter((t=>t!==e))}},ie=()=>Z;return(e,t)=>(p(),d(g,null,[l(z,{getList:u(q),handleSearch:I,searchKeywordsData:T,index:u(U),getElTableExpose:u(Q),projectList:e.$props.projectList,handleFilterSearch:X,crudSchemas:P,dynamicTags:ee.value,handleClose:ae,onUpdateColumnVisibility:N,searchResultCount:u(K),getFilter:ie},null,8,["getList","index","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),l(u(S),null,{default:m((()=>[l(u(_),null,{default:m((()=>[l(u(b),{style:{height:"min-content"}},{default:m((()=>[l(u(w),{pageSize:u(G),"onUpdate:pageSize":t[0]||(t[0]=e=>c(G)?G.value=e:null),currentPage:u(M),"onUpdate:currentPage":t[1]||(t[1]=e=>c(M)?M.value=e:null),columns:u(W).tableColumns,data:u(J),stripe:"","max-height":A.value,border:!0,loading:u(B),resizable:!0,onRegister:u(F),headerCellStyle:Y,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),l(u(_),{":span":24},{default:m((()=>[l(u(b),null,{default:m((()=>[l(u(x),{pageSize:u(G),"onUpdate:pageSize":t[2]||(t[2]=e=>c(G)?G.value=e:null),currentPage:u(M),"onUpdate:currentPage":t[3]||(t[3]=e=>c(M)?M.value=e:null),"page-sizes":[20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:u(K)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-87c8d9ed"]]);export{T as default};
