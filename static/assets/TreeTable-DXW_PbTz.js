import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,a,l,s as o,e as i,G as r,o as s,i as p,w as n,I as m,J as d,_ as c}from"./index-DfJTpRkj.js";import{_ as u}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{b}from"./index-E-CbF_DZ.js";import{E as g}from"./el-tag-CbhrEnto.js";import{u as j}from"./useTable-CtyddZqf.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";const f=c(t({__name:"TreeTable",setup(t){const{tableRegister:c,tableState:f}=j({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=f,l=await b({pageIndex:a(e),pageSize:a(t)});return{list:l.data.list,total:l.data.total}}}),{loading:_,dataList:y,total:D,currentPage:x,pageSize:v}=f,{t:w}=l(),S=o([{field:"selection",type:"selection"},{field:"index",label:w("tableDemo.index"),type:"index"},{field:"content",label:w("tableDemo.header"),children:[{field:"title",label:w("tableDemo.title")},{field:"author",label:w("tableDemo.author")},{field:"display_time",label:w("tableDemo.displayTime")},{field:"importance",label:w("tableDemo.importance"),formatter:(e,t,a)=>i(g,{type:1===a?"success":2===a?"warning":"danger"},{default:()=>[w(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")]})},{field:"pageviews",label:w("tableDemo.pageviews")}]},{field:"action",label:w("tableDemo.action"),slots:{default:e=>{let t;return i(r,{type:"primary",onClick:()=>z(e)},"function"==typeof(a=t=w("tableDemo.action"))||"[object Object]"===Object.prototype.toString.call(a)&&!d(a)?t:{default:()=>[t]});var a}}}]),z=e=>{};return(t,l)=>(s(),p(a(e),{title:`${a(w)("router.treeTable")} ${a(w)("tableDemo.example")}`},{default:n((()=>[i(a(u),{pageSize:a(v),"onUpdate:pageSize":l[0]||(l[0]=e=>m(v)?v.value=e:null),currentPage:a(x),"onUpdate:currentPage":l[1]||(l[1]=e=>m(x)?x.value=e:null),columns:S,data:a(y),"row-key":"id",loading:a(_),sortable:"",pagination:{total:a(D)},onRegister:a(c)},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister"])])),_:1},8,["title"]))}}),[["__scopeId","data-v-ef67ff34"]]);export{f as default};
