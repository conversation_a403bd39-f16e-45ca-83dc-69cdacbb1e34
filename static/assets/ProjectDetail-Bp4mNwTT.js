import{d as e,o as t,i as s,w as r,e as o,a as i,l as p}from"./index-C6fb_XFi.js";import{E as m,a}from"./el-tab-pane-DDoZFwPS.js";import l from"./Dashboard-CeSwHMqJ.js";import j from"./Subdomain-2rO10qM1.js";import d from"./Port-l-8HztMT.js";import n from"./Service-DjB6apIr.js";import"./strings-BiUeKphX.js";import"./debounce-BwgdhaaK.js";import"./el-col-Dl4_4Pn5.js";import"./el-card-B37ahJ8o.js";import"./el-descriptions-item-DRtMZ3Vw.js";import"./el-tag-C_oEQYGz.js";import"./el-divider-Bw95UAdD.js";import"./el-skeleton-item-Cr_-rSiV.js";import"./el-table-column-C9CkC7I1.js";import"./el-popper-CeVwVUf9.js";import"./index-BWEJ0epC.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-text-BnUG9HvL.js";import"./el-space-CdSK6Ce1.js";import"./useIcon-BxqaCND-.js";import"./index-DKxEKp57.js";import"./index-CnCQNuY4.js";import"./index-BBupWySc.js";import"./useTable-CijeIiBB.js";import"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./useCrudSchemas-CEXr0LRM.js";import"./tree-BfZhwLPs.js";const u=e({__name:"ProjectDetail",setup(e){const{t:u}=p();return(e,p)=>(t(),s(i(a),{type:"border-card"},{default:r((()=>[o(i(m),{label:i(u)("project.overview")},{default:r((()=>[o(l)])),_:1},8,["label"]),o(i(m),{label:i(u)("subdomain.subdomainName")},{default:r((()=>[o(j)])),_:1},8,["label"]),o(i(m),{label:i(u)("asset.port")},{default:r((()=>[o(d)])),_:1},8,["label"]),o(i(m),{label:i(u)("asset.service")},{default:r((()=>[o(n)])),_:1},8,["label"])])),_:1}))}});export{u as default};
