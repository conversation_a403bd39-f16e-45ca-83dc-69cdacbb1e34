import{r as a}from"./index-Dz8ZrwBc.js";const i=()=>a.get({url:"/api/configuration/subfinder/data"}),t=i=>a.post({url:"/api/configuration/subfinder/save",data:{content:i}}),o=()=>a.get({url:"/api/configuration/rad/data"}),n=i=>a.post({url:"/api/configuration/rad/save",data:{content:i}}),e=()=>a.get({url:"/api/configuration/system/data"}),s=(i,t)=>a.post({url:"/api/configuration/system/save",data:{timezone:i,ModulesConfig:t}}),r=()=>a.get({url:"/api/notification/data"}),u=(i,t,o,n,e,s)=>a.post({url:"/api/notification/add",data:{name:i,url:t,method:o,contentType:n,data:e,state:s}}),d=(i,t,o,n,e,s,r)=>a.post({url:"/api/notification/add",data:{id:i,name:t,url:o,method:n,contentType:e,data:s,state:r}}),c=i=>a.post({url:"/api/notification/delete",data:{ids:i}}),p=()=>a.get({url:"/api/notification/config/data"}),f=(i,t,o,n,e,s,r)=>a.post({url:"/api/notification/config/update",data:{dirScanNotification:i,portScanNotification:t,sensitiveNotification:o,subdomainNotification:n,subdomainTakeoverNotification:e,pageMonNotification:s,vulNotification:r}}),l=()=>a.get({url:"/api/configuration/deduplication/config"}),g=(i,t,o,n,e,s,r,u,d,c,p,f)=>a.post({url:"/api/configuration/deduplication/save",data:{asset:i,subdomain:t,SubdoaminTakerResult:o,UrlScan:n,crawler:e,SensitiveResult:s,DirScanResult:r,vulnerability:u,PageMonitoring:d,hour:c,flag:p,runNow:f}});export{i as a,t as b,o as c,n as d,c as e,r as f,e as g,p as h,u as i,d as j,g as k,l,s,f as u};
