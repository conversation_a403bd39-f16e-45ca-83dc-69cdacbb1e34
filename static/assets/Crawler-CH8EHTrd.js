import{d as e,s as t,r as a,e as l,v as i,A as s,B as o,H as r,o as n,c as p,a as d,w as u,I as m,F as c,J as j,l as g,Y as f,_ as h}from"./index-3XfDPlIS.js";import{u as b}from"./useTable-BezX3TfM.js";import{E as v}from"./el-card-CuEws33_.js";import{E as _}from"./el-pagination-DwzzZyu4.js";import{E as y}from"./el-tag-DcMbxLLg.js";import"./el-select-DH55-Cab.js";import"./el-popper-DVoWBu_3.js";import{E as x,a as S}from"./el-col-CN1tVfqh.js";import{E as w}from"./el-link-Dzmhaz1a.js";import{_ as k}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as E}from"./useCrudSchemas-6tFKup3N.js";import{a as C,d as z,j as A}from"./index-BAb9yQka.js";import V from"./Csearch-CpC9XwHn.js";import"./index-tjM0-mlU.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-table-column-B5hg3WH6.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./tree-BfZhwLPs.js";import"./index-Dz8ZrwBc.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import"./el-text-CLWE0mUm.js";import"./el-divider-D9UCOo44.js";import"./el-autocomplete-DpYoUHkX.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-switch-C-DLgt5X.js";import"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import"./useIcon-k-uSyz6l.js";import"./exportData.vue_vue_type_script_setup_true_lang--cnrLcU_.js";import"./el-tab-pane-xcqYouKU.js";import"./el-form-BY8piFS2.js";import"./el-radio-group-evFfsZkP.js";import"./el-space-BFgHxiAK.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import"./el-virtual-list-Drl4IGmp.js";import"./el-select-v2-CJw7ZO42.js";import"./index-lpN3i-fa.js";import"./index-BuRY9bVn.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-Cyn7ZzlS.js";import"./index-D4TGn7aE.js";const H=h(e({__name:"Crawler",props:{projectList:{}},setup(e){const{t:h}=g(),H=t({}),L=[{keyword:"url",example:'url="http://example.com"',explain:h("searchHelp.url")},{keyword:"method",example:'method="POST"',explain:h("searchHelp.method")},{keyword:"body",example:'body="username=admin"',explain:h("searchHelp.crawlerBody")},{keyword:"project",example:'project="Hackerone"',explain:h("searchHelp.project")}],T=a(""),I=e=>{T.value=e,Y()},O=t([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:h("tableDemo.index"),type:"index",minWidth:55},{field:"method",label:"Method",minWidth:100},{field:"url",label:"URL",minWidth:500,formatter:(e,t,a)=>{return l(w,{href:a,underline:!1,target:"_blank"},"function"==typeof(i=a)||"[object Object]"===Object.prototype.toString.call(i)&&!j(i)?a:{default:()=>[a]});var i}},{field:"body",label:h("crawler.postParameter"),minWidth:300},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,l)=>{null==l&&(l=[]),H[e.id]||(H[e.id]={inputVisible:!1,inputValue:"",inputRef:a(null)});const r=H[e.id],n=async()=>{r.inputValue&&(l.push(r.inputValue),C(e.id,P,r.inputValue)),r.inputVisible=!1,r.inputValue=""};return i(S,{},(()=>[...l.map((t=>i(x,{span:24,key:t},(()=>[i("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||U("tags",t)})(e,t)},[i(y,{closable:!0,onClose:()=>(async t=>{const a=l.indexOf(t);a>-1&&l.splice(a,1),z(e.id,P,t)})(t)},(()=>t))])])))),i(x,{span:24},r.inputVisible?()=>i(s,{ref:r.inputRef,modelValue:r.inputValue,"onUpdate:modelValue":e=>r.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&n()},onBlur:n}):()=>i(o,{class:"button-new-tag",size:"small",onClick:()=>(r.inputVisible=!0,void f((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:h("asset.time"),minWidth:"170"}]);let P="crawler";const R=a([]),U=(e,t)=>{const a=`${e}=${t}`;R.value=[...R.value,a]},W=e=>{if(R.value){const[t,a]=e.split("=");t in ae&&Array.isArray(ae[t])&&(ae[t]=ae[t].filter((e=>e!==a)),0===ae[t].length&&delete ae[t]),R.value=R.value.filter((t=>t!==e))}};O.forEach((e=>{e.hidden=e.hidden??!1}));let D=a(!1);const N=({field:e,hidden:t})=>{const a=O.findIndex((t=>t.field===e));-1!==a&&(O[a].hidden=t),(()=>{const e=O.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=D.value,localStorage.setItem(`columnConfig_${P}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${P}`)||"{}");O.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),D.value=e.statisticsHidden})();const{allSchemas:F}=E(O),{tableRegister:$,tableState:B,tableMethods:J}=b({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=B,a=await A(T.value,e.value,t.value,ae);return{list:a.data.list,total:a.data.total}},immediate:!1}),{loading:M,dataList:K,total:G,currentPage:q,pageSize:Q}=B,{getList:Y,getElTableExpose:Z}=J;function X(){return{background:"var(--el-fill-color-light)"}}r((()=>{te(),window.addEventListener("resize",te)}));const ee=a(0),te=()=>{const e=window.innerHeight||document.documentElement.clientHeight;ee.value=.7*e},ae=t({}),le=(e,t)=>{Object.keys(ae).forEach((e=>delete ae[e])),Object.assign(ae,t),T.value=e,Y()},ie=()=>ae;return(e,t)=>(n(),p(c,null,[l(V,{getList:d(Y),handleSearch:I,searchKeywordsData:L,index:d(P),dynamicTags:R.value,handleClose:W,getElTableExpose:d(Z),projectList:e.$props.projectList,handleFilterSearch:le,crudSchemas:O,onUpdateColumnVisibility:N,searchResultCount:d(G),getFilter:ie},null,8,["getList","index","dynamicTags","getElTableExpose","projectList","crudSchemas","searchResultCount"]),l(d(S),null,{default:u((()=>[l(d(x),null,{default:u((()=>[l(d(v),null,{default:u((()=>[l(d(k),{pageSize:d(Q),"onUpdate:pageSize":t[0]||(t[0]=e=>m(Q)?Q.value=e:null),currentPage:d(q),"onUpdate:currentPage":t[1]||(t[1]=e=>m(q)?q.value=e:null),columns:d(F).tableColumns,data:d(K),"max-height":ee.value,stripe:"",border:!0,loading:d(M),resizable:!0,onRegister:d($),headerCellStyle:X,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!0,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!1},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),l(d(x),{":span":24},{default:u((()=>[l(d(v),null,{default:u((()=>[l(d(_),{pageSize:d(Q),"onUpdate:pageSize":t[2]||(t[2]=e=>m(Q)?Q.value=e:null),currentPage:d(q),"onUpdate:currentPage":t[3]||(t[3]=e=>m(q)?q.value=e:null),"page-sizes":[20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:d(G)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-0c302d1d"]]);export{H as default};
