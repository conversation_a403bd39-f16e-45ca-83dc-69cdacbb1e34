import{d as t,b5 as e,ab as i,a8 as n,H as s,O as r,x as o,v as a,bK as l}from"./index-3XfDPlIS.js";class h{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){[t,e]=w(this,t,e);let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),f.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){[t,e]=w(this,t,e);let i=[];return this.decompose(t,e,i,0),f.from(i,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new O(this),s=new O(t);for(let r=e,o=e;;){if(n.next(r),s.next(r),r=0,n.lineBreak!=s.lineBreak||n.done!=s.done||n.value!=s.value)return!1;if(o+=n.value.length,n.done||o>=i)return!0}}iter(t=1){return new O(this,t)}iterRange(t,e=this.length){return new p(this,t,e)}iterLines(t,e){let i;if(null==t)i=this.iter();else{null==e&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new g(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(0==t.length)throw new RangeError("A document must have at least one line");return 1!=t.length||t[0]?t.length<=32?new c(t):f.from(c.split(t,[])):h.empty}}class c extends h{constructor(t,e=function(t){let e=-1;for(let i of t)e+=i.length+1;return e}(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let s=0;;s++){let r=this.text[s],o=n+r.length;if((e?i:o)>=t)return new m(n,o,i,r);n=o+1,i++}}decompose(t,e,i,n){let s=t<=0&&e>=this.length?this:new c(d(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(1&n){let t=i.pop(),e=u(s.text,t.text.slice(),0,s.length);if(e.length<=32)i.push(new c(e,t.length+s.length));else{let t=e.length>>1;i.push(new c(e.slice(0,t)),new c(e.slice(t)))}}else i.push(s)}replace(t,e,i){if(!(i instanceof c))return super.replace(t,e,i);[t,e]=w(this,t,e);let n=u(this.text,u(i.text,d(this.text,0,t)),e),s=this.length+i.length-(e-t);return n.length<=32?new c(n,s):f.from(c.split(n,[]),s)}sliceString(t,e=this.length,i="\n"){[t,e]=w(this,t,e);let n="";for(let s=0,r=0;s<=e&&r<this.text.length;r++){let o=this.text[r],a=s+o.length;s>t&&r&&(n+=i),t<a&&e>s&&(n+=o.slice(Math.max(0,t-s),e-s)),s=a+1}return n}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let s of t)i.push(s),n+=s.length+1,32==i.length&&(e.push(new c(i,n)),i=[],n=-1);return n>-1&&e.push(new c(i,n)),e}}class f extends h{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let i of t)this.lines+=i.lines}lineInner(t,e,i,n){for(let s=0;;s++){let r=this.children[s],o=n+r.length,a=i+r.lines-1;if((e?a:o)>=t)return r.lineInner(t,e,i,n);n=o+1,i=a+1}}decompose(t,e,i,n){for(let s=0,r=0;r<=e&&s<this.children.length;s++){let o=this.children[s],a=r+o.length;if(t<=a&&e>=r){let s=n&((r<=t?1:0)|(a>=e?2:0));r>=t&&a<=e&&!s?i.push(o):o.decompose(t-r,e-r,i,s)}r=a+1}}replace(t,e,i){if([t,e]=w(this,t,e),i.lines<this.lines)for(let n=0,s=0;n<this.children.length;n++){let r=this.children[n],o=s+r.length;if(t>=s&&e<=o){let a=r.replace(t-s,e-s,i),l=this.lines-r.lines+a.lines;if(a.lines<l>>4&&a.lines>l>>6){let s=this.children.slice();return s[n]=a,new f(s,this.length-(e-t)+i.length)}return super.replace(s,o,a)}s=o+1}return super.replace(t,e,i)}sliceString(t,e=this.length,i="\n"){[t,e]=w(this,t,e);let n="";for(let s=0,r=0;s<this.children.length&&r<=e;s++){let o=this.children[s],a=r+o.length;r>t&&s&&(n+=i),t<a&&e>r&&(n+=o.sliceString(t-r,e-r,i)),r=a+1}return n}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof f))return 0;let i=0,[n,s,r,o]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,s+=e){if(n==r||s==o)return i;let a=this.children[n],l=t.children[s];if(a!=l)return i+a.scanIdentical(l,e);i+=a.length+1}}static from(t,e=t.reduce(((t,e)=>t+e.length+1),-1)){let i=0;for(let c of t)i+=c.lines;if(i<32){let i=[];for(let e of t)e.flatten(i);return new c(i,e)}let n=Math.max(32,i>>5),s=n<<1,r=n>>1,o=[],a=0,l=-1,h=[];function u(t){let e;if(t.lines>s&&t instanceof f)for(let i of t.children)u(i);else t.lines>r&&(a>r||!a)?(d(),o.push(t)):t instanceof c&&a&&(e=h[h.length-1])instanceof c&&t.lines+e.lines<=32?(a+=t.lines,l+=t.length+1,h[h.length-1]=new c(e.text.concat(t.text),e.length+1+t.length)):(a+t.lines>n&&d(),a+=t.lines,l+=t.length+1,h.push(t))}function d(){0!=a&&(o.push(1==h.length?h[0]:f.from(h,l)),l=-1,a=h.length=0)}for(let c of t)u(c);return d(),1==o.length?o[0]:new f(o,e)}}function u(t,e,i=0,n=1e9){for(let s=0,r=0,o=!0;r<t.length&&s<=n;r++){let a=t[r],l=s+a.length;l>=i&&(l>n&&(a=a.slice(0,n-s)),s<i&&(a=a.slice(i-s)),o?(e[e.length-1]+=a,o=!1):e.push(a)),s=l+1}return e}function d(t,e,i){return u(t,[""],e,i)}h.empty=new c([""],0);class O{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof c?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],s=this.offsets[i],r=s>>1,o=n instanceof c?n.text.length:n.children.length;if(r==(e>0?o:0)){if(0==i)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((1&s)==(e>0?0:1)){if(this.offsets[i]+=e,0==t)return this.lineBreak=!0,this.value="\n",this;t--}else if(n instanceof c){let s=n.text[r+(e<0?-1:0)];if(this.offsets[i]+=e,s.length>Math.max(0,t))return this.value=0==t?s:e>0?s.slice(t):s.slice(0,s.length-t),this;t-=s.length}else{let s=n.children[r+(e<0?-1:0)];t>s.length?(t-=s.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(s),this.offsets.push(e>0?1:(s instanceof c?s.text.length:s.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class p{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new O(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&""!=this.value}}class g{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e&&this.afterBreak?(this.value="",this.afterBreak=!1):e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}"undefined"!=typeof Symbol&&(h.prototype[Symbol.iterator]=function(){return this.iter()},O.prototype[Symbol.iterator]=p.prototype[Symbol.iterator]=g.prototype[Symbol.iterator]=function(){return this});class m{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n}get length(){return this.to-this.from}}function w(t,e,i){return[e=Math.max(0,Math.min(t.length,e)),Math.max(e,Math.min(t.length,i))]}let v="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map((t=>t?parseInt(t,36):1));for(let Xp=1;Xp<v.length;Xp++)v[Xp]+=v[Xp-1];function b(t){for(let e=1;e<v.length;e+=2)if(v[e]>t)return v[e-1]<=t;return!1}function y(t){return t>=127462&&t<=127487}const Q=8205;function k(t,e,i=!0,n=!0){return(i?S:x)(t,e,n)}function S(t,e,i){if(e==t.length)return e;e&&$(t.charCodeAt(e))&&P(t.charCodeAt(e-1))&&e--;let n=Z(t,e);for(e+=T(n);e<t.length;){let s=Z(t,e);if(n==Q||s==Q||i&&b(s))e+=T(s),n=s;else{if(!y(s))break;{let i=0,n=e-2;for(;n>=0&&y(Z(t,n));)i++,n-=2;if(i%2==0)break;e+=2}}}return e}function x(t,e,i){for(;e>0;){let n=S(t,e-2,i);if(n<e)return n;e--}return 0}function $(t){return t>=56320&&t<57344}function P(t){return t>=55296&&t<56320}function Z(t,e){let i=t.charCodeAt(e);if(!P(i)||e+1==t.length)return i;let n=t.charCodeAt(e+1);return $(n)?n-56320+(i-55296<<10)+65536:i}function C(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}function T(t){return t<65536?1:2}const A=/\r\n?|\n/;var X=function(t){return t[t.Simple=0]="Simple",t[t.TrackDel=1]="TrackDel",t[t.TrackBefore=2]="TrackBefore",t[t.TrackAfter=3]="TrackAfter",t}(X||(X={}));class W{constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i}return t}get empty(){return 0==this.sections.length||2==this.sections.length&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let s=this.sections[e++],r=this.sections[e++];r<0?(t(i,n,s),n+=s):n+=r,i+=s}}iterChangedRanges(t,e=!1){M(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i)}return new W(t)}composeDesc(t){return this.empty?t:t.empty?this:D(this,t)}mapDesc(t,e=!1){return t.empty?this:E(this,t,e)}mapPos(t,e=-1,i=X.Simple){let n=0,s=0;for(let r=0;r<this.sections.length;){let o=this.sections[r++],a=this.sections[r++],l=n+o;if(a<0){if(l>t)return s+(t-n);s+=o}else{if(i!=X.Simple&&l>=t&&(i==X.TrackDel&&n<t&&l>t||i==X.TrackBefore&&n<t||i==X.TrackAfter&&l>t))return null;if(l>t||l==t&&e<0&&!o)return t==n||e<0?s:s+a;s+=a}n=l}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return s}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let s=n+this.sections[i++];if(this.sections[i++]>=0&&n<=e&&s>=t)return!(n<t&&s>e)||"cover";n=s}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some((t=>"number"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeDesc");return new W(t)}static create(t){return new W(t)}}class R extends W{constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return M(this,((e,i,n,s,r)=>t=t.replace(n,n+(i-e),r)),!1),t}mapDesc(t,e=!1){return E(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,s=0;n<e.length;n+=2){let r=e[n],o=e[n+1];if(o>=0){e[n]=o,e[n+1]=r;let a=n>>1;for(;i.length<a;)i.push(h.empty);i.push(r?t.slice(s,s+r):h.empty)}s+=r}return new R(e,i)}compose(t){return this.empty?t:t.empty?this:D(this,t,!0)}map(t,e=!1){return t.empty?this:E(this,t,e,!0)}iterChanges(t,e=!1){M(this,t,e)}get desc(){return W.create(this.sections)}filter(t){let e=[],i=[],n=[],s=new j(this);t:for(let r=0,o=0;;){let a=r==t.length?1e9:t[r++];for(;o<a||o==a&&0==s.len;){if(s.done)break t;let t=Math.min(s.len,a-o);Y(n,t,-1);let r=-1==s.ins?-1:0==s.off?s.ins:0;Y(e,t,r),r>0&&q(i,e,s.text),s.forward(t),o+=t}let l=t[r++];for(;o<l;){if(s.done)break t;let t=Math.min(s.len,l-o);Y(e,t,-1),Y(n,t,-1==s.ins?-1:0==s.off?s.ins:0),s.forward(t),o+=t}}return{changes:new R(e,i),filtered:W.create(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):0==n?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,i){let n=[],s=[],r=0,o=null;function a(t=!1){if(!t&&!n.length)return;r<e&&Y(n,e-r,-1);let i=new R(n,s);o=o?o.compose(i.map(o)):i,n=[],s=[],r=0}return function t(l){if(Array.isArray(l))for(let e of l)t(e);else if(l instanceof R){if(l.length!=e)throw new RangeError(`Mismatched change set length (got ${l.length}, expected ${e})`);a(),o=o?o.compose(l.map(o)):l}else{let{from:t,to:o=t,insert:c}=l;if(t>o||t<0||o>e)throw new RangeError(`Invalid change range ${t} to ${o} (in doc of length ${e})`);let f=c?"string"==typeof c?h.of(c.split(i||A)):c:h.empty,u=f.length;if(t==o&&0==u)return;t<r&&a(),t>r&&Y(n,t-r,-1),Y(n,o-t,u),q(s,n,f),r=o}}(t),a(!o),o}static empty(t){return new R(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let s=t[n];if("number"==typeof s)e.push(s,-1);else{if(!Array.isArray(s)||"number"!=typeof s[0]||s.some(((t,e)=>e&&"string"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeSet");if(1==s.length)e.push(s[0],0);else{for(;i.length<n;)i.push(h.empty);i[n]=h.of(s.slice(1)),e.push(s[0],i[n].length)}}}return new R(e,i)}static createSet(t,e){return new R(t,e)}}function Y(t,e,i,n=!1){if(0==e&&i<=0)return;let s=t.length-2;s>=0&&i<=0&&i==t[s+1]?t[s]+=e:0==e&&0==t[s]?t[s+1]+=i:n?(t[s]+=e,t[s+1]+=i):t.push(e,i)}function q(t,e,i){if(0==i.length)return;let n=e.length-2>>1;if(n<t.length)t[t.length-1]=t[t.length-1].append(i);else{for(;t.length<n;)t.push(h.empty);t.push(i)}}function M(t,e,i){let n=t.inserted;for(let s=0,r=0,o=0;o<t.sections.length;){let a=t.sections[o++],l=t.sections[o++];if(l<0)s+=a,r+=a;else{let c=s,f=r,u=h.empty;for(;c+=a,f+=l,l&&n&&(u=u.append(n[o-2>>1])),!(i||o==t.sections.length||t.sections[o+1]<0);)a=t.sections[o++],l=t.sections[o++];e(s,c,r,f,u),s=c,r=f}}}function E(t,e,i,n=!1){let s=[],r=n?[]:null,o=new j(t),a=new j(e);for(let l=-1;;)if(-1==o.ins&&-1==a.ins){let t=Math.min(o.len,a.len);Y(s,t,-1),o.forward(t),a.forward(t)}else if(a.ins>=0&&(o.ins<0||l==o.i||0==o.off&&(a.len<o.len||a.len==o.len&&!i))){let t=a.len;for(Y(s,a.ins,-1);t;){let e=Math.min(o.len,t);o.ins>=0&&l<o.i&&o.len<=e&&(Y(s,0,o.ins),r&&q(r,s,o.text),l=o.i),o.forward(e),t-=e}a.next()}else{if(!(o.ins>=0)){if(o.done&&a.done)return r?R.createSet(s,r):W.create(s);throw new Error("Mismatched change set lengths")}{let t=0,e=o.len;for(;e;)if(-1==a.ins){let i=Math.min(e,a.len);t+=i,e-=i,a.forward(i)}else{if(!(0==a.ins&&a.len<e))break;e-=a.len,a.next()}Y(s,t,l<o.i?o.ins:0),r&&l<o.i&&q(r,s,o.text),l=o.i,o.forward(o.len-e)}}}function D(t,e,i=!1){let n=[],s=i?[]:null,r=new j(t),o=new j(e);for(let a=!1;;){if(r.done&&o.done)return s?R.createSet(n,s):W.create(n);if(0==r.ins)Y(n,r.len,0,a),r.next();else if(0!=o.len||o.done){if(r.done||o.done)throw new Error("Mismatched change set lengths");{let t=Math.min(r.len2,o.len),e=n.length;if(-1==r.ins){let e=-1==o.ins?-1:o.off?0:o.ins;Y(n,t,e,a),s&&e&&q(s,n,o.text)}else-1==o.ins?(Y(n,r.off?0:r.len,t,a),s&&q(s,n,r.textBit(t))):(Y(n,r.off?0:r.len,o.off?0:o.ins,a),s&&!o.off&&q(s,n,o.text));a=(r.ins>t||o.ins>=0&&o.len>t)&&(a||n.length>e),r.forward2(t),o.forward(t)}}else Y(n,0,o.ins,a),s&&q(s,n,o.text),o.next()}}class j{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return-2==this.ins}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?h.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?h.empty:e[i].slice(this.off,null==t?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){-1==this.ins?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class _{constructor(t,e,i){this.from=t,this.to=e,this.flags=i}get anchor(){return 32&this.flags?this.to:this.from}get head(){return 32&this.flags?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return 8&this.flags?-1:16&this.flags?1:0}get bidiLevel(){let t=7&this.flags;return 7==t?null:t}get goalColumn(){let t=this.flags>>6;return 16777215==t?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new _(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return V.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return V.range(this.anchor,i)}eq(t,e=!1){return!(this.anchor!=t.anchor||this.head!=t.head||e&&this.empty&&this.assoc!=t.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||"number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid JSON representation for SelectionRange");return V.range(t.anchor,t.head)}static create(t,e,i){return new _(t,e,i)}}class V{constructor(t,e){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:V.create(this.ranges.map((i=>i.map(t,e))),this.mainIndex)}eq(t,e=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(t.ranges[i],e))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return 1==this.ranges.length?this:new V([this.main],0)}addRange(t,e=!0){return V.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,V.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map((t=>t.toJSON())),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||"number"!=typeof t.main||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new V(t.ranges.map((t=>_.fromJSON(t))),t.main)}static single(t,e=t){return new V([V.range(t,e)],0)}static create(t,e=0){if(0==t.length)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let s=t[n];if(s.empty?s.from<=i:s.from<i)return V.normalized(t.slice(),e);i=s.to}return new V(t,e)}static cursor(t,e=0,i,n){return _.create(t,t,(0==e?0:e<0?8:16)|(null==i?7:Math.min(6,i))|(null!=n?n:16777215)<<6)}static range(t,e,i,n){let s=(null!=i?i:16777215)<<6|(null==n?7:Math.min(6,n));return e<t?_.create(e,t,48|s):_.create(t,e,(e>t?8:0)|s)}static normalized(t,e=0){let i=t[e];t.sort(((t,e)=>t.from-e.from)),e=t.indexOf(i);for(let n=1;n<t.length;n++){let i=t[n],s=t[n-1];if(i.empty?i.from<=s.to:i.from<s.to){let r=s.from,o=Math.max(i.to,s.to);n<=e&&e--,t.splice(--n,2,i.anchor>i.head?V.range(o,r):V.range(r,o))}}return new V(t,e)}}function B(t,e){for(let i of t.ranges)if(i.to>e)throw new RangeError("Selection points outside of document")}let z=0;class G{constructor(t,e,i,n,s){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.id=z++,this.default=t([]),this.extensions="function"==typeof s?s(this):s}get reader(){return this}static define(t={}){return new G(t.combine||(t=>t),t.compareInput||((t,e)=>t===e),t.compare||(t.combine?(t,e)=>t===e:I),!!t.static,t.enables)}of(t){return new L([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new L(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new L(t,this,2,e)}from(t,e){return e||(e=t=>t),this.compute([t],(i=>e(i.field(t))))}}function I(t,e){return t==e||t.length==e.length&&t.every(((t,i)=>t===e[i]))}class L{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=z++}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,s=this.id,r=t[s]>>1,o=2==this.type,a=!1,l=!1,h=[];for(let c of this.dependencies)"doc"==c?a=!0:"selection"==c?l=!0:1&(null!==(e=t[c.id])&&void 0!==e?e:1)||h.push(t[c.id]);return{create:t=>(t.values[r]=i(t),1),update(t,e){if(a&&e.docChanged||l&&(e.docChanged||e.selection)||U(t,h)){let e=i(t);if(o?!N(e,t.values[r],n):!n(e,t.values[r]))return t.values[r]=e,1}return 0},reconfigure:(t,e)=>{let a,l=e.config.address[s];if(null!=l){let s=ct(e,l);if(this.dependencies.every((i=>i instanceof G?e.facet(i)===t.facet(i):!(i instanceof K)||e.field(i,!1)==t.field(i,!1)))||(o?N(a=i(t),s,n):n(a=i(t),s)))return t.values[r]=s,0}else a=i(t);return t.values[r]=a,1}}}}function N(t,e,i){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!i(t[n],e[n]))return!1;return!0}function U(t,e){let i=!1;for(let n of e)1&ht(t,n)&&(i=!0);return i}function H(t,e,i){let n=i.map((e=>t[e.id])),s=i.map((t=>t.type)),r=n.filter((t=>!(1&t))),o=t[e.id]>>1;function a(t){let i=[];for(let e=0;e<n.length;e++){let r=ct(t,n[e]);if(2==s[e])for(let t of r)i.push(t);else i.push(r)}return e.combine(i)}return{create(t){for(let e of n)ht(t,e);return t.values[o]=a(t),1},update(t,i){if(!U(t,r))return 0;let n=a(t);return e.compare(n,t.values[o])?0:(t.values[o]=n,1)},reconfigure(t,s){let r=U(t,n),l=s.config.facets[e.id],h=s.facet(e);if(l&&!r&&I(i,l))return t.values[o]=h,0;let c=a(t);return e.compare(c,h)?(t.values[o]=h,0):(t.values[o]=c,1)}}}const F=G.define({static:!0});class K{constructor(t,e,i,n,s){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=s,this.provides=void 0}static define(t){let e=new K(z++,t.create,t.update,t.compare||((t,e)=>t===e),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(F).find((t=>t.field==this));return((null==e?void 0:e.create)||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:t=>(t.values[e]=this.create(t),1),update:(t,i)=>{let n=t.values[e],s=this.updateF(n,i);return this.compareF(n,s)?0:(t.values[e]=s,1)},reconfigure:(t,i)=>null!=i.config.address[this.id]?(t.values[e]=i.field(this),0):(t.values[e]=this.create(t),1)}}init(t){return[this,F.of({field:this,create:t})]}get extension(){return this}}const J=4,tt=3,et=2,it=1;function nt(t){return e=>new rt(e,t)}const st={highest:nt(0),high:nt(it),default:nt(et),low:nt(tt),lowest:nt(J)};class rt{constructor(t,e){this.inner=t,this.prec=e}}class ot{of(t){return new at(this,t)}reconfigure(t){return ot.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class at{constructor(t,e){this.compartment=t,this.inner=e}}class lt{constructor(t,e,i,n,s,r){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=s,this.facets=r,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return null==e?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],s=Object.create(null),r=new Map;for(let f of function(t,e,i){let n=[[],[],[],[],[]],s=new Map;function r(t,o){let a=s.get(t);if(null!=a){if(a<=o)return;let e=n[a].indexOf(t);e>-1&&n[a].splice(e,1),t instanceof at&&i.delete(t.compartment)}if(s.set(t,o),Array.isArray(t))for(let e of t)r(e,o);else if(t instanceof at){if(i.has(t.compartment))throw new RangeError("Duplicate use of compartment in extensions");let n=e.get(t.compartment)||t.inner;i.set(t.compartment,n),r(n,o)}else if(t instanceof rt)r(t.inner,t.prec);else if(t instanceof K)n[o].push(t),t.provides&&r(t.provides,o);else if(t instanceof L)n[o].push(t),t.facet.extensions&&r(t.facet.extensions,et);else{let e=t.extension;if(!e)throw new Error(`Unrecognized extension value in extension set (${t}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(e,o)}}return r(t,et),n.reduce(((t,e)=>t.concat(e)))}(t,e,r))f instanceof K?n.push(f):(s[f.facet.id]||(s[f.facet.id]=[])).push(f);let o=Object.create(null),a=[],l=[];for(let f of n)o[f.id]=l.length<<1,l.push((t=>f.slot(t)));let h=null==i?void 0:i.config.facets;for(let f in s){let t=s[f],e=t[0].facet,n=h&&h[f]||[];if(t.every((t=>0==t.type)))if(o[e.id]=a.length<<1|1,I(n,t))a.push(i.facet(e));else{let n=e.combine(t.map((t=>t.value)));a.push(i&&e.compare(n,i.facet(e))?i.facet(e):n)}else{for(let e of t)0==e.type?(o[e.id]=a.length<<1|1,a.push(e.value)):(o[e.id]=l.length<<1,l.push((t=>e.dynamicSlot(t))));o[e.id]=l.length<<1,l.push((i=>H(i,e,t)))}}let c=l.map((t=>t(o)));return new lt(t,r,c,o,a,s)}}function ht(t,e){if(1&e)return 2;let i=e>>1,n=t.status[i];if(4==n)throw new Error("Cyclic dependency between fields and/or facets");if(2&n)return n;t.status[i]=4;let s=t.computeSlot(t,t.config.dynamicSlots[i]);return t.status[i]=2|s}function ct(t,e){return 1&e?t.config.staticValues[e>>1]:t.values[e>>1]}const ft=G.define(),ut=G.define({combine:t=>t.some((t=>t)),static:!0}),dt=G.define({combine:t=>t.length?t[0]:void 0,static:!0}),Ot=G.define(),pt=G.define(),gt=G.define(),mt=G.define({combine:t=>!!t.length&&t[0]});class wt{constructor(t,e){this.type=t,this.value=e}static define(){return new vt}}class vt{of(t){return new wt(this,t)}}class bt{constructor(t){this.map=t}of(t){return new yt(this,t)}}class yt{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return void 0===e?void 0:e==this.value?this:new yt(this.type,e)}is(t){return this.type==t}static define(t={}){return new bt(t.map||(t=>t))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let t=n.map(e);t&&i.push(t)}return i}}yt.reconfigure=yt.define(),yt.appendConfig=yt.define();class Qt{constructor(t,e,i,n,s,r){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=s,this.scrollIntoView=r,this._doc=null,this._state=null,i&&B(i,e.newLength),s.some((t=>t.type==Qt.time))||(this.annotations=s.concat(Qt.time.of(Date.now())))}static create(t,e,i,n,s,r){return new Qt(t,e,i,n,s,r)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(Qt.userEvent);return!(!e||!(e==t||e.length>t.length&&e.slice(0,t.length)==t&&"."==e[t.length]))}}function kt(t,e){let i=[];for(let n=0,s=0;;){let r,o;if(n<t.length&&(s==e.length||e[s]>=t[n]))r=t[n++],o=t[n++];else{if(!(s<e.length))return i;r=e[s++],o=e[s++]}!i.length||i[i.length-1]<r?i.push(r,o):i[i.length-1]<o&&(i[i.length-1]=o)}}function St(t,e,i){var n;let s,r,o;return i?(s=e.changes,r=R.empty(e.changes.length),o=t.changes.compose(e.changes)):(s=e.changes.map(t.changes),r=t.changes.mapDesc(e.changes,!0),o=t.changes.compose(s)),{changes:o,selection:e.selection?e.selection.map(r):null===(n=t.selection)||void 0===n?void 0:n.map(s),effects:yt.mapEffects(t.effects,s).concat(yt.mapEffects(e.effects,r)),annotations:t.annotations.length?t.annotations.concat(e.annotations):e.annotations,scrollIntoView:t.scrollIntoView||e.scrollIntoView}}function xt(t,e,i){let n=e.selection,s=Zt(e.annotations);return e.userEvent&&(s=s.concat(Qt.userEvent.of(e.userEvent))),{changes:e.changes instanceof R?e.changes:R.of(e.changes||[],i,t.facet(dt)),selection:n&&(n instanceof V?n:V.single(n.anchor,n.head)),effects:Zt(e.effects),annotations:s,scrollIntoView:!!e.scrollIntoView}}function $t(t,e,i){let n=xt(t,e.length?e[0]:{},t.doc.length);e.length&&!1===e[0].filter&&(i=!1);for(let r=1;r<e.length;r++){!1===e[r].filter&&(i=!1);let s=!!e[r].sequential;n=St(n,xt(t,e[r],s?n.changes.newLength:t.doc.length),s)}let s=Qt.create(t,n.changes,n.selection,n.effects,n.annotations,n.scrollIntoView);return function(t){let e=t.startState,i=e.facet(gt),n=t;for(let s=i.length-1;s>=0;s--){let r=i[s](t);r&&Object.keys(r).length&&(n=St(n,xt(e,r,t.changes.newLength),!0))}return n==t?t:Qt.create(e,t.changes,t.selection,n.effects,n.annotations,n.scrollIntoView)}(i?function(t){let e=t.startState,i=!0;for(let s of e.facet(Ot)){let e=s(t);if(!1===e){i=!1;break}Array.isArray(e)&&(i=!0===i?e:kt(i,e))}if(!0!==i){let n,s;if(!1===i)s=t.changes.invertedDesc,n=R.empty(e.doc.length);else{let e=t.changes.filter(i);n=e.changes,s=e.filtered.mapDesc(e.changes).invertedDesc}t=Qt.create(e,n,t.selection&&t.selection.map(s),yt.mapEffects(t.effects,s),t.annotations,t.scrollIntoView)}let n=e.facet(pt);for(let s=n.length-1;s>=0;s--){let i=n[s](t);t=i instanceof Qt?i:Array.isArray(i)&&1==i.length&&i[0]instanceof Qt?i[0]:$t(e,Zt(i),!1)}return t}(s):s)}Qt.time=wt.define(),Qt.userEvent=wt.define(),Qt.addToHistory=wt.define(),Qt.remote=wt.define();const Pt=[];function Zt(t){return null==t?Pt:Array.isArray(t)?t:[t]}var Ct=function(t){return t[t.Word=0]="Word",t[t.Space=1]="Space",t[t.Other=2]="Other",t}(Ct||(Ct={}));const Tt=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let At;try{At=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch(Ap){}function Xt(t){return e=>{if(!/\S/.test(e))return Ct.Space;if(function(t){if(At)return At.test(t);for(let e=0;e<t.length;e++){let i=t[e];if(/\w/.test(i)||i>""&&(i.toUpperCase()!=i.toLowerCase()||Tt.test(i)))return!0}return!1}(e))return Ct.Word;for(let i=0;i<t.length;i++)if(e.indexOf(t[i])>-1)return Ct.Word;return Ct.Other}}class Wt{constructor(t,e,i,n,s,r){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=s,r&&(r._state=this);for(let o=0;o<this.config.dynamicSlots.length;o++)ht(this,o<<1);this.computeSlot=null}field(t,e=!0){let i=this.config.address[t.id];if(null!=i)return ht(this,i),ct(this,i);if(e)throw new RangeError("Field is not present in this state")}update(...t){return $t(this,t,!0)}applyTransaction(t){let e,i=this.config,{base:n,compartments:s}=i;for(let o of t.effects)o.is(ot.reconfigure)?(i&&(s=new Map,i.compartments.forEach(((t,e)=>s.set(e,t))),i=null),s.set(o.value.compartment,o.value.extension)):o.is(yt.reconfigure)?(i=null,n=o.value):o.is(yt.appendConfig)&&(i=null,n=Zt(n).concat(o.value));if(i)e=t.startState.values.slice();else{i=lt.resolve(n,s,this),e=new Wt(i,this.doc,this.selection,i.dynamicSlots.map((()=>null)),((t,e)=>e.reconfigure(t,this)),null).values}let r=t.startState.facet(ut)?t.newSelection:t.newSelection.asSingle();new Wt(i,t.newDoc,r,e,((e,i)=>i.update(e,t)),t)}replaceSelection(t){return"string"==typeof t&&(t=this.toText(t)),this.changeByRange((e=>({changes:{from:e.from,to:e.to,insert:t},range:V.cursor(e.from+t.length)})))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),s=[i.range],r=Zt(i.effects);for(let o=1;o<e.ranges.length;o++){let i=t(e.ranges[o]),a=this.changes(i.changes),l=a.map(n);for(let t=0;t<o;t++)s[t]=s[t].map(l);let h=n.mapDesc(a,!0);s.push(i.range.map(h)),n=n.compose(l),r=yt.mapEffects(r,l).concat(yt.mapEffects(Zt(i.effects),h))}return{changes:n,selection:V.create(s,e.mainIndex),effects:r}}changes(t=[]){return t instanceof R?t:R.of(t,this.doc.length,this.facet(Wt.lineSeparator))}toText(t){return h.of(t.split(this.facet(Wt.lineSeparator)||A))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return null==e?t.default:(ht(this,e),ct(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof K&&null!=this.config.address[n.id]&&(e[i]=n.spec.toJSON(this.field(t[i]),this))}return e}static fromJSON(t,e={},i){if(!t||"string"!=typeof t.doc)throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i)for(let s in i)if(Object.prototype.hasOwnProperty.call(t,s)){let e=i[s],r=t[s];n.push(e.init((t=>e.spec.fromJSON(r,t))))}return Wt.create({doc:t.doc,selection:V.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=lt.resolve(t.extensions||[],new Map),i=t.doc instanceof h?t.doc:h.of((t.doc||"").split(e.staticFacet(Wt.lineSeparator)||A)),n=t.selection?t.selection instanceof V?t.selection:V.single(t.selection.anchor,t.selection.head):V.single(0);return B(n,i.length),e.staticFacet(ut)||(n=n.asSingle()),new Wt(e,i,n,e.dynamicSlots.map((()=>null)),((t,e)=>e.create(t)),null)}get tabSize(){return this.facet(Wt.tabSize)}get lineBreak(){return this.facet(Wt.lineSeparator)||"\n"}get readOnly(){return this.facet(mt)}phrase(t,...e){for(let i of this.facet(Wt.phrases))if(Object.prototype.hasOwnProperty.call(i,t)){t=i[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,((t,i)=>{if("$"==i)return"$";let n=+(i||1);return!n||n>e.length?t:e[n-1]}))),t}languageDataAt(t,e,i=-1){let n=[];for(let s of this.facet(ft))for(let r of s(this,e,i))Object.prototype.hasOwnProperty.call(r,t)&&n.push(r[t]);return n}charCategorizer(t){return Xt(this.languageDataAt("wordChars",t).join(""))}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),s=this.charCategorizer(t),r=t-i,o=t-i;for(;r>0;){let t=k(e,r,!1);if(s(e.slice(t,r))!=Ct.Word)break;r=t}for(;o<n;){let t=k(e,o);if(s(e.slice(o,t))!=Ct.Word)break;o=t}return r==o?null:V.range(r+i,o+i)}}function Rt(t,e,i={}){let n={};for(let s of t)for(let t of Object.keys(s)){let e=s[t],r=n[t];if(void 0===r)n[t]=e;else if(r===e||void 0===e);else{if(!Object.hasOwnProperty.call(i,t))throw new Error("Config merge conflict for field "+t);n[t]=i[t](r,e)}}for(let s in e)void 0===n[s]&&(n[s]=e[s]);return n}Wt.allowMultipleSelections=ut,Wt.tabSize=G.define({combine:t=>t.length?t[0]:4}),Wt.lineSeparator=dt,Wt.readOnly=mt,Wt.phrases=G.define({compare(t,e){let i=Object.keys(t),n=Object.keys(e);return i.length==n.length&&i.every((i=>t[i]==e[i]))}}),Wt.languageData=ft,Wt.changeFilter=Ot,Wt.transactionFilter=pt,Wt.transactionExtender=gt,ot.reconfigure=yt.define();class Yt{eq(t){return this==t}range(t,e=t){return qt.create(t,e,this)}}Yt.prototype.startSide=Yt.prototype.endSide=0,Yt.prototype.point=!1,Yt.prototype.mapMode=X.TrackDel;let qt=class t{constructor(t,e,i){this.from=t,this.to=e,this.value=i}static create(e,i,n){return new t(e,i,n)}};function Mt(t,e){return t.from-e.from||t.value.startSide-e.value.startSide}class Et{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let s=i?this.to:this.from;for(let r=n,o=s.length;;){if(r==o)return r;let n=r+o>>1,a=s[n]-t||(i?this.value[n].endSide:this.value[n].startSide)-e;if(n==r)return a>=0?r:o;a>=0?o=n:r=n+1}}between(t,e,i,n){for(let s=this.findIndex(e,-1e9,!0),r=this.findIndex(i,1e9,!1,s);s<r;s++)if(!1===n(this.from[s]+t,this.to[s]+t,this.value[s]))return!1}map(t,e){let i=[],n=[],s=[],r=-1,o=-1;for(let a=0;a<this.value.length;a++){let l,h,c=this.value[a],f=this.from[a]+t,u=this.to[a]+t;if(f==u){let t=e.mapPos(f,c.startSide,c.mapMode);if(null==t)continue;if(l=h=t,c.startSide!=c.endSide&&(h=e.mapPos(f,c.endSide),h<l))continue}else if(l=e.mapPos(f,c.startSide),h=e.mapPos(u,c.endSide),l>h||l==h&&c.startSide>0&&c.endSide<=0)continue;(h-l||c.endSide-c.startSide)<0||(r<0&&(r=l),c.point&&(o=Math.max(o,h-l)),i.push(c),n.push(l-r),s.push(h-r))}return{mapped:i.length?new Et(n,s,i,o):null,pos:r}}}class Dt{constructor(t,e,i,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n}static create(t,e,i,n){return new Dt(t,e,i,n)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:s=this.length}=t,r=t.filter;if(0==e.length&&!r)return this;if(i&&(e=e.slice().sort(Mt)),this.isEmpty)return e.length?Dt.of(e):this;let o=new Vt(this,null,-1).goto(0),a=0,l=[],h=new jt;for(;o.value||a<e.length;)if(a<e.length&&(o.from-e[a].from||o.startSide-e[a].value.startSide)>=0){let t=e[a++];h.addInner(t.from,t.to,t.value)||l.push(t)}else 1==o.rangeIndex&&o.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(o.chunkIndex)<e[a].from)&&(!r||n>this.chunkEnd(o.chunkIndex)||s<this.chunkPos[o.chunkIndex])&&h.addChunk(this.chunkPos[o.chunkIndex],this.chunk[o.chunkIndex])?o.nextChunk():((!r||n>o.to||s<o.from||r(o.from,o.to,o.value))&&(h.addInner(o.from,o.to,o.value)||l.push(qt.create(o.from,o.to,o.value))),o.next());return h.finishInner(this.nextLayer.isEmpty&&!l.length?Dt.empty:this.nextLayer.update({add:l,filter:r,filterFrom:n,filterTo:s}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],i=[],n=-1;for(let r=0;r<this.chunk.length;r++){let s=this.chunkPos[r],o=this.chunk[r],a=t.touchesRange(s,s+o.length);if(!1===a)n=Math.max(n,o.maxPoint),e.push(o),i.push(t.mapPos(s));else if(!0===a){let{mapped:r,pos:a}=o.map(s,t);r&&(n=Math.max(n,r.maxPoint),e.push(r),i.push(a))}}let s=this.nextLayer.map(t);return 0==e.length?s:new Dt(i,e,s||Dt.empty,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let s=this.chunkPos[n],r=this.chunk[n];if(e>=s&&t<=s+r.length&&!1===r.between(s,t-s,e-s,i))return}this.nextLayer.between(t,e,i)}}iter(t=0){return Bt.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Bt.from(t).goto(e)}static compare(t,e,i,n,s=-1){let r=t.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=s)),o=e.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=s)),a=_t(r,o,i),l=new Gt(r,a,s),h=new Gt(o,a,s);i.iterGaps(((t,e,i)=>It(l,t,h,e,i,n))),i.empty&&0==i.length&&It(l,0,h,0,0,n)}static eq(t,e,i=0,n){null==n&&(n=999999999);let s=t.filter((t=>!t.isEmpty&&e.indexOf(t)<0)),r=e.filter((e=>!e.isEmpty&&t.indexOf(e)<0));if(s.length!=r.length)return!1;if(!s.length)return!0;let o=_t(s,r),a=new Gt(s,o,0).goto(i),l=new Gt(r,o,0).goto(i);for(;;){if(a.to!=l.to||!Lt(a.active,l.active)||a.point&&(!l.point||!a.point.eq(l.point)))return!1;if(a.to>n)return!0;a.next(),l.next()}}static spans(t,e,i,n,s=-1){let r=new Gt(t,null,s).goto(e),o=e,a=r.openStart;for(;;){let t=Math.min(r.to,i);if(r.point){let i=r.activeForPoint(r.to),s=r.pointFrom<e?i.length+1:r.point.startSide<0?i.length:Math.min(i.length,a);n.point(o,t,r.point,i,s,r.pointRank),a=Math.min(r.openEnd(t),i.length)}else t>o&&(n.span(o,t,r.active,a),a=r.openEnd(t));if(r.to>i)return a+(r.point&&r.to>i?1:0);o=r.to,r.next()}}static of(t,e=!1){let i=new jt;for(let n of t instanceof qt?[t]:e?function(t){if(t.length>1)for(let e=t[0],i=1;i<t.length;i++){let n=t[i];if(Mt(e,n)>0)return t.slice().sort(Mt);e=n}return t}(t):t)i.add(n.from,n.to,n.value);return i.finish()}static join(t){if(!t.length)return Dt.empty;let e=t[t.length-1];for(let i=t.length-2;i>=0;i--)for(let n=t[i];n!=Dt.empty;n=n.nextLayer)e=new Dt(n.chunkPos,n.chunk,e,Math.max(n.maxPoint,e.maxPoint));return e}}Dt.empty=new Dt([],[],null,-1),Dt.empty.nextLayer=Dt.empty;class jt{finishChunk(t){this.chunks.push(new Et(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new jt)).add(t,e,i)}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return!(n<0)&&(250==this.from.length&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),!0)}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(Dt.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),0==this.chunks.length)return t;let e=Dt.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function _t(t,e,i){let n=new Map;for(let r of t)for(let t=0;t<r.chunk.length;t++)r.chunk[t].maxPoint<=0&&n.set(r.chunk[t],r.chunkPos[t]);let s=new Set;for(let r of e)for(let t=0;t<r.chunk.length;t++){let e=n.get(r.chunk[t]);null==e||(i?i.mapPos(e):e)!=r.chunkPos[t]||(null==i?void 0:i.touchesRange(e,e+r.chunk[t].length))||s.add(r.chunk[t])}return s}class Vt{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let e=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(e)||this.layer.chunkEnd(this.chunkIndex)<t||e.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;){if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class Bt{constructor(t){this.heap=t}static from(t,e=null,i=-1){let n=[];for(let s=0;s<t.length;s++)for(let r=t[s];!r.isEmpty;r=r.nextLayer)r.maxPoint>=i&&n.push(new Vt(r,e,i,s));return 1==n.length?n[0]:new Bt(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let i=this.heap.length>>1;i>=0;i--)zt(this.heap,i);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let i=this.heap.length>>1;i>=0;i--)zt(this.heap,i);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(0==this.heap.length)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),zt(this.heap,0)}}}function zt(t,e){for(let i=t[e];;){let n=1+(e<<1);if(n>=t.length)break;let s=t[n];if(n+1<t.length&&s.compare(t[n+1])>=0&&(s=t[n+1],n++),i.compare(s)<0)break;t[n]=i,t[e]=s,e=n}}class Gt{constructor(t,e,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Bt.from(t,e,i)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){Nt(this.active,t),Nt(this.activeTo,t),Nt(this.activeRank,t),this.minActive=Ht(this.active,this.activeTo)}addActive(t){let e=0,{value:i,to:n,rank:s}=this.cursor;for(;e<this.activeRank.length&&(s-this.activeRank[e]||n-this.activeTo[e])>0;)e++;Ut(this.active,e,i),Ut(this.activeTo,e,n),Ut(this.activeRank,e,s),t&&Ut(t,e,this.cursor.from),this.minActive=Ht(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let n=this.minActive;if(n>-1&&(this.activeTo[n]-this.cursor.from||this.active[n].endSide-this.cursor.startSide)<0){if(this.activeTo[n]>t){this.to=this.activeTo[n],this.endSide=this.active[n].endSide;break}this.removeActive(n),i&&Nt(i,n)}else{if(!this.cursor.value){this.to=this.endSide=1e9;break}if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}{let t=this.cursor.value;if(t.point){if(!(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)){this.point=t,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=t.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}this.cursor.next()}else this.addActive(i),this.cursor.next()}}}if(i){this.openStart=0;for(let e=i.length-1;e>=0&&i[e]<t;e--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function It(t,e,i,n,s,r){t.goto(e),i.goto(n);let o=n+s,a=n,l=n-e;for(;;){let e=t.to+l-i.to||t.endSide-i.endSide,n=e<0?t.to+l:i.to,s=Math.min(n,o);if(t.point||i.point?t.point&&i.point&&(t.point==i.point||t.point.eq(i.point))&&Lt(t.activeForPoint(t.to),i.activeForPoint(i.to))||r.comparePoint(a,s,t.point,i.point):s>a&&!Lt(t.active,i.active)&&r.compareRange(a,s,t.active,i.active),n>o)break;a=n,e<=0&&t.next(),e>=0&&i.next()}}function Lt(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(t[i]!=e[i]&&!t[i].eq(e[i]))return!1;return!0}function Nt(t,e){for(let i=e,n=t.length-1;i<n;i++)t[i]=t[i+1];t.pop()}function Ut(t,e,i){for(let n=t.length-1;n>=e;n--)t[n+1]=t[n];t[e]=i}function Ht(t,e){let i=-1,n=1e9;for(let s=0;s<e.length;s++)(e[s]-n||t[s].endSide-t[i].endSide)<0&&(i=s,n=e[s]);return i}function Ft(t,e,i=t.length){let n=0;for(let s=0;s<i;)9==t.charCodeAt(s)?(n+=e-n%e,s++):(n++,s=k(t,s));return n}function Kt(t,e,i,n){for(let s=0,r=0;;){if(r>=e)return s;if(s==t.length)break;r+=9==t.charCodeAt(s)?i-r%i:1,s=k(t,s)}return!0===n?-1:t.length}const Jt="undefined"==typeof Symbol?"__ͼ":Symbol.for("ͼ"),te="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),ee="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class ie{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(t){return/^@/.test(t)?[t]:t.split(/,\s*/)}function s(t,e,r,o){let a=[],l=/^@(\w+)\b/.exec(t[0]),h=l&&"keyframes"==l[1];if(l&&null==e)return r.push(t[0]+";");for(let i in e){let o=e[i];if(/&/.test(i))s(i.split(/,\s*/).map((e=>t.map((t=>e.replace(/&/,t))))).reduce(((t,e)=>t.concat(e))),o,r);else if(o&&"object"==typeof o){if(!l)throw new RangeError("The value of a property ("+i+") should be a primitive value.");s(n(i),o,a,h)}else null!=o&&a.push(i.replace(/_.*/,"").replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))+": "+o+";")}(a.length||h)&&r.push((!i||l||o?t:t.map(i)).join(", ")+" {"+a.join(" ")+"}")}for(let r in t)s(n(r),t[r],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let t=ee[Jt]||1;return ee[Jt]=t+1,"ͼ"+t.toString(36)}static mount(t,e,i){let n=t[te],s=i&&i.nonce;n?s&&n.setNonce(s):n=new se(t,s),n.mount(Array.isArray(e)?e:[e],t)}}let ne=new Map;class se{constructor(t,e){let i=t.ownerDocument||t,n=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&n.CSSStyleSheet){let e=ne.get(i);if(e)return t[te]=e;this.sheet=new n.CSSStyleSheet,ne.set(i,this)}else this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);this.modules=[],t[te]=this}mount(t,e){let i=this.sheet,n=0,s=0;for(let r=0;r<t.length;r++){let e=t[r],o=this.modules.indexOf(e);if(o<s&&o>-1&&(this.modules.splice(o,1),s--,o=-1),-1==o){if(this.modules.splice(s++,0,e),i)for(let t=0;t<e.rules.length;t++)i.insertRule(e.rules[t],n++)}else{for(;s<o;)n+=this.modules[s++].rules.length;n+=e.rules.length,s++}}if(i)e.adoptedStyleSheets.indexOf(this.sheet)<0&&(e.adoptedStyleSheets=[this.sheet,...e.adoptedStyleSheets]);else{let t="";for(let e=0;e<this.modules.length;e++)t+=this.modules[e].getRules()+"\n";this.styleTag.textContent=t;let i=e.head||e;this.styleTag.parentNode!=i&&i.insertBefore(this.styleTag,i.firstChild)}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}for(var re={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},oe={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},ae="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),le="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),he=0;he<10;he++)re[48+he]=re[96+he]=String(he);for(he=1;he<=24;he++)re[he+111]="F"+he;for(he=65;he<=90;he++)re[he]=String.fromCharCode(he+32),oe[he]=String.fromCharCode(he);for(var ce in re)oe.hasOwnProperty(ce)||(oe[ce]=re[ce]);function fe(t){let e;return e=11==t.nodeType?t.getSelection?t:t.ownerDocument:t,e.getSelection()}function ue(t,e){return!!e&&(t==e||t.contains(1!=e.nodeType?e.parentNode:e))}function de(t,e){if(!e.anchorNode)return!1;try{return ue(t,e.anchorNode)}catch(Ap){return!1}}function Oe(t){return 3==t.nodeType?Pe(t,0,t.nodeValue.length).getClientRects():1==t.nodeType?t.getClientRects():[]}function pe(t,e,i,n){return!!i&&(we(t,e,i,n,-1)||we(t,e,i,n,1))}function ge(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e}function me(t){return 1==t.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}function we(t,e,i,n,s){for(;;){if(t==i&&e==n)return!0;if(e==(s<0?0:ve(t))){if("DIV"==t.nodeName)return!1;let i=t.parentNode;if(!i||1!=i.nodeType)return!1;e=ge(t)+(s<0?0:1),t=i}else{if(1!=t.nodeType)return!1;if(1==(t=t.childNodes[e+(s<0?-1:0)]).nodeType&&"false"==t.contentEditable)return!1;e=s<0?ve(t):0}}}function ve(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function be(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}function ye(t){let e=t.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight}}function Qe(t,e){let i=e.width/t.offsetWidth,n=e.height/t.offsetHeight;return(i>.995&&i<1.005||!isFinite(i)||Math.abs(e.width-t.offsetWidth)<1)&&(i=1),(n>.995&&n<1.005||!isFinite(n)||Math.abs(e.height-t.offsetHeight)<1)&&(n=1),{scaleX:i,scaleY:n}}class ke{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?ve(e):0),i,Math.min(t.focusOffset,i?ve(i):0))}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n}}let Se,xe=null;function $e(t){if(t.setActive)return t.setActive();if(xe)return t.focus(xe);let e=[];for(let i=t;i&&(e.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(t.focus(null==xe?{get preventScroll(){return xe={preventScroll:!0},!0}}:void 0),!xe){xe=!1;for(let t=0;t<e.length;){let i=e[t++],n=e[t++],s=e[t++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=s&&(i.scrollLeft=s)}}}function Pe(t,e,i=e){let n=Se||(Se=document.createRange());return n.setEnd(t,i),n.setStart(t,e),n}function Ze(t,e,i,n){let s={key:e,code:e,keyCode:i,which:i,cancelable:!0};n&&({altKey:s.altKey,ctrlKey:s.ctrlKey,shiftKey:s.shiftKey,metaKey:s.metaKey}=n);let r=new KeyboardEvent("keydown",s);r.synthetic=!0,t.dispatchEvent(r);let o=new KeyboardEvent("keyup",s);return o.synthetic=!0,t.dispatchEvent(o),r.defaultPrevented||o.defaultPrevented}function Ce(t){for(;t.attributes.length;)t.removeAttributeNode(t.attributes[0])}function Te(t){return t.scrollTop>Math.max(1,t.scrollHeight-t.clientHeight-4)}function Ae(t,e){for(let i=t,n=e;;){if(3==i.nodeType&&n>0)return{node:i,offset:n};if(1==i.nodeType&&n>0){if("false"==i.contentEditable)return null;i=i.childNodes[n-1],n=ve(i)}else{if(!i.parentNode||me(i))return null;n=ge(i),i=i.parentNode}}}function Xe(t,e){for(let i=t,n=e;;){if(3==i.nodeType&&n<i.nodeValue.length)return{node:i,offset:n};if(1==i.nodeType&&n<i.childNodes.length){if("false"==i.contentEditable)return null;i=i.childNodes[n],n=0}else{if(!i.parentNode||me(i))return null;n=ge(i)+1,i=i.parentNode}}}class We{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new We(t.parentNode,ge(t),e)}static after(t,e){return new We(t.parentNode,ge(t)+1,e)}}const Re=[];class Ye{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(2&this.flags){let i,n=this.dom,s=null;for(let r of this.children){if(7&r.flags){if(!r.dom&&(i=s?s.nextSibling:n.firstChild)){let t=Ye.get(i);(!t||!t.parent&&t.canReuseDOM(r))&&r.reuseDOM(i)}r.sync(t,e),r.flags&=-8}if(i=s?s.nextSibling:n.firstChild,e&&!e.written&&e.node==n&&i!=r.dom&&(e.written=!0),r.dom.parentNode==n)for(;i&&i!=r.dom;)i=qe(i);else n.insertBefore(r.dom,i);s=r.dom}for(i=s?s.nextSibling:n.firstChild,i&&e&&e.node==n&&(e.written=!0);i;)i=qe(i)}else if(1&this.flags)for(let i of this.children)7&i.flags&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let n=0==ve(t)?0:0==e?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;0==n&&e.firstChild!=e.lastChild&&(n=t==e.firstChild?-1:1),t=e}i=n<0?t:t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!Ye.get(i);)i=i.nextSibling;if(!i)return this.length;for(let n=0,s=0;;n++){let t=this.children[n];if(t.dom==i)return s;s+=t.length+t.breakAfter}}domBoundsAround(t,e,i=0){let n=-1,s=-1,r=-1,o=-1;for(let a=0,l=i,h=i;a<this.children.length;a++){let i=this.children[a],c=l+i.length;if(l<t&&c>e)return i.domBoundsAround(t,e,l);if(c>=t&&-1==n&&(n=a,s=l),l>e&&i.dom.parentNode==this.dom){r=a,o=h;break}h=c,l=c+i.breakAfter}return{from:s,to:o<0?i+this.length:o,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:r<this.children.length&&r>=0?this.children[r].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),1&e.flags)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,7&this.flags&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=Re){this.markDirty();for(let n=t;n<e;n++){let t=this.children[n];t.parent==this&&i.indexOf(t)<0&&t.destroy()}this.children.splice(t,e-t,...i);for(let n=0;n<i.length;n++)i[n].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new Me(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==t?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,n,s,r){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!(8&(this.flags|t.flags))}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}function qe(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}Ye.prototype.breakAfter=0;class Me{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function Ee(t,e,i,n,s,r,o,a,l){let{children:h}=t,c=h.length?h[e]:null,f=r.length?r[r.length-1]:null,u=f?f.breakAfter:o;if(!(e==n&&c&&!o&&!u&&r.length<2&&c.merge(i,s,r.length?f:null,0==i,a,l))){if(n<h.length){let t=h[n];t&&(s<t.length||t.breakAfter&&(null==f?void 0:f.breakAfter))?(e==n&&(t=t.split(s),s=0),!u&&f&&t.merge(0,s,f,!0,0,l)?r[r.length-1]=t:((s||t.children.length&&!t.children[0].length)&&t.merge(0,s,null,!1,0,l),r.push(t))):(null==t?void 0:t.breakAfter)&&(f?f.breakAfter=1:o=1),n++}for(c&&(c.breakAfter=o,i>0&&(!o&&r.length&&c.merge(i,c.length,r[0],!1,a,0)?c.breakAfter=r.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,a,0),e++));e<n&&r.length;)if(h[n-1].become(r[r.length-1]))n--,r.pop(),l=r.length?0:a;else{if(!h[e].become(r[0]))break;e++,r.shift(),a=r.length?0:l}!r.length&&e&&n<h.length&&!h[e-1].breakAfter&&h[n].merge(0,0,h[e-1],!1,a,l)&&e--,(e<n||r.length)&&t.replaceChildren(e,n,r)}}function De(t,e,i,n,s,r){let o=t.childCursor(),{i:a,off:l}=o.findPos(i,1),{i:h,off:c}=o.findPos(e,-1),f=e-i;for(let u of n)f+=u.length;t.length+=f,Ee(t,h,c,a,l,n,0,s,r)}let je="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},_e="undefined"!=typeof document?document:{documentElement:{style:{}}};const Ve=/Edge\/(\d+)/.exec(je.userAgent),Be=/MSIE \d/.test(je.userAgent),ze=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(je.userAgent),Ge=!!(Be||ze||Ve),Ie=!Ge&&/gecko\/(\d+)/i.test(je.userAgent),Le=!Ge&&/Chrome\/(\d+)/.exec(je.userAgent),Ne="webkitFontSmoothing"in _e.documentElement.style,Ue=!Ge&&/Apple Computer/.test(je.vendor),He=Ue&&(/Mobile\/\w+/.test(je.userAgent)||je.maxTouchPoints>2);var Fe={mac:He||/Mac/.test(je.platform),windows:/Win/.test(je.platform),linux:/Linux|X11/.test(je.platform),ie:Ge,ie_version:Be?_e.documentMode||6:ze?+ze[1]:Ve?+Ve[1]:0,gecko:Ie,gecko_version:Ie?+(/Firefox\/(\d+)/.exec(je.userAgent)||[0,0])[1]:0,chrome:!!Le,chrome_version:Le?+Le[1]:0,ios:He,android:/Android\b/.test(je.userAgent),webkit:Ne,safari:Ue,webkit_version:Ne?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:null!=_e.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};class Ke extends Ye{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){3==t.nodeType&&this.createDOM(t)}merge(t,e,i){return!(8&this.flags||i&&(!(i instanceof Ke)||this.length-(e-t)+i.length>256||8&i.flags))&&(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new Ke(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=8&this.flags,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new We(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return function(t,e,i){let n=t.nodeValue.length;e>n&&(e=n);let s=e,r=e,o=0;0==e&&i<0||e==n&&i>=0?Fe.chrome||Fe.gecko||(e?(s--,o=1):r<n&&(r++,o=-1)):i<0?s--:r<n&&r++;let a=Pe(t,s,r).getClientRects();if(!a.length)return null;let l=a[(o?o<0:i>=0)?0:a.length-1];Fe.safari&&!o&&0==l.width&&(l=Array.prototype.find.call(a,(t=>t.width))||l);return o?be(l,o<0):l||null}(this.dom,t,e)}}class Je extends Ye{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let n of e)n.setParent(this)}setAttrs(t){if(Ce(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!(8&(this.flags|t.flags))}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?4&this.flags&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,n,s,r){return(!i||!(!(i instanceof Je&&i.mark.eq(this.mark))||t&&s<=0||e<this.length&&r<=0))&&(De(this,t,e,i?i.children.slice():[],s-1,r-1),this.markDirty(),!0)}split(t){let e=[],i=0,n=-1,s=0;for(let o of this.children){let r=i+o.length;r>t&&e.push(i<t?o.split(t-i):o),n<0&&i>=t&&(n=s),i=r,s++}let r=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new Je(this.mark,e,r)}domAtPos(t){return ii(this,t)}coordsAt(t,e){return si(this,t,e)}}class ti extends Ye{static create(t,e,i){return new ti(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=ti.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,n,s,r){return!(i&&(!(i instanceof ti&&this.widget.compare(i.widget))||t>0&&s<=0||e<this.length&&r<=0))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof ti&&t.side==this.side&&this.widget.constructor==t.widget.constructor&&(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(0==this.length)return h.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):h.empty}domAtPos(t){return(this.length?0==t:this.side>0)?We.before(this.dom):We.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let n=this.dom.getClientRects(),s=null;if(!n.length)return null;let r=this.side?this.side<0:t>0;for(let o=r?n.length-1:0;s=n[o],!(t>0?0==o:o==n.length-1||s.top<s.bottom);o+=r?-1:1);return be(s,!r)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class ei extends Ye{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof ei&&t.side==this.side}split(){return new ei(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?We.before(this.dom):We.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return h.empty}get isHidden(){return!0}}function ii(t,e){let i=t.dom,{children:n}=t,s=0;for(let r=0;s<n.length;s++){let t=n[s],o=r+t.length;if(!(o==r&&t.getSide()<=0)){if(e>r&&e<o&&t.dom.parentNode==i)return t.domAtPos(e-r);if(e<=r)break;r=o}}for(let r=s;r>0;r--){let t=n[r-1];if(t.dom.parentNode==i)return t.domAtPos(t.length)}for(let r=s;r<n.length;r++){let t=n[r];if(t.dom.parentNode==i)return t.domAtPos(0)}return new We(i,0)}function ni(t,e,i){let n,{children:s}=t;i>0&&e instanceof Je&&s.length&&(n=s[s.length-1])instanceof Je&&n.mark.eq(e.mark)?ni(n,e.children[0],i-1):(s.push(e),e.setParent(t)),t.length+=e.length}function si(t,e,i){let n=null,s=-1,r=null,o=-1;!function t(e,a){for(let l=0,h=0;l<e.children.length&&h<=a;l++){let c=e.children[l],f=h+c.length;f>=a&&(c.children.length?t(c,a-h):(!r||r.isHidden&&i>0)&&(f>a||h==f&&c.getSide()>0)?(r=c,o=a-h):(h<a||h==f&&c.getSide()<0&&!c.isHidden)&&(n=c,s=a-h)),h=f}}(t,e);let a=(i<0?n:r)||n||r;return a?a.coordsAt(Math.max(0,a==n?s:o),i):function(t){let e=t.dom.lastChild;if(!e)return t.dom.getBoundingClientRect();let i=Oe(e);return i[i.length-1]||null}(t)}function ri(t,e){for(let i in t)"class"==i&&e.class?e.class+=" "+t.class:"style"==i&&e.style?e.style+=";"+t.style:e[i]=t[i];return e}Ke.prototype.children=ti.prototype.children=ei.prototype.children=Re;const oi=Object.create(null);function ai(t,e,i){if(t==e)return!0;t||(t=oi),e||(e=oi);let n=Object.keys(t),s=Object.keys(e);if(n.length-(i&&n.indexOf(i)>-1?1:0)!=s.length-(i&&s.indexOf(i)>-1?1:0))return!1;for(let r of n)if(r!=i&&(-1==s.indexOf(r)||t[r]!==e[r]))return!1;return!0}function li(t,e,i){let n=!1;if(e)for(let s in e)i&&s in i||(n=!0,"style"==s?t.style.cssText="":t.removeAttribute(s));if(i)for(let s in i)e&&e[s]==i[s]||(n=!0,"style"==s?t.style.cssText=i[s]:t.setAttribute(s,i[s]));return n}function hi(t){let e=Object.create(null);for(let i=0;i<t.attributes.length;i++){let n=t.attributes[i];e[n.name]=n.value}return e}class ci extends Ye{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,n,s,r){if(i){if(!(i instanceof ci))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),De(this,t,e,i?i.children.slice():[],s,r),!0}split(t){let e=new ci;if(e.breakAfter=this.breakAfter,0==this.length)return e;let{i:i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let s=i;s<this.children.length;s++)e.append(this.children[s],0);for(;i>0&&0==this.children[i-1].length;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){ai(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){ni(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=ri(e,this.attrs||{})),i&&(this.attrs=ri({class:i},this.attrs||{}))}domAtPos(t){return ii(this,t)}reuseDOM(t){"DIV"==t.nodeName&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?4&this.flags&&(Ce(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(li(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let n=this.dom.lastChild;for(;n&&Ye.get(n)instanceof Je;)n=n.lastChild;if(!(n&&this.length&&("BR"==n.nodeName||0!=(null===(i=Ye.get(n))||void 0===i?void 0:i.isEditable)||Fe.ios&&this.children.some((t=>t instanceof Ke))))){let t=document.createElement("BR");t.cmIgnore=!0,this.dom.appendChild(t)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let t,e=0;for(let i of this.children){if(!(i instanceof Ke)||/[^ -~]/.test(i.text))return null;let n=Oe(i.dom);if(1!=n.length)return null;e+=n[0].width,t=n[0].height}return e?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length,textHeight:t}:null}coordsAt(t,e){let i=si(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:t}=this.parent.view.viewState,e=i.bottom-i.top;if(Math.abs(e-t.lineHeight)<2&&t.textHeight<e){let n=(e-t.textHeight)/2;return{top:i.top+n,bottom:i.bottom-n,left:i.left,right:i.left}}}return i}become(t){return!1}covers(){return!0}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let s=t.children[i],r=n+s.length;if(r>=e){if(s instanceof ci)return s;if(r>e)break}n=r+s.breakAfter}return null}}class fi extends Ye{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,n,s,r){return!(i&&(!(i instanceof fi&&this.widget.compare(i.widget))||t>0&&s<=0||e<this.length&&r<=0))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return 0==t?We.before(this.dom):We.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new fi(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return Re}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):h.empty}domBoundsAround(){return null}become(t){return t instanceof fi&&t.widget.constructor==this.widget.constructor&&(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){return this.widget.coordsAt(this.dom,t,e)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e!=i&&(t<0?e<0:i>0)}}class ui{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var di=function(t){return t[t.Text=0]="Text",t[t.WidgetBefore=1]="WidgetBefore",t[t.WidgetAfter=2]="WidgetAfter",t[t.WidgetRange=3]="WidgetRange",t}(di||(di={}));class Oi extends Yt{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(t){return new pi(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new mi(t,e,e,i,t.widget||null,!1)}static replace(t){let e,i,n=!!t.block;if(t.isBlockGap)e=-5e8,i=4e8;else{let{start:s,end:r}=wi(t,n);e=(s?n?-3e8:-1:5e8)-1,i=1+(r?n?2e8:1:-6e8)}return new mi(t,e,i,n,t.widget||null,!0)}static line(t){return new gi(t)}static set(t,e=!1){return Dt.of(t,e)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}Oi.none=Dt.empty;class pi extends Oi{constructor(t){let{start:e,end:i}=wi(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof pi&&this.tagName==t.tagName&&(this.class||(null===(e=this.attrs)||void 0===e?void 0:e.class))==(t.class||(null===(i=t.attrs)||void 0===i?void 0:i.class))&&ai(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}pi.prototype.point=!1;class gi extends Oi{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof gi&&this.spec.class==t.spec.class&&ai(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}gi.prototype.mapMode=X.TrackBefore,gi.prototype.point=!0;class mi extends Oi{constructor(t,e,i,n,s,r){super(e,i,s,t),this.block=n,this.isReplace=r,this.mapMode=n?e<=0?X.TrackBefore:X.TrackAfter:X.TrackDel}get type(){return this.startSide!=this.endSide?di.WidgetRange:this.startSide<=0?di.WidgetBefore:di.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof mi&&(e=this.widget,i=t.widget,e==i||!!(e&&i&&e.compare(i)))&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide;var e,i}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}function wi(t,e=!1){let{inclusiveStart:i,inclusiveEnd:n}=t;return null==i&&(i=t.inclusive),null==n&&(n=t.inclusive),{start:null!=i?i:e,end:null!=n?n:e}}function vi(t,e,i,n=0){let s=i.length-1;s>=0&&i[s]+n>=t?i[s]=Math.max(i[s],e):i.push(t,e)}mi.prototype.point=!0;class bi{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof fi&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new ci),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(yi(new ei(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,this.posCovered()||t&&this.content.length&&this.content[this.content.length-1]instanceof fi||this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:n}=this.cursor.next(this.skip);if(this.skip=0,n)throw new Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}this.text=e,this.textOff=0}let n=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(yi(new Ke(this.text.slice(this.textOff,this.textOff+n)),e),i),this.atCursorPos=!0,this.textOff+=n,t-=n,i=0}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n)}point(t,e,i,n,s,r){if(this.disallowBlockEffectsFor[r]&&i instanceof mi){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let o=e-t;if(i instanceof mi)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new fi(i.widget||Qi.block,o,i));else{let r=ti.create(i.widget||Qi.inline,o,o?0:i.startSide),a=this.atCursorPos&&!r.isEditable&&s<=n.length&&(t<e||i.startSide>0),l=!r.isEditable&&(t<e||s>n.length||i.startSide<=0),h=this.getLine();2!=this.pendingBuffer||a||r.isEditable||(this.pendingBuffer=0),this.flushBuffer(n),a&&(h.append(yi(new ei(1),n),s),s=n.length+Math.max(0,s-n.length)),h.append(yi(r,n),s),this.atCursorPos=l,this.pendingBuffer=l?t<e||s>n.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=n.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);o&&(this.textOff+o<=this.text.length?this.textOff+=o:(this.skip+=o-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=s)}static build(t,e,i,n,s){let r=new bi(t,e,i,s);return r.openEnd=Dt.spans(n,e,i,r),r.openStart<0&&(r.openStart=r.openEnd),r.finish(r.openEnd),r}}function yi(t,e){for(let i of e)t=new Je(i,[t],t.length);return t}class Qi extends ui{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}Qi.inline=new Qi("span"),Qi.block=new Qi("div");var ki=function(t){return t[t.LTR=0]="LTR",t[t.RTL=1]="RTL",t}(ki||(ki={}));const Si=ki.LTR,xi=ki.RTL;function $i(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<+t[i]);return e}const Pi=$i("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),Zi=$i("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),Ci=Object.create(null),Ti=[];for(let Xp of["()","[]","{}"]){let t=Xp.charCodeAt(0),e=Xp.charCodeAt(1);Ci[t]=e,Ci[e]=-t}function Ai(t){return t<=247?Pi[t]:1424<=t&&t<=1524?2:1536<=t&&t<=1785?Zi[t-1536]:1774<=t&&t<=2220?4:8192<=t&&t<=8204?256:64336<=t&&t<=65023?4:1}const Xi=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class Wi{get dir(){return this.level%2?xi:Si}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,n){let s=-1;for(let r=0;r<t.length;r++){let o=t[r];if(o.from<=e&&o.to>=e){if(o.level==i)return r;(s<0||(0!=n?n<0?o.from<e:o.to>e:t[s].level>o.level))&&(s=r)}}if(s<0)throw new RangeError("Index out of range");return s}}function Ri(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],s=e[i];if(n.from!=s.from||n.to!=s.to||n.direction!=s.direction||!Ri(n.inner,s.inner))return!1}return!0}const Yi=[];function qi(t,e,i,n,s,r,o){let a=n%2?2:1;if(n%2==s%2)for(let l=e,h=0;l<i;){let e=!0,c=!1;if(h==r.length||l<r[h].from){let t=Yi[l];t!=a&&(e=!1,c=16==t)}let f=e||1!=a?null:[],u=e?n:n+1,d=l;t:for(;;)if(h<r.length&&d==r[h].from){if(c)break t;let O=r[h];if(!e)for(let t=O.to,e=h+1;;){if(t==i)break t;if(!(e<r.length&&r[e].from==t)){if(Yi[t]==a)break t;break}t=r[e++].to}if(h++,f)f.push(O);else{O.from>l&&o.push(new Wi(l,O.from,u)),Mi(t,O.direction==Si!=!(u%2)?n+1:n,s,O.inner,O.from,O.to,o),l=O.to}d=O.to}else{if(d==i||(e?Yi[d]!=a:Yi[d]==a))break;d++}f?qi(t,l,d,n+1,s,f,o):l<d&&o.push(new Wi(l,d,u)),l=d}else for(let l=i,h=r.length;l>e;){let i=!0,c=!1;if(!h||l>r[h-1].to){let t=Yi[l-1];t!=a&&(i=!1,c=16==t)}let f=i||1!=a?null:[],u=i?n:n+1,d=l;t:for(;;)if(h&&d==r[h-1].to){if(c)break t;let O=r[--h];if(!i)for(let t=O.from,i=h;;){if(t==e)break t;if(!i||r[i-1].to!=t){if(Yi[t-1]==a)break t;break}t=r[--i].from}if(f)f.push(O);else{O.to<l&&o.push(new Wi(O.to,l,u)),Mi(t,O.direction==Si!=!(u%2)?n+1:n,s,O.inner,O.from,O.to,o),l=O.from}d=O.from}else{if(d==e||(i?Yi[d-1]!=a:Yi[d-1]==a))break;d--}f?qi(t,d,l,n+1,s,f,o):d<l&&o.push(new Wi(d,l,u)),l=d}}function Mi(t,e,i,n,s,r,o){let a=e%2?2:1;!function(t,e,i,n,s){for(let r=0;r<=n.length;r++){let o=r?n[r-1].to:e,a=r<n.length?n[r].from:i,l=r?256:s;for(let e=o,i=l,n=l;e<a;e++){let s=Ai(t.charCodeAt(e));512==s?s=i:8==s&&4==n&&(s=16),Yi[e]=4==s?2:s,7&s&&(n=s),i=s}for(let t=o,e=l,n=l;t<a;t++){let s=Yi[t];if(128==s)t<a-1&&e==Yi[t+1]&&24&e?s=Yi[t]=e:Yi[t]=256;else if(64==s){let s=t+1;for(;s<a&&64==Yi[s];)s++;let r=t&&8==e||s<i&&8==Yi[s]?1==n?1:8:256;for(let e=t;e<s;e++)Yi[e]=r;t=s-1}else 8==s&&1==n&&(Yi[t]=1);e=s,7&s&&(n=s)}}}(t,s,r,n,a),function(t,e,i,n,s){let r=1==s?2:1;for(let o=0,a=0,l=0;o<=n.length;o++){let h=o?n[o-1].to:e,c=o<n.length?n[o].from:i;for(let e,i,n,o=h;o<c;o++)if(i=Ci[e=t.charCodeAt(o)])if(i<0){for(let t=a-3;t>=0;t-=3)if(Ti[t+1]==-i){let e=Ti[t+2],i=2&e?s:4&e?1&e?r:s:0;i&&(Yi[o]=Yi[Ti[t]]=i),a=t;break}}else{if(189==Ti.length)break;Ti[a++]=o,Ti[a++]=e,Ti[a++]=l}else if(2==(n=Yi[o])||1==n){let t=n==s;l=t?0:1;for(let e=a-3;e>=0;e-=3){let i=Ti[e+2];if(2&i)break;if(t)Ti[e+2]|=2;else{if(4&i)break;Ti[e+2]|=4}}}}}(t,s,r,n,a),function(t,e,i,n){for(let s=0,r=n;s<=i.length;s++){let o=s?i[s-1].to:t,a=s<i.length?i[s].from:e;for(let l=o;l<a;){let o=Yi[l];if(256==o){let o=l+1;for(;;)if(o==a){if(s==i.length)break;o=i[s++].to,a=s<i.length?i[s].from:e}else{if(256!=Yi[o])break;o++}let h=1==r,c=h==(1==(o<e?Yi[o]:n))?h?1:2:n;for(let e=o,n=s,r=n?i[n-1].to:t;e>l;)e==r&&(e=i[--n].from,r=n?i[n-1].to:t),Yi[--e]=c;l=o}else r=o,l++}}}(s,r,n,a),qi(t,s,r,e,i,n,o)}function Ei(t){return[new Wi(0,t,0)]}let Di="";function ji(t,e,i,n,s){var r;let o=n.head-t.from,a=Wi.find(e,o,null!==(r=n.bidiLevel)&&void 0!==r?r:-1,n.assoc),l=e[a],h=l.side(s,i);if(o==h){let t=a+=s?1:-1;if(t<0||t>=e.length)return null;l=e[a=t],o=l.side(!s,i),h=l.side(s,i)}let c=k(t.text,o,l.forward(s,i));(c<l.from||c>l.to)&&(c=h),Di=t.text.slice(Math.min(o,c),Math.max(o,c));let f=a==(s?e.length-1:0)?null:e[a+(s?1:-1)];return f&&c==h&&f.level+(s?0:1)<l.level?V.cursor(f.side(!s,i)+t.from,f.forward(s,i)?1:-1,f.level):V.cursor(c+t.from,l.forward(s,i)?-1:1,l.level)}function _i(t,e,i){for(let n=e;n<i;n++){let e=Ai(t.charCodeAt(n));if(1==e)return Si;if(2==e||4==e)return xi}return Si}const Vi=G.define(),Bi=G.define(),zi=G.define(),Gi=G.define(),Ii=G.define(),Li=G.define(),Ni=G.define(),Ui=G.define({combine:t=>t.some((t=>t))}),Hi=G.define({combine:t=>t.some((t=>t))}),Fi=G.define();class Ki{constructor(t,e="nearest",i="nearest",n=5,s=5,r=!1){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=s,this.isSnapshot=r}map(t){return t.empty?this:new Ki(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new Ki(V.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const Ji=yt.define({map:(t,e)=>t.map(e)});function tn(t,e,i){let n=t.facet(Gi);n.length?n[0](e):window.onerror&&window.onerror(String(e),i,void 0,void 0,e)}const en=G.define({combine:t=>!t.length||t[0]});let nn=0;const sn=G.define();class rn{constructor(t,e,i,n,s){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=n,this.extension=s(this)}static define(t,e){const{eventHandlers:i,eventObservers:n,provide:s,decorations:r}=e||{};return new rn(nn++,t,i,n,(t=>{let e=[sn.of(t)];return r&&e.push(hn.of((e=>{let i=e.plugin(t);return i?r(i):Oi.none}))),s&&e.push(s(t)),e}))}static fromClass(t,e){return rn.define((e=>new t(e)),e)}}class on{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}update(t){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(tO){if(tn(t.state,tO,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(Ap){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(t)}catch(tO){tn(t.state,tO,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(null===(e=this.value)||void 0===e?void 0:e.destroy)try{this.value.destroy()}catch(tO){tn(t.state,tO,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const an=G.define(),ln=G.define(),hn=G.define(),cn=G.define(),fn=G.define(),un=G.define();function dn(t,e){let i=t.state.facet(un);if(!i.length)return i;let n=i.map((e=>e instanceof Function?e(t):e)),s=[];return Dt.spans(n,e.from,e.to,{point(){},span(t,i,n,r){let o=t-e.from,a=i-e.from,l=s;for(let s=n.length-1;s>=0;s--,r--){let t,i=n[s].spec.bidiIsolate;if(null==i&&(i=_i(e.text,o,a)),r>0&&l.length&&(t=l[l.length-1]).to==o&&t.direction==i)t.to=a,l=t.inner;else{let t={from:o,to:a,direction:i,inner:[]};l.push(t),l=t.inner}}}}),s}const On=G.define();function pn(t){let e=0,i=0,n=0,s=0;for(let r of t.state.facet(On)){let o=r(t);o&&(null!=o.left&&(e=Math.max(e,o.left)),null!=o.right&&(i=Math.max(i,o.right)),null!=o.top&&(n=Math.max(n,o.top)),null!=o.bottom&&(s=Math.max(s,o.bottom)))}return{left:e,right:i,top:n,bottom:s}}const gn=G.define();class mn{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n}join(t){return new mn(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(0==e.length)return t;let i=[];for(let n=0,s=0,r=0,o=0;;n++){let a=n==t.length?null:t[n],l=r-o,h=a?a.fromB:1e9;for(;s<e.length&&e[s]<h;){let t=e[s],n=e[s+1],r=Math.max(o,t),a=Math.min(h,n);if(r<=a&&new mn(r+l,a+l,r,a).addToSet(i),n>h)break;s+=2}if(!a)return i;new mn(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),r=a.toA,o=a.toB}}}class wn{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=R.empty(this.startState.doc.length);for(let s of i)this.changes=this.changes.compose(s.changes);let n=[];this.changes.iterChangedRanges(((t,e,i,s)=>n.push(new mn(t,e,i,s)))),this.changedRanges=n}static create(t,e,i){return new wn(t,e,i)}get viewportChanged(){return(4&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(10&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some((t=>t.selection))}get empty(){return 0==this.flags&&0==this.transactions.length}}class vn extends Ye{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new ci],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new mn(0,0,0,t.state.doc.length)],0,null)}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every((({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo))?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0);let n=-1;this.view.inputState.composing>=0&&((null===(e=this.domChanged)||void 0===e?void 0:e.newSel)?n=this.domChanged.newSel.head:function(t,e){let i=!1;e&&t.iterChangedRanges(((t,n)=>{t<e.to&&n>e.from&&(i=!0)}));return i}(t.changes,this.hasComposition)||t.selectionSet||(n=t.state.selection.main.head));let s=n>-1?function(t,e,i){let n=yn(t,i);if(!n)return null;let{node:s,from:r,to:o}=n,a=s.nodeValue;if(/[\n\r]/.test(a))return null;if(t.state.doc.sliceString(n.from,n.to)!=a)return null;let l=e.invertedDesc,h=new mn(l.mapPos(r),l.mapPos(o),r,o),c=[];for(let f=s.parentNode;;f=f.parentNode){let e=Ye.get(f);if(e instanceof Je)c.push({node:f,deco:e.mark});else{if(e instanceof ci||"DIV"==f.nodeName&&f.parentNode==t.contentDOM)return{range:h,text:s,marks:c,line:f};if(f==t.contentDOM)return null;c.push({node:f,deco:new pi({inclusive:!0,attributes:hi(f),tagName:f.tagName.toLowerCase()})})}}}(this.view,t.changes,n):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:e,to:n}=this.hasComposition;i=new mn(e,n,t.changes.mapPos(e,-1),t.changes.mapPos(n,1)).addToSet(i.slice())}this.hasComposition=s?{from:s.range.fromB,to:s.range.toB}:null,(Fe.ie||Fe.chrome)&&!s&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let r=function(t,e,i){let n=new Qn;return Dt.compare(t,e,i,n),n.changes}(this.decorations,this.updateDeco(),t.changes);return i=mn.extendWithRanges(i,r),!!(7&this.flags||0!=i.length)&&(this.updateInner(i,t.startState.doc.length,s),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:n}=this.view;n.ignore((()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let t=Fe.chrome||Fe.ios?{node:n.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,t),this.flags&=-8,t&&(t.written||n.selectionRange.focusNode!=t.node)&&(this.forceSelection=!0),this.dom.style.height=""})),this.markedForComposition.forEach((t=>t.flags&=-9));let s=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let r of this.children)r instanceof fi&&r.widget instanceof bn&&s.push(r.dom);n.updateGaps(s)}updateChildren(t,e,i){let n=i?i.range.addToSet(t.slice()):t,s=this.childCursor(e);for(let r=n.length-1;;r--){let t=r>=0?n[r]:null;if(!t)break;let e,o,a,l,{fromA:h,toA:c,fromB:f,toB:u}=t;if(i&&i.range.fromB<u&&i.range.toB>f){let t=bi.build(this.view.state.doc,f,i.range.fromB,this.decorations,this.dynamicDecorationMap),n=bi.build(this.view.state.doc,i.range.toB,u,this.decorations,this.dynamicDecorationMap);o=t.breakAtStart,a=t.openStart,l=n.openEnd;let s=this.compositionView(i);n.breakAtStart?s.breakAfter=1:n.content.length&&s.merge(s.length,s.length,n.content[0],!1,n.openStart,0)&&(s.breakAfter=n.content[0].breakAfter,n.content.shift()),t.content.length&&s.merge(0,0,t.content[t.content.length-1],!0,0,t.openEnd)&&t.content.pop(),e=t.content.concat(s).concat(n.content)}else({content:e,breakAtStart:o,openStart:a,openEnd:l}=bi.build(this.view.state.doc,f,u,this.decorations,this.dynamicDecorationMap));let{i:d,off:O}=s.findPos(c,1),{i:p,off:g}=s.findPos(h,-1);Ee(this,p,g,d,O,e,o,a,l)}i&&this.fixCompositionDOM(i)}compositionView(t){let e=new Ke(t.text.nodeValue);e.flags|=8;for(let{deco:n}of t.marks)e=new Je(n,[e],e.length);let i=new ci;return i.append(e,0),i}fixCompositionDOM(t){let e=(t,e)=>{e.flags|=8|(e.children.some((t=>7&t.flags))?1:0),this.markedForComposition.add(e);let i=Ye.get(t);i&&i!=e&&(i.dom=null),e.setDOM(t)},i=this.childPos(t.range.fromB,1),n=this.children[i.i];e(t.line,n);for(let s=t.marks.length-1;s>=-1;s--)i=n.childPos(i.off,1),n=n.children[i.i],e(s>=0?t.marks[s].node:t.text,n)}updateSelection(t=!1,e=!1){!t&&this.view.observer.selectionRange.focusNode||this.view.observer.readSelectionRange();let i=this.view.root.activeElement,n=i==this.dom,s=!n&&de(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(n||e||s))return;let r=this.forceSelection;this.forceSelection=!1;let o=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(o.anchor)),l=o.empty?a:this.moveToLine(this.domAtPos(o.head));if(Fe.gecko&&o.empty&&!this.hasComposition&&(1==(h=a).node.nodeType&&h.node.firstChild&&(0==h.offset||"false"==h.node.childNodes[h.offset-1].contentEditable)&&(h.offset==h.node.childNodes.length||"false"==h.node.childNodes[h.offset].contentEditable))){let t=document.createTextNode("");this.view.observer.ignore((()=>a.node.insertBefore(t,a.node.childNodes[a.offset]||null))),a=l=new We(t,0),r=!0}var h;let c=this.view.observer.selectionRange;!r&&c.focusNode&&(pe(a.node,a.offset,c.anchorNode,c.anchorOffset)&&pe(l.node,l.offset,c.focusNode,c.focusOffset)||this.suppressWidgetCursorChange(c,o))||(this.view.observer.ignore((()=>{Fe.android&&Fe.chrome&&this.dom.contains(c.focusNode)&&function(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let t=fe(this.view.root);if(t)if(o.empty){if(Fe.gecko){let t=(e=a.node,n=a.offset,1!=e.nodeType?0:(n&&"false"==e.childNodes[n-1].contentEditable?1:0)|(n<e.childNodes.length&&"false"==e.childNodes[n].contentEditable?2:0));if(t&&3!=t){let e=(1==t?Ae:Xe)(a.node,a.offset);e&&(a=new We(e.node,e.offset))}}t.collapse(a.node,a.offset),null!=o.bidiLevel&&void 0!==t.caretBidiLevel&&(t.caretBidiLevel=o.bidiLevel)}else if(t.extend){t.collapse(a.node,a.offset);try{t.extend(l.node,l.offset)}catch(Ap){}}else{let e=document.createRange();o.anchor>o.head&&([a,l]=[l,a]),e.setEnd(l.node,l.offset),e.setStart(a.node,a.offset),t.removeAllRanges(),t.addRange(e)}else;var e,n;s&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())})),this.view.observer.setSelectionRange(a,l)),this.impreciseAnchor=a.precise?null:new We(c.anchorNode,c.anchorOffset),this.impreciseHead=l.precise?null:new We(c.focusNode,c.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&pe(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=fe(t.root),{anchorNode:n,anchorOffset:s}=t.observer.selectionRange;if(!(i&&e.empty&&e.assoc&&i.modify))return;let r=ci.find(this,e.head);if(!r)return;let o=r.posAtStart;if(e.head==o||e.head==o+r.length)return;let a=this.coordsAt(e.head,-1),l=this.coordsAt(e.head,1);if(!a||!l||a.bottom>l.top)return;let h=this.domAtPos(e.head+e.assoc);i.collapse(h.node,h.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let c=t.observer.selectionRange;t.docView.posFromDOM(c.anchorNode,c.anchorOffset)!=e.from&&i.collapse(n,s)}moveToLine(t){let e,i=this.dom;if(t.node!=i)return t;for(let n=t.offset;!e&&n<i.childNodes.length;n++){let t=Ye.get(i.childNodes[n]);t instanceof ci&&(e=t.domAtPos(0))}for(let n=t.offset-1;!e&&n>=0;n--){let t=Ye.get(i.childNodes[n]);t instanceof ci&&(e=t.domAtPos(t.length))}return e?new We(e.node,e.offset,!0):t}nearest(t){for(let e=t;e;){let t=Ye.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof ci)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,n=0;for(let s=this.length,r=this.children.length-1;r>=0;r--){let o=this.children[r],a=s-o.breakAfter,l=a-o.length;if(a<t)break;l<=t&&(l<t||o.covers(-1))&&(a>t||o.covers(1))&&(!i||o instanceof ci&&!(i instanceof ci&&e>=0))&&(i=o,n=l),s=l}return i?i.coordsAt(t-n,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),n=this.children[e];if(!(n instanceof ci))return null;for(;n.children.length;){let{i:t,off:e}=n.childPos(i,1);for(;;t++){if(t==n.children.length)return null;if((n=n.children[t]).length)break}i=e}if(!(n instanceof Ke))return null;let s=k(n.text,i);if(s==i)return null;let r=Pe(n.dom,i,s).getClientRects();for(let o=0;o<r.length;o++){let t=r[o];if(o==r.length-1||t.top<t.bottom&&t.left<t.right)return t}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:n}=t,s=this.view.contentDOM.clientWidth,r=s>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,o=-1,a=this.view.textDirection==ki.LTR;for(let l=0,h=0;h<this.children.length;h++){let t=this.children[h],c=l+t.length;if(c>n)break;if(l>=i){let i=t.dom.getBoundingClientRect();if(e.push(i.height),r){let e=t.dom.lastChild,n=e?Oe(e):[];if(n.length){let t=n[n.length-1],e=a?t.right-i.left:i.right-t.left;e>o&&(o=e,this.minWidth=s,this.minWidthFrom=l,this.minWidthTo=c)}}}l=c+t.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return"rtl"==getComputedStyle(this.children[e].dom).direction?ki.RTL:ki.LTR}measureTextSize(){for(let s of this.children)if(s instanceof ci){let t=s.measureTextSize();if(t)return t}let t,e,i,n=document.createElement("div");return n.className="cm-line",n.style.width="99999px",n.style.position="absolute",n.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore((()=>{this.dom.appendChild(n);let s=Oe(n.firstChild)[0];t=n.getBoundingClientRect().height,e=s?s.width/27:7,i=s?s.height:t,n.remove()})),{lineHeight:t,charWidth:e,textHeight:i}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new Me(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let s=n==e.viewports.length?null:e.viewports[n],r=s?s.from-1:this.length;if(r>i){let n=(e.lineBlockAt(r).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(Oi.replace({widget:new bn(n),block:!0,inclusive:!0,isBlockGap:!0}).range(i,r))}if(!s)break;i=s.to+1}return Oi.set(t)}updateDeco(){let t=0,e=this.view.state.facet(hn).map((e=>(this.dynamicDecorationMap[t++]="function"==typeof e)?e(this.view):e)),i=!1,n=this.view.state.facet(cn).map(((t,e)=>{let n="function"==typeof t;return n&&(i=!0),n?t(this.view):t}));for(n.length&&(this.dynamicDecorationMap[t++]=i,e.push(Dt.join(n))),this.decorations=[...e,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let e=this.view.viewState.lineBlockAt(t.range.head);return this.view.scrollDOM.scrollTop=e.top-t.yMargin,void(this.view.scrollDOM.scrollLeft=t.xMargin)}for(let l of this.view.state.facet(Fi))try{if(l(this.view,t.range,t))return!0}catch(tO){tn(this.view.state,tO,"scroll handler")}let e,{range:i}=t,n=this.coordsAt(i.head,i.empty?i.assoc:i.head>i.anchor?-1:1);if(!n)return;!i.empty&&(e=this.coordsAt(i.anchor,i.anchor>i.head?-1:1))&&(n={left:Math.min(n.left,e.left),top:Math.min(n.top,e.top),right:Math.max(n.right,e.right),bottom:Math.max(n.bottom,e.bottom)});let s=pn(this.view),r={left:n.left-s.left,top:n.top-s.top,right:n.right+s.right,bottom:n.bottom+s.bottom},{offsetWidth:o,offsetHeight:a}=this.view.scrollDOM;!function(t,e,i,n,s,r,o,a){let l=t.ownerDocument,h=l.defaultView||window;for(let c=t,f=!1;c&&!f;)if(1==c.nodeType){let t,u=c==l.body,d=1,O=1;if(u)t=ye(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(f=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let e=c.getBoundingClientRect();({scaleX:d,scaleY:O}=Qe(c,e)),t={left:e.left,right:e.left+c.clientWidth*d,top:e.top,bottom:e.top+c.clientHeight*O}}let p=0,g=0;if("nearest"==s)e.top<t.top?(g=-(t.top-e.top+o),i>0&&e.bottom>t.bottom+g&&(g=e.bottom-t.bottom+g+o)):e.bottom>t.bottom&&(g=e.bottom-t.bottom+o,i<0&&e.top-g<t.top&&(g=-(t.top+g-e.top+o)));else{let n=e.bottom-e.top,r=t.bottom-t.top;g=("center"==s&&n<=r?e.top+n/2-r/2:"start"==s||"center"==s&&i<0?e.top-o:e.bottom-r+o)-t.top}if("nearest"==n?e.left<t.left?(p=-(t.left-e.left+r),i>0&&e.right>t.right+p&&(p=e.right-t.right+p+r)):e.right>t.right&&(p=e.right-t.right+r,i<0&&e.left<t.left+p&&(p=-(t.left+p-e.left+r))):p=("center"==n?e.left+(e.right-e.left)/2-(t.right-t.left)/2:"start"==n==a?e.left-r:e.right-(t.right-t.left)+r)-t.left,p||g)if(u)h.scrollBy(p,g);else{let t=0,i=0;if(g){let t=c.scrollTop;c.scrollTop+=g/O,i=(c.scrollTop-t)*O}if(p){let e=c.scrollLeft;c.scrollLeft+=p/d,t=(c.scrollLeft-e)*d}e={left:e.left-t,top:e.top-i,right:e.right-t,bottom:e.bottom-i},t&&Math.abs(t-p)<1&&(n="nearest"),i&&Math.abs(i-g)<1&&(s="nearest")}if(u)break;c=c.assignedSlot||c.parentNode}else{if(11!=c.nodeType)break;c=c.host}}(this.view.scrollDOM,r,i.head<i.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,o),-o),Math.max(Math.min(t.yMargin,a),-a),this.view.textDirection==ki.LTR)}}class bn extends ui{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}function yn(t,e){let i=t.observer.selectionRange;if(!i.focusNode)return null;let n=Ae(i.focusNode,i.focusOffset),s=Xe(i.focusNode,i.focusOffset),r=n||s;if(s&&n&&s.node!=n.node){let e=Ye.get(s.node);if(!e||e instanceof Ke&&e.text!=s.node.nodeValue)r=s;else if(t.docView.lastCompositionAfterCursor){let t=Ye.get(n.node);!t||t instanceof Ke&&t.text!=n.node.nodeValue||(r=s)}}if(t.docView.lastCompositionAfterCursor=r!=n,!r)return null;let o=e-r.offset;return{from:o,to:o+r.node.nodeValue.length,node:r.node}}let Qn=class{constructor(){this.changes=[]}compareRange(t,e){vi(t,e,this.changes)}comparePoint(t,e){vi(t,e,this.changes)}};function kn(t,e){return e.left>t?e.left-t:Math.max(0,t-e.right)}function Sn(t,e){return e.top>t?e.top-t:Math.max(0,t-e.bottom)}function xn(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function $n(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function Pn(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function Zn(t,e,i){let n,s,r,o,a,l,h,c,f=!1;for(let d=t.firstChild;d;d=d.nextSibling){let t=Oe(d);for(let u=0;u<t.length;u++){let O=t[u];s&&xn(s,O)&&(O=$n(Pn(O,s.bottom),s.top));let p=kn(e,O),g=Sn(i,O);if(0==p&&0==g)return 3==d.nodeType?Cn(d,e,i):Zn(d,e,i);if(!n||o>g||o==g&&r>p){n=d,s=O,r=p,o=g;let a=g?i<O.top?-1:1:p?e<O.left?-1:1:0;f=!a||(a>0?u<t.length-1:u>0)}0==p?i>O.bottom&&(!h||h.bottom<O.bottom)?(a=d,h=O):i<O.top&&(!c||c.top>O.top)&&(l=d,c=O):h&&xn(h,O)?h=Pn(h,O.bottom):c&&xn(c,O)&&(c=$n(c,O.top))}}if(h&&h.bottom>=i?(n=a,s=h):c&&c.top<=i&&(n=l,s=c),!n)return{node:t,offset:0};let u=Math.max(s.left,Math.min(s.right,e));return 3==n.nodeType?Cn(n,u,i):f&&"false"!=n.contentEditable?Zn(n,u,i):{node:t,offset:Array.prototype.indexOf.call(t.childNodes,n)+(e>=(s.left+s.right)/2?1:0)}}function Cn(t,e,i){let n=t.nodeValue.length,s=-1,r=1e9,o=0;for(let a=0;a<n;a++){let n=Pe(t,a,a+1).getClientRects();for(let l=0;l<n.length;l++){let h=n[l];if(h.top==h.bottom)continue;o||(o=e-h.left);let c=(h.top>i?h.top-i:i-h.bottom)-1;if(h.left-1<=e&&h.right+1>=e&&c<r){let i=e>=(h.left+h.right)/2,n=i;if(Fe.chrome||Fe.gecko){Pe(t,a).getBoundingClientRect().left==h.right&&(n=!i)}if(c<=0)return{node:t,offset:a+(n?1:0)};s=a+(n?1:0),r=c}}}return{node:t,offset:s>-1?s:o>0?t.nodeValue.length:0}}function Tn(t,e,i,n=-1){var s,r;let o,a=t.contentDOM.getBoundingClientRect(),l=a.top+t.viewState.paddingTop,{docHeight:h}=t.viewState,{x:c,y:f}=e,u=f-l;if(u<0)return 0;if(u>h)return t.state.doc.length;for(let b=t.viewState.heightOracle.textHeight/2,y=!1;o=t.elementAtHeight(u),o.type!=di.Text;)for(;u=n>0?o.bottom+b:o.top-b,!(u>=0&&u<=h);){if(y)return i?null:0;y=!0,n=-n}f=l+u;let d=o.from;if(d<t.viewport.from)return 0==t.viewport.from?0:i?null:An(t,a,o,c,f);if(d>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:i?null:An(t,a,o,c,f);let O=t.dom.ownerDocument,p=t.root.elementFromPoint?t.root:O,g=p.elementFromPoint(c,f);g&&!t.contentDOM.contains(g)&&(g=null),g||(c=Math.max(a.left+1,Math.min(a.right-1,c)),g=p.elementFromPoint(c,f),g&&!t.contentDOM.contains(g)&&(g=null));let m,w=-1;if(g&&0!=(null===(s=t.docView.nearest(g))||void 0===s?void 0:s.isEditable))if(O.caretPositionFromPoint){let t=O.caretPositionFromPoint(c,f);t&&({offsetNode:m,offset:w}=t)}else if(O.caretRangeFromPoint){let e=O.caretRangeFromPoint(c,f);e&&(({startContainer:m,startOffset:w}=e),(!t.contentDOM.contains(m)||Fe.safari&&function(t,e,i){let n;if(3!=t.nodeType||e!=(n=t.nodeValue.length))return!1;for(let s=t.nextSibling;s;s=s.nextSibling)if(1!=s.nodeType||"BR"!=s.nodeName)return!1;return Pe(t,n-1,n).getBoundingClientRect().left>i}(m,w,c)||Fe.chrome&&function(t,e,i){if(0!=e)return!1;for(let s=t;;){let t=s.parentNode;if(!t||1!=t.nodeType||t.firstChild!=s)return!1;if(t.classList.contains("cm-line"))break;s=t}let n=1==t.nodeType?t.getBoundingClientRect():Pe(t,0,Math.max(t.nodeValue.length,1)).getBoundingClientRect();return i-n.left>5}(m,w,c))&&(m=void 0))}if(!m||!t.docView.dom.contains(m)){let e=ci.find(t.docView,d);if(!e)return u>o.top+o.height/2?o.to:o.from;({node:m,offset:w}=Zn(e.dom,c,f))}let v=t.docView.nearest(m);if(!v)return null;if(v.isWidget&&1==(null===(r=v.dom)||void 0===r?void 0:r.nodeType)){let t=v.dom.getBoundingClientRect();return e.y<t.top||e.y<=t.bottom&&e.x<=(t.left+t.right)/2?v.posAtStart:v.posAtEnd}return v.localPosFromDOM(m,w)+v.posAtStart}function An(t,e,i,n,s){let r=Math.round((n-e.left)*t.defaultCharacterWidth);if(t.lineWrapping&&i.height>1.5*t.defaultLineHeight){let e=t.viewState.heightOracle.textHeight;r+=Math.floor((s-i.top-.5*(t.defaultLineHeight-e))/e)*t.viewState.heightOracle.lineLength}let o=t.state.sliceDoc(i.from,i.to);return i.from+Kt(o,r,t.state.tabSize)}function Xn(t,e){let i=t.lineBlockAt(e);if(Array.isArray(i.type))for(let n of i.type)if(n.to>e||n.to==e&&(n.to==i.to||n.type==di.Text))return n;return i}function Wn(t,e,i,n){let s=t.state.doc.lineAt(e.head),r=t.bidiSpans(s),o=t.textDirectionAt(s.from);for(let a=e,l=null;;){let e=ji(s,r,o,a,i),h=Di;if(!e){if(s.number==(i?t.state.doc.lines:1))return a;h="\n",s=t.state.doc.line(s.number+(i?1:-1)),r=t.bidiSpans(s),e=t.visualLineSide(s,!i)}if(l){if(!l(h))return a}else{if(!n)return e;l=n(h)}a=e}}function Rn(t,e,i){for(;;){let n=0;for(let s of t)s.between(e-1,e+1,((t,s,r)=>{if(e>t&&e<s){let r=n||i||(e-t<s-e?-1:1);e=r<0?t:s,n=r}}));if(!n)return e}}function Yn(t,e,i){let n=Rn(t.state.facet(fn).map((e=>e(t))),i.from,e.head>i.from?-1:1);return n==i.from?i:V.cursor(n,n<i.from?1:-1)}class qn{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){var e;this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,Fe.safari&&t.contentDOM.addEventListener("input",(()=>null)),Fe.gecko&&(e=t.contentDOM.ownerDocument,ls.has(e)||(ls.add(e),e.addEventListener("copy",(()=>{})),e.addEventListener("cut",(()=>{}))))}handleEvent(t){(function(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let i,n=e.target;n!=t.contentDOM;n=n.parentNode)if(!n||11==n.nodeType||(i=Ye.get(n))&&i.ignoreEvent(e))return!1;return!0})(this.view,t)&&!this.ignoreDuringComposition(t)&&("keydown"==t.type&&this.keydown(t)||this.runHandlers(t.type,t))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let t of i.observers)t(this.view,e);for(let t of i.handlers){if(e.defaultPrevented)break;if(t(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=function(t){let e=Object.create(null);function i(t){return e[t]||(e[t]={observers:[],handlers:[]})}for(let n of t){let t=n.spec;if(t&&t.domEventHandlers)for(let e in t.domEventHandlers){let s=t.domEventHandlers[e];s&&i(e).handlers.push(Mn(n.value,s))}if(t&&t.domEventObservers)for(let e in t.domEventObservers){let s=t.domEventObservers[e];s&&i(e).observers.push(Mn(n.value,s))}}for(let n in Bn)i(n).handlers.push(Bn[n]);for(let n in zn)i(n).observers.push(zn[n]);return e}(t),i=this.handlers,n=this.view.contentDOM;for(let s in e)if("scroll"!=s){let t=!e[s].handlers.length,r=i[s];r&&t!=!r.handlers.length&&(n.removeEventListener(s,this.handleEvent),r=null),r||n.addEventListener(s,this.handleEvent,{passive:t})}for(let s in i)"scroll"==s||e[s]||n.removeEventListener(s,this.handleEvent);this.handlers=e}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),9==t.keyCode&&Date.now()<this.lastEscPress+2e3)return!0;if(27!=t.keyCode&&jn.indexOf(t.keyCode)<0&&(this.view.inputState.lastEscPress=0),Fe.android&&Fe.chrome&&!t.synthetic&&(13==t.keyCode||8==t.keyCode))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return!Fe.ios||t.synthetic||t.altKey||t.metaKey||!((e=En.find((e=>e.keyCode==t.keyCode)))&&!t.ctrlKey||Dn.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(229!=t.keyCode&&this.view.observer.forceFlush(),!1):(this.pendingIOSKey=e||t,setTimeout((()=>this.flushIOSKey()),250),!0)}flushIOSKey(t){let e=this.pendingIOSKey;return!!e&&(!("Enter"==e.key&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString()))&&(this.pendingIOSKey=void 0,Ze(this.view.contentDOM,e.key,e.keyCode,e instanceof KeyboardEvent?e:void 0)))}ignoreDuringComposition(t){return!!/^key/.test(t.type)&&(this.composing>0||!!(Fe.safari&&!Fe.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100)&&(this.compositionPendingKey=!1,!0))}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function Mn(t,e){return(i,n)=>{try{return e.call(t,n,i)}catch(tO){tn(i.state,tO)}}}const En=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Dn="dthko",jn=[16,17,18,20,91,92,224,225];function _n(t){return.7*Math.max(0,t)+8}class Vn{constructor(t,e,i,n){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=n,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParent=function(t){let e=t.ownerDocument;for(let i=t.parentNode;i&&i!=e.body;)if(1==i.nodeType){if(i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)return i;i=i.assignedSlot||i.parentNode}else{if(11!=i.nodeType)break;i=i.host}return null}(t.contentDOM),this.atoms=t.state.facet(fn).map((e=>e(t)));let s=t.contentDOM.ownerDocument;s.addEventListener("mousemove",this.move=this.move.bind(this)),s.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(Wt.allowMultipleSelections)&&function(t,e){let i=t.state.facet(Vi);return i.length?i[0](e):Fe.mac?e.metaKey:e.ctrlKey}(t,e),this.dragging=!(!function(t,e){let{main:i}=t.state.selection;if(i.empty)return!1;let n=fe(t.root);if(!n||0==n.rangeCount)return!0;let s=n.getRangeAt(0).getClientRects();for(let r=0;r<s.length;r++){let t=s[r];if(t.left<=e.clientX&&t.right>=e.clientX&&t.top<=e.clientY&&t.bottom>=e.clientY)return!0}return!1}(t,e)||1!=is(e))&&null}start(t){!1===this.dragging&&this.select(t)}move(t){var e,i,n;if(0==t.buttons)return this.destroy();if(this.dragging||null==this.dragging&&(i=this.startEvent,n=t,Math.max(Math.abs(i.clientX-n.clientX),Math.abs(i.clientY-n.clientY))<10))return;this.select(this.lastEvent=t);let s=0,r=0,o=(null===(e=this.scrollParent)||void 0===e?void 0:e.getBoundingClientRect())||{left:0,top:0,right:this.view.win.innerWidth,bottom:this.view.win.innerHeight},a=pn(this.view);t.clientX-a.left<=o.left+6?s=-_n(o.left-t.clientX):t.clientX+a.right>=o.right-6&&(s=_n(t.clientX-o.right)),t.clientY-a.top<=o.top+6?r=-_n(o.top-t.clientY):t.clientY+a.bottom>=o.bottom-6&&(r=_n(t.clientY-o.bottom)),this.setScrollSpeed(s,r)}up(t){null==this.dragging&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval((()=>this.scroll()),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){this.scrollParent?(this.scrollParent.scrollLeft+=this.scrollSpeed.x,this.scrollParent.scrollTop+=this.scrollSpeed.y):this.view.win.scrollBy(this.scrollSpeed.x,this.scrollSpeed.y),!1===this.dragging&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let n=t.ranges[i],s=null;if(n.empty){let t=Rn(this.atoms,n.from,0);t!=n.from&&(s=V.cursor(t,-1))}else{let t=Rn(this.atoms,n.from,-1),e=Rn(this.atoms,n.to,1);t==n.from&&e==n.to||(s=V.range(n.from==n.anchor?t:e,n.from==n.head?t:e))}s&&(e||(e=t.ranges.slice()),e[i]=s)}return e?V.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));!this.mustSelect&&i.eq(e.state.selection,!1===this.dragging)||this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){this.style.update(t)&&setTimeout((()=>this.select(this.lastEvent)),20)}}const Bn=Object.create(null),zn=Object.create(null),Gn=Fe.ie&&Fe.ie_version<15||Fe.ios&&Fe.webkit_version<604;function In(t,e){let i,{state:n}=t,s=1,r=n.toText(e),o=r.lines==n.selection.ranges.length;if(null!=ss&&n.selection.ranges.every((t=>t.empty))&&ss==r.toString()){let t=-1;i=n.changeByRange((i=>{let a=n.doc.lineAt(i.from);if(a.from==t)return{range:i};t=a.from;let l=n.toText((o?r.line(s++).text:e)+n.lineBreak);return{changes:{from:a.from,insert:l},range:V.cursor(i.from+l.length)}}))}else i=o?n.changeByRange((t=>{let e=r.line(s++);return{changes:{from:t.from,to:t.to,insert:e.text},range:V.cursor(t.from+e.length)}})):n.replaceSelection(r);t.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}function Ln(t,e,i,n){if(1==n)return V.cursor(e,i);if(2==n)return function(t,e,i=1){let n=t.charCategorizer(e),s=t.doc.lineAt(e),r=e-s.from;if(0==s.length)return V.cursor(e);0==r?i=1:r==s.length&&(i=-1);let o=r,a=r;i<0?o=k(s.text,r,!1):a=k(s.text,r);let l=n(s.text.slice(o,a));for(;o>0;){let t=k(s.text,o,!1);if(n(s.text.slice(t,o))!=l)break;o=t}for(;a<s.length;){let t=k(s.text,a);if(n(s.text.slice(a,t))!=l)break;a=t}return V.range(o+s.from,a+s.from)}(t.state,e,i);{let i=ci.find(t.docView,e),n=t.state.doc.lineAt(i?i.posAtEnd:e),s=i?i.posAtStart:n.from,r=i?i.posAtEnd:n.to;return r<t.state.doc.length&&r==n.to&&r++,V.range(s,r)}}zn.scroll=t=>{t.inputState.lastScrollTop=t.scrollDOM.scrollTop,t.inputState.lastScrollLeft=t.scrollDOM.scrollLeft},Bn.keydown=(t,e)=>(t.inputState.setSelectionOrigin("select"),27==e.keyCode&&(t.inputState.lastEscPress=Date.now()),!1),zn.touchstart=(t,e)=>{t.inputState.lastTouchTime=Date.now(),t.inputState.setSelectionOrigin("select.pointer")},zn.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")},Bn.mousedown=(t,e)=>{if(t.observer.flush(),t.inputState.lastTouchTime>Date.now()-2e3)return!1;let i=null;for(let n of t.state.facet(zi))if(i=n(t,e),i)break;if(i||0!=e.button||(i=function(t,e){let i=Fn(t,e),n=is(e),s=t.state.selection;return{update(t){t.docChanged&&(i.pos=t.changes.mapPos(i.pos),s=s.map(t.changes))},get(e,r,o){let a,l=Fn(t,e),h=Ln(t,l.pos,l.bias,n);if(i.pos!=l.pos&&!r){let e=Ln(t,i.pos,i.bias,n),s=Math.min(e.from,h.from),r=Math.max(e.to,h.to);h=s<h.from?V.range(s,r):V.range(r,s)}return r?s.replaceRange(s.main.extend(h.from,h.to)):o&&1==n&&s.ranges.length>1&&(a=function(t,e){for(let i=0;i<t.ranges.length;i++){let{from:n,to:s}=t.ranges[i];if(n<=e&&s>=e)return V.create(t.ranges.slice(0,i).concat(t.ranges.slice(i+1)),t.mainIndex==i?0:t.mainIndex-(t.mainIndex>i?1:0))}return null}(s,l.pos))?a:o?s.addRange(h):V.create([h])}}}(t,e)),i){let n=!t.hasFocus;t.inputState.startMouseSelection(new Vn(t,e,i,n)),n&&t.observer.ignore((()=>$e(t.contentDOM)));let s=t.inputState.mouseSelection;if(s)return s.start(e),!1===s.dragging}return!1};let Nn=(t,e)=>t>=e.top&&t<=e.bottom,Un=(t,e,i)=>Nn(e,i)&&t>=i.left&&t<=i.right;function Hn(t,e,i,n){let s=ci.find(t.docView,e);if(!s)return 1;let r=e-s.posAtStart;if(0==r)return 1;if(r==s.length)return-1;let o=s.coordsAt(r,-1);if(o&&Un(i,n,o))return-1;let a=s.coordsAt(r,1);return a&&Un(i,n,a)?1:o&&Nn(n,o)?-1:1}function Fn(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:i,bias:Hn(t,i,e.clientX,e.clientY)}}const Kn=Fe.ie&&Fe.ie_version<=11;let Jn=null,ts=0,es=0;function is(t){if(!Kn)return t.detail;let e=Jn,i=es;return Jn=t,es=Date.now(),ts=!e||i>Date.now()-400&&Math.abs(e.clientX-t.clientX)<2&&Math.abs(e.clientY-t.clientY)<2?(ts+1)%3:1}function ns(t,e,i,n){if(!i)return;let s=t.posAtCoords({x:e.clientX,y:e.clientY},!1),{draggedContent:r}=t.inputState,o=n&&r&&function(t,e){let i=t.state.facet(Bi);return i.length?i[0](e):Fe.mac?!e.altKey:!e.ctrlKey}(t,e)?{from:r.from,to:r.to}:null,a={from:s,insert:i},l=t.state.changes(o?[o,a]:a);t.focus(),t.dispatch({changes:l,selection:{anchor:l.mapPos(s,-1),head:l.mapPos(s,1)},userEvent:o?"move.drop":"input.drop"}),t.inputState.draggedContent=null}Bn.dragstart=(t,e)=>{let{selection:{main:i}}=t.state;if(e.target.draggable){let n=t.docView.nearest(e.target);if(n&&n.isWidget){let t=n.posAtStart,e=t+n.length;(t>=i.to||e<=i.from)&&(i=V.range(t,e))}}let{inputState:n}=t;return n.mouseSelection&&(n.mouseSelection.dragging=!0),n.draggedContent=i,e.dataTransfer&&(e.dataTransfer.setData("Text",t.state.sliceDoc(i.from,i.to)),e.dataTransfer.effectAllowed="copyMove"),!1},Bn.dragend=t=>(t.inputState.draggedContent=null,!1),Bn.drop=(t,e)=>{if(!e.dataTransfer)return!1;if(t.state.readOnly)return!0;let i=e.dataTransfer.files;if(i&&i.length){let n=Array(i.length),s=0,r=()=>{++s==i.length&&ns(t,e,n.filter((t=>null!=t)).join(t.state.lineBreak),!1)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=r,e.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(e.result)||(n[t]=e.result),r()},e.readAsText(i[t])}return!0}{let i=e.dataTransfer.getData("Text");if(i)return ns(t,e,i,!0),!0}return!1},Bn.paste=(t,e)=>{if(t.state.readOnly)return!0;t.observer.flush();let i=Gn?null:e.clipboardData;return i?(In(t,i.getData("text/plain")||i.getData("text/uri-list")),!0):(function(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout((()=>{t.focus(),i.remove(),In(t,i.value)}),50)}(t),!1)};let ss=null;Bn.copy=Bn.cut=(t,e)=>{let{text:i,ranges:n,linewise:s}=function(t){let e=[],i=[],n=!1;for(let s of t.selection.ranges)s.empty||(e.push(t.sliceDoc(s.from,s.to)),i.push(s));if(!e.length){let s=-1;for(let{from:n}of t.selection.ranges){let r=t.doc.lineAt(n);r.number>s&&(e.push(r.text),i.push({from:r.from,to:Math.min(t.doc.length,r.to+1)})),s=r.number}n=!0}return{text:e.join(t.lineBreak),ranges:i,linewise:n}}(t.state);if(!i&&!s)return!1;ss=s?i:null,"cut"!=e.type||t.state.readOnly||t.dispatch({changes:n,scrollIntoView:!0,userEvent:"delete.cut"});let r=Gn?null:e.clipboardData;return r?(r.clearData(),r.setData("text/plain",i),!0):(function(t,e){let i=t.dom.parentNode;if(!i)return;let n=i.appendChild(document.createElement("textarea"));n.style.cssText="position: fixed; left: -10000px; top: 10px",n.value=e,n.focus(),n.selectionEnd=e.length,n.selectionStart=0,setTimeout((()=>{n.remove(),t.focus()}),50)}(t,i),!1)};const rs=wt.define();function os(t,e){let i=[];for(let n of t.facet(Ni)){let s=n(t,e);s&&i.push(s)}return i?t.update({effects:i,annotations:rs.of(!0)}):null}function as(t){setTimeout((()=>{let e=t.hasFocus;if(e!=t.inputState.notifiedFocused){let i=os(t.state,e);i?t.dispatch(i):t.update([])}}),10)}zn.focus=t=>{t.inputState.lastFocusTime=Date.now(),t.scrollDOM.scrollTop||!t.inputState.lastScrollTop&&!t.inputState.lastScrollLeft||(t.scrollDOM.scrollTop=t.inputState.lastScrollTop,t.scrollDOM.scrollLeft=t.inputState.lastScrollLeft),as(t)},zn.blur=t=>{t.observer.clearSelectionRange(),as(t)},zn.compositionstart=zn.compositionupdate=t=>{null==t.inputState.compositionFirstChange&&(t.inputState.compositionFirstChange=!0),t.inputState.composing<0&&(t.inputState.composing=0)},zn.compositionend=t=>{t.inputState.composing=-1,t.inputState.compositionEndedAt=Date.now(),t.inputState.compositionPendingKey=!0,t.inputState.compositionPendingChange=t.observer.pendingRecords().length>0,t.inputState.compositionFirstChange=null,Fe.chrome&&Fe.android?t.observer.flushSoon():t.inputState.compositionPendingChange?Promise.resolve().then((()=>t.observer.flush())):setTimeout((()=>{t.inputState.composing<0&&t.docView.hasComposition&&t.update([])}),50)},zn.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()},Bn.beforeinput=(t,e)=>{var i;let n;if(Fe.chrome&&Fe.android&&(n=En.find((t=>t.inputType==e.inputType)))&&(t.observer.delayAndroidKey(n.key,n.keyCode),"Backspace"==n.key||"Delete"==n.key)){let e=(null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0;setTimeout((()=>{var i;((null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0)>e+10&&t.hasFocus&&(t.contentDOM.blur(),t.focus())}),100)}return Fe.ios&&"deleteContentForward"==e.inputType&&t.observer.flushSoon(),Fe.safari&&"insertText"==e.inputType&&t.inputState.composing>=0&&setTimeout((()=>zn.compositionend(t,e)),20),!1};const ls=new Set;const hs=["pre-wrap","normal","pre-line","break-spaces"];class cs{constructor(t){this.lineWrapping=t,this.doc=h.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30,this.heightChanged=!1}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){if(!this.lineWrapping)return this.lineHeight;return(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return hs.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(10*n)]||(e=!0,this.heightSamples[Math.floor(10*n)]=!0)}return e}refresh(t,e,i,n,s,r){let o=hs.indexOf(t)>-1,a=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=o;if(this.lineWrapping=o,this.lineHeight=e,this.charWidth=i,this.textHeight=n,this.lineLength=s,a){this.heightSamples={};for(let t=0;t<r.length;t++){let e=r[t];e<0?t++:this.heightSamples[Math.floor(10*e)]=!0}}return a}}class fs{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class us{constructor(t,e,i,n,s){this.from=t,this.length=e,this.top=i,this.height=n,this._content=s}get type(){return"number"==typeof this._content?di.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof mi?this._content.widget:null}get widgetLineBreaks(){return"number"==typeof this._content?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new us(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var ds=function(t){return t[t.ByPos=0]="ByPos",t[t.ByHeight=1]="ByHeight",t[t.ByPosNoHeight=2]="ByPosNoHeight",t}(ds||(ds={}));const Os=.001;class ps{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(t){this.flags=(t?2:0)|-3&this.flags}setHeight(t,e){this.height!=e&&(Math.abs(this.height-e)>Os&&(t.heightChanged=!0),this.height=e)}replace(t,e,i){return ps.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,n){let s=this,r=i.doc;for(let o=n.length-1;o>=0;o--){let{fromA:a,toA:l,fromB:h,toB:c}=n[o],f=s.lineAt(a,ds.ByPosNoHeight,i.setDoc(e),0,0),u=f.to>=l?f:s.lineAt(l,ds.ByPosNoHeight,i,0,0);for(c+=u.to-l,l=u.to;o>0&&f.from<=n[o-1].toA;)a=n[o-1].fromA,h=n[o-1].fromB,o--,a<f.from&&(f=s.lineAt(a,ds.ByPosNoHeight,i,0,0));h+=f.from-a,a=f.from;let d=ys.build(i.setDoc(r),t,h,c);s=s.replace(a,l,d)}return s.updateHeight(i,0)}static empty(){return new ms(0,0)}static of(t){if(1==t.length)return t[0];let e=0,i=t.length,n=0,s=0;for(;;)if(e==i)if(n>2*s){let s=t[e-1];s.break?t.splice(--e,1,s.left,null,s.right):t.splice(--e,1,s.left,s.right),i+=1+s.break,n-=s.size}else{if(!(s>2*n))break;{let e=t[i];e.break?t.splice(i,1,e.left,null,e.right):t.splice(i,1,e.left,e.right),i+=2+e.break,s-=e.size}}else if(n<s){let i=t[e++];i&&(n+=i.size)}else{let e=t[--i];e&&(s+=e.size)}let r=0;return null==t[e-1]?(r=1,e--):null==t[e]&&(r=1,i++),new vs(ps.of(t.slice(0,e)),r,ps.of(t.slice(i)))}}ps.prototype.size=1;class gs extends ps{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,n){return new us(n,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,n,s){return this.blockAt(0,i,n,s)}forEachLine(t,e,i,n,s,r){t<=s+this.length&&e>=s&&r(this.blockAt(0,i,n,s))}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(t,n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class ms extends gs{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,n){return new us(n,this.length,i,this.height,this.breaks)}replace(t,e,i){let n=i[0];return 1==i.length&&(n instanceof ms||n instanceof ws&&4&n.flags)&&Math.abs(this.length-n.length)<10?(n instanceof ws?n=new ms(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):ps.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(t,n.heights[n.index++]):(i||this.outdated)&&this.setHeight(t,Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class ws extends ps{constructor(t){super(t,0)}heightMetrics(t,e){let i,n=t.doc.lineAt(e).number,s=t.doc.lineAt(e+this.length).number,r=s-n+1,o=0;if(t.lineWrapping){let e=Math.min(this.height,t.lineHeight*r);i=e/r,this.length>r+1&&(o=(this.height-e)/(this.length-r-1))}else i=this.height/r;return{firstLine:n,lastLine:s,perLine:i,perChar:o}}blockAt(t,e,i,n){let{firstLine:s,lastLine:r,perLine:o,perChar:a}=this.heightMetrics(e,n);if(e.lineWrapping){let s=n+(t<e.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length)),r=e.doc.lineAt(s),l=o+r.length*a,h=Math.max(i,t-l/2);return new us(r.from,r.length,h,l,0)}{let n=Math.max(0,Math.min(r-s,Math.floor((t-i)/o))),{from:a,length:l}=e.doc.line(s+n);return new us(a,l,i+o*n,o,0)}}lineAt(t,e,i,n,s){if(e==ds.ByHeight)return this.blockAt(t,i,n,s);if(e==ds.ByPosNoHeight){let{from:e,to:n}=i.doc.lineAt(t);return new us(e,n-e,0,0,0)}let{firstLine:r,perLine:o,perChar:a}=this.heightMetrics(i,s),l=i.doc.lineAt(t),h=o+l.length*a,c=l.number-r,f=n+o*c+a*(l.from-s-c);return new us(l.from,l.length,Math.max(n,Math.min(f,n+this.height-h)),h,0)}forEachLine(t,e,i,n,s,r){t=Math.max(t,s),e=Math.min(e,s+this.length);let{firstLine:o,perLine:a,perChar:l}=this.heightMetrics(i,s);for(let h=t,c=n;h<=e;){let e=i.doc.lineAt(h);if(h==t){let i=e.number-o;c+=a*i+l*(t-s-i)}let n=a+l*e.length;r(new us(e.from,e.length,c,n,0)),c+=n,h=e.to+1}}replace(t,e,i){let n=this.length-e;if(n>0){let t=i[i.length-1];t instanceof ws?i[i.length-1]=new ws(t.length+n):i.push(null,new ws(n-1))}if(t>0){let e=i[0];e instanceof ws?i[0]=new ws(t+e.length):i.unshift(new ws(t-1),null)}return ps.of(i)}decomposeLeft(t,e){e.push(new ws(t-1),null)}decomposeRight(t,e){e.push(null,new ws(this.length-t-1))}updateHeight(t,e=0,i=!1,n){let s=e+this.length;if(n&&n.from<=e+this.length&&n.more){let i=[],r=Math.max(e,n.from),o=-1;for(n.from>e&&i.push(new ws(n.from-e-1).updateHeight(t,e));r<=s&&n.more;){let e=t.doc.lineAt(r).length;i.length&&i.push(null);let s=n.heights[n.index++];-1==o?o=s:Math.abs(s-o)>=Os&&(o=-2);let a=new ms(e,s);a.outdated=!1,i.push(a),r+=e+1}r<=s&&i.push(null,new ws(s-r).updateHeight(t,r));let a=ps.of(i);return(o<0||Math.abs(a.height-this.height)>=Os||Math.abs(o-this.heightMetrics(t,e).perLine)>=Os)&&(t.heightChanged=!0),a}return(i||this.outdated)&&(this.setHeight(t,t.heightForGap(e,e+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class vs extends ps{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return 1&this.flags}blockAt(t,e,i,n){let s=i+this.left.height;return t<s?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,s,n+this.left.length+this.break)}lineAt(t,e,i,n,s){let r=n+this.left.height,o=s+this.left.length+this.break,a=e==ds.ByHeight?t<r:t<o,l=a?this.left.lineAt(t,e,i,n,s):this.right.lineAt(t,e,i,r,o);if(this.break||(a?l.to<o:l.from>o))return l;let h=e==ds.ByPosNoHeight?ds.ByPosNoHeight:ds.ByPos;return a?l.join(this.right.lineAt(o,h,i,r,o)):this.left.lineAt(o,h,i,n,s).join(l)}forEachLine(t,e,i,n,s,r){let o=n+this.left.height,a=s+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,s,r),e>=a&&this.right.forEachLine(t,e,i,o,a,r);else{let l=this.lineAt(a,ds.ByPos,i,n,s);t<l.from&&this.left.forEachLine(t,l.from-1,i,n,s,r),l.to>=t&&l.from<=e&&r(l),e>l.to&&this.right.forEachLine(l.to+1,e,i,o,a,r)}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let s=[];t>0&&this.decomposeLeft(t,s);let r=s.length;for(let o of i)s.push(o);if(t>0&&bs(s,r-1),e<this.length){let t=s.length;this.decomposeRight(e,s),bs(s,t)}return ps.of(s)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?ps.of(this.break?[t,null,e]:[t,e]):(this.left=t,this.right=e,this.height=t.height+e.height,this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:s,right:r}=this,o=e+s.length+this.break,a=null;return n&&n.from<=e+s.length&&n.more?a=s=s.updateHeight(t,e,i,n):s.updateHeight(t,e,i),n&&n.from<=o+r.length&&n.more?a=r=r.updateHeight(t,o,i,n):r.updateHeight(t,o,i),a?this.balanced(s,r):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function bs(t,e){let i,n;null==t[e]&&(i=t[e-1])instanceof ws&&(n=t[e+1])instanceof ws&&t.splice(e-1,3,new ws(i.length+1+n.length))}class ys{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof ms?i.length+=t-this.pos:(t>this.pos||!this.isCovered)&&this.nodes.push(new ms(t-this.pos,-1)),this.writtenTo=t,e>t&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0,s=i.widget?i.widget.lineBreaks:0;n<0&&(n=this.oracle.lineHeight);let r=e-t;i.block?this.addBlock(new gs(r,n,i)):(r||s||n>=5)&&this.addLineDeco(n,s,r)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new ms(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new ws(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof ms)return t;let e=new ms(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let n=this.ensureLine();n.length+=i,n.collapsed+=i,n.widgetHeight=Math.max(n.widgetHeight,t),n.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||e instanceof ms||this.isCovered?(this.writtenTo<this.pos||null==e)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new ms(0,-1));let i=t;for(let n of this.nodes)n instanceof ms&&n.updateHeight(this.oracle,i),i+=n?n.length:1;return this.nodes}static build(t,e,i,n){let s=new ys(i,t);return Dt.spans(e,i,n,s,0),s.finish(i)}}class Qs{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&vi(t,e,this.changes,5)}}function ks(t,e){let i=t.getBoundingClientRect(),n=t.ownerDocument,s=n.defaultView||window,r=Math.max(0,i.left),o=Math.min(s.innerWidth,i.right),a=Math.max(0,i.top),l=Math.min(s.innerHeight,i.bottom);for(let h=t.parentNode;h&&h!=n.body;)if(1==h.nodeType){let e=h,i=window.getComputedStyle(e);if((e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth)&&"visible"!=i.overflow){let i=e.getBoundingClientRect();r=Math.max(r,i.left),o=Math.min(o,i.right),a=Math.max(a,i.top),l=h==t.parentNode?i.bottom:Math.min(l,i.bottom)}h="absolute"==i.position||"fixed"==i.position?e.offsetParent:e.parentNode}else{if(11!=h.nodeType)break;h=h.host}return{left:r-i.left,right:Math.max(r,o)-i.left,top:a-(i.top+e),bottom:Math.max(a,l)-(i.top+e)}}function Ss(t,e){let i=t.getBoundingClientRect();return{left:0,right:i.right-i.left,top:e,bottom:i.bottom-(i.top+e)}}class xs{constructor(t,e,i){this.from=t,this.to=e,this.size=i}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],s=e[i];if(n.from!=s.from||n.to!=s.to||n.size!=s.size)return!1}return!0}draw(t,e){return Oi.replace({widget:new $s(this.size*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class $s extends ui{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class Ps{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!0,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=Xs,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=ki.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(ln).some((t=>"function"!=typeof t&&"cm-lineWrapping"==t.class));this.heightOracle=new cs(e),this.stateDeco=t.facet(hn).filter((t=>"function"!=typeof t)),this.heightMap=ps.empty().applyChanges(this.stateDeco,h.empty,this.heightOracle.setDoc(t.doc),[new mn(0,0,0,t.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=Oi.set(this.lineGaps.map((t=>t.draw(this,!1)))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some((({from:t,to:e})=>n>=t&&n<=e))){let{from:e,to:i}=this.lineBlockAt(n);t.push(new Zs(e,i))}}this.viewports=t.sort(((t,e)=>t.from-e.from)),this.scaler=this.heightMap.height<=7e6?Xs:new Ws(this.heightOracle,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,(t=>{this.viewportLines.push(1==this.scaler.scale?t:Rs(t,this.scaler))}))}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(hn).filter((t=>"function"!=typeof t));let n=t.changedRanges,s=mn.extendWithRanges(n,function(t,e,i){let n=new Qs;return Dt.compare(t,e,i,n,0),n.changes}(i,this.stateDeco,t?t.changes:R.empty(this.state.doc.length))),r=this.heightMap.height,o=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),s),this.heightMap.height!=r&&(t.flags|=2),o?(this.scrollAnchorPos=t.changes.mapPos(o.from,-1),this.scrollAnchorHeight=o.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let a=s.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let l=!t.changes.empty||2&t.flags||a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,this.updateForViewport(),l&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(Hi)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,s=i.whiteSpace;this.defaultTextDirection="rtl"==i.direction?ki.RTL:ki.LTR;let r=this.heightOracle.mustRefreshForWrapping(s),o=e.getBoundingClientRect(),a=r||this.mustMeasureContent||this.contentDOMHeight!=o.height;this.contentDOMHeight=o.height,this.mustMeasureContent=!1;let l=0,c=0;if(o.width&&o.height){let{scaleX:t,scaleY:i}=Qe(e,o);(t>.005&&Math.abs(this.scaleX-t)>.005||i>.005&&Math.abs(this.scaleY-i)>.005)&&(this.scaleX=t,this.scaleY=i,l|=8,r=a=!0)}let f=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;this.paddingTop==f&&this.paddingBottom==u||(this.paddingTop=f,this.paddingBottom=u,l|=10),this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,l|=8);let d=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=d&&(this.scrollAnchorHeight=-1,this.scrollTop=d),this.scrolledToBottom=Te(t.scrollDOM);let O=(this.printing?Ss:ks)(e,this.paddingTop),p=O.top-this.pixelViewport.top,g=O.bottom-this.pixelViewport.bottom;this.pixelViewport=O;let m=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(m!=this.inView&&(this.inView=m,m&&(a=!0)),!this.inView&&!this.scrollTarget)return 0;let w=o.width;if(this.contentDOMWidth==w&&this.editorHeight==t.scrollDOM.clientHeight||(this.contentDOMWidth=o.width,this.editorHeight=t.scrollDOM.clientHeight,l|=8),a){let e=t.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(e)&&(r=!0),r||n.lineWrapping&&Math.abs(w-this.contentDOMWidth)>n.charWidth){let{lineHeight:i,charWidth:o,textHeight:a}=t.docView.measureTextSize();r=i>0&&n.refresh(s,i,o,a,w/o,e),r&&(t.docView.minWidth=0,l|=8)}p>0&&g>0?c=Math.max(p,g):p<0&&g<0&&(c=Math.min(p,g)),n.heightChanged=!1;for(let i of this.viewports){let s=i.from==this.viewport.from?e:t.docView.measureVisibleLineHeights(i);this.heightMap=(r?ps.empty().applyChanges(this.stateDeco,h.empty,this.heightOracle,[new mn(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(n,0,r,new fs(i.from,s))}n.heightChanged&&(l|=2)}let v=!this.viewportIsAppropriate(this.viewport,c)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return v&&(this.viewport=this.getViewport(c,this.scrollTarget)),this.updateForViewport(),(2&l||v)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(r?[]:this.lineGaps,t)),l|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),l}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,s=this.heightOracle,{visibleTop:r,visibleBottom:o}=this,a=new Zs(n.lineAt(r-1e3*i,ds.ByHeight,s,0,0).from,n.lineAt(o+1e3*(1-i),ds.ByHeight,s,0,0).to);if(e){let{head:t}=e.range;if(t<a.from||t>a.to){let i,r=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),o=n.lineAt(t,ds.ByPos,s,0,0);i="center"==e.y?(o.top+o.bottom)/2-r/2:"start"==e.y||"nearest"==e.y&&t<a.from?o.top:o.bottom-r,a=new Zs(n.lineAt(i-500,ds.ByHeight,s,0,0).from,n.lineAt(i+r+500,ds.ByHeight,s,0,0).to)}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new Zs(this.heightMap.lineAt(i,ds.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(n,ds.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(t,ds.ByPos,this.heightOracle,0,0),{bottom:s}=this.heightMap.lineAt(e,ds.ByPos,this.heightOracle,0,0),{visibleTop:r,visibleBottom:o}=this;return(0==t||n<=r-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||s>=o+Math.max(10,Math.min(i,250)))&&n>r-2e3&&s<o+2e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new xs(e.mapPos(n.from),e.mapPos(n.to),n.size));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,n=i?1e4:2e3,s=n>>1,r=n<<1;if(this.defaultTextDirection!=ki.LTR&&!i)return[];let o=[],a=(n,r,l,h)=>{if(r-n<s)return;let c=this.state.selection.main,f=[c.from];c.empty||f.push(c.to);for(let t of f)if(t>n&&t<r)return a(n,t-10,l,h),void a(t+10,r,l,h);let u=function(t,e){for(let i of t)if(e(i))return i;return}(t,(t=>t.from>=l.from&&t.to<=l.to&&Math.abs(t.from-n)<s&&Math.abs(t.to-r)<s&&!f.some((e=>t.from<e&&t.to>e))));if(!u){if(r<l.to&&e&&i&&e.visibleRanges.some((t=>t.from<=r&&t.to>=r))){let t=e.moveToLineBoundary(V.cursor(r),!1,!0).head;t>n&&(r=t)}u=new xs(n,r,this.gapSize(l,n,r,h))}o.push(u)};for(let l of this.viewportLines){if(l.length<r)continue;let t=Cs(l.from,l.to,this.stateDeco);if(t.total<r)continue;let e,s,o=this.scrollTarget?this.scrollTarget.range.head:null;if(i){let i,r,a=n/this.heightOracle.lineLength*this.heightOracle.lineHeight;if(null!=o){let e=As(t,o),n=((this.visibleBottom-this.visibleTop)/2+a)/l.height;i=e-n,r=e+n}else i=(this.visibleTop-l.top-a)/l.height,r=(this.visibleBottom-l.top+a)/l.height;e=Ts(t,i),s=Ts(t,r)}else{let i,r,a=t.total*this.heightOracle.charWidth,l=n*this.heightOracle.charWidth;if(null!=o){let e=As(t,o),n=((this.pixelViewport.right-this.pixelViewport.left)/2+l)/a;i=e-n,r=e+n}else i=(this.pixelViewport.left-l)/a,r=(this.pixelViewport.right+l)/a;e=Ts(t,i),s=Ts(t,r)}e>l.from&&a(l.from,e,l,t),s<l.to&&a(s,l.to,l,t)}return o}gapSize(t,e,i,n){let s=As(n,i)-As(n,e);return this.heightOracle.lineWrapping?t.height*s:n.total*this.heightOracle.charWidth*s}updateLineGaps(t){xs.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=Oi.set(t.map((t=>t.draw(this,this.heightOracle.lineWrapping)))))}computeVisibleRanges(){let t=this.stateDeco;this.lineGaps.length&&(t=t.concat(this.lineGapDeco));let e=[];Dt.spans(t,this.viewport.from,this.viewport.to,{span(t,i){e.push({from:t,to:i})},point(){}},20);let i=e.length!=this.visibleRanges.length||this.visibleRanges.some(((t,i)=>t.from!=e[i].from||t.to!=e[i].to));return this.visibleRanges=e,i?4:0}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find((e=>e.from<=t&&e.to>=t))||Rs(this.heightMap.lineAt(t,ds.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return Rs(this.heightMap.lineAt(this.scaler.fromDOM(t),ds.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return Rs(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class Zs{constructor(t,e){this.from=t,this.to=e}}function Cs(t,e,i){let n=[],s=t,r=0;return Dt.spans(i,t,e,{span(){},point(t,e){t>s&&(n.push({from:s,to:t}),r+=t-s),s=e}},20),s<e&&(n.push({from:s,to:e}),r+=e-s),{total:r,ranges:n}}function Ts({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let n=Math.floor(t*i);for(let s=0;;s++){let{from:t,to:i}=e[s],r=i-t;if(n<=r)return t+n;n-=r}}function As(t,e){let i=0;for(let{from:n,to:s}of t.ranges){if(e<=s){i+=e-n;break}i+=s-n}return i/t.total}const Xs={toDOM:t=>t,fromDOM:t=>t,scale:1};class Ws{constructor(t,e,i){let n=0,s=0,r=0;this.viewports=i.map((({from:i,to:s})=>{let r=e.lineAt(i,ds.ByPos,t,0,0).top,o=e.lineAt(s,ds.ByPos,t,0,0).bottom;return n+=o-r,{from:i,to:s,top:r,bottom:o,domTop:0,domBottom:0}})),this.scale=(7e6-n)/(e.height-n);for(let o of this.viewports)o.domTop=r+(o.top-s)*this.scale,r=o.domBottom=o.domTop+(o.bottom-o.top),s=o.bottom}toDOM(t){for(let e=0,i=0,n=0;;e++){let s=e<this.viewports.length?this.viewports[e]:null;if(!s||t<s.top)return n+(t-i)*this.scale;if(t<=s.bottom)return s.domTop+(t-s.top);i=s.bottom,n=s.domBottom}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let s=e<this.viewports.length?this.viewports[e]:null;if(!s||t<s.domTop)return i+(t-n)/this.scale;if(t<=s.domBottom)return s.top+(t-s.domTop);i=s.bottom,n=s.domBottom}}}function Rs(t,e){if(1==e.scale)return t;let i=e.toDOM(t.top),n=e.toDOM(t.bottom);return new us(t.from,t.length,i,n-i,Array.isArray(t._content)?t._content.map((t=>Rs(t,e))):t._content)}const Ys=G.define({combine:t=>t.join(" ")}),qs=G.define({combine:t=>t.indexOf(!0)>-1}),Ms=ie.newName(),Es=ie.newName(),Ds=ie.newName(),js={"&light":"."+Es,"&dark":"."+Ds};function _s(t,e,i){return new ie(e,{finish:e=>/&/.test(e)?e.replace(/&\w*/,(e=>{if("&"==e)return t;if(!i||!i[e])throw new RangeError(`Unsupported selector: ${e}`);return i[e]})):t+" "+e})}const Vs=_s("."+Ms,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace:before":{content:"attr(data-display)",position:"absolute",pointerEvents:"none",color:"#888"},".cm-highlightTab":{backgroundImage:'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>\')',backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},js),Bs="￿";class zs{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(Wt.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=Bs}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n);let t=this.text.length;this.readNode(n);let s=n.nextSibling;if(s==e)break;let r=Ye.get(n),o=Ye.get(s);(r&&o?r.breakAfter:(r?r.breakAfter:me(n))||me(s)&&("BR"!=n.nodeName||n.cmIgnore)&&this.text.length>t)&&this.lineBreak(),n=s}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let s,r=-1,o=1;if(this.lineSeparator?(r=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(s=n.exec(e))&&(r=s.index,o=s[0].length),this.append(e.slice(i,r<0?e.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let e of this.points)e.node==t&&e.pos>this.text.length&&(e.pos-=o-1);i=r+o}}readNode(t){if(t.cmIgnore)return;let e=Ye.get(t),i=e&&e.overrideDOMText;if(null!=i){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;)t.lineBreak?this.lineBreak():this.append(t.value)}else 3==t.nodeType?this.readTextNode(t):"BR"==t.nodeName?t.nextSibling&&this.lineBreak():1==t.nodeType&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(3==t.nodeType?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(Gs(t,i.node,i.offset)?e:0))}}function Gs(t,e,i){for(;;){if(!e||i<ve(e))return!1;if(e==t)return!0;i=ge(e)+1,e=e.parentNode}}class Is{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class Ls{constructor(t,e,i,n){this.typeOver=n,this.bounds=null,this.text="";let{impreciseHead:s,impreciseAnchor:r}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let e=s||r?[]:function(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:n,focusNode:s,focusOffset:r}=t.observer.selectionRange;i&&(e.push(new Is(i,n)),s==i&&r==n||e.push(new Is(s,r)));return e}(t),i=new zs(e,t.state);i.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=i.text,this.newSel=function(t,e){if(0==t.length)return null;let i=t[0].pos,n=2==t.length?t[1].pos:i;return i>-1&&n>-1?V.single(i+e,n+e):null}(e,this.bounds.from)}else{let e=t.observer.selectionRange,i=s&&s.node==e.focusNode&&s.offset==e.focusOffset||!ue(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset),n=r&&r.node==e.anchorNode&&r.offset==e.anchorOffset||!ue(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset),o=t.viewport;if((Fe.ios||Fe.chrome)&&t.state.selection.main.empty&&i!=n&&(o.from>0||o.to<t.state.doc.length)){let e=Math.min(i,n),s=Math.max(i,n),r=o.from-e,a=o.to-s;0!=r&&1!=r&&0!=e||0!=a&&-1!=a&&s!=t.state.doc.length||(i=0,n=t.state.doc.length)}this.newSel=V.single(n,i)}}}function Ns(t,e){let i,{newSel:n}=e,s=t.state.selection.main,r=t.inputState.lastKeyTime>Date.now()-100?t.inputState.lastKeyCode:-1;if(e.bounds){let{from:n,to:o}=e.bounds,a=s.from,l=null;(8===r||Fe.android&&e.text.length<o-n)&&(a=s.to,l="end");let c=function(t,e,i,n){let s=Math.min(t.length,e.length),r=0;for(;r<s&&t.charCodeAt(r)==e.charCodeAt(r);)r++;if(r==s&&t.length==e.length)return null;let o=t.length,a=e.length;for(;o>0&&a>0&&t.charCodeAt(o-1)==e.charCodeAt(a-1);)o--,a--;if("end"==n){i-=o+Math.max(0,r-Math.min(o,a))-r}if(o<r&&t.length<e.length){r-=i<=r&&i>=o?r-i:0,a=r+(a-o),o=r}else if(a<r){r-=i<=r&&i>=a?r-i:0,o=r+(o-a),a=r}return{from:r,toA:o,toB:a}}(t.state.doc.sliceString(n,o,Bs),e.text,a-n,l);c&&(Fe.chrome&&13==r&&c.toB==c.from+2&&e.text.slice(c.from,c.toB)==Bs+Bs&&c.toB--,i={from:n+c.from,to:n+c.toA,insert:h.of(e.text.slice(c.from,c.toB).split(Bs))})}else n&&(!t.hasFocus&&t.state.facet(en)||n.main.eq(s))&&(n=null);if(!i&&!n)return!1;if(!i&&e.typeOver&&!s.empty&&n&&n.main.empty?i={from:s.from,to:s.to,insert:t.state.doc.slice(s.from,s.to)}:i&&i.from>=s.from&&i.to<=s.to&&(i.from!=s.from||i.to!=s.to)&&s.to-s.from-(i.to-i.from)<=4?i={from:s.from,to:s.to,insert:t.state.doc.slice(s.from,i.from).append(i.insert).append(t.state.doc.slice(i.to,s.to))}:(Fe.mac||Fe.android)&&i&&i.from==i.to&&i.from==s.head-1&&/^\. ?$/.test(i.insert.toString())&&"off"==t.contentDOM.getAttribute("autocorrect")?(n&&2==i.insert.length&&(n=V.single(n.main.anchor-1,n.main.head-1)),i={from:s.from,to:s.to,insert:h.of([" "])}):Fe.chrome&&i&&i.from==i.to&&i.from==s.head&&"\n "==i.insert.toString()&&t.lineWrapping&&(n&&(n=V.single(n.main.anchor-1,n.main.head-1)),i={from:s.from,to:s.to,insert:h.of([" "])}),i){if(Fe.ios&&t.inputState.flushIOSKey(i))return!0;if(Fe.android&&(i.to==s.to&&(i.from==s.from||i.from==s.from-1&&" "==t.state.sliceDoc(i.from,s.from))&&1==i.insert.length&&2==i.insert.lines&&Ze(t.contentDOM,"Enter",13)||(i.from==s.from-1&&i.to==s.to&&0==i.insert.length||8==r&&i.insert.length<i.to-i.from&&i.to>s.head)&&Ze(t.contentDOM,"Backspace",8)||i.from==s.from&&i.to==s.to+1&&0==i.insert.length&&Ze(t.contentDOM,"Delete",46)))return!0;let e,o=i.insert.toString();t.inputState.composing>=0&&t.inputState.composing++;let a=()=>e||(e=function(t,e,i){let n,s=t.state,r=s.selection.main;if(e.from>=r.from&&e.to<=r.to&&e.to-e.from>=(r.to-r.from)/3&&(!i||i.main.empty&&i.main.from==e.from+e.insert.length)&&t.inputState.composing<0){let i=r.from<e.from?s.sliceDoc(r.from,e.from):"",o=r.to>e.to?s.sliceDoc(e.to,r.to):"";n=s.replaceSelection(t.state.toText(i+e.insert.sliceString(0,void 0,t.state.lineBreak)+o))}else{let o=s.changes(e),a=i&&i.main.to<=o.newLength?i.main:void 0;if(s.selection.ranges.length>1&&t.inputState.composing>=0&&e.to<=r.to&&e.to>=r.to-10){let l,h=t.state.sliceDoc(e.from,e.to),c=i&&yn(t,i.main.head);if(c){let t=e.insert.length-(e.to-e.from);l={from:c.from,to:c.to-t}}else l=t.state.doc.lineAt(r.head);let f=r.to-e.to,u=r.to-r.from;n=s.changeByRange((i=>{if(i.from==r.from&&i.to==r.to)return{changes:o,range:a||i.map(o)};let n=i.to-f,c=n-h.length;if(i.to-i.from!=u||t.state.sliceDoc(c,n)!=h||i.to>=l.from&&i.from<=l.to)return{range:i};let d=s.changes({from:c,to:n,insert:e.insert}),O=i.to-r.to;return{changes:d,range:a?V.range(Math.max(0,a.anchor+O),Math.max(0,a.head+O)):i.map(d)}}))}else n={changes:o,selection:a&&s.selection.replaceRange(a)}}let o="input.type";(t.composing||t.inputState.compositionPendingChange&&t.inputState.compositionEndedAt>Date.now()-50)&&(t.inputState.compositionPendingChange=!1,o+=".compose",t.inputState.compositionFirstChange&&(o+=".start",t.inputState.compositionFirstChange=!1));return s.update(n,{userEvent:o,scrollIntoView:!0})}(t,i,n));return t.state.facet(Li).some((e=>e(t,i.from,i.to,o,a)))||t.dispatch(a()),!0}if(n&&!n.main.eq(s)){let e=!1,i="select";return t.inputState.lastSelectionTime>Date.now()-50&&("select"==t.inputState.lastSelectionOrigin&&(e=!0),i=t.inputState.lastSelectionOrigin),t.dispatch({selection:n,scrollIntoView:e,userEvent:i}),!0}return!1}const Us={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},Hs=Fe.ie&&Fe.ie_version<=11;class Fs{constructor(t){this.view=t,this.active=!1,this.selectionRange=new ke,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver((e=>{for(let t of e)this.queue.push(t);(Fe.ie&&Fe.ie_version<=11||Fe.ios&&t.composing)&&e.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),Hs&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),"function"==typeof ResizeObserver&&(this.resizeScroll=new ResizeObserver((()=>{var t;(null===(t=this.view.docView)||void 0===t?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()})),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver((t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))}),{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver((t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))}),{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout((()=>{this.resizeTimeout=-1,this.view.requestMeasure()}),50))}onPrint(t){("change"!=t.type||t.matches)&&(this.view.viewState.printing=!0,this.view.measure(),setTimeout((()=>{this.view.viewState.printing=!1,this.view.requestMeasure()}),500))}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some(((e,i)=>e!=t[i])))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,n=this.selectionRange;if(i.state.facet(en)?i.root.activeElement!=this.dom:!de(i.dom,n))return;let s=n.anchorNode&&i.docView.nearest(n.anchorNode);s&&s.ignoreEvent(t)?e||(this.selectionChanged=!1):(Fe.ie&&Fe.ie_version<=11||Fe.android&&Fe.chrome)&&!i.state.selection.main.empty&&n.focusNode&&pe(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=fe(t.root);if(!e)return!1;let i=Fe.safari&&11==t.root.nodeType&&function(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function(t,e){if(e.getComposedRanges){let i=e.getComposedRanges(t.root)[0];if(i)return Js(t,i)}let i=null;function n(t){t.preventDefault(),t.stopImmediatePropagation(),i=t.getTargetRanges()[0]}return t.contentDOM.addEventListener("beforeinput",n,!0),t.dom.ownerDocument.execCommand("indent"),t.contentDOM.removeEventListener("beforeinput",n,!0),i?Js(t,i):null}(this.view,e)||e;if(!i||this.selectionRange.eq(i))return!1;let n=de(this.dom,i);return n&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&function(t,e){let i=e.focusNode,n=e.focusOffset;if(!i||e.anchorNode!=i||e.anchorOffset!=n)return!1;for(n=Math.min(n,ve(i));;)if(n){if(1!=i.nodeType)return!1;let t=i.childNodes[n-1];"false"==t.contentEditable?n--:(i=t,n=ve(i))}else{if(i==t)return!0;n=ge(i),i=i.parentNode}}(this.dom,i)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(i),n&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(1==i.nodeType)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else{if(11!=i.nodeType)break;i=i.host}if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,Us),Hs&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),Hs&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let t=()=>{let t=this.delayedAndroidKey;if(t){this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=t.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&t.force&&Ze(this.dom,t.key,t.keyCode)}};this.flushingAndroidKey=this.view.win.requestAnimationFrame(t)}this.delayedAndroidKey&&"Enter"!=t||(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(null===(i=this.delayedAndroidKey)||void 0===i?void 0:i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame((()=>{this.delayedFlush=-1,this.flush()})))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let s of t){let t=this.readMutation(s);t&&(t.typeOver&&(n=!0),-1==e?({from:e,to:i}=t):(e=Math.min(t.from,e),i=Math.max(t.to,i)))}return{from:e,to:i,typeOver:n}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),n=this.selectionChanged&&de(this.dom,this.selectionRange);if(t<0&&!n)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let s=new Ls(this.view,t,e,i);return this.view.docView.domChanged={newSel:s.newSel?s.newSel.main:null},s}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,n=Ns(this.view,e);return this.view.state==i&&this.view.update([]),n}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty("attributes"==t.type),"attributes"==t.type&&(e.flags|=4),"childList"==t.type){let i=Ks(e,t.previousSibling||t.target.previousSibling,-1),n=Ks(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}return"characterData"==t.type?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener("change",this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener("change",this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}destroy(){var t,e,i;this.stop(),null===(t=this.intersection)||void 0===t||t.disconnect(),null===(e=this.gapIntersection)||void 0===e||e.disconnect(),null===(i=this.resizeScroll)||void 0===i||i.disconnect();for(let n of this.scrollTargets)n.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey)}}function Ks(t,e,i){for(;e;){let n=Ye.get(e);if(n&&n.parent==t)return n;let s=e.parentNode;e=s!=t.dom?s:i>0?e.nextSibling:e.previousSibling}return null}function Js(t,e){let i=e.startContainer,n=e.startOffset,s=e.endContainer,r=e.endOffset,o=t.docView.domAtPos(t.state.selection.main.anchor);return pe(o.node,o.offset,s,r)&&([i,n,s,r]=[s,r,i,n]),{anchorNode:i,anchorOffset:n,focusNode:s,focusOffset:r}}class tr{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:e}=t;this.dispatchTransactions=t.dispatchTransactions||e&&(t=>t.forEach((t=>e(t,this))))||(t=>this.update(t)),this.dispatch=this.dispatch.bind(this),this._root=t.root||function(t){for(;t;){if(t&&(9==t.nodeType||11==t.nodeType&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}(t.parent)||document,this.viewState=new Ps(t.state||Wt.create(t)),t.scrollTo&&t.scrollTo.is(Ji)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(sn).map((t=>new on(t)));for(let i of this.plugins)i.update(this);this.observer=new Fs(this),this.inputState=new qn(this),this.inputState.ensureHandlers(this.plugins),this.docView=new vn(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure()}dispatch(...t){let e=1==t.length&&t[0]instanceof Qt?t:1==t.length&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(0!=this.updateState)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e,i=!1,n=!1,s=this.state;for(let f of t){if(f.startState!=s)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");s=f.state}if(this.destroyed)return void(this.viewState.state=s);let r=this.hasFocus,o=0,a=null;t.some((t=>t.annotation(rs)))?(this.inputState.notifiedFocused=r,o=1):r!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=r,a=os(s,r),a||(o=1));let l=this.observer.delayedAndroidKey,h=null;if(l?(this.observer.clearDelayedAndroidKey(),h=this.observer.readChange(),(h&&!this.state.doc.eq(s.doc)||!this.state.selection.eq(s.selection))&&(h=null)):this.observer.clear(),s.facet(Wt.phrases)!=this.state.facet(Wt.phrases))return this.setState(s);e=wn.create(this,s,t),e.flags|=o;let c=this.viewState.scrollTarget;try{this.updateState=2;for(let e of t){if(c&&(c=c.map(e.changes)),e.scrollIntoView){let{main:t}=e.state.selection;c=new Ki(t.empty?t:V.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)t.is(Ji)&&(c=t.value.clip(this.state))}this.viewState.update(e,c),this.bidiCache=nr.update(this.bidiCache,e.changes),e.empty||(this.updatePlugins(e),this.inputState.update(e)),i=this.docView.update(e),this.state.facet(gn)!=this.styleModules&&this.mountStyles(),n=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(i,t.some((t=>t.isUserEvent("select.pointer"))))}finally{this.updateState=0}if(e.startState.facet(Ys)!=e.state.facet(Ys)&&(this.viewState.mustMeasureContent=!0),(i||n||c||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),i&&this.docViewUpdate(),!e.empty)for(let f of this.state.facet(Ii))try{f(e)}catch(tO){tn(this.state,tO,"update listener")}(a||h)&&Promise.resolve().then((()=>{a&&this.state==a.startState&&this.dispatch(a),h&&!Ns(this,h)&&l.force&&Ze(this.contentDOM,l.key,l.keyCode)}))}setState(t){if(0!=this.updateState)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed)return void(this.viewState.state=t);this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);this.viewState=new Ps(t),this.plugins=t.facet(sn).map((t=>new on(t))),this.pluginMap.clear();for(let t of this.plugins)t.update(this);this.docView.destroy(),this.docView=new vn(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(sn),i=t.state.facet(sn);if(e!=i){let n=[];for(let s of i){let i=e.indexOf(s);if(i<0)n.push(new on(s));else{let e=this.plugins[i];e.mustUpdate=t,n.push(e)}}for(let e of this.plugins)e.mustUpdate!=t&&e.destroy(this);this.plugins=n,this.pluginMap.clear()}else for(let n of this.plugins)n.mustUpdate=t;for(let n=0;n<this.plugins.length;n++)this.plugins[n].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let t of this.plugins){let e=t.value;if(e&&e.docViewUpdate)try{e.docViewUpdate(this)}catch(tO){tn(this.state,tO,"doc view update listener")}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey)return this.measureScheduled=-1,void this.requestMeasure();this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,n=i.scrollTop*this.scaleY,{scrollAnchorPos:s,scrollAnchorHeight:r}=this.viewState;Math.abs(n-this.viewState.scrollTop)>1&&(r=-1),this.viewState.scrollAnchorHeight=-1;try{for(let t=0;;t++){if(r<0)if(Te(i))s=-1,r=this.viewState.heightMap.height;else{let t=this.viewState.scrollAnchorAt(n);s=t.from,r=t.top}this.updateState=1;let o=this.viewState.measure(this);if(!o&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(t>5)break;let a=[];4&o||([this.measureRequests,a]=[a,this.measureRequests]);let l=a.map((t=>{try{return t.read(this)}catch(tO){return tn(this.state,tO),ir}})),h=wn.create(this,this.state,[]),c=!1;h.flags|=o,e?e.flags|=o:e=h,this.updateState=2,h.empty||(this.updatePlugins(h),this.inputState.update(h),this.updateAttrs(),c=this.docView.update(h),c&&this.docViewUpdate());for(let t=0;t<a.length;t++)if(l[t]!=ir)try{let e=a[t];e.write&&e.write(l[t],this)}catch(tO){tn(this.state,tO)}if(c&&this.docView.updateSelection(!0),!h.viewportChanged&&0==this.measureRequests.length){if(this.viewState.editorHeight){if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,r=-1;continue}{let t=(s<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(s).top)-r;if(t>1||t<-1){n+=t,i.scrollTop=n/this.scaleY,r=-1;continue}}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let o of this.state.facet(Ii))o(e)}get themeClasses(){return Ms+" "+(this.state.facet(qs)?Ds:Es)+" "+this.state.facet(Ys)}updateAttrs(){let t=sr(this,an,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(en)?"true":"false",class:"cm-content",style:`${Fe.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),sr(this,ln,e);let i=this.observer.ignore((()=>{let i=li(this.contentDOM,this.contentAttrs,e),n=li(this.dom,this.editorAttrs,t);return i||n}));return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let t of i.effects)if(t.is(tr.announce)){e&&(this.announceDOM.textContent=""),e=!1,this.announceDOM.appendChild(document.createElement("div")).textContent=t.value}}mountStyles(){this.styleModules=this.state.facet(gn);let t=this.state.facet(tr.cspNonce);ie.mount(this.root,this.styleModules.concat(Vs).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(2==this.updateState)throw new Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame((()=>this.measure()))),t){if(this.measureRequests.indexOf(t)>-1)return;if(null!=t.key)for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key)return void(this.measureRequests[e]=t);this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(void 0===e||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find((e=>e.spec==t))||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return Yn(this,t,Wn(this,t,e,i))}moveByGroup(t,e){return Yn(this,t,Wn(this,t,e,(e=>function(t,e,i){let n=t.state.charCategorizer(e),s=n(i);return t=>{let e=n(t);return s==Ct.Space&&(s=e),s==e}}(this,t.head,e))))}visualLineSide(t,e){let i=this.bidiSpans(t),n=this.textDirectionAt(t.from),s=i[e?i.length-1:0];return V.cursor(s.side(e,n)+t.from,s.forward(!e,n)?1:-1)}moveToLineBoundary(t,e,i=!0){return function(t,e,i,n){let s=Xn(t,e.head),r=n&&s.type==di.Text&&(t.lineWrapping||s.widgetLineBreaks)?t.coordsAtPos(e.assoc<0&&e.head>s.from?e.head-1:e.head):null;if(r){let e=t.dom.getBoundingClientRect(),n=t.textDirectionAt(s.from),o=t.posAtCoords({x:i==(n==ki.LTR)?e.right-1:e.left+1,y:(r.top+r.bottom)/2});if(null!=o)return V.cursor(o,i?-1:1)}return V.cursor(i?s.to:s.from,i?-1:1)}(this,t,e,i)}moveVertically(t,e,i){return Yn(this,t,function(t,e,i,n){let s=e.head,r=i?1:-1;if(s==(i?t.state.doc.length:0))return V.cursor(s,e.assoc);let o,a=e.goalColumn,l=t.contentDOM.getBoundingClientRect(),h=t.coordsAtPos(s,e.assoc||-1),c=t.documentTop;if(h)null==a&&(a=h.left-l.left),o=r<0?h.top:h.bottom;else{let e=t.viewState.lineBlockAt(s);null==a&&(a=Math.min(l.right-l.left,t.defaultCharacterWidth*(s-e.from))),o=(r<0?e.top:e.bottom)+c}let f=l.left+a,u=null!=n?n:t.viewState.heightOracle.textHeight>>1;for(let d=0;;d+=10){let e=o+(u+d)*r,i=Tn(t,{x:f,y:e},!1,r);if(e<l.top||e>l.bottom||(r<0?i<s:i>s)){let n=t.docView.coordsForChar(i),s=!n||e<n.top?-1:1;return V.cursor(i,s,void 0,a)}}}(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),Tn(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),s=this.bidiSpans(n);return be(i,s[Wi.find(s,t-n.from,-1,e)].dir==ki.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(Ui)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>er)return Ei(t.length);let e,i=this.textDirectionAt(t.from);for(let s of this.bidiCache)if(s.from==t.from&&s.dir==i&&(s.fresh||Ri(s.isolates,e=dn(this,t))))return s.order;e||(e=dn(this,t));let n=function(t,e,i){if(!t)return[new Wi(0,0,e==xi?1:0)];if(e==Si&&!i.length&&!Xi.test(t))return Ei(t.length);if(i.length)for(;t.length>Yi.length;)Yi[Yi.length]=256;let n=[],s=e==Si?0:1;return Mi(t,s,s,i,0,t.length,n),n}(t.text,i,e);return this.bidiCache.push(new nr(t.from,t.to,i,e,!0,n)),n}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||Fe.safari&&(null===(t=this.inputState)||void 0===t?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore((()=>{$e(this.contentDOM),this.docView.updateSelection()}))}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((9==t.nodeType?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return Ji.of(new Ki("number"==typeof t?V.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return Ji.of(new Ki(V.cursor(i.from),"start","start",i.top-t,e,!0))}static domEventHandlers(t){return rn.define((()=>({})),{eventHandlers:t})}static domEventObservers(t){return rn.define((()=>({})),{eventObservers:t})}static theme(t,e){let i=ie.newName(),n=[Ys.of(i),gn.of(_s(`.${i}`,t))];return e&&e.dark&&n.push(qs.of(!0)),n}static baseTheme(t){return st.lowest(gn.of(_s("."+Ms,t,js)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),n=i&&Ye.get(i)||Ye.get(t);return(null===(e=null==n?void 0:n.rootView)||void 0===e?void 0:e.view)||null}}tr.styleModule=gn,tr.inputHandler=Li,tr.scrollHandler=Fi,tr.focusChangeEffect=Ni,tr.perLineTextDirection=Ui,tr.exceptionSink=Gi,tr.updateListener=Ii,tr.editable=en,tr.mouseSelectionStyle=zi,tr.dragMovesSelection=Bi,tr.clickAddsSelectionRange=Vi,tr.decorations=hn,tr.outerDecorations=cn,tr.atomicRanges=fn,tr.bidiIsolatedRanges=un,tr.scrollMargins=On,tr.darkTheme=qs,tr.cspNonce=G.define({combine:t=>t.length?t[0]:""}),tr.contentAttributes=ln,tr.editorAttributes=an,tr.lineWrapping=tr.contentAttributes.of({class:"cm-lineWrapping"}),tr.announce=yt.define();const er=4096,ir={};class nr{constructor(t,e,i,n,s,r){this.from=t,this.to=e,this.dir=i,this.isolates=n,this.fresh=s,this.order=r}static update(t,e){if(e.empty&&!t.some((t=>t.fresh)))return t;let i=[],n=t.length?t[t.length-1].dir:ki.LTR;for(let s=Math.max(0,t.length-10);s<t.length;s++){let r=t[s];r.dir!=n||e.touchesRange(r.from,r.to)||i.push(new nr(e.mapPos(r.from,1),e.mapPos(r.to,-1),r.dir,r.isolates,!1,r.order))}return i}}function sr(t,e,i){for(let n=t.state.facet(e),s=n.length-1;s>=0;s--){let e=n[s],r="function"==typeof e?e(t):e;r&&ri(r,i)}return i}const rr=Fe.mac?"mac":Fe.windows?"win":Fe.linux?"linux":"key";function or(t,e,i){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),!1!==i&&e.shiftKey&&(t="Shift-"+t),t}const ar=st.default(tr.domEventHandlers({keydown:(t,e)=>dr(cr(e.state),t,e,"editor")})),lr=G.define({enables:ar}),hr=new WeakMap;function cr(t){let e=t.facet(lr),i=hr.get(e);return i||hr.set(e,i=function(t,e=rr){let i=Object.create(null),n=Object.create(null),s=(t,e)=>{let i=n[t];if(null==i)n[t]=e;else if(i!=e)throw new Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")},r=(t,n,r,o,a)=>{var l,h;let c=i[t]||(i[t]=Object.create(null)),f=n.split(/ (?!$)/).map((t=>function(t,e){const i=t.split(/-(?!$)/);let n,s,r,o,a=i[i.length-1];"Space"==a&&(a=" ");for(let l=0;l<i.length-1;++l){const t=i[l];if(/^(cmd|meta|m)$/i.test(t))o=!0;else if(/^a(lt)?$/i.test(t))n=!0;else if(/^(c|ctrl|control)$/i.test(t))s=!0;else if(/^s(hift)?$/i.test(t))r=!0;else{if(!/^mod$/i.test(t))throw new Error("Unrecognized modifier name: "+t);"mac"==e?o=!0:s=!0}}return n&&(a="Alt-"+a),s&&(a="Ctrl-"+a),o&&(a="Meta-"+a),r&&(a="Shift-"+a),a}(t,e)));for(let e=1;e<f.length;e++){let i=f.slice(0,e).join(" ");s(i,!0),c[i]||(c[i]={preventDefault:!0,stopPropagation:!1,run:[e=>{let n=fr={view:e,prefix:i,scope:t};return setTimeout((()=>{fr==n&&(fr=null)}),ur),!0}]})}let u=f.join(" ");s(u,!1);let d=c[u]||(c[u]={preventDefault:!1,stopPropagation:!1,run:(null===(h=null===(l=c._any)||void 0===l?void 0:l.run)||void 0===h?void 0:h.slice())||[]});r&&d.run.push(r),o&&(d.preventDefault=!0),a&&(d.stopPropagation=!0)};for(let o of t){let t=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let e of t){let t=i[e]||(i[e]=Object.create(null));t._any||(t._any={preventDefault:!1,stopPropagation:!1,run:[]});for(let e in t)t[e].run.push(o.any)}let n=o[e]||o.key;if(n)for(let e of t)r(e,n,o.run,o.preventDefault,o.stopPropagation),o.shift&&r(e,"Shift-"+n,o.shift,o.preventDefault,o.stopPropagation)}return i}(e.reduce(((t,e)=>t.concat(e)),[]))),i}let fr=null;const ur=4e3;function dr(t,e,i,n){let s=function(t){var e=!(ae&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||le&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?oe:re)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(e),r=T(Z(s,0))==s.length&&" "!=s,o="",a=!1,l=!1,h=!1;fr&&fr.view==i&&fr.scope==n&&(o=fr.prefix+" ",jn.indexOf(e.keyCode)<0&&(l=!0,fr=null));let c,f,u=new Set,d=t=>{if(t){for(let n of t.run)if(!u.has(n)&&(u.add(n),n(i,e)))return t.stopPropagation&&(h=!0),!0;t.preventDefault&&(t.stopPropagation&&(h=!0),l=!0)}return!1},O=t[n];return O&&(d(O[o+or(s,e,!r)])?a=!0:r&&(e.altKey||e.metaKey||e.ctrlKey)&&!(Fe.windows&&e.ctrlKey&&e.altKey)&&(c=re[e.keyCode])&&c!=s?(d(O[o+or(c,e,!0)])||e.shiftKey&&(f=oe[e.keyCode])!=s&&f!=c&&d(O[o+or(f,e,!1)]))&&(a=!0):r&&e.shiftKey&&d(O[o+or(s,e,!0)])&&(a=!0),!a&&d(O._any)&&(a=!0)),l&&(a=!0),a&&h&&e.stopPropagation(),a}class Or{constructor(t,e,i,n,s){this.className=t,this.left=e,this.top=i,this.width=n,this.height=s}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className==this.className&&(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",null!=this.width&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let n=t.coordsAtPos(i.head,i.assoc||1);if(!n)return[];let s=pr(t);return[new Or(e,n.left-s.left,n.top-s.top,null,n.bottom-n.top)]}return function(t,e,i){if(i.to<=t.viewport.from||i.from>=t.viewport.to)return[];let n=Math.max(i.from,t.viewport.from),s=Math.min(i.to,t.viewport.to),r=t.textDirection==ki.LTR,o=t.contentDOM,a=o.getBoundingClientRect(),l=pr(t),h=o.querySelector(".cm-line"),c=h&&window.getComputedStyle(h),f=a.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=a.right-(c?parseInt(c.paddingRight):0),d=Xn(t,n),O=Xn(t,s),p=d.type==di.Text?d:null,g=O.type==di.Text?O:null;p&&(t.lineWrapping||d.widgetLineBreaks)&&(p=gr(t,n,p));g&&(t.lineWrapping||O.widgetLineBreaks)&&(g=gr(t,s,g));if(p&&g&&p.from==g.from)return w(v(i.from,i.to,p));{let e=p?v(i.from,null,p):b(d,!1),n=g?v(null,i.to,g):b(O,!0),s=[];return(p||d).to<(g||O).from-(p&&g?1:0)||d.widgetLineBreaks>1&&e.bottom+t.defaultLineHeight/2<n.top?s.push(m(f,e.bottom,u,n.top)):e.bottom<n.top&&t.elementAtHeight((e.bottom+n.top)/2).type==di.Text&&(e.bottom=n.top=(e.bottom+n.top)/2),w(e).concat(s).concat(w(n))}function m(t,i,n,s){return new Or(e,t-l.left,i-l.top-.01,n-t,s-i+.01)}function w({top:t,bottom:e,horizontal:i}){let n=[];for(let s=0;s<i.length;s+=2)n.push(m(i[s],t,i[s+1],e));return n}function v(e,i,n){let s=1e9,o=-1e9,a=[];function l(e,i,l,h,c){let d=t.coordsAtPos(e,e==n.to?-2:2),O=t.coordsAtPos(l,l==n.from?2:-2);d&&O&&(s=Math.min(d.top,O.top,s),o=Math.max(d.bottom,O.bottom,o),c==ki.LTR?a.push(r&&i?f:d.left,r&&h?u:O.right):a.push(!r&&h?f:O.left,!r&&i?u:d.right))}let h=null!=e?e:n.from,c=null!=i?i:n.to;for(let r of t.visibleRanges)if(r.to>h&&r.from<c)for(let n=Math.max(r.from,h),s=Math.min(r.to,c);;){let r=t.state.doc.lineAt(n);for(let o of t.bidiSpans(r)){let t=o.from+r.from,a=o.to+r.from;if(t>=s)break;a>n&&l(Math.max(t,n),null==e&&t<=h,Math.min(a,s),null==i&&a>=c,o.dir)}if(n=r.to+1,n>=s)break}return 0==a.length&&l(h,null==e,c,null==i,t.textDirection),{top:s,bottom:o,horizontal:a}}function b(t,e){let i=a.top+(e?t.top:t.bottom);return{top:i,bottom:i,horizontal:[]}}}(t,e,i)}}function pr(t){let e=t.scrollDOM.getBoundingClientRect();return{left:(t.textDirection==ki.LTR?e.left:e.right-t.scrollDOM.clientWidth*t.scaleX)-t.scrollDOM.scrollLeft*t.scaleX,top:e.top-t.scrollDOM.scrollTop*t.scaleY}}function gr(t,e,i){let n=V.cursor(e);return{from:Math.max(i.from,t.moveToLineBoundary(n,!1,!0).from),to:Math.min(i.to,t.moveToLineBoundary(n,!0,!0).from),type:di.Text}}class mr{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(wr)!=t.state.facet(wr)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}docViewUpdate(t){!1!==this.layer.updateOnDocViewUpdate&&t.requestMeasure(this.measureReq)}setOrder(t){let e=0,i=t.facet(wr);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;t==this.scaleX&&e==this.scaleY||(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some(((t,e)=>{return i=t,n=this.drawn[e],!(i.constructor==n.constructor&&i.eq(n));var i,n}))){let e=this.dom.firstChild,i=0;for(let n of t)n.update&&e&&n.constructor&&this.drawn[i].constructor&&n.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(n.draw(),e);for(;e;){let t=e.nextSibling;e.remove(),e=t}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const wr=G.define();function vr(t){return[rn.define((e=>new mr(e,t))),wr.of(t)]}const br=!Fe.ios,yr=G.define({combine:t=>Rt(t,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})});function Qr(t={}){return[yr.of(t),Sr,$r,Zr,Hi.of(!0)]}function kr(t){return t.startState.facet(yr)!=t.state.facet(yr)}const Sr=vr({above:!0,markers(t){let{state:e}=t,i=e.facet(yr),n=[];for(let s of e.selection.ranges){let r=s==e.selection.main;if(s.empty?!r||br:i.drawRangeCursor){let e=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",i=s.empty?s:V.cursor(s.head,s.head>s.anchor?-1:1);for(let s of Or.forRange(t,e,i))n.push(s)}}return n},update(t,e){t.transactions.some((t=>t.selection))&&(e.style.animationName="cm-blink"==e.style.animationName?"cm-blink2":"cm-blink");let i=kr(t);return i&&xr(t.state,e),t.docChanged||t.selectionSet||i},mount(t,e){xr(e.state,t)},class:"cm-cursorLayer"});function xr(t,e){e.style.animationDuration=t.facet(yr).cursorBlinkRate+"ms"}const $r=vr({above:!1,markers:t=>t.state.selection.ranges.map((e=>e.empty?[]:Or.forRange(t,"cm-selectionBackground",e))).reduce(((t,e)=>t.concat(e))),update:(t,e)=>t.docChanged||t.selectionSet||t.viewportChanged||kr(t),class:"cm-selectionLayer"}),Pr={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};br&&(Pr[".cm-line"].caretColor="transparent !important",Pr[".cm-content"]={caretColor:"transparent !important"});const Zr=st.highest(tr.theme(Pr)),Cr=yt.define({map:(t,e)=>null==t?null:e.mapPos(t)}),Tr=K.define({create:()=>null,update:(t,e)=>(null!=t&&(t=e.changes.mapPos(t)),e.effects.reduce(((t,e)=>e.is(Cr)?e.value:t),t))}),Ar=rn.fromClass(class{constructor(t){this.view=t,this.cursor=null,this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(t){var e;let i=t.state.field(Tr);null==i?null!=this.cursor&&(null===(e=this.cursor)||void 0===e||e.remove(),this.cursor=null):(this.cursor||(this.cursor=this.view.scrollDOM.appendChild(document.createElement("div")),this.cursor.className="cm-dropCursor"),(t.startState.field(Tr)!=i||t.docChanged||t.geometryChanged)&&this.view.requestMeasure(this.measureReq))}readPos(){let{view:t}=this,e=t.state.field(Tr),i=null!=e&&t.coordsAtPos(e);if(!i)return null;let n=t.scrollDOM.getBoundingClientRect();return{left:i.left-n.left+t.scrollDOM.scrollLeft*t.scaleX,top:i.top-n.top+t.scrollDOM.scrollTop*t.scaleY,height:i.bottom-i.top}}drawCursor(t){if(this.cursor){let{scaleX:e,scaleY:i}=this.view;t?(this.cursor.style.left=t.left/e+"px",this.cursor.style.top=t.top/i+"px",this.cursor.style.height=t.height/i+"px"):this.cursor.style.left="-100000px"}}destroy(){this.cursor&&this.cursor.remove()}setDropPos(t){this.view.state.field(Tr)!=t&&this.view.dispatch({effects:Cr.of(t)})}},{eventObservers:{dragover(t){this.setDropPos(this.view.posAtCoords({x:t.clientX,y:t.clientY}))},dragleave(t){t.target!=this.view.contentDOM&&this.view.contentDOM.contains(t.relatedTarget)||this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function Xr(t,e,i,n,s){e.lastIndex=0;for(let r,o=t.iterRange(i,n),a=i;!o.next().done;a+=o.value.length)if(!o.lineBreak)for(;r=e.exec(o.value);)s(a+r.index,r)}class Wr{constructor(t){const{regexp:e,decoration:i,decorate:n,boundary:s,maxLength:r=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,n)this.addMatch=(t,e,i,s)=>n(s,i,i+t[0].length,t,e);else if("function"==typeof i)this.addMatch=(t,e,n,s)=>{let r=i(t,e,n);r&&s(n,n+t[0].length,r)};else{if(!i)throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.addMatch=(t,e,n,s)=>s(n,n+t[0].length,i)}this.boundary=s,this.maxLength=r}createDeco(t){let e=new jt,i=e.add.bind(e);for(let{from:n,to:s}of function(t,e){let i=t.visibleRanges;if(1==i.length&&i[0].from==t.viewport.from&&i[0].to==t.viewport.to)return i;let n=[];for(let{from:s,to:r}of i)s=Math.max(t.state.doc.lineAt(s).from,s-e),r=Math.min(t.state.doc.lineAt(r).to,r+e),n.length&&n[n.length-1].to>=s?n[n.length-1].to=r:n.push({from:s,to:r});return n}(t,this.maxLength))Xr(t.state.doc,this.regexp,n,s,((e,n)=>this.addMatch(n,t,e,i)));return e.finish()}updateDeco(t,e){let i=1e9,n=-1;return t.docChanged&&t.changes.iterChanges(((e,s,r,o)=>{o>t.view.viewport.from&&r<t.view.viewport.to&&(i=Math.min(r,i),n=Math.max(o,n))})),t.viewportChanged||n-i>1e3?this.createDeco(t.view):n>-1?this.updateRange(t.view,e.map(t.changes),i,n):e}updateRange(t,e,i,n){for(let s of t.visibleRanges){let r=Math.max(s.from,i),o=Math.min(s.to,n);if(o>r){let i=t.state.doc.lineAt(r),n=i.to<o?t.state.doc.lineAt(o):i,a=Math.max(s.from,i.from),l=Math.min(s.to,n.to);if(this.boundary){for(;r>i.from;r--)if(this.boundary.test(i.text[r-1-i.from])){a=r;break}for(;o<n.to;o++)if(this.boundary.test(n.text[o-n.from])){l=o;break}}let h,c=[],f=(t,e,i)=>c.push(i.range(t,e));if(i==n)for(this.regexp.lastIndex=a-i.from;(h=this.regexp.exec(i.text))&&h.index<l-i.from;)this.addMatch(h,t,h.index+i.from,f);else Xr(t.state.doc,this.regexp,a,l,((e,i)=>this.addMatch(i,t,e,f)));e=e.update({filterFrom:a,filterTo:l,filter:(t,e)=>t<a||e>l,add:c})}}return e}}const Rr=null!=/x/.unicode?"gu":"g",Yr=new RegExp("[\0-\b\n--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\ufeff￹-￼]",Rr),qr={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let Mr=null;const Er=G.define({combine(t){let e=Rt(t,{render:null,specialChars:Yr,addSpecialChars:null});return(e.replaceTabs=!function(){var t;if(null==Mr&&"undefined"!=typeof document&&document.body){let e=document.body.style;Mr=null!=(null!==(t=e.tabSize)&&void 0!==t?t:e.MozTabSize)}return Mr||!1}())&&(e.specialChars=new RegExp("\t|"+e.specialChars.source,Rr)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,Rr)),e}});function Dr(t={}){return[Er.of(t),jr||(jr=rn.fromClass(class{constructor(t){this.view=t,this.decorations=Oi.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(t.state.facet(Er)),this.decorations=this.decorator.createDeco(t)}makeDecorator(t){return new Wr({regexp:t.specialChars,decoration:(e,i,n)=>{let{doc:s}=i.state,r=Z(e[0],0);if(9==r){let t=s.lineAt(n),e=i.state.tabSize,r=Ft(t.text,e,n-t.from);return Oi.replace({widget:new Vr((e-r%e)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=Oi.replace({widget:new _r(t,r)}))},boundary:t.replaceTabs?void 0:/[^]/})}update(t){let e=t.state.facet(Er);t.startState.facet(Er)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(t.view)):this.decorations=this.decorator.updateDeco(t,this.decorations)}},{decorations:t=>t.decorations}))]}let jr=null;class _r extends ui{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=function(t){return t>=32?"•":10==t?"␤":String.fromCharCode(9216+t)}(this.code),i=t.state.phrase("Control character")+" "+(qr[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,e);if(n)return n;let s=document.createElement("span");return s.textContent=e,s.title=i,s.setAttribute("aria-label",i),s.className="cm-specialChar",s}ignoreEvent(){return!1}}class Vr extends ui{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="\t",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}const Br=Oi.line({class:"cm-activeLine"}),zr=rn.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.docChanged||t.selectionSet)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=-1,i=[];for(let n of t.state.selection.ranges){let s=t.lineBlockAt(n.head);s.from>e&&(i.push(Br.range(s.from)),e=s.from)}return Oi.set(i)}},{decorations:t=>t.decorations});class Gr extends ui{constructor(t){super(),this.content=t}toDOM(){let t=document.createElement("span");return t.className="cm-placeholder",t.style.pointerEvents="none",t.appendChild("string"==typeof this.content?document.createTextNode(this.content):this.content),"string"==typeof this.content?t.setAttribute("aria-label","placeholder "+this.content):t.setAttribute("aria-hidden","true"),t}coordsAt(t){let e=t.firstChild?Oe(t.firstChild):[];if(!e.length)return null;let i=window.getComputedStyle(t.parentNode),n=be(e[0],"rtl"!=i.direction),s=parseInt(i.lineHeight);return n.bottom-n.top>1.5*s?{left:n.left,right:n.right,top:n.top,bottom:n.top+s}:n}ignoreEvent(){return!1}}const Ir=2e3;function Lr(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1),n=t.state.doc.lineAt(i),s=i-n.from,r=s>Ir?-1:s==n.length?function(t,e){let i=t.coordsAtPos(t.viewport.from);return i?Math.round(Math.abs((i.left-e)/t.defaultCharacterWidth)):-1}(t,e.clientX):Ft(n.text,t.state.tabSize,i-n.from);return{line:n.number,col:r,off:s}}function Nr(t,e){let i=Lr(t,e),n=t.state.selection;return i?{update(t){if(t.docChanged){let e=t.changes.mapPos(t.startState.doc.line(i.line).from),s=t.state.doc.lineAt(e);i={line:s.number,col:i.col,off:Math.min(i.off,s.length)},n=n.map(t.changes)}},get(e,s,r){let o=Lr(t,e);if(!o)return n;let a=function(t,e,i){let n=Math.min(e.line,i.line),s=Math.max(e.line,i.line),r=[];if(e.off>Ir||i.off>Ir||e.col<0||i.col<0){let o=Math.min(e.off,i.off),a=Math.max(e.off,i.off);for(let e=n;e<=s;e++){let i=t.doc.line(e);i.length<=a&&r.push(V.range(i.from+o,i.to+a))}}else{let o=Math.min(e.col,i.col),a=Math.max(e.col,i.col);for(let e=n;e<=s;e++){let i=t.doc.line(e),n=Kt(i.text,o,t.tabSize,!0);if(n<0)r.push(V.cursor(i.to));else{let e=Kt(i.text,a,t.tabSize);r.push(V.range(i.from+n,i.from+e))}}}return r}(t.state,i,o);return a.length?r?V.create(a.concat(n.ranges)):V.create(a):n}}:null}function Ur(t){let e=(null==t?void 0:t.eventFilter)||(t=>t.altKey&&0==t.button);return tr.mouseSelectionStyle.of(((t,i)=>e(i)?Nr(t,i):null))}const Hr={Alt:[18,t=>!!t.altKey],Control:[17,t=>!!t.ctrlKey],Shift:[16,t=>!!t.shiftKey],Meta:[91,t=>!!t.metaKey]},Fr={style:"cursor: crosshair"};function Kr(t={}){let[e,i]=Hr[t.key||"Alt"],n=rn.fromClass(class{constructor(t){this.view=t,this.isDown=!1}set(t){this.isDown!=t&&(this.isDown=t,this.view.update([]))}},{eventObservers:{keydown(t){this.set(t.keyCode==e||i(t))},keyup(t){t.keyCode!=e&&i(t)||this.set(!1)},mousemove(t){this.set(i(t))}}});return[n,tr.contentAttributes.of((t=>{var e;return(null===(e=t.plugin(n))||void 0===e?void 0:e.isDown)?Fr:null}))]}const Jr="-10000px";class to{constructor(t,e,i,n){this.facet=e,this.createTooltipView=i,this.removeTooltipView=n,this.input=t.state.facet(e),this.tooltips=this.input.filter((t=>t));let s=null;this.tooltipViews=this.tooltips.map((t=>s=i(t,s)))}update(t,e){var i;let n=t.state.facet(this.facet),s=n.filter((t=>t));if(n===this.input){for(let e of this.tooltipViews)e.update&&e.update(t);return!1}let r=[],o=e?[]:null;for(let a=0;a<s.length;a++){let i=s[a],n=-1;if(i){for(let t=0;t<this.tooltips.length;t++){let e=this.tooltips[t];e&&e.create==i.create&&(n=t)}if(n<0)r[a]=this.createTooltipView(i,a?r[a-1]:null),o&&(o[a]=!!i.above);else{let i=r[a]=this.tooltipViews[n];o&&(o[a]=e[n]),i.update&&i.update(t)}}}for(let a of this.tooltipViews)r.indexOf(a)<0&&(this.removeTooltipView(a),null===(i=a.destroy)||void 0===i||i.call(a));return e&&(o.forEach(((t,i)=>e[i]=t)),e.length=o.length),this.input=n,this.tooltips=s,this.tooltipViews=r,!0}}function eo(t){let{win:e}=t;return{top:0,left:0,bottom:e.innerHeight,right:e.innerWidth}}const io=G.define({combine:t=>{var e,i,n;return{position:Fe.ios?"absolute":(null===(e=t.find((t=>t.position)))||void 0===e?void 0:e.position)||"fixed",parent:(null===(i=t.find((t=>t.parent)))||void 0===i?void 0:i.parent)||null,tooltipSpace:(null===(n=t.find((t=>t.tooltipSpace)))||void 0===n?void 0:n.tooltipSpace)||eo}}}),no=new WeakMap,so=rn.fromClass(class{constructor(t){this.view=t,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let e=t.state.facet(io);this.position=e.position,this.parent=e.parent,this.classes=t.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver="function"==typeof ResizeObserver?new ResizeObserver((()=>this.measureSoon())):null,this.manager=new to(t,ao,((t,e)=>this.createTooltip(t,e)),(t=>{this.resizeObserver&&this.resizeObserver.unobserve(t.dom),t.dom.remove()})),this.above=this.manager.tooltips.map((t=>!!t.above)),this.intersectionObserver="function"==typeof IntersectionObserver?new IntersectionObserver((t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()}),{threshold:[1]}):null,this.observeIntersection(),t.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let t of this.manager.tooltipViews)this.intersectionObserver.observe(t.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout((()=>{this.measureTimeout=-1,this.maybeMeasure()}),50))}update(t){t.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(t,this.above);e&&this.observeIntersection();let i=e||t.geometryChanged,n=t.state.facet(io);if(n.position!=this.position&&!this.madeAbsolute){this.position=n.position;for(let t of this.manager.tooltipViews)t.dom.style.position=this.position;i=!0}if(n.parent!=this.parent){this.parent&&this.container.remove(),this.parent=n.parent,this.createContainer();for(let t of this.manager.tooltipViews)this.container.appendChild(t.dom);i=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);i&&this.maybeMeasure()}createTooltip(t,e){let i=t.create(this.view),n=e?e.dom:null;if(i.dom.classList.add("cm-tooltip"),t.arrow&&!i.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",i.dom.insertBefore(t,n)}return i.dom.style.position=this.position,i.dom.style.top=Jr,i.dom.style.left="0px",this.container.insertBefore(i.dom,n),i.mount&&i.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(i.dom),i}destroy(){var t,e,i;this.view.win.removeEventListener("resize",this.measureSoon);for(let n of this.manager.tooltipViews)n.dom.remove(),null===(t=n.destroy)||void 0===t||t.call(n);this.parent&&this.container.remove(),null===(e=this.resizeObserver)||void 0===e||e.disconnect(),null===(i=this.intersectionObserver)||void 0===i||i.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let t=this.view.dom.getBoundingClientRect(),e=1,i=1,n=!1;if("fixed"==this.position&&this.manager.tooltipViews.length){let{dom:t}=this.manager.tooltipViews[0];if(Fe.gecko)n=t.offsetParent!=this.container.ownerDocument.body;else if(t.style.top==Jr&&"0px"==t.style.left){let e=t.getBoundingClientRect();n=Math.abs(e.top+1e4)>1||Math.abs(e.left)>1}}if(n||"absolute"==this.position)if(this.parent){let t=this.parent.getBoundingClientRect();t.width&&t.height&&(e=t.width/this.parent.offsetWidth,i=t.height/this.parent.offsetHeight)}else({scaleX:e,scaleY:i}=this.view.viewState);return{editor:t,parent:this.parent?this.container.getBoundingClientRect():t,pos:this.manager.tooltips.map(((t,e)=>{let i=this.manager.tooltipViews[e];return i.getCoords?i.getCoords(t.pos):this.view.coordsAtPos(t.pos)})),size:this.manager.tooltipViews.map((({dom:t})=>t.getBoundingClientRect())),space:this.view.state.facet(io).tooltipSpace(this.view),scaleX:e,scaleY:i,makeAbsolute:n}}writeMeasure(t){var e;if(t.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let t of this.manager.tooltipViews)t.dom.style.position="absolute"}let{editor:i,space:n,scaleX:s,scaleY:r}=t,o=[];for(let a=0;a<this.manager.tooltips.length;a++){let l=this.manager.tooltips[a],h=this.manager.tooltipViews[a],{dom:c}=h,f=t.pos[a],u=t.size[a];if(!f||f.bottom<=Math.max(i.top,n.top)||f.top>=Math.min(i.bottom,n.bottom)||f.right<Math.max(i.left,n.left)-.1||f.left>Math.min(i.right,n.right)+.1){c.style.top=Jr;continue}let d=l.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,O=d?7:0,p=u.right-u.left,g=null!==(e=no.get(h))&&void 0!==e?e:u.bottom-u.top,m=h.offset||oo,w=this.view.textDirection==ki.LTR,v=u.width>n.right-n.left?w?n.left:n.right-u.width:w?Math.min(f.left-(d?14:0)+m.x,n.right-p):Math.max(n.left,f.left-p+(d?14:0)-m.x),b=this.above[a];!l.strictSide&&(b?f.top-(u.bottom-u.top)-m.y<n.top:f.bottom+(u.bottom-u.top)+m.y>n.bottom)&&b==n.bottom-f.bottom>f.top-n.top&&(b=this.above[a]=!b);let y=(b?f.top-n.top:n.bottom-f.bottom)-O;if(y<g&&!1!==h.resize){if(y<this.view.defaultLineHeight){c.style.top=Jr;continue}no.set(h,g),c.style.height=(g=y)/r+"px"}else c.style.height&&(c.style.height="");let Q=b?f.top-g-O-m.y:f.bottom+O+m.y,k=v+p;if(!0!==h.overlap)for(let t of o)t.left<k&&t.right>v&&t.top<Q+g&&t.bottom>Q&&(Q=b?t.top-g-2-O:t.bottom+O+2);if("absolute"==this.position?(c.style.top=(Q-t.parent.top)/r+"px",c.style.left=(v-t.parent.left)/s+"px"):(c.style.top=Q/r+"px",c.style.left=v/s+"px"),d){let t=f.left+(w?m.x:-m.x)-(v+14-7);d.style.left=t/s+"px"}!0!==h.overlap&&o.push({left:v,top:Q,right:k,bottom:Q+g}),c.classList.toggle("cm-tooltip-above",b),c.classList.toggle("cm-tooltip-below",!b),h.positioned&&h.positioned(t.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let t of this.manager.tooltipViews)t.dom.style.top=Jr}},{eventObservers:{scroll(){this.maybeMeasure()}}}),ro=tr.baseTheme({".cm-tooltip":{zIndex:100,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:"14px",position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),oo={x:0,y:0},ao=G.define({enables:[so,ro]}),lo=G.define({combine:t=>t.reduce(((t,e)=>t.concat(e)),[])});class ho{static create(t){return new ho(t)}constructor(t){this.view=t,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new to(t,lo,((t,e)=>this.createHostedView(t,e)),(t=>t.dom.remove()))}createHostedView(t,e){let i=t.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,e?e.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(t){for(let e of this.manager.tooltipViews)e.mount&&e.mount(t);this.mounted=!0}positioned(t){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned(t)}update(t){this.manager.update(t)}destroy(){var t;for(let e of this.manager.tooltipViews)null===(t=e.destroy)||void 0===t||t.call(e)}passProp(t){let e;for(let i of this.manager.tooltipViews){let n=i[t];if(void 0!==n)if(void 0===e)e=n;else if(e!==n)return}return e}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}const co=ao.compute([lo],(t=>{let e=t.facet(lo);return 0===e.length?null:{pos:Math.min(...e.map((t=>t.pos))),end:Math.max(...e.map((t=>{var e;return null!==(e=t.end)&&void 0!==e?e:t.pos}))),create:ho.create,above:e[0].above,arrow:e.some((t=>t.arrow))}}));class fo{constructor(t,e,i,n,s){this.view=t,this.source=e,this.field=i,this.setHover=n,this.hoverTime=s,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:t.dom,time:0},this.checkHover=this.checkHover.bind(this),t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout((()=>this.startHover()),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let t=Date.now()-this.lastMove.time;t<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:t,lastMove:e}=this,i=t.docView.nearest(e.target);if(!i)return;let n,s=1;if(i instanceof ti)n=i.posAtStart;else{if(n=t.posAtCoords(e),null==n)return;let i=t.coordsAtPos(n);if(!i||e.y<i.top||e.y>i.bottom||e.x<i.left-t.defaultCharacterWidth||e.x>i.right+t.defaultCharacterWidth)return;let r=t.bidiSpans(t.state.doc.lineAt(n)).find((t=>t.from<=n&&t.to>=n)),o=r&&r.dir==ki.RTL?-1:1;s=e.x<i.left?-o:o}let r=this.source(t,n,s);if(null==r?void 0:r.then){let e=this.pending={pos:n};r.then((i=>{this.pending==e&&(this.pending=null,!i||Array.isArray(i)&&!i.length||t.dispatch({effects:this.setHover.of(Array.isArray(i)?i:[i])}))}),(e=>tn(t.state,e,"hover tooltip")))}else!r||Array.isArray(r)&&!r.length||t.dispatch({effects:this.setHover.of(Array.isArray(r)?r:[r])})}get tooltip(){let t=this.view.plugin(so),e=t?t.manager.tooltips.findIndex((t=>t.create==ho.create)):-1;return e>-1?t.manager.tooltipViews[e]:null}mousemove(t){var e,i;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:n,tooltip:s}=this;if(n.length&&s&&!function(t,e){let i=t.getBoundingClientRect();return e.clientX>=i.left-uo&&e.clientX<=i.right+uo&&e.clientY>=i.top-uo&&e.clientY<=i.bottom+uo}(s.dom,t)||this.pending){let{pos:s}=n[0]||this.pending,r=null!==(i=null===(e=n[0])||void 0===e?void 0:e.end)&&void 0!==i?i:s;(s==r?this.view.posAtCoords(this.lastMove)==s:function(t,e,i,n,s,r){let o=t.scrollDOM.getBoundingClientRect(),a=t.documentTop+t.documentPadding.top+t.contentHeight;if(o.left>n||o.right<n||o.top>s||Math.min(o.bottom,a)<s)return!1;let l=t.posAtCoords({x:n,y:s},!1);return l>=e&&l<=i}(this.view,s,r,t.clientX,t.clientY))||(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null)}}mouseleave(t){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:e}=this;if(e.length){let{tooltip:e}=this;e&&e.dom.contains(t.relatedTarget)?this.watchTooltipLeave(e.dom):this.view.dispatch({effects:this.setHover.of([])})}}watchTooltipLeave(t){let e=i=>{t.removeEventListener("mouseleave",e),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])})};t.addEventListener("mouseleave",e)}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}const uo=4;function Oo(t,e={}){let i=yt.define(),n=K.define({create:()=>[],update(t,n){if(t.length&&(e.hideOnChange&&(n.docChanged||n.selection)?t=[]:e.hideOn&&(t=t.filter((t=>!e.hideOn(n,t)))),n.docChanged)){let e=[];for(let i of t){let t=n.changes.mapPos(i.pos,-1,X.TrackDel);if(null!=t){let s=Object.assign(Object.create(null),i);s.pos=t,null!=s.end&&(s.end=n.changes.mapPos(s.end)),e.push(s)}}t=e}for(let e of n.effects)e.is(i)&&(t=e.value),e.is(go)&&(t=[]);return t},provide:t=>lo.from(t)});return[n,rn.define((s=>new fo(s,t,n,i,e.hoverTime||300))),co]}function po(t,e){let i=t.plugin(so);if(!i)return null;let n=i.manager.tooltips.indexOf(e);return n<0?null:i.manager.tooltipViews[n]}const go=yt.define(),mo=G.define({combine(t){let e,i;for(let n of t)e=e||n.topContainer,i=i||n.bottomContainer;return{topContainer:e,bottomContainer:i}}});function wo(t,e){let i=t.plugin(vo),n=i?i.specs.indexOf(e):-1;return n>-1?i.panels[n]:null}const vo=rn.fromClass(class{constructor(t){this.input=t.state.facet(Qo),this.specs=this.input.filter((t=>t)),this.panels=this.specs.map((e=>e(t)));let e=t.state.facet(mo);this.top=new bo(t,!0,e.topContainer),this.bottom=new bo(t,!1,e.bottomContainer),this.top.sync(this.panels.filter((t=>t.top))),this.bottom.sync(this.panels.filter((t=>!t.top)));for(let i of this.panels)i.dom.classList.add("cm-panel"),i.mount&&i.mount()}update(t){let e=t.state.facet(mo);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new bo(t.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new bo(t.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let i=t.state.facet(Qo);if(i!=this.input){let e=i.filter((t=>t)),n=[],s=[],r=[],o=[];for(let i of e){let e,a=this.specs.indexOf(i);a<0?(e=i(t.view),o.push(e)):(e=this.panels[a],e.update&&e.update(t)),n.push(e),(e.top?s:r).push(e)}this.specs=e,this.panels=n,this.top.sync(s),this.bottom.sync(r);for(let t of o)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}else for(let n of this.panels)n.update&&n.update(t)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:t=>tr.scrollMargins.of((e=>{let i=e.plugin(t);return i&&{top:i.top.scrollMargin(),bottom:i.bottom.scrollMargin()}}))});class bo{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(0==this.panels.length)return void(this.dom&&(this.dom.remove(),this.dom=void 0));if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=yo(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=yo(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(this.container&&this.classes!=this.view.themeClasses){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function yo(t){let e=t.nextSibling;return t.remove(),e}const Qo=G.define({enables:vo});class ko extends Yt{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}ko.prototype.elementClass="",ko.prototype.toDOM=void 0,ko.prototype.mapMode=X.TrackBefore,ko.prototype.startSide=ko.prototype.endSide=-1,ko.prototype.point=!0;const So=G.define(),xo={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>Dt.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},$o=G.define();function Po(t){return[Co(),$o.of(Object.assign(Object.assign({},xo),t))]}const Zo=G.define({combine:t=>t.some((t=>t))});function Co(t){let e=[To];return t&&!1===t.fixed&&e.push(Zo.of(!0)),e}const To=rn.fromClass(class{constructor(t){this.view=t,this.prevViewport=t.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=t.state.facet($o).map((e=>new Ro(t,e)));for(let e of this.gutters)this.dom.appendChild(e.dom);this.fixed=!t.state.facet(Zo),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),t.scrollDOM.insertBefore(this.dom,t.contentDOM)}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport,n=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(n<.8*(i.to-i.from))}t.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px"),this.view.state.facet(Zo)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;t&&this.dom.remove();let i=Dt.iter(this.view.state.facet(So),this.view.viewport.from),n=[],s=this.gutters.map((t=>new Wo(t,this.view.viewport,-this.view.documentPadding.top)));for(let r of this.view.viewportLineBlocks)if(n.length&&(n=[]),Array.isArray(r.type)){let t=!0;for(let e of r.type)if(e.type==di.Text&&t){Xo(i,n,e.from);for(let t of s)t.line(this.view,e,n);t=!1}else if(e.widget)for(let t of s)t.widget(this.view,e)}else if(r.type==di.Text){Xo(i,n,r.from);for(let t of s)t.line(this.view,r,n)}else if(r.widget)for(let t of s)t.widget(this.view,r);for(let r of s)r.finish();t&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(t){let e=t.startState.facet($o),i=t.state.facet($o),n=t.docChanged||t.heightChanged||t.viewportChanged||!Dt.eq(t.startState.facet(So),t.state.facet(So),t.view.viewport.from,t.view.viewport.to);if(e==i)for(let s of this.gutters)s.update(t)&&(n=!0);else{n=!0;let s=[];for(let n of i){let i=e.indexOf(n);i<0?s.push(new Ro(this.view,n)):(this.gutters[i].update(t),s.push(this.gutters[i]))}for(let t of this.gutters)t.dom.remove(),s.indexOf(t)<0&&t.destroy();for(let t of s)this.dom.appendChild(t.dom);this.gutters=s}return n}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove()}},{provide:t=>tr.scrollMargins.of((e=>{let i=e.plugin(t);return i&&0!=i.gutters.length&&i.fixed?e.textDirection==ki.LTR?{left:i.dom.offsetWidth*e.scaleX}:{right:i.dom.offsetWidth*e.scaleX}:null}))});function Ao(t){return Array.isArray(t)?t:[t]}function Xo(t,e,i){for(;t.value&&t.from<=i;)t.from==i&&e.push(t.value),t.next()}class Wo{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=Dt.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:n}=this,s=(e.top-this.height)/t.scaleY,r=e.height/t.scaleY;if(this.i==n.elements.length){let e=new Yo(t,r,s,i);n.elements.push(e),n.dom.appendChild(e.dom)}else n.elements[this.i].update(t,r,s,i);this.height=e.bottom,this.i++}line(t,e,i){let n=[];Xo(this.cursor,n,e.from),i.length&&(n=n.concat(i));let s=this.gutter.config.lineMarker(t,e,n);s&&n.unshift(s);let r=this.gutter;(0!=n.length||r.config.renderEmptyElements)&&this.addElement(t,e,n)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e);i&&this.addElement(t,e,[i])}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class Ro{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,(n=>{let s,r=n.target;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let t=r.getBoundingClientRect();s=(t.top+t.bottom)/2}else s=n.clientY;let o=t.lineBlockAtHeight(s-t.documentTop);e.domEventHandlers[i](t,o,n)&&n.preventDefault()}));this.markers=Ao(e.markers(t)),e.initialSpacer&&(this.spacer=new Yo(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=Ao(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);e!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!Dt.eq(this.markers,e,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(t)}destroy(){for(let t of this.elements)t.destroy()}}class Yo{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,n)}update(t,e,i,n){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),function(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return!1;return!0}(this.markers,n)||this.setMarkers(t,n)}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let s=0,r=0;;){let o=r,a=s<e.length?e[s++]:null,l=!1;if(a){let t=a.elementClass;t&&(i+=" "+t);for(let e=r;e<this.markers.length;e++)if(this.markers[e].compare(a)){o=e,l=!0;break}}else o=this.markers.length;for(;r<o;){let t=this.markers[r++];if(t.toDOM){t.destroy(n);let e=n.nextSibling;n.remove(),n=e}}if(!a)break;a.toDOM&&(l?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),l&&r++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}const qo=G.define(),Mo=G.define({combine:t=>Rt(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let n in e){let t=i[n],s=e[n];i[n]=t?(e,i,n)=>t(e,i,n)||s(e,i,n):s}return i}})});class Eo extends ko{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function Do(t,e){return t.state.facet(Mo).formatNumber(e,t.state)}const jo=$o.compute([Mo],(t=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers:t=>t.state.facet(qo),lineMarker:(t,e,i)=>i.some((t=>t.toDOM))?null:new Eo(Do(t,t.state.doc.lineAt(e.from).number)),widgetMarker:()=>null,lineMarkerChange:t=>t.startState.facet(Mo)!=t.state.facet(Mo),initialSpacer:t=>new Eo(Do(t,Vo(t.state.doc.lines))),updateSpacer(t,e){let i=Do(e.view,Vo(e.view.state.doc.lines));return i==t.number?t:new Eo(i)},domEventHandlers:t.facet(Mo).domEventHandlers})));function _o(t={}){return[Mo.of(t),Co(),jo]}function Vo(t){let e=9;for(;e<t;)e=10*e+9;return e}const Bo=new class extends ko{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},zo=So.compute(["selection"],(t=>{let e=[],i=-1;for(let n of t.selection.ranges){let s=t.doc.lineAt(n.head).from;s>i&&(i=s,e.push(Bo.range(s)))}return Dt.of(e)}));const Go=1024;let Io=0;class Lo{constructor(t,e){this.from=t,this.to=e}}class No{constructor(t={}){this.id=Io++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof t&&(t=Fo.match(t)),e=>{let i=t(e);return void 0===i?null:[this,i]}}}No.closedBy=new No({deserialize:t=>t.split(" ")}),No.openedBy=new No({deserialize:t=>t.split(" ")}),No.group=new No({deserialize:t=>t.split(" ")}),No.isolate=new No({deserialize:t=>{if(t&&"rtl"!=t&&"ltr"!=t&&"auto"!=t)throw new RangeError("Invalid value for isolate: "+t);return t||"auto"}}),No.contextHash=new No({perNode:!0}),No.lookAhead=new No({perNode:!0}),No.mounted=new No({perNode:!0});class Uo{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i}static get(t){return t&&t.props&&t.props[No.mounted.id]}}const Ho=Object.create(null);class Fo{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n}static define(t){let e=t.props&&t.props.length?Object.create(null):Ho,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(null==t.name?8:0),n=new Fo(t.name||"",e,t.id,i);if(t.props)for(let s of t.props)if(Array.isArray(s)||(s=s(n)),s){if(s[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[s[0].id]=s[1]}return n}prop(t){return this.props[t.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(t){if("string"==typeof t){if(this.name==t)return!0;let e=this.prop(No.group);return!!e&&e.indexOf(t)>-1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return t=>{for(let i=t.prop(No.group),n=-1;n<(i?i.length:0);n++){let s=e[n<0?t.name:i[n]];if(s)return s}}}}Fo.none=new Fo("",Object.create(null),0,8);class Ko{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let e of t){let t=e(i);t&&(n||(n=Object.assign({},i.props)),n[t[0].id]=t[1])}e.push(n?new Fo(i.name,n,i.id,i.flags):i)}return new Ko(e)}}const Jo=new WeakMap,ta=new WeakMap;var ea,ia;(ia=ea||(ea={}))[ia.ExcludeBuffers=1]="ExcludeBuffers",ia[ia.IncludeAnonymous=2]="IncludeAnonymous",ia[ia.IgnoreMounts=4]="IgnoreMounts",ia[ia.IgnoreOverlays=8]="IgnoreOverlays";class na{constructor(t,e,i,n,s){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,s&&s.length){this.props=Object.create(null);for(let[t,e]of s)this.props["number"==typeof t?t:t.id]=e}}toString(){let t=Uo.get(this);if(t&&!t.overlay)return t.tree.toString();let e="";for(let i of this.children){let t=i.toString();t&&(e&&(e+=","),e+=t)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t=0){return new ga(this.topNode,t)}cursorAt(t,e=0,i=0){let n=Jo.get(this)||this.topNode,s=new ga(n);return s.moveTo(t,e),Jo.set(this,s._tree),s}get topNode(){return new ha(this,0,0,null)}resolve(t,e=0){let i=aa(Jo.get(this)||this.topNode,t,e,!1);return Jo.set(this,i),i}resolveInner(t,e=0){let i=aa(ta.get(this)||this.topNode,t,e,!0);return ta.set(this,i),i}resolveStack(t,e=0){return function(t,e,i){let n=t.resolveInner(e,i),s=null;for(let r=n instanceof ha?n:n.context.parent;r;r=r.parent)if(r.index<0){let t=r.parent;(s||(s=[n])).push(t.resolve(e,i)),r=t}else{let t=Uo.get(r.tree);if(t&&t.overlay&&t.overlay[0].from<=e&&t.overlay[t.overlay.length-1].to>=e){let o=new ha(t.tree,t.overlay[0].from+r.from,-1,r);(s||(s=[n])).push(aa(o,e,i,!1))}}return s?Oa(s):n}(this,t,e)}iterate(t){let{enter:e,leave:i,from:n=0,to:s=this.length}=t,r=t.mode||0,o=(r&ea.IncludeAnonymous)>0;for(let a=this.cursor(r|ea.IncludeAnonymous);;){let t=!1;if(a.from<=s&&a.to>=n&&(!o&&a.type.isAnonymous||!1!==e(a))){if(a.firstChild())continue;t=!0}for(;t&&i&&(o||!a.type.isAnonymous)&&i(a),!a.nextSibling();){if(!a.parent())return;t=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:ba(Fo.none,this.children,this.positions,0,this.children.length,0,this.length,((t,e,i)=>new na(this.type,t,e,i,this.propValues)),t.makeTree||((t,e,i)=>new na(Fo.none,t,e,i)))}static build(t){return function(t){var e;let{buffer:i,nodeSet:n,maxBufferLength:s=Go,reused:r=[],minRepeatType:o=n.types.length}=t,a=Array.isArray(i)?new sa(i,i.length):i,l=n.types,h=0,c=0;function f(t,e,i,w,v,b){let{id:y,start:Q,end:k,size:S}=a,x=c;for(;S<0;){if(a.next(),-1==S){let e=r[y];return i.push(e),void w.push(Q-t)}if(-3==S)return void(h=y);if(-4==S)return void(c=y);throw new RangeError(`Unrecognized record size: ${S}`)}let $,P,Z=l[y],C=Q-t;if(k-Q<=s&&(P=g(a.pos-e,v))){let e=new Uint16Array(P.size-P.skip),i=a.pos-P.size,s=e.length;for(;a.pos>i;)s=m(P.start,e,s);$=new ra(e,k-P.start,n),C=P.start-t}else{let t=a.pos-S;a.next();let e=[],i=[],n=y>=o?y:-1,r=0,l=k;for(;a.pos>t;)n>=0&&a.id==n&&a.size>=0?(a.end<=l-s&&(O(e,i,Q,r,a.end,l,n,x),r=e.length,l=a.end),a.next()):b>2500?u(Q,t,e,i):f(Q,t,e,i,n,b+1);if(n>=0&&r>0&&r<e.length&&O(e,i,Q,r,Q,l,n,x),e.reverse(),i.reverse(),n>-1&&r>0){let t=d(Z);$=ba(Z,e,i,0,e.length,0,k-Q,t,t)}else $=p(Z,e,i,k-Q,x-k)}i.push($),w.push(C)}function u(t,e,i,r){let o=[],l=0,h=-1;for(;a.pos>e;){let{id:t,start:e,end:i,size:n}=a;if(n>4)a.next();else{if(h>-1&&e<h)break;h<0&&(h=i-s),o.push(t,e,i),l++,a.next()}}if(l){let e=new Uint16Array(4*l),s=o[o.length-2];for(let t=o.length-3,i=0;t>=0;t-=3)e[i++]=o[t],e[i++]=o[t+1]-s,e[i++]=o[t+2]-s,e[i++]=i;i.push(new ra(e,o[2]-s,n)),r.push(s-t)}}function d(t){return(e,i,n)=>{let s,r,o=0,a=e.length-1;if(a>=0&&(s=e[a])instanceof na){if(!a&&s.type==t&&s.length==n)return s;(r=s.prop(No.lookAhead))&&(o=i[a]+s.length+r)}return p(t,e,i,n,o)}}function O(t,e,i,s,r,o,a,l){let h=[],c=[];for(;t.length>s;)h.push(t.pop()),c.push(e.pop()+i-r);t.push(p(n.types[a],h,c,o-r,l-o)),e.push(r-i)}function p(t,e,i,n,s=0,r){if(h){let t=[No.contextHash,h];r=r?[t].concat(r):[t]}if(s>25){let t=[No.lookAhead,s];r=r?[t].concat(r):[t]}return new na(t,e,i,n,r)}function g(t,e){let i=a.fork(),n=0,r=0,l=0,h=i.end-s,c={size:0,start:0,skip:0};t:for(let s=i.pos-t;i.pos>s;){let t=i.size;if(i.id==e&&t>=0){c.size=n,c.start=r,c.skip=l,l+=4,n+=4,i.next();continue}let a=i.pos-t;if(t<0||a<s||i.start<h)break;let f=i.id>=o?4:0,u=i.start;for(i.next();i.pos>a;){if(i.size<0){if(-3!=i.size)break t;f+=4}else i.id>=o&&(f+=4);i.next()}r=u,n+=t,l+=f}return(e<0||n==t)&&(c.size=n,c.start=r,c.skip=l),c.size>4?c:void 0}function m(t,e,i){let{id:n,start:s,end:r,size:l}=a;if(a.next(),l>=0&&n<o){let o=i;if(l>4){let n=a.pos-(l-4);for(;a.pos>n;)i=m(t,e,i)}e[--i]=o,e[--i]=r-t,e[--i]=s-t,e[--i]=n}else-3==l?h=n:-4==l&&(c=n);return i}let w=[],v=[];for(;a.pos>0;)f(t.start||0,t.bufferStart||0,w,v,-1,0);let b=null!==(e=t.length)&&void 0!==e?e:w.length?v[0]+w[0].length:0;return new na(l[t.topID],w.reverse(),v.reverse(),b)}(t)}}na.empty=new na(Fo.none,[],[],0);class sa{constructor(t,e){this.buffer=t,this.index=e}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new sa(this.buffer,this.index)}}class ra{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i}get type(){return Fo.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],s=n.name;if(/\W/.test(s)&&!n.isError&&(s=JSON.stringify(s)),i==(t+=4))return s;let r=[];for(;t<i;)r.push(this.childString(t)),t=this.buffer[t+3];return s+"("+r.join(",")+")"}findChild(t,e,i,n,s){let{buffer:r}=this,o=-1;for(let a=t;a!=e&&!(oa(s,n,r[a+1],r[a+2])&&(o=a,i>0));a=r[a+3]);return o}slice(t,e,i){let n=this.buffer,s=new Uint16Array(e-t),r=0;for(let o=t,a=0;o<e;){s[a++]=n[o++],s[a++]=n[o++]-i;let e=s[a++]=n[o++]-i;s[a++]=n[o++]-t,r=Math.max(r,e)}return new ra(s,r,this.set)}}function oa(t,e,i,n){switch(t){case-2:return i<e;case-1:return n>=e&&i<e;case 0:return i<e&&n>e;case 1:return i<=e&&n>e;case 2:return n>e;case 4:return!0}}function aa(t,e,i,n){for(var s;t.from==t.to||(i<1?t.from>=e:t.from>e)||(i>-1?t.to<=e:t.to<e);){let e=!n&&t instanceof ha&&t.index<0?null:t.parent;if(!e)return t;t=e}let r=n?0:ea.IgnoreOverlays;if(n)for(let o=t,a=o.parent;a;o=a,a=o.parent)o instanceof ha&&o.index<0&&(null===(s=a.enter(e,i,r))||void 0===s?void 0:s.from)!=o.from&&(t=a);for(;;){let n=t.enter(e,i,r);if(!n)return t;t=n}}class la{cursor(t=0){return new ga(this,t)}getChild(t,e=null,i=null){let n=ca(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return ca(this,t,e,i)}resolve(t,e=0){return aa(this,t,e,!1)}resolveInner(t,e=0){return aa(this,t,e,!0)}matchContext(t){return fa(this,t)}enterUnfinishedNodesBefore(t){let e=this.childBefore(t),i=this;for(;e;){let t=e.lastChild;if(!t||t.to!=e.to)break;t.type.isError&&t.from==t.to?(i=e,e=t.prevSibling):e=t}return i}get node(){return this}get next(){return this.parent}}class ha extends la{constructor(t,e,i,n){super(),this._tree=t,this.from=e,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,e,i,n,s=0){for(let r=this;;){for(let{children:o,positions:a}=r._tree,l=e>0?o.length:-1;t!=l;t+=e){let l=o[t],h=a[t]+r.from;if(oa(n,i,h,h+l.length))if(l instanceof ra){if(s&ea.ExcludeBuffers)continue;let o=l.findChild(0,l.buffer.length,e,i-h,n);if(o>-1)return new da(new ua(r,l,t,h),null,o)}else if(s&ea.IncludeAnonymous||!l.type.isAnonymous||ma(l)){let o;if(!(s&ea.IgnoreMounts)&&(o=Uo.get(l))&&!o.overlay)return new ha(o.tree,h,t,r);let a=new ha(l,h,t,r);return s&ea.IncludeAnonymous||!a.type.isAnonymous?a:a.nextChild(e<0?l.children.length-1:0,e,i,n)}}if(s&ea.IncludeAnonymous||!r.type.isAnonymous)return null;if(t=r.index>=0?r.index+e:e<0?-1:r._parent._tree.children.length,r=r._parent,!r)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,e,i=0){let n;if(!(i&ea.IgnoreOverlays)&&(n=Uo.get(this._tree))&&n.overlay){let i=t-this.from;for(let{from:t,to:s}of n.overlay)if((e>0?t<=i:t<i)&&(e<0?s>=i:s>i))return new ha(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,i)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function ca(t,e,i,n){let s=t.cursor(),r=[];if(!s.firstChild())return r;if(null!=i)for(let o=!1;!o;)if(o=s.type.is(i),!s.nextSibling())return r;for(;;){if(null!=n&&s.type.is(n))return r;if(s.type.is(e)&&r.push(s.node),!s.nextSibling())return null==n?r:[]}}function fa(t,e,i=e.length-1){for(let n=t.parent;i>=0;n=n.parent){if(!n)return!1;if(!n.type.isAnonymous){if(e[i]&&e[i]!=n.name)return!1;i--}}return!0}class ua{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n}}class da extends la{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,e,i){super(),this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]]}child(t,e,i){let{buffer:n}=this.context,s=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return s<0?null:new da(this.context,this,s)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i=0){if(i&ea.ExcludeBuffers)return null;let{buffer:n}=this.context,s=n.findChild(this.index+4,n.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return s<0?null:new da(this.context,this,s)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new da(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new da(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,s=i.buffer[this.index+3];if(s>n){let r=i.buffer[this.index+1];t.push(i.slice(n,s,r)),e.push(0)}return new na(this.type,t,e,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function Oa(t){if(!t.length)return null;let e=0,i=t[0];for(let r=1;r<t.length;r++){let n=t[r];(n.from>i.from||n.to<i.to)&&(i=n,e=r)}let n=i instanceof ha&&i.index<0?null:i.parent,s=t.slice();return n?s[e]=n:s.splice(e,1),new pa(s,i)}class pa{constructor(t,e){this.heads=t,this.node=e}get next(){return Oa(this.heads)}}class ga{get name(){return this.type.name}constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof ha)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let e=t._parent;e;e=e._parent)this.stack.unshift(e.index);this.bufferNode=t,this.yieldBuf(t.index)}}yieldNode(t){return!!t&&(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0)}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return!!t&&(t instanceof ha?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,s=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return!(s<0)&&(this.stack.push(this.index),this.yieldBuf(s))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=this.mode){return this.buffer?!(i&ea.ExcludeBuffers)&&this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&ea.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&ea.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode));let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let t=i<0?0:this.stack[i]+4;if(this.index!=t)return this.yieldBuf(e.findChild(t,this.index,-1,0,4))}else{let t=e.buffer[this.index+3];if(t<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(t)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return!1}else for(let t=0;t<this.index;t++)if(n.buffer.buffer[t+3]<this.index)return!1;({index:e,parent:i}=n)}else({index:e,_parent:i}=this._tree);for(;i;({index:e,_parent:i}=i))if(e>-1)for(let n=e+t,s=t<0?-1:i._tree.children.length;n!=s;n+=t){let t=i._tree.children[n];if(this.mode&ea.IncludeAnonymous||t instanceof ra||!t.type.isAnonymous||ma(t))return!1}return!0}move(t,e){if(e&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,s=this.stack.length;s>=0;){for(let r=t;r;r=r._parent)if(r.index==n){if(n==this.index)return r;e=r,i=s+1;break t}n=this.stack[--s]}for(let n=i;n<this.stack.length;n++)e=new da(this.buffer,e,this.stack[n]);return this.bufferNode=new da(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,e){for(let i=0;;){let n=!1;if(this.type.isAnonymous||!1!==t(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;n&&e&&e(this),n=this.type.isAnonymous,!this.nextSibling();){if(!i)return;this.parent(),i--,n=!0}}}matchContext(t){if(!this.buffer)return fa(this.node,t);let{buffer:e}=this.buffer,{types:i}=e.set;for(let n=t.length-1,s=this.stack.length-1;n>=0;s--){if(s<0)return fa(this.node,t,n);let r=i[e.buffer[this.stack[s]]];if(!r.isAnonymous){if(t[n]&&t[n]!=r.name)return!1;n--}}return!0}}function ma(t){return t.children.some((t=>t instanceof ra||!t.type.isAnonymous||ma(t)))}const wa=new WeakMap;function va(t,e){if(!t.isAnonymous||e instanceof ra||e.type!=t)return 1;let i=wa.get(e);if(null==i){i=1;for(let n of e.children){if(n.type!=t||!(n instanceof na)){i=1;break}i+=va(t,n)}wa.set(e,i)}return i}function ba(t,e,i,n,s,r,o,a,l){let h=0;for(let d=n;d<s;d++)h+=va(t,e[d]);let c=Math.ceil(1.5*h/8),f=[],u=[];return function e(i,n,s,o,a){for(let h=s;h<o;){let s=h,d=n[h],O=va(t,i[h]);for(h++;h<o;h++){let e=va(t,i[h]);if(O+e>=c)break;O+=e}if(h==s+1){if(O>c){let t=i[s];e(t.children,t.positions,0,t.children.length,n[s]+a);continue}f.push(i[s])}else{let e=n[h-1]+i[h-1].length-d;f.push(ba(t,i,n,s,h,d,e,null,l))}u.push(d+a-r)}}(e,i,n,s,0),(a||l)(f,u,o)}class ya{constructor(){this.map=new WeakMap}setBuffer(t,e,i){let n=this.map.get(t);n||this.map.set(t,n=new Map),n.set(e,i)}getBuffer(t,e){let i=this.map.get(t);return i&&i.get(e)}set(t,e){t instanceof da?this.setBuffer(t.context.buffer,t.index,e):t instanceof ha&&this.map.set(t.tree,e)}get(t){return t instanceof da?this.getBuffer(t.context.buffer,t.index):t instanceof ha?this.map.get(t.tree):void 0}cursorSet(t,e){t.buffer?this.setBuffer(t.buffer.buffer,t.index,e):this.map.set(t.tree,e)}cursorGet(t){return t.buffer?this.getBuffer(t.buffer.buffer,t.index):this.map.get(t.tree)}}class Qa{constructor(t,e,i,n,s=!1,r=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(s?1:0)|(r?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(t,e=[],i=!1){let n=[new Qa(0,t.length,t,0,!1,i)];for(let s of e)s.to>t.length&&n.push(s);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],s=1,r=t.length?t[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<e.length?e[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;r&&r.from<c;){let e=r;if(a>=e.from||c<=e.to||l){let t=Math.max(e.from,a)-l,i=Math.min(e.to,c)-l;e=t>=i?null:new Qa(t,i,e.tree,e.offset+l,o>0,!!h)}if(e&&n.push(e),r.to>c)break;r=s<t.length?t[s++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return n}}class ka{startParse(t,e,i){return"string"==typeof t&&(t=new Sa(t)),i=i?i.length?i.map((t=>new Lo(t.from,t.to))):[new Lo(0,0)]:[new Lo(0,t.length)],this.createParse(t,e||[],i)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let t=n.advance();if(t)return t}}}class Sa{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,e){return this.string.slice(t,e)}}new No({perNode:!0});let xa=0;class $a{constructor(t,e,i){this.set=t,this.base=e,this.modified=i,this.id=xa++}static define(t){if(null==t?void 0:t.base)throw new Error("Can not derive from a modified tag");let e=new $a([],null,[]);if(e.set.push(e),t)for(let i of t.set)e.set.push(i);return e}static defineModifier(){let t=new Za;return e=>e.modified.indexOf(t)>-1?e:Za.get(e.base||e,e.modified.concat(t).sort(((t,e)=>t.id-e.id)))}}let Pa=0;class Za{constructor(){this.instances=[],this.id=Pa++}static get(t,e){if(!e.length)return t;let i=e[0].instances.find((i=>{return i.base==t&&(n=e,s=i.modified,n.length==s.length&&n.every(((t,e)=>t==s[e])));var n,s}));if(i)return i;let n=[],s=new $a(n,t,e);for(let o of e)o.instances.push(s);let r=function(t){let e=[[]];for(let i=0;i<t.length;i++)for(let n=0,s=e.length;n<s;n++)e.push(e[n].concat(t[i]));return e.sort(((t,e)=>e.length-t.length))}(e);for(let o of t.set)if(!o.modified.length)for(let t of r)n.push(Za.get(o,t));return s}}function Ca(t){let e=Object.create(null);for(let i in t){let n=t[i];Array.isArray(n)||(n=[n]);for(let t of i.split(" "))if(t){let i=[],s=2,r=t;for(let e=0;;){if("..."==r&&e>0&&e+3==t.length){s=1;break}let n=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(r);if(!n)throw new RangeError("Invalid path: "+t);if(i.push("*"==n[0]?"":'"'==n[0][0]?JSON.parse(n[0]):n[0]),e+=n[0].length,e==t.length)break;let o=t[e++];if(e==t.length&&"!"==o){s=0;break}if("/"!=o)throw new RangeError("Invalid path: "+t);r=t.slice(e)}let o=i.length-1,a=i[o];if(!a)throw new RangeError("Invalid path: "+t);let l=new Aa(n,s,o>0?i.slice(0,o):null);e[a]=l.sort(e[a])}}return Ta.add(e)}const Ta=new No;class Aa{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}function Xa(t,e){let i=Object.create(null);for(let r of t)if(Array.isArray(r.tag))for(let t of r.tag)i[t.id]=r.class;else i[r.tag.id]=r.class;let{scope:n,all:s=null}=e||{};return{style:t=>{let e=s;for(let n of t)for(let t of n.set){let n=i[t.id];if(n){e=e?e+" "+n:n;break}}return e},scope:n}}function Wa(t,e,i,n=0,s=t.length){let r=new Ra(n,Array.isArray(e)?e:[e],i);r.highlightRange(t.cursor(),n,s,"",r.highlighters),r.flush(s)}Aa.empty=new Aa([],2,null);class Ra{constructor(t,e,i){this.at=t,this.highlighters=e,this.span=i,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,i,n,s){let{type:r,from:o,to:a}=t;if(o>=i||a<=e)return;r.isTop&&(s=this.highlighters.filter((t=>!t.scope||t.scope(r))));let l=n,h=function(t){let e=t.type.prop(Ta);for(;e&&e.context&&!t.matchContext(e.context);)e=e.next;return e||null}(t)||Aa.empty,c=function(t,e){let i=null;for(let n of t){let t=n.style(e);t&&(i=i?i+" "+t:t)}return i}(s,h.tags);if(c&&(l&&(l+=" "),l+=c,1==h.mode&&(n+=(n?" ":"")+c)),this.startSpan(Math.max(e,o),l),h.opaque)return;let f=t.tree&&t.tree.prop(No.mounted);if(f&&f.overlay){let r=t.node.enter(f.overlay[0].from+o,1),h=this.highlighters.filter((t=>!t.scope||t.scope(f.tree.type))),c=t.firstChild();for(let u=0,d=o;;u++){let O=u<f.overlay.length?f.overlay[u]:null,p=O?O.from+o:a,g=Math.max(e,d),m=Math.min(i,p);if(g<m&&c)for(;t.from<m&&(this.highlightRange(t,g,m,n,s),this.startSpan(Math.min(m,t.to),l),!(t.to>=p)&&t.nextSibling()););if(!O||p>i)break;d=O.to+o,d>e&&(this.highlightRange(r.cursor(),Math.max(e,O.from+o),Math.min(i,d),"",h),this.startSpan(Math.min(i,d),l))}c&&t.parent()}else if(t.firstChild()){f&&(n="");do{if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,n,s),this.startSpan(Math.min(i,t.to),l)}}while(t.nextSibling());t.parent()}}}const Ya=$a.define,qa=Ya(),Ma=Ya(),Ea=Ya(Ma),Da=Ya(Ma),ja=Ya(),_a=Ya(ja),Va=Ya(ja),Ba=Ya(),za=Ya(Ba),Ga=Ya(),Ia=Ya(),La=Ya(),Na=Ya(La),Ua=Ya(),Ha={comment:qa,lineComment:Ya(qa),blockComment:Ya(qa),docComment:Ya(qa),name:Ma,variableName:Ya(Ma),typeName:Ea,tagName:Ya(Ea),propertyName:Da,attributeName:Ya(Da),className:Ya(Ma),labelName:Ya(Ma),namespace:Ya(Ma),macroName:Ya(Ma),literal:ja,string:_a,docString:Ya(_a),character:Ya(_a),attributeValue:Ya(_a),number:Va,integer:Ya(Va),float:Ya(Va),bool:Ya(ja),regexp:Ya(ja),escape:Ya(ja),color:Ya(ja),url:Ya(ja),keyword:Ga,self:Ya(Ga),null:Ya(Ga),atom:Ya(Ga),unit:Ya(Ga),modifier:Ya(Ga),operatorKeyword:Ya(Ga),controlKeyword:Ya(Ga),definitionKeyword:Ya(Ga),moduleKeyword:Ya(Ga),operator:Ia,derefOperator:Ya(Ia),arithmeticOperator:Ya(Ia),logicOperator:Ya(Ia),bitwiseOperator:Ya(Ia),compareOperator:Ya(Ia),updateOperator:Ya(Ia),definitionOperator:Ya(Ia),typeOperator:Ya(Ia),controlOperator:Ya(Ia),punctuation:La,separator:Ya(La),bracket:Na,angleBracket:Ya(Na),squareBracket:Ya(Na),paren:Ya(Na),brace:Ya(Na),content:Ba,heading:za,heading1:Ya(za),heading2:Ya(za),heading3:Ya(za),heading4:Ya(za),heading5:Ya(za),heading6:Ya(za),contentSeparator:Ya(Ba),list:Ya(Ba),quote:Ya(Ba),emphasis:Ya(Ba),strong:Ya(Ba),link:Ya(Ba),monospace:Ya(Ba),strikethrough:Ya(Ba),inserted:Ya(),deleted:Ya(),changed:Ya(),invalid:Ya(),meta:Ua,documentMeta:Ya(Ua),annotation:Ya(Ua),processingInstruction:Ya(Ua),definition:$a.defineModifier(),constant:$a.defineModifier(),function:$a.defineModifier(),standard:$a.defineModifier(),local:$a.defineModifier(),special:$a.defineModifier()};var Fa;Xa([{tag:Ha.link,class:"tok-link"},{tag:Ha.heading,class:"tok-heading"},{tag:Ha.emphasis,class:"tok-emphasis"},{tag:Ha.strong,class:"tok-strong"},{tag:Ha.keyword,class:"tok-keyword"},{tag:Ha.atom,class:"tok-atom"},{tag:Ha.bool,class:"tok-bool"},{tag:Ha.url,class:"tok-url"},{tag:Ha.labelName,class:"tok-labelName"},{tag:Ha.inserted,class:"tok-inserted"},{tag:Ha.deleted,class:"tok-deleted"},{tag:Ha.literal,class:"tok-literal"},{tag:Ha.string,class:"tok-string"},{tag:Ha.number,class:"tok-number"},{tag:[Ha.regexp,Ha.escape,Ha.special(Ha.string)],class:"tok-string2"},{tag:Ha.variableName,class:"tok-variableName"},{tag:Ha.local(Ha.variableName),class:"tok-variableName tok-local"},{tag:Ha.definition(Ha.variableName),class:"tok-variableName tok-definition"},{tag:Ha.special(Ha.variableName),class:"tok-variableName2"},{tag:Ha.definition(Ha.propertyName),class:"tok-propertyName tok-definition"},{tag:Ha.typeName,class:"tok-typeName"},{tag:Ha.namespace,class:"tok-namespace"},{tag:Ha.className,class:"tok-className"},{tag:Ha.macroName,class:"tok-macroName"},{tag:Ha.propertyName,class:"tok-propertyName"},{tag:Ha.operator,class:"tok-operator"},{tag:Ha.comment,class:"tok-comment"},{tag:Ha.meta,class:"tok-meta"},{tag:Ha.invalid,class:"tok-invalid"},{tag:Ha.punctuation,class:"tok-punctuation"}]);const Ka=new No;function Ja(t){return G.define({combine:t?e=>e.concat(t):void 0})}const tl=new No;class el{constructor(t,e,i=[],n=""){this.data=t,this.name=n,Wt.prototype.hasOwnProperty("tree")||Object.defineProperty(Wt.prototype,"tree",{get(){return sl(this)}}),this.parser=e,this.extension=[dl.of(this),Wt.languageData.of(((t,e,i)=>{let n=il(t,e,i),s=n.type.prop(Ka);if(!s)return[];let r=t.facet(s),o=n.type.prop(tl);if(o){let s=n.resolve(e-n.from,i);for(let e of o)if(e.test(s,t)){let i=t.facet(e.facet);return"replace"==e.type?i:i.concat(r)}}return r}))].concat(i)}isActiveAt(t,e,i=-1){return il(t,e,i).type.prop(Ka)==this.data}findRegions(t){let e=t.facet(dl);if((null==e?void 0:e.data)==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let i=[],n=(t,e)=>{if(t.prop(Ka)==this.data)return void i.push({from:e,to:e+t.length});let s=t.prop(No.mounted);if(s){if(s.tree.prop(Ka)==this.data){if(s.overlay)for(let t of s.overlay)i.push({from:t.from+e,to:t.to+e});else i.push({from:e,to:e+t.length});return}if(s.overlay){let t=i.length;if(n(s.tree,s.overlay[0].from+e),i.length>t)return}}for(let i=0;i<t.children.length;i++){let s=t.children[i];s instanceof na&&n(s,t.positions[i]+e)}};return n(sl(t),0),i}get allowsNesting(){return!0}}function il(t,e,i){let n=t.facet(dl),s=sl(t).topNode;if(!n||n.allowsNesting)for(let r=s;r;r=r.enter(e,i,ea.ExcludeBuffers))r.type.isTop&&(s=r);return s}el.setState=yt.define();class nl extends el{constructor(t,e,i){super(t,e,[],i),this.parser=e}static define(t){let e=Ja(t.languageData);return new nl(e,t.parser.configure({props:[Ka.add((t=>t.isTop?e:void 0))]}),t.name)}configure(t,e){return new nl(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function sl(t){let e=t.field(el.state,!1);return e?e.tree:na.empty}class rl{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let ol=null;class al{constructor(t,e,i=[],n,s,r,o,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=s,this.viewport=r,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(t,e,i){return new al(t,e,[],na.empty,0,i,[],null)}startParse(){return this.parser.startParse(new rl(this.state.doc),this.fragments)}work(t,e){return null!=e&&e>=this.state.doc.length&&(e=void 0),this.tree!=na.empty&&this.isDone(null!=e?e:this.state.doc.length)?(this.takeTree(),!0):this.withContext((()=>{var i;if("number"==typeof t){let e=Date.now()+t;t=()=>Date.now()>e}for(this.parse||(this.parse=this.startParse()),null!=e&&(null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let n=this.parse.advance();if(n){if(this.fragments=this.withoutTempSkipped(Qa.addTree(n,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=n,this.parse=null,!(this.treeLen<(null!=e?e:this.state.doc.length)))return!0;this.parse=this.startParse()}if(t())return!1}}))}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext((()=>{for(;!(e=this.parse.advance()););})),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(Qa.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=ol;ol=this;try{return t()}finally{ol=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=ll(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:s,viewport:r,skipped:o}=this;if(this.takeTree(),!t.empty){let e=[];if(t.iterChangedRanges(((t,i,n,s)=>e.push({fromA:t,toA:i,fromB:n,toB:s}))),i=Qa.applyChanges(i,e),n=na.empty,s=0,r={from:t.mapPos(r.from,-1),to:t.mapPos(r.to,1)},this.skipped.length){o=[];for(let e of this.skipped){let i=t.mapPos(e.from,1),n=t.mapPos(e.to,-1);i<n&&o.push({from:i,to:n})}}}return new al(this.parser,e,i,n,s,r,o,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:e,to:n}=this.skipped[i];e<t.to&&n>t.from&&(this.fragments=ll(this.fragments,e,n),this.skipped.splice(i--,1))}return!(this.skipped.length>=e)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends ka{createParse(e,i,n){let s=n[0].from,r=n[n.length-1].to;return{parsedPos:s,advance(){let e=ol;if(e){for(let t of n)e.tempSkipped.push(t);t&&(e.scheduleOn=e.scheduleOn?Promise.all([e.scheduleOn,t]):t)}return this.parsedPos=r,new na(Fo.none,[],[],r-s)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&0==e[0].from&&e[0].to>=t}static get(){return ol}}function ll(t,e,i){return Qa.applyChanges(t,[{fromA:e,toA:i,fromB:e,toB:i}])}class hl{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new hl(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=al.create(t.facet(dl).parser,t,{from:0,to:e});return i.work(20,e)||i.takeTree(),new hl(i)}}el.state=K.define({create:hl.init,update(t,e){for(let i of e.effects)if(i.is(el.setState))return i.value;return e.startState.facet(dl)!=e.state.facet(dl)?hl.init(e.state):t.apply(e)}});let cl=t=>{let e=setTimeout((()=>t()),500);return()=>clearTimeout(e)};"undefined"!=typeof requestIdleCallback&&(cl=t=>{let e=-1,i=setTimeout((()=>{e=requestIdleCallback(t,{timeout:400})}),100);return()=>e<0?clearTimeout(i):cancelIdleCallback(e)});const fl="undefined"!=typeof navigator&&(null===(Fa=navigator.scheduling)||void 0===Fa?void 0:Fa.isInputPending)?()=>navigator.scheduling.isInputPending():null,ul=rn.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(el.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(el.state);e.tree==e.context.tree&&e.context.isDone(t.doc.length)||(this.working=cl(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,s=i.field(el.state);if(s.tree==s.context.tree&&s.context.isDone(n+1e5))return;let r=Date.now()+Math.min(this.chunkBudget,100,t&&!fl?Math.max(25,t.timeRemaining()-5):1e9),o=s.context.treeLen<n&&i.doc.length>n+1e3,a=s.context.work((()=>fl&&fl()||Date.now()>r),n+(o?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(s.context.takeTree(),this.view.dispatch({effects:el.setState.of(new hl(s.context))})),this.chunkBudget>0&&(!a||o)&&this.scheduleWork(),this.checkAsyncSchedule(s.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then((()=>this.scheduleWork())).catch((t=>tn(this.view.state,t))).then((()=>this.workScheduled--)),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),dl=G.define({combine:t=>t.length?t[0]:null,enables:t=>[el.state,ul,tr.contentAttributes.compute([t],(e=>{let i=e.facet(t);return i&&i.name?{"data-language":i.name}:{}}))]});class Ol{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}const pl=G.define(),gl=G.define({combine:t=>{if(!t.length)return"  ";let e=t[0];if(!e||/\S/.test(e)||Array.from(e).some((t=>t!=e[0])))throw new Error("Invalid indent unit: "+JSON.stringify(t[0]));return e}});function ml(t){let e=t.facet(gl);return 9==e.charCodeAt(0)?t.tabSize*e.length:e.length}function wl(t,e){let i="",n=t.tabSize,s=t.facet(gl)[0];if("\t"==s){for(;e>=n;)i+="\t",e-=n;s=" "}for(let r=0;r<e;r++)i+=s;return i}function vl(t,e){t instanceof Wt&&(t=new bl(t));for(let n of t.state.facet(pl)){let i=n(t,e);if(void 0!==i)return i}let i=sl(t.state);return i.length>=e?function(t,e,i){let n=e.resolveStack(i),s=n.node.enterUnfinishedNodesBefore(i);if(s!=n.node){let t=[];for(let e=s;e!=n.node;e=e.parent)t.push(e);for(let e=t.length-1;e>=0;e--)n={node:t[e],next:n}}return Ql(n,t,i)}(t,i,e):null}class bl{constructor(t,e={}){this.state=t,this.options=e,this.unit=ml(t)}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n,simulateDoubleBreak:s}=this.options;return null!=n&&n>=i.from&&n<=i.to?s&&n==t?{text:"",from:t}:(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),s=this.countColumn(i,t-n),r=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return r>-1&&(s+=r-this.countColumn(i,i.search(/\S|$/))),s}countColumn(t,e=t.length){return Ft(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),s=this.options.overrideIndentation;if(s){let t=s(n);if(t>-1)return t}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const yl=new No;function Ql(t,e,i){for(let n=t;n;n=n.next){let t=kl(n.node);if(t)return t(xl.create(e,i,n))}return 0}function kl(t){let e=t.type.prop(yl);if(e)return e;let i,n=t.firstChild;if(n&&(i=n.type.prop(No.closedBy))){let e=t.lastChild,n=e&&i.indexOf(e.name)>-1;return t=>Zl(t,!0,1,void 0,n&&!function(t){return t.pos==t.options.simulateBreak&&t.options.simulateDoubleBreak}(t)?e.from:void 0)}return null==t.parent?Sl:null}function Sl(){return 0}class xl extends bl{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.context=i}get node(){return this.context.node}static create(t,e,i){return new xl(t,e,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let i=t.resolve(e.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if($l(i,t))break;e=this.state.doc.lineAt(i.from)}return this.lineIndent(e.from)}continue(){return Ql(this.context.next,this.base,this.pos)}}function $l(t,e){for(let i=e;i;i=i.parent)if(t==i)return!0;return!1}function Pl({closing:t,align:e=!0,units:i=1}){return n=>Zl(n,e,i,t)}function Zl(t,e,i,n,s){let r=t.textAfter,o=r.match(/^\s*/)[0].length,a=n&&r.slice(o,o+n.length)==n||s==t.pos+o,l=e?function(t){let e=t.node,i=e.childAfter(e.from),n=e.lastChild;if(!i)return null;let s=t.options.simulateBreak,r=t.state.doc.lineAt(i.from),o=null==s||s<=r.from?r.to:Math.min(r.to,s);for(let a=i.to;;){let t=e.childAfter(a);if(!t||t==n)return null;if(!t.type.isSkipped)return t.from<o?i:null;a=t.to}}(t):null;return l?a?t.column(l.from):t.column(l.to):t.baseIndent+(a?0:t.unit*i)}function Cl({except:t,units:e=1}={}){return i=>{let n=t&&t.test(i.textAfter);return i.baseIndent+(n?0:e*i.unit)}}const Tl=G.define(),Al=new No;function Xl(t){let e=t.lastChild;return e&&e.to==t.to&&e.type.isError}function Wl(t,e,i){for(let n of t.facet(Tl)){let s=n(t,e,i);if(s)return s}return function(t,e,i){let n=sl(t);if(n.length<i)return null;let s=null;for(let r=n.resolveStack(i,1);r;r=r.next){let o=r.node;if(o.to<=i||o.from>i)continue;if(s&&o.from<e)break;let a=o.type.prop(Al);if(a&&(o.to<n.length-50||n.length==t.doc.length||!Xl(o))){let n=a(o,t);n&&n.from<=i&&n.from>=e&&n.to>i&&(s=n)}}return s}(t,e,i)}function Rl(t,e){let i=e.mapPos(t.from,1),n=e.mapPos(t.to,-1);return i>=n?void 0:{from:i,to:n}}const Yl=yt.define({map:Rl}),ql=yt.define({map:Rl});function Ml(t){let e=[];for(let{head:i}of t.state.selection.ranges)e.some((t=>t.from<=i&&t.to>=i))||e.push(t.lineBlockAt(i));return e}const El=K.define({create:()=>Oi.none,update(t,e){t=t.map(e.changes);for(let i of e.effects)if(i.is(Yl)&&!jl(t,i.value.from,i.value.to)){let{preparePlaceholder:n}=e.state.facet(Gl),s=n?Oi.replace({widget:new Ul(n(e.state,i.value))}):Nl;t=t.update({add:[s.range(i.value.from,i.value.to)]})}else i.is(ql)&&(t=t.update({filter:(t,e)=>i.value.from!=t||i.value.to!=e,filterFrom:i.value.from,filterTo:i.value.to}));if(e.selection){let i=!1,{head:n}=e.selection.main;t.between(n,n,((t,e)=>{t<n&&e>n&&(i=!0)})),i&&(t=t.update({filterFrom:n,filterTo:n,filter:(t,e)=>e<=n||t>=n}))}return t},provide:t=>tr.decorations.from(t),toJSON(t,e){let i=[];return t.between(0,e.doc.length,((t,e)=>{i.push(t,e)})),i},fromJSON(t){if(!Array.isArray(t)||t.length%2)throw new RangeError("Invalid JSON for fold state");let e=[];for(let i=0;i<t.length;){let n=t[i++],s=t[i++];if("number"!=typeof n||"number"!=typeof s)throw new RangeError("Invalid JSON for fold state");e.push(Nl.range(n,s))}return Oi.set(e,!0)}});function Dl(t,e,i){var n;let s=null;return null===(n=t.field(El,!1))||void 0===n||n.between(e,i,((t,e)=>{(!s||s.from>t)&&(s={from:t,to:e})})),s}function jl(t,e,i){let n=!1;return t.between(e,e,((t,s)=>{t==e&&s==i&&(n=!0)})),n}function _l(t,e){return t.field(El,!1)?e:e.concat(yt.appendConfig.of(Il()))}function Vl(t,e,i=!0){let n=t.state.doc.lineAt(e.from).number,s=t.state.doc.lineAt(e.to).number;return tr.announce.of(`${t.state.phrase(i?"Folded lines":"Unfolded lines")} ${n} ${t.state.phrase("to")} ${s}.`)}const Bl=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:t=>{for(let e of Ml(t)){let i=Wl(t.state,e.from,e.to);if(i)return t.dispatch({effects:_l(t.state,[Yl.of(i),Vl(t,i)])}),!0}return!1}},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:t=>{if(!t.state.field(El,!1))return!1;let e=[];for(let i of Ml(t)){let n=Dl(t.state,i.from,i.to);n&&e.push(ql.of(n),Vl(t,n,!1))}return e.length&&t.dispatch({effects:e}),e.length>0}},{key:"Ctrl-Alt-[",run:t=>{let{state:e}=t,i=[];for(let n=0;n<e.doc.length;){let s=t.lineBlockAt(n),r=Wl(e,s.from,s.to);r&&i.push(Yl.of(r)),n=(r?t.lineBlockAt(r.to):s).to+1}return i.length&&t.dispatch({effects:_l(t.state,i)}),!!i.length}},{key:"Ctrl-Alt-]",run:t=>{let e=t.state.field(El,!1);if(!e||!e.size)return!1;let i=[];return e.between(0,t.state.doc.length,((t,e)=>{i.push(ql.of({from:t,to:e}))})),t.dispatch({effects:i}),!0}}],zl={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},Gl=G.define({combine:t=>Rt(t,zl)});function Il(t){let e=[El,Jl];return t&&e.push(Gl.of(t)),e}function Ll(t,e){let{state:i}=t,n=i.facet(Gl),s=e=>{let i=t.lineBlockAt(t.posAtDOM(e.target)),n=Dl(t.state,i.from,i.to);n&&t.dispatch({effects:ql.of(n)}),e.preventDefault()};if(n.placeholderDOM)return n.placeholderDOM(t,s,e);let r=document.createElement("span");return r.textContent=n.placeholderText,r.setAttribute("aria-label",i.phrase("folded code")),r.title=i.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=s,r}const Nl=Oi.replace({widget:new class extends ui{toDOM(t){return Ll(t,null)}}});class Ul extends ui{constructor(t){super(),this.value=t}eq(t){return this.value==t.value}toDOM(t){return Ll(t,this.value)}}const Hl={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Fl extends ko{constructor(t,e){super(),this.config=t,this.open=e}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function Kl(t={}){let e=Object.assign(Object.assign({},Hl),t),i=new Fl(e,!0),n=new Fl(e,!1),s=rn.fromClass(class{constructor(t){this.from=t.viewport.from,this.markers=this.buildMarkers(t)}update(t){(t.docChanged||t.viewportChanged||t.startState.facet(dl)!=t.state.facet(dl)||t.startState.field(El,!1)!=t.state.field(El,!1)||sl(t.startState)!=sl(t.state)||e.foldingChanged(t))&&(this.markers=this.buildMarkers(t.view))}buildMarkers(t){let e=new jt;for(let s of t.viewportLineBlocks){let r=Dl(t.state,s.from,s.to)?n:Wl(t.state,s.from,s.to)?i:null;r&&e.add(s.from,s.from,r)}return e.finish()}}),{domEventHandlers:r}=e;return[s,Po({class:"cm-foldGutter",markers(t){var e;return(null===(e=t.plugin(s))||void 0===e?void 0:e.markers)||Dt.empty},initialSpacer:()=>new Fl(e,!1),domEventHandlers:Object.assign(Object.assign({},r),{click:(t,e,i)=>{if(r.click&&r.click(t,e,i))return!0;let n=Dl(t.state,e.from,e.to);if(n)return t.dispatch({effects:ql.of(n)}),!0;let s=Wl(t.state,e.from,e.to);return!!s&&(t.dispatch({effects:Yl.of(s)}),!0)}})}),Il()]}const Jl=tr.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class th{constructor(t,e){let i;function n(t){let e=ie.newName();return(i||(i=Object.create(null)))["."+e]=t,e}this.specs=t;const s="string"==typeof e.all?e.all:e.all?n(e.all):void 0,r=e.scope;this.scope=r instanceof el?t=>t.prop(Ka)==r.data:r?t=>t==r:void 0,this.style=Xa(t.map((t=>({tag:t.tag,class:t.class||n(Object.assign({},t,{tag:null}))}))),{all:s}).style,this.module=i?new ie(i):null,this.themeType=e.themeType}static define(t,e){return new th(t,e||{})}}const eh=G.define(),ih=G.define({combine:t=>t.length?[t[0]]:null});function nh(t){let e=t.facet(eh);return e.length?e:t.facet(ih)}function sh(t,e){let i,n=[oh];return t instanceof th&&(t.module&&n.push(tr.styleModule.of(t.module)),i=t.themeType),(null==e?void 0:e.fallback)?n.push(ih.of(t)):i?n.push(eh.computeN([tr.darkTheme],(e=>e.facet(tr.darkTheme)==("dark"==i)?[t]:[]))):n.push(eh.of(t)),n}class rh{constructor(t){this.markCache=Object.create(null),this.tree=sl(t.state),this.decorations=this.buildDeco(t,nh(t.state)),this.decoratedTo=t.viewport.to}update(t){let e=sl(t.state),i=nh(t.state),n=i!=nh(t.startState),{viewport:s}=t.view,r=t.changes.mapPos(this.decoratedTo,1);e.length<s.to&&!n&&e.type==this.tree.type&&r>=s.to?(this.decorations=this.decorations.map(t.changes),this.decoratedTo=r):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i),this.decoratedTo=s.to)}buildDeco(t,e){if(!e||!this.tree.length)return Oi.none;let i=new jt;for(let{from:n,to:s}of t.visibleRanges)Wa(this.tree,e,((t,e,n)=>{i.add(t,e,this.markCache[n]||(this.markCache[n]=Oi.mark({class:n})))}),n,s);return i.finish()}}const oh=st.high(rn.fromClass(rh,{decorations:t=>t.decorations})),ah=th.define([{tag:Ha.meta,color:"#404740"},{tag:Ha.link,textDecoration:"underline"},{tag:Ha.heading,textDecoration:"underline",fontWeight:"bold"},{tag:Ha.emphasis,fontStyle:"italic"},{tag:Ha.strong,fontWeight:"bold"},{tag:Ha.strikethrough,textDecoration:"line-through"},{tag:Ha.keyword,color:"#708"},{tag:[Ha.atom,Ha.bool,Ha.url,Ha.contentSeparator,Ha.labelName],color:"#219"},{tag:[Ha.literal,Ha.inserted],color:"#164"},{tag:[Ha.string,Ha.deleted],color:"#a11"},{tag:[Ha.regexp,Ha.escape,Ha.special(Ha.string)],color:"#e40"},{tag:Ha.definition(Ha.variableName),color:"#00f"},{tag:Ha.local(Ha.variableName),color:"#30a"},{tag:[Ha.typeName,Ha.namespace],color:"#085"},{tag:Ha.className,color:"#167"},{tag:[Ha.special(Ha.variableName),Ha.macroName],color:"#256"},{tag:Ha.definition(Ha.propertyName),color:"#00c"},{tag:Ha.comment,color:"#940"},{tag:Ha.invalid,color:"#f00"}]),lh=tr.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),hh=1e4,ch="()[]{}",fh=G.define({combine:t=>Rt(t,{afterCursor:!0,brackets:ch,maxScanDistance:hh,renderMatch:Oh})}),uh=Oi.mark({class:"cm-matchingBracket"}),dh=Oi.mark({class:"cm-nonmatchingBracket"});function Oh(t){let e=[],i=t.matched?uh:dh;return e.push(i.range(t.start.from,t.start.to)),t.end&&e.push(i.range(t.end.from,t.end.to)),e}const ph=[K.define({create:()=>Oi.none,update(t,e){if(!e.docChanged&&!e.selection)return t;let i=[],n=e.state.facet(fh);for(let s of e.state.selection.ranges){if(!s.empty)continue;let t=bh(e.state,s.head,-1,n)||s.head>0&&bh(e.state,s.head-1,1,n)||n.afterCursor&&(bh(e.state,s.head,1,n)||s.head<e.state.doc.length&&bh(e.state,s.head+1,-1,n));t&&(i=i.concat(n.renderMatch(t,e.state)))}return Oi.set(i,!0)},provide:t=>tr.decorations.from(t)}),lh];function gh(t={}){return[fh.of(t),ph]}const mh=new No;function wh(t,e,i){let n=t.prop(e<0?No.openedBy:No.closedBy);if(n)return n;if(1==t.name.length){let n=i.indexOf(t.name);if(n>-1&&n%2==(e<0?1:0))return[i[n+e]]}return null}function vh(t){let e=t.type.prop(mh);return e?e(t.node):t}function bh(t,e,i,n={}){let s=n.maxScanDistance||hh,r=n.brackets||ch,o=sl(t),a=o.resolveInner(e,i);for(let l=a;l;l=l.parent){let n=wh(l.type,i,r);if(n&&l.from<l.to){let s=vh(l);if(s&&(i>0?e>=s.from&&e<s.to:e>s.from&&e<=s.to))return yh(t,e,i,l,s,n,r)}}return function(t,e,i,n,s,r,o){let a=i<0?t.sliceDoc(e-1,e):t.sliceDoc(e,e+1),l=o.indexOf(a);if(l<0||l%2==0!=i>0)return null;let h={from:i<0?e-1:e,to:i>0?e+1:e},c=t.doc.iterRange(e,i>0?t.doc.length:0),f=0;for(let u=0;!c.next().done&&u<=r;){let t=c.value;i<0&&(u+=t.length);let r=e+u*i;for(let e=i>0?0:t.length-1,a=i>0?t.length:-1;e!=a;e+=i){let a=o.indexOf(t[e]);if(!(a<0||n.resolveInner(r+e,1).type!=s))if(a%2==0==i>0)f++;else{if(1==f)return{start:h,end:{from:r+e,to:r+e+1},matched:a>>1==l>>1};f--}}i>0&&(u+=t.length)}return c.done?{start:h,matched:!1}:null}(t,e,i,o,a.type,s,r)}function yh(t,e,i,n,s,r,o){let a=n.parent,l={from:s.from,to:s.to},h=0,c=null==a?void 0:a.cursor();if(c&&(i<0?c.childBefore(n.from):c.childAfter(n.to)))do{if(i<0?c.to<=n.from:c.from>=n.to){if(0==h&&r.indexOf(c.type.name)>-1&&c.from<c.to){let t=vh(c);return{start:l,end:t?{from:t.from,to:t.to}:void 0,matched:!0}}if(wh(c.type,i,o))h++;else if(wh(c.type,-i,o)){if(0==h){let t=vh(c);return{start:l,end:t&&t.from<t.to?{from:t.from,to:t.to}:void 0,matched:!1}}h--}}}while(i<0?c.prevSibling():c.nextSibling());return{start:l,matched:!1}}const Qh=Object.create(null),kh=[Fo.none],Sh=[],xh=Object.create(null),$h=Object.create(null);for(let[Xp,Wp]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])$h[Xp]=Zh(Qh,Wp);function Ph(t,e){Sh.indexOf(t)>-1||Sh.push(t)}function Zh(t,e){let i=[];for(let a of e.split(" ")){let e=[];for(let i of a.split(".")){let n=t[i]||Ha[i];n?"function"==typeof n?e.length?e=e.map(n):Ph(i):e.length?Ph(i):e=Array.isArray(n)?n:[n]:Ph(i)}for(let t of e)i.push(t)}if(!i.length)return 0;let n=e.replace(/ /g,"_"),s=n+" "+i.map((t=>t.id)),r=xh[s];if(r)return r.id;let o=xh[s]=Fo.define({id:kh.length,name:n,props:[Ca({[n]:i})]});return kh.push(o),o.id}ki.RTL,ki.LTR;function Ch(t,e){return({state:i,dispatch:n})=>{if(i.readOnly)return!1;let s=t(e,i);return!!s&&(n(i.update(s)),!0)}}const Th=Ch(qh,0),Ah=Ch(Yh,0),Xh=Ch(((t,e)=>Yh(t,e,function(t){let e=[];for(let i of t.selection.ranges){let n=t.doc.lineAt(i.from),s=i.to<=n.to?n:t.doc.lineAt(i.to),r=e.length-1;r>=0&&e[r].to>n.from?e[r].to=s.to:e.push({from:n.from+/^\s*/.exec(n.text)[0].length,to:s.to})}return e}(e))),0);function Wh(t,e){let i=t.languageDataAt("commentTokens",e);return i.length?i[0]:{}}const Rh=50;function Yh(t,e,i=e.selection.ranges){let n=i.map((t=>Wh(e,t.from).block));if(!n.every((t=>t)))return null;let s=i.map(((t,i)=>function(t,{open:e,close:i},n,s){let r,o,a=t.sliceDoc(n-Rh,n),l=t.sliceDoc(s,s+Rh),h=/\s*$/.exec(a)[0].length,c=/^\s*/.exec(l)[0].length,f=a.length-h;if(a.slice(f-e.length,f)==e&&l.slice(c,c+i.length)==i)return{open:{pos:n-h,margin:h&&1},close:{pos:s+c,margin:c&&1}};s-n<=2*Rh?r=o=t.sliceDoc(n,s):(r=t.sliceDoc(n,n+Rh),o=t.sliceDoc(s-Rh,s));let u=/^\s*/.exec(r)[0].length,d=/\s*$/.exec(o)[0].length,O=o.length-d-i.length;return r.slice(u,u+e.length)==e&&o.slice(O,O+i.length)==i?{open:{pos:n+u+e.length,margin:/\s/.test(r.charAt(u+e.length))?1:0},close:{pos:s-d-i.length,margin:/\s/.test(o.charAt(O-1))?1:0}}:null}(e,n[i],t.from,t.to)));if(2!=t&&!s.every((t=>t)))return{changes:e.changes(i.map(((t,e)=>s[e]?[]:[{from:t.from,insert:n[e].open+" "},{from:t.to,insert:" "+n[e].close}])))};if(1!=t&&s.some((t=>t))){let t=[];for(let e,i=0;i<s.length;i++)if(e=s[i]){let s=n[i],{open:r,close:o}=e;t.push({from:r.pos-s.open.length,to:r.pos+r.margin},{from:o.pos-o.margin,to:o.pos+s.close.length})}return{changes:t}}return null}function qh(t,e,i=e.selection.ranges){let n=[],s=-1;for(let{from:r,to:o}of i){let t=n.length,i=1e9,a=Wh(e,r).line;if(a){for(let t=r;t<=o;){let l=e.doc.lineAt(t);if(l.from>s&&(r==o||o>l.from)){s=l.from;let t=/^\s*/.exec(l.text)[0].length,e=t==l.length,r=l.text.slice(t,t+a.length)==a?t:-1;t<l.text.length&&t<i&&(i=t),n.push({line:l,comment:r,token:a,indent:t,empty:e,single:!1})}t=l.to+1}if(i<1e9)for(let e=t;e<n.length;e++)n[e].indent<n[e].line.text.length&&(n[e].indent=i);n.length==t+1&&(n[t].single=!0)}}if(2!=t&&n.some((t=>t.comment<0&&(!t.empty||t.single)))){let t=[];for(let{line:e,token:s,indent:r,empty:o,single:a}of n)!a&&o||t.push({from:e.from+r,insert:s+" "});let i=e.changes(t);return{changes:i,selection:e.selection.map(i,1)}}if(1!=t&&n.some((t=>t.comment>=0))){let t=[];for(let{line:e,comment:i,token:s}of n)if(i>=0){let n=e.from+i,r=n+s.length;" "==e.text[r-e.from]&&r++,t.push({from:n,to:r})}return{changes:t}}return null}const Mh=wt.define(),Eh=wt.define(),Dh=G.define(),jh=G.define({combine:t=>Rt(t,{minDepth:100,newGroupDelay:500,joinToEvent:(t,e)=>e},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(t,e)=>(i,n)=>t(i,n)||e(i,n)})}),_h=K.define({create:()=>sc.empty,update(t,e){let i=e.state.facet(jh),n=e.annotation(Mh);if(n){let s=Nh.fromTransaction(e,n.selection),r=n.side,o=0==r?t.undone:t.done;return o=s?Uh(o,o.length,i.minDepth,s):Jh(o,e.startState.selection),new sc(0==r?n.rest:o,0==r?o:n.rest)}let s=e.annotation(Eh);if("full"!=s&&"before"!=s||(t=t.isolate()),!1===e.annotation(Qt.addToHistory))return e.changes.empty?t:t.addMapping(e.changes.desc);let r=Nh.fromTransaction(e),o=e.annotation(Qt.time),a=e.annotation(Qt.userEvent);return r?t=t.addChanges(r,o,a,i,e):e.selection&&(t=t.addSelection(e.startState.selection,o,a,i.newGroupDelay)),"full"!=s&&"after"!=s||(t=t.isolate()),t},toJSON:t=>({done:t.done.map((t=>t.toJSON())),undone:t.undone.map((t=>t.toJSON()))}),fromJSON:t=>new sc(t.done.map(Nh.fromJSON),t.undone.map(Nh.fromJSON))});function Vh(t={}){return[_h,jh.of(t),tr.domEventHandlers({beforeinput(t,e){let i="historyUndo"==t.inputType?zh:"historyRedo"==t.inputType?Gh:null;return!!i&&(t.preventDefault(),i(e))}})]}function Bh(t,e){return function({state:i,dispatch:n}){if(!e&&i.readOnly)return!1;let s=i.field(_h,!1);if(!s)return!1;let r=s.pop(t,i,e);return!!r&&(n(r),!0)}}const zh=Bh(0,!1),Gh=Bh(1,!1),Ih=Bh(0,!0),Lh=Bh(1,!0);class Nh{constructor(t,e,i,n,s){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=s}setSelAfter(t){return new Nh(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return{changes:null===(t=this.changes)||void 0===t?void 0:t.toJSON(),mapped:null===(e=this.mapped)||void 0===e?void 0:e.toJSON(),startSelection:null===(i=this.startSelection)||void 0===i?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map((t=>t.toJSON()))}}static fromJSON(t){return new Nh(t.changes&&R.fromJSON(t.changes),[],t.mapped&&W.fromJSON(t.mapped),t.startSelection&&V.fromJSON(t.startSelection),t.selectionsAfter.map(V.fromJSON))}static fromTransaction(t,e){let i=Fh;for(let n of t.startState.facet(Dh)){let e=n(t);e.length&&(i=i.concat(e))}return!i.length&&t.changes.empty?null:new Nh(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,Fh)}static selection(t){return new Nh(void 0,Fh,void 0,void 0,t)}}function Uh(t,e,i,n){let s=e+1>i+20?e-i-1:0,r=t.slice(s,e);return r.push(n),r}function Hh(t,e){return t.length?e.length?t.concat(e):t:e}const Fh=[],Kh=200;function Jh(t,e){if(t.length){let i=t[t.length-1],n=i.selectionsAfter.slice(Math.max(0,i.selectionsAfter.length-Kh));return n.length&&n[n.length-1].eq(e)?t:(n.push(e),Uh(t,t.length-1,1e9,i.setSelAfter(n)))}return[Nh.selection([e])]}function tc(t){let e=t[t.length-1],i=t.slice();return i[t.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),i}function ec(t,e){if(!t.length)return t;let i=t.length,n=Fh;for(;i;){let s=ic(t[i-1],e,n);if(s.changes&&!s.changes.empty||s.effects.length){let e=t.slice(0,i);return e[i-1]=s,e}e=s.mapped,i--,n=s.selectionsAfter}return n.length?[Nh.selection(n)]:Fh}function ic(t,e,i){let n=Hh(t.selectionsAfter.length?t.selectionsAfter.map((t=>t.map(e))):Fh,i);if(!t.changes)return Nh.selection(n);let s=t.changes.map(e),r=e.mapDesc(t.changes,!0),o=t.mapped?t.mapped.composeDesc(r):r;return new Nh(s,yt.mapEffects(t.effects,e),o,t.startSelection.map(r),n)}const nc=/^(input\.type|delete)($|\.)/;class sc{constructor(t,e,i=0,n=void 0){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new sc(this.done,this.undone):this}addChanges(t,e,i,n,s){let r=this.done,o=r[r.length-1];return r=o&&o.changes&&!o.changes.empty&&t.changes&&(!i||nc.test(i))&&(!o.selectionsAfter.length&&e-this.prevTime<n.newGroupDelay&&n.joinToEvent(s,function(t,e){let i=[],n=!1;return t.iterChangedRanges(((t,e)=>i.push(t,e))),e.iterChangedRanges(((t,e,s,r)=>{for(let o=0;o<i.length;){let t=i[o++],e=i[o++];r>=t&&s<=e&&(n=!0)}})),n}(o.changes,t.changes))||"input.type.compose"==i)?Uh(r,r.length-1,n.minDepth,new Nh(t.changes.compose(o.changes),Hh(t.effects,o.effects),o.mapped,o.startSelection,Fh)):Uh(r,r.length,n.minDepth,t),new sc(r,Fh,e,i)}addSelection(t,e,i,n){let s=this.done.length?this.done[this.done.length-1].selectionsAfter:Fh;return s.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&(r=s[s.length-1],o=t,r.ranges.length==o.ranges.length&&0===r.ranges.filter(((t,e)=>t.empty!=o.ranges[e].empty)).length)?this:new sc(Jh(this.done,t),this.undone,e,i);var r,o}addMapping(t){return new sc(ec(this.done,t),ec(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=0==t?this.done:this.undone;if(0==n.length)return null;let s=n[n.length-1],r=s.selectionsAfter[0]||e.selection;if(i&&s.selectionsAfter.length)return e.update({selection:s.selectionsAfter[s.selectionsAfter.length-1],annotations:Mh.of({side:t,rest:tc(n),selection:r}),userEvent:0==t?"select.undo":"select.redo",scrollIntoView:!0});if(s.changes){let i=1==n.length?Fh:n.slice(0,n.length-1);return s.mapped&&(i=ec(i,s.mapped)),e.update({changes:s.changes,selection:s.startSelection,effects:s.effects,annotations:Mh.of({side:t,rest:i,selection:r}),filter:!1,userEvent:0==t?"undo":"redo",scrollIntoView:!0})}return null}}sc.empty=new sc(Fh,Fh);const rc=[{key:"Mod-z",run:zh,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:Gh,preventDefault:!0},{linux:"Ctrl-Shift-z",run:Gh,preventDefault:!0},{key:"Mod-u",run:Ih,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:Lh,preventDefault:!0}];function oc(t,e){return V.create(t.ranges.map(e),t.mainIndex)}function ac(t,e){return t.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function lc({state:t,dispatch:e},i){let n=oc(t.selection,i);return!n.eq(t.selection,!0)&&(e(ac(t,n)),!0)}function hc(t,e){return V.cursor(e?t.to:t.from)}function cc(t,e){return lc(t,(i=>i.empty?t.moveByChar(i,e):hc(i,e)))}function fc(t){return t.textDirectionAt(t.state.selection.main.head)==ki.LTR}const uc=t=>cc(t,!fc(t)),dc=t=>cc(t,fc(t));function Oc(t,e){return lc(t,(i=>i.empty?t.moveByGroup(i,e):hc(i,e)))}function pc(t,e,i){if(e.type.prop(i))return!0;let n=e.to-e.from;return n&&(n>2||/[^\s,.;:]/.test(t.sliceDoc(e.from,e.to)))||e.firstChild}function gc(t,e,i){let n,s,r=sl(t).resolveInner(e.head),o=i?No.closedBy:No.openedBy;for(let a=e.head;;){let e=i?r.childAfter(a):r.childBefore(a);if(!e)break;pc(t,e,o)?r=e:a=i?e.to:e.from}return s=r.type.prop(o)&&(n=i?bh(t,r.from,1):bh(t,r.to,-1))&&n.matched?i?n.end.to:n.end.from:i?r.to:r.from,V.cursor(s,i?-1:1)}function mc(t,e){return lc(t,(i=>{if(!i.empty)return hc(i,e);let n=t.moveVertically(i,e);return n.head!=i.head?n:t.moveToLineBoundary(i,e)}))}const wc=t=>mc(t,!1),vc=t=>mc(t,!0);function bc(t){let e,i=t.scrollDOM.clientHeight<t.scrollDOM.scrollHeight-2,n=0,s=0;if(i){for(let e of t.state.facet(tr.scrollMargins)){let i=e(t);(null==i?void 0:i.top)&&(n=Math.max(null==i?void 0:i.top,n)),(null==i?void 0:i.bottom)&&(s=Math.max(null==i?void 0:i.bottom,s))}e=t.scrollDOM.clientHeight-n-s}else e=(t.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:n,marginBottom:s,selfScroll:i,height:Math.max(t.defaultLineHeight,e-5)}}function yc(t,e){let i,n=bc(t),{state:s}=t,r=oc(s.selection,(i=>i.empty?t.moveVertically(i,e,n.height):hc(i,e)));if(r.eq(s.selection))return!1;if(n.selfScroll){let e=t.coordsAtPos(s.selection.main.head),o=t.scrollDOM.getBoundingClientRect(),a=o.top+n.marginTop,l=o.bottom-n.marginBottom;e&&e.top>a&&e.bottom<l&&(i=tr.scrollIntoView(r.main.head,{y:"start",yMargin:e.top-a}))}return t.dispatch(ac(s,r),{effects:i}),!0}const Qc=t=>yc(t,!1),kc=t=>yc(t,!0);function Sc(t,e,i){let n=t.lineBlockAt(e.head),s=t.moveToLineBoundary(e,i);if(s.head==e.head&&s.head!=(i?n.to:n.from)&&(s=t.moveToLineBoundary(e,i,!1)),!i&&s.head==n.from&&n.length){let i=/^\s*/.exec(t.state.sliceDoc(n.from,Math.min(n.from+100,n.to)))[0].length;i&&e.head!=n.from+i&&(s=V.cursor(n.from+i))}return s}function xc(t,e){let i=oc(t.state.selection,(t=>{let i=e(t);return V.range(t.anchor,i.head,i.goalColumn,i.bidiLevel||void 0)}));return!i.eq(t.state.selection)&&(t.dispatch(ac(t.state,i)),!0)}function $c(t,e){return xc(t,(i=>t.moveByChar(i,e)))}const Pc=t=>$c(t,!fc(t)),Zc=t=>$c(t,fc(t));function Cc(t,e){return xc(t,(i=>t.moveByGroup(i,e)))}function Tc(t,e){return xc(t,(i=>t.moveVertically(i,e)))}const Ac=t=>Tc(t,!1),Xc=t=>Tc(t,!0);function Wc(t,e){return xc(t,(i=>t.moveVertically(i,e,bc(t).height)))}const Rc=t=>Wc(t,!1),Yc=t=>Wc(t,!0),qc=({state:t,dispatch:e})=>(e(ac(t,{anchor:0})),!0),Mc=({state:t,dispatch:e})=>(e(ac(t,{anchor:t.doc.length})),!0),Ec=({state:t,dispatch:e})=>(e(ac(t,{anchor:t.selection.main.anchor,head:0})),!0),Dc=({state:t,dispatch:e})=>(e(ac(t,{anchor:t.selection.main.anchor,head:t.doc.length})),!0);function jc(t,e){if(t.state.readOnly)return!1;let i="delete.selection",{state:n}=t,s=n.changeByRange((n=>{let{from:s,to:r}=n;if(s==r){let o=e(n);o<s?(i="delete.backward",o=_c(t,o,!1)):o>s&&(i="delete.forward",o=_c(t,o,!0)),s=Math.min(s,o),r=Math.max(r,o)}else s=_c(t,s,!1),r=_c(t,r,!0);return s==r?{range:n}:{changes:{from:s,to:r},range:V.cursor(s,s<n.head?-1:1)}}));return!s.changes.empty&&(t.dispatch(n.update(s,{scrollIntoView:!0,userEvent:i,effects:"delete.selection"==i?tr.announce.of(n.phrase("Selection deleted")):void 0})),!0)}function _c(t,e,i){if(t instanceof tr)for(let n of t.state.facet(tr.atomicRanges).map((e=>e(t))))n.between(e,e,((t,n)=>{t<e&&n>e&&(e=i?n:t)}));return e}const Vc=(t,e)=>jc(t,(i=>{let n,s,r=i.from,{state:o}=t,a=o.doc.lineAt(r);if(!e&&r>a.from&&r<a.from+200&&!/[^ \t]/.test(n=a.text.slice(0,r-a.from))){if("\t"==n[n.length-1])return r-1;let t=Ft(n,o.tabSize)%ml(o)||ml(o);for(let e=0;e<t&&" "==n[n.length-1-e];e++)r--;s=r}else s=k(a.text,r-a.from,e,e)+a.from,s==r&&a.number!=(e?o.doc.lines:1)?s+=e?1:-1:!e&&/[\ufe00-\ufe0f]/.test(a.text.slice(s-a.from,r-a.from))&&(s=k(a.text,s-a.from,!1,!1)+a.from);return s})),Bc=t=>Vc(t,!1),zc=t=>Vc(t,!0),Gc=(t,e)=>jc(t,(i=>{let n=i.head,{state:s}=t,r=s.doc.lineAt(n),o=s.charCategorizer(n);for(let t=null;;){if(n==(e?r.to:r.from)){n==i.head&&r.number!=(e?s.doc.lines:1)&&(n+=e?1:-1);break}let a=k(r.text,n-r.from,e)+r.from,l=r.text.slice(Math.min(n,a)-r.from,Math.max(n,a)-r.from),h=o(l);if(null!=t&&h!=t)break;" "==l&&n==i.head||(t=h),n=a}return n})),Ic=t=>Gc(t,!1);function Lc(t){let e=[],i=-1;for(let n of t.selection.ranges){let s=t.doc.lineAt(n.from),r=t.doc.lineAt(n.to);if(n.empty||n.to!=r.from||(r=t.doc.lineAt(n.to-1)),i>=s.number){let t=e[e.length-1];t.to=r.to,t.ranges.push(n)}else e.push({from:s.from,to:r.to,ranges:[n]});i=r.number+1}return e}function Nc(t,e,i){if(t.readOnly)return!1;let n=[],s=[];for(let r of Lc(t)){if(i?r.to==t.doc.length:0==r.from)continue;let e=t.doc.lineAt(i?r.to+1:r.from-1),o=e.length+1;if(i){n.push({from:r.to,to:e.to},{from:r.from,insert:e.text+t.lineBreak});for(let e of r.ranges)s.push(V.range(Math.min(t.doc.length,e.anchor+o),Math.min(t.doc.length,e.head+o)))}else{n.push({from:e.from,to:r.from},{from:r.to,insert:t.lineBreak+e.text});for(let t of r.ranges)s.push(V.range(t.anchor-o,t.head-o))}}return!!n.length&&(e(t.update({changes:n,scrollIntoView:!0,selection:V.create(s,t.selection.mainIndex),userEvent:"move.line"})),!0)}function Uc(t,e,i){if(t.readOnly)return!1;let n=[];for(let s of Lc(t))i?n.push({from:s.from,insert:t.doc.slice(s.from,s.to)+t.lineBreak}):n.push({from:s.to,insert:t.lineBreak+t.doc.slice(s.from,s.to)});return e(t.update({changes:n,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const Hc=Fc(!1);function Fc(t){return({state:e,dispatch:i})=>{if(e.readOnly)return!1;let n=e.changeByRange((i=>{let{from:n,to:s}=i,r=e.doc.lineAt(n),o=!t&&n==s&&function(t,e){if(/\(\)|\[\]|\{\}/.test(t.sliceDoc(e-1,e+1)))return{from:e,to:e};let i,n=sl(t).resolveInner(e),s=n.childBefore(e),r=n.childAfter(e);return s&&r&&s.to<=e&&r.from>=e&&(i=s.type.prop(No.closedBy))&&i.indexOf(r.name)>-1&&t.doc.lineAt(s.to).from==t.doc.lineAt(r.from).from&&!/\S/.test(t.sliceDoc(s.to,r.from))?{from:s.to,to:r.from}:null}(e,n);t&&(n=s=(s<=r.to?r:e.doc.lineAt(s)).to);let a=new bl(e,{simulateBreak:n,simulateDoubleBreak:!!o}),l=vl(a,n);for(null==l&&(l=Ft(/^\s*/.exec(e.doc.lineAt(n).text)[0],e.tabSize));s<r.to&&/\s/.test(r.text[s-r.from]);)s++;o?({from:n,to:s}=o):n>r.from&&n<r.from+100&&!/\S/.test(r.text.slice(0,n))&&(n=r.from);let c=["",wl(e,l)];return o&&c.push(wl(e,a.lineIndent(r.from,-1))),{changes:{from:n,to:s,insert:h.of(c)},range:V.cursor(n+1+c[1].length)}}));return i(e.update(n,{scrollIntoView:!0,userEvent:"input"})),!0}}function Kc(t,e){let i=-1;return t.changeByRange((n=>{let s=[];for(let o=n.from;o<=n.to;){let r=t.doc.lineAt(o);r.number>i&&(n.empty||n.to>r.from)&&(e(r,s,n),i=r.number),o=r.to+1}let r=t.changes(s);return{changes:s,range:V.range(r.mapPos(n.anchor,1),r.mapPos(n.head,1))}}))}const Jc=({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(Kc(t,((e,i)=>{i.push({from:e.from,insert:t.facet(gl)})})),{userEvent:"input.indent"})),!0),tf=({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(Kc(t,((e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let s=Ft(n,t.tabSize),r=0,o=wl(t,Math.max(0,s-ml(t)));for(;r<n.length&&r<o.length&&n.charCodeAt(r)==o.charCodeAt(r);)r++;i.push({from:e.from+r,to:e.from+n.length,insert:o.slice(r)})})),{userEvent:"delete.dedent"})),!0),ef=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:t=>lc(t,(e=>gc(t.state,e,!fc(t)))),shift:t=>xc(t,(e=>gc(t.state,e,!fc(t))))},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:t=>lc(t,(e=>gc(t.state,e,fc(t)))),shift:t=>xc(t,(e=>gc(t.state,e,fc(t))))},{key:"Alt-ArrowUp",run:({state:t,dispatch:e})=>Nc(t,e,!1)},{key:"Shift-Alt-ArrowUp",run:({state:t,dispatch:e})=>Uc(t,e,!1)},{key:"Alt-ArrowDown",run:({state:t,dispatch:e})=>Nc(t,e,!0)},{key:"Shift-Alt-ArrowDown",run:({state:t,dispatch:e})=>Uc(t,e,!0)},{key:"Escape",run:({state:t,dispatch:e})=>{let i=t.selection,n=null;return i.ranges.length>1?n=V.create([i.main]):i.main.empty||(n=V.create([V.cursor(i.main.head)])),!!n&&(e(ac(t,n)),!0)}},{key:"Mod-Enter",run:Fc(!0)},{key:"Alt-l",mac:"Ctrl-l",run:({state:t,dispatch:e})=>{let i=Lc(t).map((({from:e,to:i})=>V.range(e,Math.min(i+1,t.doc.length))));return e(t.update({selection:V.create(i),userEvent:"select"})),!0}},{key:"Mod-i",run:({state:t,dispatch:e})=>{let i=oc(t.selection,(e=>{var i;for(let n=sl(t).resolveStack(e.from,1);n;n=n.next){let{node:t}=n;if((t.from<e.from&&t.to>=e.to||t.to>e.to&&t.from<=e.from)&&(null===(i=t.parent)||void 0===i?void 0:i.parent))return V.range(t.to,t.from)}return e}));return e(ac(t,i)),!0},preventDefault:!0},{key:"Mod-[",run:tf},{key:"Mod-]",run:Jc},{key:"Mod-Alt-\\",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=Object.create(null),n=new bl(t,{overrideIndentation:t=>{let e=i[t];return null==e?-1:e}}),s=Kc(t,((e,s,r)=>{let o=vl(n,e.from);if(null==o)return;/\S/.test(e.text)||(o=0);let a=/^\s*/.exec(e.text)[0],l=wl(t,o);(a!=l||r.from<e.from+a.length)&&(i[e.from]=o,s.push({from:e.from,to:e.from+a.length,insert:l}))}));return s.changes.empty||e(t.update(s,{userEvent:"indent"})),!0}},{key:"Shift-Mod-k",run:t=>{if(t.state.readOnly)return!1;let{state:e}=t,i=e.changes(Lc(e).map((({from:t,to:i})=>(t>0?t--:i<e.doc.length&&i++,{from:t,to:i})))),n=oc(e.selection,(e=>t.moveVertically(e,!0))).map(i);return t.dispatch({changes:i,selection:n,scrollIntoView:!0,userEvent:"delete.line"}),!0}},{key:"Shift-Mod-\\",run:({state:t,dispatch:e})=>function(t,e,i){let n=!1,s=oc(t.selection,(e=>{let s=bh(t,e.head,-1)||bh(t,e.head,1)||e.head>0&&bh(t,e.head-1,1)||e.head<t.doc.length&&bh(t,e.head+1,-1);if(!s||!s.end)return e;n=!0;let r=s.start.from==e.head?s.end.to:s.end.from;return i?V.range(e.anchor,r):V.cursor(r)}));return!!n&&(e(ac(t,s)),!0)}(t,e,!1)},{key:"Mod-/",run:t=>{let{state:e}=t,i=e.doc.lineAt(e.selection.main.from),n=Wh(t.state,i.from);return n.line?Th(t):!!n.block&&Xh(t)}},{key:"Alt-A",run:Ah}].concat([{key:"ArrowLeft",run:uc,shift:Pc,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:t=>Oc(t,!fc(t)),shift:t=>Cc(t,!fc(t)),preventDefault:!0},{mac:"Cmd-ArrowLeft",run:t=>lc(t,(e=>Sc(t,e,!fc(t)))),shift:t=>xc(t,(e=>Sc(t,e,!fc(t)))),preventDefault:!0},{key:"ArrowRight",run:dc,shift:Zc,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:t=>Oc(t,fc(t)),shift:t=>Cc(t,fc(t)),preventDefault:!0},{mac:"Cmd-ArrowRight",run:t=>lc(t,(e=>Sc(t,e,fc(t)))),shift:t=>xc(t,(e=>Sc(t,e,fc(t)))),preventDefault:!0},{key:"ArrowUp",run:wc,shift:Ac,preventDefault:!0},{mac:"Cmd-ArrowUp",run:qc,shift:Ec},{mac:"Ctrl-ArrowUp",run:Qc,shift:Rc},{key:"ArrowDown",run:vc,shift:Xc,preventDefault:!0},{mac:"Cmd-ArrowDown",run:Mc,shift:Dc},{mac:"Ctrl-ArrowDown",run:kc,shift:Yc},{key:"PageUp",run:Qc,shift:Rc},{key:"PageDown",run:kc,shift:Yc},{key:"Home",run:t=>lc(t,(e=>Sc(t,e,!1))),shift:t=>xc(t,(e=>Sc(t,e,!1))),preventDefault:!0},{key:"Mod-Home",run:qc,shift:Ec},{key:"End",run:t=>lc(t,(e=>Sc(t,e,!0))),shift:t=>xc(t,(e=>Sc(t,e,!0))),preventDefault:!0},{key:"Mod-End",run:Mc,shift:Dc},{key:"Enter",run:Hc},{key:"Mod-a",run:({state:t,dispatch:e})=>(e(t.update({selection:{anchor:0,head:t.doc.length},userEvent:"select"})),!0)},{key:"Backspace",run:Bc,shift:Bc},{key:"Delete",run:zc},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Ic},{key:"Mod-Delete",mac:"Alt-Delete",run:t=>Gc(t,!0)},{mac:"Mod-Backspace",run:t=>jc(t,(e=>{let i=t.moveToLineBoundary(e,!1).head;return e.head>i?i:Math.max(0,e.head-1)}))},{mac:"Mod-Delete",run:t=>jc(t,(e=>{let i=t.moveToLineBoundary(e,!0).head;return e.head<i?i:Math.min(t.state.doc.length,e.head+1)}))}].concat([{key:"Ctrl-b",run:uc,shift:Pc,preventDefault:!0},{key:"Ctrl-f",run:dc,shift:Zc},{key:"Ctrl-p",run:wc,shift:Ac},{key:"Ctrl-n",run:vc,shift:Xc},{key:"Ctrl-a",run:t=>lc(t,(e=>V.cursor(t.lineBlockAt(e.head).from,1))),shift:t=>xc(t,(e=>V.cursor(t.lineBlockAt(e.head).from)))},{key:"Ctrl-e",run:t=>lc(t,(e=>V.cursor(t.lineBlockAt(e.head).to,-1))),shift:t=>xc(t,(e=>V.cursor(t.lineBlockAt(e.head).to)))},{key:"Ctrl-d",run:zc},{key:"Ctrl-h",run:Bc},{key:"Ctrl-k",run:t=>jc(t,(e=>{let i=t.lineBlockAt(e.head).to;return e.head<i?i:Math.min(t.state.doc.length,e.head+1)}))},{key:"Ctrl-Alt-h",run:Ic},{key:"Ctrl-o",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((t=>({changes:{from:t.from,to:t.to,insert:h.of(["",""])},range:V.cursor(t.from)})));return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}},{key:"Ctrl-t",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((e=>{if(!e.empty||0==e.from||e.from==t.doc.length)return{range:e};let i=e.from,n=t.doc.lineAt(i),s=i==n.from?i-1:k(n.text,i-n.from,!1)+n.from,r=i==n.to?i+1:k(n.text,i-n.from,!0)+n.from;return{changes:{from:s,to:r,insert:t.doc.slice(i,r).append(t.doc.slice(s,i))},range:V.cursor(r)}}));return!i.changes.empty&&(e(t.update(i,{scrollIntoView:!0,userEvent:"move.character"})),!0)}},{key:"Ctrl-v",run:kc}].map((t=>({mac:t.key,run:t.run,shift:t.shift}))))),nf={key:"Tab",run:Jc,shift:tf};function sf(){var t=arguments[0];"string"==typeof t&&(t=document.createElement(t));var e=1,i=arguments[1];if(i&&"object"==typeof i&&null==i.nodeType&&!Array.isArray(i)){for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)){var s=i[n];"string"==typeof s?t.setAttribute(n,s):null!=s&&(t[n]=s)}e++}for(;e<arguments.length;e++)rf(t,arguments[e]);return t}function rf(t,e){if("string"==typeof e)t.appendChild(document.createTextNode(e));else if(null==e);else if(null!=e.nodeType)t.appendChild(e);else{if(!Array.isArray(e))throw new RangeError("Unsupported child node: "+e);for(var i=0;i<e.length;i++)rf(t,e[i])}}const of="function"==typeof String.prototype.normalize?t=>t.normalize("NFKD"):t=>t;class af{constructor(t,e,i=0,n=t.length,s,r){this.test=r,this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=t.iterRange(i,n),this.bufferStart=i,this.normalize=s?t=>s(of(t)):of,this.query=this.normalize(e)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return Z(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let t=this.peek();if(t<0)return this.done=!0,this;let e=C(t),i=this.bufferStart+this.bufferPos;this.bufferPos+=T(t);let n=this.normalize(e);for(let s=0,r=i;;s++){let t=n.charCodeAt(s),o=this.match(t,r,this.bufferPos+this.bufferStart);if(s==n.length-1){if(o)return this.value=o,this;break}r==i&&s<e.length&&e.charCodeAt(s)==t&&r++}}}match(t,e,i){let n=null;for(let s=0;s<this.matches.length;s+=2){let e=this.matches[s],r=!1;this.query.charCodeAt(e)==t&&(e==this.query.length-1?n={from:this.matches[s+1],to:i}:(this.matches[s]++,r=!0)),r||(this.matches.splice(s,2),s-=2)}return this.query.charCodeAt(0)==t&&(1==this.query.length?n={from:e,to:i}:this.matches.push(1,e)),n&&this.test&&!this.test(n.from,n.to,this.buffer,this.bufferStart)&&(n=null),n}}"undefined"!=typeof Symbol&&(af.prototype[Symbol.iterator]=function(){return this});const lf={from:-1,to:-1,match:/.*/.exec("")},hf="gm"+(null==/x/.unicode?"":"u");class cf{constructor(t,e,i,n=0,s=t.length){if(this.text=t,this.to=s,this.curLine="",this.done=!1,this.value=lf,/\\[sWDnr]|\n|\r|\[\^/.test(e))return new df(t,e,i,n,s);this.re=new RegExp(e,hf+((null==i?void 0:i.ignoreCase)?"i":"")),this.test=null==i?void 0:i.test,this.iter=t.iter();let r=t.lineAt(n);this.curLineStart=r.from,this.matchPos=Of(t,n),this.getLine(this.curLineStart)}getLine(t){this.iter.next(t),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let t=this.matchPos-this.curLineStart;;){this.re.lastIndex=t;let e=this.matchPos<=this.to&&this.re.exec(this.curLine);if(e){let i=this.curLineStart+e.index,n=i+e[0].length;if(this.matchPos=Of(this.text,n+(i==n?1:0)),i==this.curLineStart+this.curLine.length&&this.nextLine(),(i<n||i>this.value.to)&&(!this.test||this.test(i,n,e)))return this.value={from:i,to:n,match:e},this;t=this.matchPos-this.curLineStart}else{if(!(this.curLineStart+this.curLine.length<this.to))return this.done=!0,this;this.nextLine(),t=0}}}}const ff=new WeakMap;class uf{constructor(t,e){this.from=t,this.text=e}get to(){return this.from+this.text.length}static get(t,e,i){let n=ff.get(t);if(!n||n.from>=i||n.to<=e){let n=new uf(e,t.sliceString(e,i));return ff.set(t,n),n}if(n.from==e&&n.to==i)return n;let{text:s,from:r}=n;return r>e&&(s=t.sliceString(e,r)+s,r=e),n.to<i&&(s+=t.sliceString(n.to,i)),ff.set(t,new uf(r,s)),new uf(e,s.slice(e-r,i-r))}}class df{constructor(t,e,i,n,s){this.text=t,this.to=s,this.done=!1,this.value=lf,this.matchPos=Of(t,n),this.re=new RegExp(e,hf+((null==i?void 0:i.ignoreCase)?"i":"")),this.test=null==i?void 0:i.test,this.flat=uf.get(t,n,this.chunkEnd(n+5e3))}chunkEnd(t){return t>=this.to?this.to:this.text.lineAt(t).to}next(){for(;;){let t=this.re.lastIndex=this.matchPos-this.flat.from,e=this.re.exec(this.flat.text);if(e&&!e[0]&&e.index==t&&(this.re.lastIndex=t+1,e=this.re.exec(this.flat.text)),e){let t=this.flat.from+e.index,i=t+e[0].length;if((this.flat.to>=this.to||e.index+e[0].length<=this.flat.text.length-10)&&(!this.test||this.test(t,i,e)))return this.value={from:t,to:i,match:e},this.matchPos=Of(this.text,i+(t==i?1:0)),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=uf.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+2*this.flat.text.length))}}}function Of(t,e){if(e>=t.length)return e;let i,n=t.lineAt(e);for(;e<n.to&&(i=n.text.charCodeAt(e-n.from))>=56320&&i<57344;)e++;return e}function pf(t){let e=sf("input",{class:"cm-textfield",name:"line",value:String(t.state.doc.lineAt(t.state.selection.main.head).number)});function i(){let i=/^([+-])?(\d+)?(:\d+)?(%)?$/.exec(e.value);if(!i)return;let{state:n}=t,s=n.doc.lineAt(n.selection.main.head),[,r,o,a,l]=i,h=a?+a.slice(1):0,c=o?+o:s.number;if(o&&l){let t=c/100;r&&(t=t*("-"==r?-1:1)+s.number/n.doc.lines),c=Math.round(n.doc.lines*t)}else o&&r&&(c=c*("-"==r?-1:1)+s.number);let f=n.doc.line(Math.max(1,Math.min(n.doc.lines,c))),u=V.cursor(f.from+Math.max(0,Math.min(h,f.length)));t.dispatch({effects:[gf.of(!1),tr.scrollIntoView(u.from,{y:"center"})],selection:u}),t.focus()}return{dom:sf("form",{class:"cm-gotoLine",onkeydown:e=>{27==e.keyCode?(e.preventDefault(),t.dispatch({effects:gf.of(!1)}),t.focus()):13==e.keyCode&&(e.preventDefault(),i())},onsubmit:t=>{t.preventDefault(),i()}},sf("label",t.state.phrase("Go to line"),": ",e)," ",sf("button",{class:"cm-button",type:"submit"},t.state.phrase("go")))}}"undefined"!=typeof Symbol&&(cf.prototype[Symbol.iterator]=df.prototype[Symbol.iterator]=function(){return this});const gf=yt.define(),mf=K.define({create:()=>!0,update(t,e){for(let i of e.effects)i.is(gf)&&(t=i.value);return t},provide:t=>Qo.from(t,(t=>t?pf:null))}),wf=tr.baseTheme({".cm-panel.cm-gotoLine":{padding:"2px 6px 4px","& label":{fontSize:"80%"}}}),vf={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},bf=G.define({combine:t=>Rt(t,vf,{highlightWordAroundCursor:(t,e)=>t||e,minSelectionLength:Math.min,maxMatches:Math.min})});function yf(t){let e=[$f,xf];return t&&e.push(bf.of(t)),e}const Qf=Oi.mark({class:"cm-selectionMatch"}),kf=Oi.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function Sf(t,e,i,n){return!(0!=i&&t(e.sliceDoc(i-1,i))==Ct.Word||n!=e.doc.length&&t(e.sliceDoc(n,n+1))==Ct.Word)}const xf=rn.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.selectionSet||t.docChanged||t.viewportChanged)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=t.state.facet(bf),{state:i}=t,n=i.selection;if(n.ranges.length>1)return Oi.none;let s,r=n.main,o=null;if(r.empty){if(!e.highlightWordAroundCursor)return Oi.none;let t=i.wordAt(r.head);if(!t)return Oi.none;o=i.charCategorizer(r.head),s=i.sliceDoc(t.from,t.to)}else{let t=r.to-r.from;if(t<e.minSelectionLength||t>200)return Oi.none;if(e.wholeWords){if(s=i.sliceDoc(r.from,r.to),o=i.charCategorizer(r.head),!Sf(o,i,r.from,r.to)||!function(t,e,i,n){return t(e.sliceDoc(i,i+1))==Ct.Word&&t(e.sliceDoc(n-1,n))==Ct.Word}(o,i,r.from,r.to))return Oi.none}else if(s=i.sliceDoc(r.from,r.to),!s)return Oi.none}let a=[];for(let l of t.visibleRanges){let t=new af(i.doc,s,l.from,l.to);for(;!t.next().done;){let{from:n,to:s}=t.value;if((!o||Sf(o,i,n,s))&&(r.empty&&n<=r.from&&s>=r.to?a.push(kf.range(n,s)):(n>=r.to||s<=r.from)&&a.push(Qf.range(n,s)),a.length>e.maxMatches))return Oi.none}}return Oi.set(a)}},{decorations:t=>t.decorations}),$f=tr.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}});const Pf=G.define({combine:t=>Rt(t,{top:!1,caseSensitive:!1,literal:!1,regexp:!1,wholeWord:!1,createPanel:t=>new iu(t),scrollToMatch:t=>tr.scrollIntoView(t)})});class Zf{constructor(t){this.search=t.search,this.caseSensitive=!!t.caseSensitive,this.literal=!!t.literal,this.regexp=!!t.regexp,this.replace=t.replace||"",this.valid=!!this.search&&(!this.regexp||function(t){try{return new RegExp(t,hf),!0}catch(e){return!1}}(this.search)),this.unquoted=this.unquote(this.search),this.wholeWord=!!t.wholeWord}unquote(t){return this.literal?t:t.replace(/\\([nrt\\])/g,((t,e)=>"n"==e?"\n":"r"==e?"\r":"t"==e?"\t":"\\"))}eq(t){return this.search==t.search&&this.replace==t.replace&&this.caseSensitive==t.caseSensitive&&this.regexp==t.regexp&&this.wholeWord==t.wholeWord}create(){return this.regexp?new Yf(this):new Af(this)}getCursor(t,e=0,i){let n=t.doc?t:Wt.create({doc:t});return null==i&&(i=n.doc.length),this.regexp?Xf(this,n,e,i):Tf(this,n,e,i)}}class Cf{constructor(t){this.spec=t}}function Tf(t,e,i,n){return new af(e.doc,t.unquoted,i,n,t.caseSensitive?void 0:t=>t.toLowerCase(),t.wholeWord?(s=e.doc,r=e.charCategorizer(e.selection.main.head),(t,e,i,n)=>((n>t||n+i.length<e)&&(n=Math.max(0,t-2),i=s.sliceString(n,Math.min(s.length,e+2))),!(r(Wf(i,t-n))==Ct.Word&&r(Rf(i,t-n))==Ct.Word||r(Rf(i,e-n))==Ct.Word&&r(Wf(i,e-n))==Ct.Word))):void 0);var s,r}class Af extends Cf{constructor(t){super(t)}nextMatch(t,e,i){let n=Tf(this.spec,t,i,t.doc.length).nextOverlapping();return n.done&&(n=Tf(this.spec,t,0,e).nextOverlapping()),n.done?null:n.value}prevMatchInRange(t,e,i){for(let n=i;;){let i=Math.max(e,n-1e4-this.spec.unquoted.length),s=Tf(this.spec,t,i,n),r=null;for(;!s.nextOverlapping().done;)r=s.value;if(r)return r;if(i==e)return null;n-=1e4}}prevMatch(t,e,i){return this.prevMatchInRange(t,0,e)||this.prevMatchInRange(t,i,t.doc.length)}getReplacement(t){return this.spec.unquote(this.spec.replace)}matchAll(t,e){let i=Tf(this.spec,t,0,t.doc.length),n=[];for(;!i.next().done;){if(n.length>=e)return null;n.push(i.value)}return n}highlight(t,e,i,n){let s=Tf(this.spec,t,Math.max(0,e-this.spec.unquoted.length),Math.min(i+this.spec.unquoted.length,t.doc.length));for(;!s.next().done;)n(s.value.from,s.value.to)}}function Xf(t,e,i,n){return new cf(e.doc,t.search,{ignoreCase:!t.caseSensitive,test:t.wholeWord?(s=e.charCategorizer(e.selection.main.head),(t,e,i)=>!i[0].length||(s(Wf(i.input,i.index))!=Ct.Word||s(Rf(i.input,i.index))!=Ct.Word)&&(s(Rf(i.input,i.index+i[0].length))!=Ct.Word||s(Wf(i.input,i.index+i[0].length))!=Ct.Word)):void 0},i,n);var s}function Wf(t,e){return t.slice(k(t,e,!1),e)}function Rf(t,e){return t.slice(e,k(t,e))}class Yf extends Cf{nextMatch(t,e,i){let n=Xf(this.spec,t,i,t.doc.length).next();return n.done&&(n=Xf(this.spec,t,0,e).next()),n.done?null:n.value}prevMatchInRange(t,e,i){for(let n=1;;n++){let s=Math.max(e,i-1e4*n),r=Xf(this.spec,t,s,i),o=null;for(;!r.next().done;)o=r.value;if(o&&(s==e||o.from>s+10))return o;if(s==e)return null}}prevMatch(t,e,i){return this.prevMatchInRange(t,0,e)||this.prevMatchInRange(t,i,t.doc.length)}getReplacement(t){return this.spec.unquote(this.spec.replace).replace(/\$([$&\d+])/g,((e,i)=>"$"==i?"$":"&"==i?t.match[0]:"0"!=i&&+i<t.match.length?t.match[i]:e))}matchAll(t,e){let i=Xf(this.spec,t,0,t.doc.length),n=[];for(;!i.next().done;){if(n.length>=e)return null;n.push(i.value)}return n}highlight(t,e,i,n){let s=Xf(this.spec,t,Math.max(0,e-250),Math.min(i+250,t.doc.length));for(;!s.next().done;)n(s.value.from,s.value.to)}}const qf=yt.define(),Mf=yt.define(),Ef=K.define({create:t=>new Df(Hf(t).create(),null),update(t,e){for(let i of e.effects)i.is(qf)?t=new Df(i.value.create(),t.panel):i.is(Mf)&&(t=new Df(t.query,i.value?Uf:null));return t},provide:t=>Qo.from(t,(t=>t.panel))});class Df{constructor(t,e){this.query=t,this.panel=e}}const jf=Oi.mark({class:"cm-searchMatch"}),_f=Oi.mark({class:"cm-searchMatch cm-searchMatch-selected"}),Vf=rn.fromClass(class{constructor(t){this.view=t,this.decorations=this.highlight(t.state.field(Ef))}update(t){let e=t.state.field(Ef);(e!=t.startState.field(Ef)||t.docChanged||t.selectionSet||t.viewportChanged)&&(this.decorations=this.highlight(e))}highlight({query:t,panel:e}){if(!e||!t.spec.valid)return Oi.none;let{view:i}=this,n=new jt;for(let s=0,r=i.visibleRanges,o=r.length;s<o;s++){let{from:e,to:a}=r[s];for(;s<o-1&&a>r[s+1].from-500;)a=r[++s].to;t.highlight(i.state,e,a,((t,e)=>{let s=i.state.selection.ranges.some((i=>i.from==t&&i.to==e));n.add(t,e,s?_f:jf)}))}return n.finish()}},{decorations:t=>t.decorations});function Bf(t){return e=>{let i=e.state.field(Ef,!1);return i&&i.query.spec.valid?t(e,i):Jf(e)}}const zf=Bf(((t,{query:e})=>{let{to:i}=t.state.selection.main,n=e.nextMatch(t.state,i,i);if(!n)return!1;let s=V.single(n.from,n.to),r=t.state.facet(Pf);return t.dispatch({selection:s,effects:[ou(t,n),r.scrollToMatch(s.main,t)],userEvent:"select.search"}),Kf(t),!0})),Gf=Bf(((t,{query:e})=>{let{state:i}=t,{from:n}=i.selection.main,s=e.prevMatch(i,n,n);if(!s)return!1;let r=V.single(s.from,s.to),o=t.state.facet(Pf);return t.dispatch({selection:r,effects:[ou(t,s),o.scrollToMatch(r.main,t)],userEvent:"select.search"}),Kf(t),!0})),If=Bf(((t,{query:e})=>{let i=e.matchAll(t.state,1e3);return!(!i||!i.length)&&(t.dispatch({selection:V.create(i.map((t=>V.range(t.from,t.to)))),userEvent:"select.search.matches"}),!0)})),Lf=Bf(((t,{query:e})=>{let{state:i}=t,{from:n,to:s}=i.selection.main;if(i.readOnly)return!1;let r=e.nextMatch(i,n,n);if(!r)return!1;let o,a,l=[],h=[];if(r.from==n&&r.to==s&&(a=i.toText(e.getReplacement(r)),l.push({from:r.from,to:r.to,insert:a}),r=e.nextMatch(i,r.from,r.to),h.push(tr.announce.of(i.phrase("replaced match on line $",i.doc.lineAt(n).number)+"."))),r){let e=0==l.length||l[0].from>=r.to?0:r.to-r.from-a.length;o=V.single(r.from-e,r.to-e),h.push(ou(t,r)),h.push(i.facet(Pf).scrollToMatch(o.main,t))}return t.dispatch({changes:l,selection:o,effects:h,userEvent:"input.replace"}),!0})),Nf=Bf(((t,{query:e})=>{if(t.state.readOnly)return!1;let i=e.matchAll(t.state,1e9).map((t=>{let{from:i,to:n}=t;return{from:i,to:n,insert:e.getReplacement(t)}}));if(!i.length)return!1;let n=t.state.phrase("replaced $ matches",i.length)+".";return t.dispatch({changes:i,effects:tr.announce.of(n),userEvent:"input.replace.all"}),!0}));function Uf(t){return t.state.facet(Pf).createPanel(t)}function Hf(t,e){var i,n,s,r,o;let a=t.selection.main,l=a.empty||a.to>a.from+100?"":t.sliceDoc(a.from,a.to);if(e&&!l)return e;let h=t.facet(Pf);return new Zf({search:(null!==(i=null==e?void 0:e.literal)&&void 0!==i?i:h.literal)?l:l.replace(/\n/g,"\\n"),caseSensitive:null!==(n=null==e?void 0:e.caseSensitive)&&void 0!==n?n:h.caseSensitive,literal:null!==(s=null==e?void 0:e.literal)&&void 0!==s?s:h.literal,regexp:null!==(r=null==e?void 0:e.regexp)&&void 0!==r?r:h.regexp,wholeWord:null!==(o=null==e?void 0:e.wholeWord)&&void 0!==o?o:h.wholeWord})}function Ff(t){let e=wo(t,Uf);return e&&e.dom.querySelector("[main-field]")}function Kf(t){let e=Ff(t);e&&e==t.root.activeElement&&e.select()}const Jf=t=>{let e=t.state.field(Ef,!1);if(e&&e.panel){let i=Ff(t);if(i&&i!=t.root.activeElement){let n=Hf(t.state,e.query.spec);n.valid&&t.dispatch({effects:qf.of(n)}),i.focus(),i.select()}}else t.dispatch({effects:[Mf.of(!0),e?qf.of(Hf(t.state,e.query.spec)):yt.appendConfig.of(lu)]});return!0},tu=t=>{let e=t.state.field(Ef,!1);if(!e||!e.panel)return!1;let i=wo(t,Uf);return i&&i.dom.contains(t.root.activeElement)&&t.focus(),t.dispatch({effects:Mf.of(!1)}),!0},eu=[{key:"Mod-f",run:Jf,scope:"editor search-panel"},{key:"F3",run:zf,shift:Gf,scope:"editor search-panel",preventDefault:!0},{key:"Mod-g",run:zf,shift:Gf,scope:"editor search-panel",preventDefault:!0},{key:"Escape",run:tu,scope:"editor search-panel"},{key:"Mod-Shift-l",run:({state:t,dispatch:e})=>{let i=t.selection;if(i.ranges.length>1||i.main.empty)return!1;let{from:n,to:s}=i.main,r=[],o=0;for(let a=new af(t.doc,t.sliceDoc(n,s));!a.next().done;){if(r.length>1e3)return!1;a.value.from==n&&(o=r.length),r.push(V.range(a.value.from,a.value.to))}return e(t.update({selection:V.create(r,o),userEvent:"select.search.matches"})),!0}},{key:"Mod-Alt-g",run:t=>{let e=wo(t,pf);if(!e){let i=[gf.of(!0)];null==t.state.field(mf,!1)&&i.push(yt.appendConfig.of([mf,wf])),t.dispatch({effects:i}),e=wo(t,pf)}return e&&e.dom.querySelector("input").select(),!0}},{key:"Mod-d",run:({state:t,dispatch:e})=>{let{ranges:i}=t.selection;if(i.some((t=>t.from===t.to)))return(({state:t,dispatch:e})=>{let{selection:i}=t,n=V.create(i.ranges.map((e=>t.wordAt(e.head)||V.cursor(e.head))),i.mainIndex);return!n.eq(i)&&(e(t.update({selection:n})),!0)})({state:t,dispatch:e});let n=t.sliceDoc(i[0].from,i[0].to);if(t.selection.ranges.some((e=>t.sliceDoc(e.from,e.to)!=n)))return!1;let s=function(t,e){let{main:i,ranges:n}=t.selection,s=t.wordAt(i.head),r=s&&s.from==i.from&&s.to==i.to;for(let o=!1,a=new af(t.doc,e,n[n.length-1].to);;){if(a.next(),!a.done){if(o&&n.some((t=>t.from==a.value.from)))continue;if(r){let e=t.wordAt(a.value.from);if(!e||e.from!=a.value.from||e.to!=a.value.to)continue}return a.value}if(o)return null;a=new af(t.doc,e,0,Math.max(0,n[n.length-1].from-1)),o=!0}}(t,n);return!!s&&(e(t.update({selection:t.selection.addRange(V.range(s.from,s.to),!1),effects:tr.scrollIntoView(s.to)})),!0)},preventDefault:!0}];class iu{constructor(t){this.view=t;let e=this.query=t.state.field(Ef).query.spec;function i(t,e,i){return sf("button",{class:"cm-button",name:t,onclick:e,type:"button"},i)}this.commit=this.commit.bind(this),this.searchField=sf("input",{value:e.search,placeholder:nu(t,"Find"),"aria-label":nu(t,"Find"),class:"cm-textfield",name:"search",form:"","main-field":"true",onchange:this.commit,onkeyup:this.commit}),this.replaceField=sf("input",{value:e.replace,placeholder:nu(t,"Replace"),"aria-label":nu(t,"Replace"),class:"cm-textfield",name:"replace",form:"",onchange:this.commit,onkeyup:this.commit}),this.caseField=sf("input",{type:"checkbox",name:"case",form:"",checked:e.caseSensitive,onchange:this.commit}),this.reField=sf("input",{type:"checkbox",name:"re",form:"",checked:e.regexp,onchange:this.commit}),this.wordField=sf("input",{type:"checkbox",name:"word",form:"",checked:e.wholeWord,onchange:this.commit}),this.dom=sf("div",{onkeydown:t=>this.keydown(t),class:"cm-search"},[this.searchField,i("next",(()=>zf(t)),[nu(t,"next")]),i("prev",(()=>Gf(t)),[nu(t,"previous")]),i("select",(()=>If(t)),[nu(t,"all")]),sf("label",null,[this.caseField,nu(t,"match case")]),sf("label",null,[this.reField,nu(t,"regexp")]),sf("label",null,[this.wordField,nu(t,"by word")]),...t.state.readOnly?[]:[sf("br"),this.replaceField,i("replace",(()=>Lf(t)),[nu(t,"replace")]),i("replaceAll",(()=>Nf(t)),[nu(t,"replace all")])],sf("button",{name:"close",onclick:()=>tu(t),"aria-label":nu(t,"close"),type:"button"},["×"])])}commit(){let t=new Zf({search:this.searchField.value,caseSensitive:this.caseField.checked,regexp:this.reField.checked,wholeWord:this.wordField.checked,replace:this.replaceField.value});t.eq(this.query)||(this.query=t,this.view.dispatch({effects:qf.of(t)}))}keydown(t){var e,i,n;e=this.view,i=t,n="search-panel",dr(cr(e.state),i,e,n)?t.preventDefault():13==t.keyCode&&t.target==this.searchField?(t.preventDefault(),(t.shiftKey?Gf:zf)(this.view)):13==t.keyCode&&t.target==this.replaceField&&(t.preventDefault(),Lf(this.view))}update(t){for(let e of t.transactions)for(let t of e.effects)t.is(qf)&&!t.value.eq(this.query)&&this.setQuery(t.value)}setQuery(t){this.query=t,this.searchField.value=t.search,this.replaceField.value=t.replace,this.caseField.checked=t.caseSensitive,this.reField.checked=t.regexp,this.wordField.checked=t.wholeWord}mount(){this.searchField.select()}get pos(){return 80}get top(){return this.view.state.facet(Pf).top}}function nu(t,e){return t.state.phrase(e)}const su=30,ru=/[\s\.,:;?!]/;function ou(t,{from:e,to:i}){let n=t.state.doc.lineAt(e),s=t.state.doc.lineAt(i).to,r=Math.max(n.from,e-su),o=Math.min(s,i+su),a=t.state.sliceDoc(r,o);if(r!=n.from)for(let l=0;l<su;l++)if(!ru.test(a[l+1])&&ru.test(a[l])){a=a.slice(l);break}if(o!=s)for(let l=a.length-1;l>a.length-su;l--)if(!ru.test(a[l-1])&&ru.test(a[l])){a=a.slice(0,l);break}return tr.announce.of(`${t.state.phrase("current match")}. ${a} ${t.state.phrase("on line")} ${n.number}.`)}const au=tr.baseTheme({".cm-panel.cm-search":{padding:"2px 6px 4px",position:"relative","& [name=close]":{position:"absolute",top:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:0,margin:0},"& input, & button, & label":{margin:".2em .6em .2em 0"},"& input[type=checkbox]":{marginRight:".2em"},"& label":{fontSize:"80%",whiteSpace:"pre"}},"&light .cm-searchMatch":{backgroundColor:"#ffff0054"},"&dark .cm-searchMatch":{backgroundColor:"#00ffff8a"},"&light .cm-searchMatch-selected":{backgroundColor:"#ff6a0054"},"&dark .cm-searchMatch-selected":{backgroundColor:"#ff00ff8a"}}),lu=[Ef,st.low(Vf),au];class hu{constructor(t,e,i){this.state=t,this.pos=e,this.explicit=i,this.abortListeners=[]}tokenBefore(t){let e=sl(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),s=n.search(Ou(t,!1));return s<0?null:{from:i+s,to:this.pos,text:n.slice(s)}}get aborted(){return null==this.abortListeners}addEventListener(t,e){"abort"==t&&this.abortListeners&&this.abortListeners.push(e)}}function cu(t){let e=Object.keys(t).join(""),i=/\w/.test(e);return i&&(e=e.replace(/\w/g,"")),`[${i?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function fu(t){let e=t.map((t=>"string"==typeof t?{label:t}:t)),[i,n]=e.every((t=>/^\w+$/.test(t.label)))?[/\w*$/,/\w+$/]:function(t){let e=Object.create(null),i=Object.create(null);for(let{label:s}of t){e[s[0]]=!0;for(let t=1;t<s.length;t++)i[s[t]]=!0}let n=cu(e)+cu(i)+"*$";return[new RegExp("^"+n),new RegExp(n)]}(e);return t=>{let s=t.matchBefore(n);return s||t.explicit?{from:s?s.from:t.pos,options:e,validFor:i}:null}}class uu{constructor(t,e,i,n){this.completion=t,this.source=e,this.match=i,this.score=n}}function du(t){return t.selection.main.from}function Ou(t,e){var i;let{source:n}=t,s=e&&"^"!=n[0],r="$"!=n[n.length-1];return s||r?new RegExp(`${s?"^":""}(?:${n})${r?"$":""}`,null!==(i=t.flags)&&void 0!==i?i:t.ignoreCase?"i":""):t}const pu=wt.define();const gu=new WeakMap;function mu(t){if(!Array.isArray(t))return t;let e=gu.get(t);return e||gu.set(t,e=fu(t)),e}const wu=yt.define(),vu=yt.define();class bu{constructor(t){this.pattern=t,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let e=0;e<t.length;){let i=Z(t,e),n=T(i);this.chars.push(i);let s=t.slice(e,e+n),r=s.toUpperCase();this.folded.push(Z(r==s?s.toLowerCase():r,0)),e+=n}this.astral=t.length!=this.chars.length}ret(t,e){return this.score=t,this.matched=e,this}match(t){if(0==this.pattern.length)return this.ret(-100,[]);if(t.length<this.pattern.length)return null;let{chars:e,folded:i,any:n,precise:s,byWord:r}=this;if(1==e.length){let n=Z(t,0),s=T(n),r=s==t.length?0:-100;if(n==e[0]);else{if(n!=i[0])return null;r+=-200}return this.ret(r,[0,s])}let o=t.indexOf(this.pattern);if(0==o)return this.ret(t.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=e.length,l=0;if(o<0){for(let s=0,r=Math.min(t.length,200);s<r&&l<a;){let r=Z(t,s);r!=e[l]&&r!=i[l]||(n[l++]=s),s+=T(r)}if(l<a)return null}let h=0,c=0,f=!1,u=0,d=-1,O=-1,p=/[a-z]/.test(t),g=!0;for(let m=0,w=Math.min(t.length,200),v=0;m<w&&c<a;){let n=Z(t,m);o<0&&(h<a&&n==e[h]&&(s[h++]=m),u<a&&(n==e[u]||n==i[u]?(0==u&&(d=m),O=m+1,u++):u=0));let l,w=n<255?n>=48&&n<=57||n>=97&&n<=122?2:n>=65&&n<=90?1:0:(l=C(n))!=l.toLowerCase()?1:l!=l.toUpperCase()?2:0;(!m||1==w&&p||0==v&&0!=w)&&(e[c]==n||i[c]==n&&(f=!0)?r[c++]=m:r.length&&(g=!1)),v=w,m+=T(n)}return c==a&&0==r[0]&&g?this.result((f?-200:0)-100,r,t):u==a&&0==d?this.ret(-200-t.length+(O==t.length?0:-100),[0,O]):o>-1?this.ret(-700-t.length,[o,o+this.pattern.length]):u==a?this.ret(-900-t.length,[d,O]):c==a?this.result((f?-200:0)-100-700+(g?0:-1100),r,t):2==e.length?null:this.result((n[0]?-700:0)-200-1100,n,t)}result(t,e,i){let n=[],s=0;for(let r of e){let t=r+(this.astral?T(Z(i,r)):1);s&&n[s-1]==r?n[s-1]=t:(n[s++]=r,n[s++]=t)}return this.ret(t-i.length,n)}}class yu{constructor(t){this.pattern=t,this.matched=[],this.score=0,this.folded=t.toLowerCase()}match(t){if(t.length<this.pattern.length)return null;let e=t.slice(0,this.pattern.length),i=e==this.pattern?0:e.toLowerCase()==this.folded?-200:null;return null==i?null:(this.matched=[0,e.length],this.score=i+(t.length==this.pattern.length?0:-100),this)}}const Qu=G.define({combine:t=>Rt(t,{activateOnTyping:!0,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:Su,filterStrict:!1,compareCompletions:(t,e)=>t.label.localeCompare(e.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(t,e)=>t&&e,closeOnBlur:(t,e)=>t&&e,icons:(t,e)=>t&&e,tooltipClass:(t,e)=>i=>ku(t(i),e(i)),optionClass:(t,e)=>i=>ku(t(i),e(i)),addToOptions:(t,e)=>t.concat(e),filterStrict:(t,e)=>t||e})});function ku(t,e){return t?e?t+" "+e:t:e}function Su(t,e,i,n,s,r){let o,a,l=t.textDirection==ki.RTL,h=l,c=!1,f="top",u=e.left-s.left,d=s.right-e.right,O=n.right-n.left,p=n.bottom-n.top;if(h&&u<Math.min(O,d)?h=!1:!h&&d<Math.min(O,u)&&(h=!0),O<=(h?u:d))o=Math.max(s.top,Math.min(i.top,s.bottom-p))-e.top,a=Math.min(400,h?u:d);else{c=!0,a=Math.min(400,(l?e.right:s.right-e.left)-30);let t=s.bottom-e.bottom;t>=p||t>e.top?o=i.bottom-e.top:(f="bottom",o=e.bottom-i.top)}return{style:`${f}: ${o/((e.bottom-e.top)/r.offsetHeight)}px; max-width: ${a/((e.right-e.left)/r.offsetWidth)}px`,class:"cm-completionInfo-"+(c?l?"left-narrow":"right-narrow":h?"left":"right")}}function xu(t,e,i){if(t<=i)return{from:0,to:t};if(e<0&&(e=0),e<=t>>1){let t=Math.floor(e/i);return{from:t*i,to:(t+1)*i}}let n=Math.floor((t-e)/i);return{from:t-(n+1)*i,to:t-n*i}}class $u{constructor(t,e,i){this.view=t,this.stateField=e,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:t=>this.placeInfo(t),key:this},this.space=null,this.currentClass="";let n=t.state.field(e),{options:s,selected:r}=n.open,o=t.state.facet(Qu);this.optionContent=function(t){let e=t.addToOptions.slice();return t.icons&&e.push({render(t){let e=document.createElement("div");return e.classList.add("cm-completionIcon"),t.type&&e.classList.add(...t.type.split(/\s+/g).map((t=>"cm-completionIcon-"+t))),e.setAttribute("aria-hidden","true"),e},position:20}),e.push({render(t,e,i,n){let s=document.createElement("span");s.className="cm-completionLabel";let r=t.displayLabel||t.label,o=0;for(let a=0;a<n.length;){let t=n[a++],e=n[a++];t>o&&s.appendChild(document.createTextNode(r.slice(o,t)));let i=s.appendChild(document.createElement("span"));i.appendChild(document.createTextNode(r.slice(t,e))),i.className="cm-completionMatchedText",o=e}return o<r.length&&s.appendChild(document.createTextNode(r.slice(o))),s},position:50},{render(t){if(!t.detail)return null;let e=document.createElement("span");return e.className="cm-completionDetail",e.textContent=t.detail,e},position:80}),e.sort(((t,e)=>t.position-e.position)).map((t=>t.render))}(o),this.optionClass=o.optionClass,this.tooltipClass=o.tooltipClass,this.range=xu(s.length,r,o.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(t.state),this.dom.addEventListener("mousedown",(i=>{let{options:n}=t.state.field(e).open;for(let e,s=i.target;s&&s!=this.dom;s=s.parentNode)if("LI"==s.nodeName&&(e=/-(\d+)$/.exec(s.id))&&+e[1]<n.length)return this.applyCompletion(t,n[+e[1]]),void i.preventDefault()})),this.dom.addEventListener("focusout",(e=>{let i=t.state.field(this.stateField,!1);i&&i.tooltip&&t.state.facet(Qu).closeOnBlur&&e.relatedTarget!=t.contentDOM&&t.dispatch({effects:vu.of(null)})})),this.showOptions(s,n.id)}mount(){this.updateSel()}showOptions(t,e){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t,e,this.range)),this.list.addEventListener("scroll",(()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)}))}update(t){var e;let i=t.state.field(this.stateField),n=t.startState.field(this.stateField);if(this.updateTooltipClass(t.state),i!=n){let{options:s,selected:r,disabled:o}=i.open;n.open&&n.open.options==s||(this.range=xu(s.length,r,t.state.facet(Qu).maxRenderedOptions),this.showOptions(s,i.id)),this.updateSel(),o!=(null===(e=n.open)||void 0===e?void 0:e.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!o)}}updateTooltipClass(t){let e=this.tooltipClass(t);if(e!=this.currentClass){for(let t of this.currentClass.split(" "))t&&this.dom.classList.remove(t);for(let t of e.split(" "))t&&this.dom.classList.add(t);this.currentClass=e}}positioned(t){this.space=t,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let t=this.view.state.field(this.stateField),e=t.open;if((e.selected>-1&&e.selected<this.range.from||e.selected>=this.range.to)&&(this.range=xu(e.options.length,e.selected,this.view.state.facet(Qu).maxRenderedOptions),this.showOptions(e.options,t.id)),this.updateSelectedOption(e.selected)){this.destroyInfo();let{completion:i}=e.options[e.selected],{info:n}=i;if(!n)return;let s="string"==typeof n?document.createTextNode(n):n(i);if(!s)return;"then"in s?s.then((e=>{e&&this.view.state.field(this.stateField,!1)==t&&this.addInfoPane(e,i)})).catch((t=>tn(this.view.state,t,"completion info"))):this.addInfoPane(s,i)}}addInfoPane(t,e){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",null!=t.nodeType)i.appendChild(t),this.infoDestroy=null;else{let{dom:e,destroy:n}=t;i.appendChild(e),this.infoDestroy=n||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(t){let e=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)"LI"==i.nodeName&&i.id?n==t?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),e=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected"):n--;return e&&function(t,e){let i=t.getBoundingClientRect(),n=e.getBoundingClientRect(),s=i.height/t.offsetHeight;n.top<i.top?t.scrollTop-=(i.top-n.top)/s:n.bottom>i.bottom&&(t.scrollTop+=(n.bottom-i.bottom)/s)}(this.list,e),e}measureInfo(){let t=this.dom.querySelector("[aria-selected]");if(!t||!this.info)return null;let e=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=t.getBoundingClientRect(),s=this.space;if(!s){let t=this.dom.ownerDocument.defaultView||window;s={left:0,top:0,right:t.innerWidth,bottom:t.innerHeight}}return n.top>Math.min(s.bottom,e.bottom)-10||n.bottom<Math.max(s.top,e.top)+10?null:this.view.state.facet(Qu).positionInfo(this.view,e,n,i,s,this.dom)}placeInfo(t){this.info&&(t?(t.style&&(this.info.style.cssText=t.style),this.info.className="cm-tooltip cm-completionInfo "+(t.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(t,e,i){const n=document.createElement("ul");n.id=e,n.setAttribute("role","listbox"),n.setAttribute("aria-expanded","true"),n.setAttribute("aria-label",this.view.state.phrase("Completions"));let s=null;for(let r=i.from;r<i.to;r++){let{completion:o,match:a}=t[r],{section:l}=o;if(l){let t="string"==typeof l?l:l.name;if(t!=s&&(r>i.from||0==i.from))if(s=t,"string"!=typeof l&&l.header)n.appendChild(l.header(l));else{n.appendChild(document.createElement("completion-section")).textContent=t}}const h=n.appendChild(document.createElement("li"));h.id=e+"-"+r,h.setAttribute("role","option");let c=this.optionClass(o);c&&(h.className=c);for(let t of this.optionContent){let e=t(o,this.view.state,this.view,a);e&&h.appendChild(e)}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<t.length&&n.classList.add("cm-completionListIncompleteBottom"),n}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function Pu(t,e){return i=>new $u(i,t,e)}function Zu(t){return 100*(t.boost||0)+(t.apply?10:0)+(t.info?5:0)+(t.type?1:0)}class Cu{constructor(t,e,i,n,s,r){this.options=t,this.attrs=e,this.tooltip=i,this.timestamp=n,this.selected=s,this.disabled=r}setSelected(t,e){return t==this.selected||t>=this.options.length?this:new Cu(this.options,Xu(e,t),this.tooltip,this.timestamp,t,this.disabled)}static build(t,e,i,n,s){let r=function(t,e){let i=[],n=null,s=t=>{i.push(t);let{section:e}=t.completion;if(e){n||(n=[]);let t="string"==typeof e?e:e.name;n.some((e=>e.name==t))||n.push("string"==typeof e?{name:t}:e)}},r=e.facet(Qu);for(let h of t)if(h.hasResult()){let t=h.result.getMatch;if(!1===h.result.filter)for(let e of h.result.options)s(new uu(e,h.source,t?t(e):[],1e9-i.length));else{let i,n=e.sliceDoc(h.from,h.to),o=r.filterStrict?new yu(n):new bu(n);for(let e of h.result.options)if(i=o.match(e.label)){let n=e.displayLabel?t?t(e,i.matched):[]:i.matched;s(new uu(e,h.source,n,i.score+(e.boost||0)))}}}if(n){let t=Object.create(null),e=0,s=(t,e)=>{var i,n;return(null!==(i=t.rank)&&void 0!==i?i:1e9)-(null!==(n=e.rank)&&void 0!==n?n:1e9)||(t.name<e.name?-1:1)};for(let i of n.sort(s))e-=1e5,t[i.name]=e;for(let n of i){let{section:e}=n.completion;e&&(n.score+=t["string"==typeof e?e:e.name])}}let o=[],a=null,l=r.compareCompletions;for(let h of i.sort(((t,e)=>e.score-t.score||l(t.completion,e.completion)))){let t=h.completion;!a||a.label!=t.label||a.detail!=t.detail||null!=a.type&&null!=t.type&&a.type!=t.type||a.apply!=t.apply||a.boost!=t.boost?o.push(h):Zu(h.completion)>Zu(a)&&(o[o.length-1]=h),a=h.completion}return o}(t,e);if(!r.length)return n&&t.some((t=>1==t.state))?new Cu(n.options,n.attrs,n.tooltip,n.timestamp,n.selected,!0):null;let o=e.facet(Qu).selectOnOpen?0:-1;if(n&&n.selected!=o&&-1!=n.selected){let t=n.options[n.selected].completion;for(let e=0;e<r.length;e++)if(r[e].completion==t){o=e;break}}return new Cu(r,Xu(i,o),{pos:t.reduce(((t,e)=>e.hasResult()?Math.min(t,e.from):t),1e8),create:_u,above:s.aboveCursor},n?n.timestamp:Date.now(),o,!1)}map(t){return new Cu(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:t.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}}class Tu{constructor(t,e,i){this.active=t,this.id=e,this.open=i}static start(){return new Tu(Wu,"cm-ac-"+Math.floor(2e6*Math.random()).toString(36),null)}update(t){let{state:e}=t,i=e.facet(Qu),n=(i.override||e.languageDataAt("autocomplete",du(e)).map(mu)).map((e=>(this.active.find((t=>t.source==e))||new Yu(e,this.active.some((t=>0!=t.state))?1:0)).update(t,i)));n.length==this.active.length&&n.every(((t,e)=>t==this.active[e]))&&(n=this.active);let s=this.open;s&&t.docChanged&&(s=s.map(t.changes)),t.selection||n.some((e=>e.hasResult()&&t.changes.touchesRange(e.from,e.to)))||!function(t,e){if(t==e)return!0;for(let i=0,n=0;;){for(;i<t.length&&!t[i].hasResult;)i++;for(;n<e.length&&!e[n].hasResult;)n++;let s=i==t.length,r=n==e.length;if(s||r)return s==r;if(t[i++].result!=e[n++].result)return!1}}(n,this.active)?s=Cu.build(n,e,this.id,s,i):s&&s.disabled&&!n.some((t=>1==t.state))&&(s=null),!s&&n.every((t=>1!=t.state))&&n.some((t=>t.hasResult()))&&(n=n.map((t=>t.hasResult()?new Yu(t.source,0):t)));for(let r of t.effects)r.is(Eu)&&(s=s&&s.setSelected(r.value,this.id));return n==this.active&&s==this.open?this:new Tu(n,this.id,s)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:Au}}const Au={"aria-autocomplete":"list"};function Xu(t,e){let i={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":t};return e>-1&&(i["aria-activedescendant"]=t+"-"+e),i}const Wu=[];function Ru(t){return t.isUserEvent("input.type")?"input":t.isUserEvent("delete.backward")?"delete":null}class Yu{constructor(t,e,i=-1){this.source=t,this.state=e,this.explicitPos=i}hasResult(){return!1}update(t,e){let i=Ru(t),n=this;i?n=n.handleUserEvent(t,i,e):t.docChanged?n=n.handleChange(t):t.selection&&0!=n.state&&(n=new Yu(n.source,0));for(let s of t.effects)if(s.is(wu))n=new Yu(n.source,1,s.value?du(t.state):-1);else if(s.is(vu))n=new Yu(n.source,0);else if(s.is(Mu))for(let t of s.value)t.source==n.source&&(n=t);return n}handleUserEvent(t,e,i){return"delete"!=e&&i.activateOnTyping?new Yu(this.source,1):this.map(t.changes)}handleChange(t){return t.changes.touchesRange(du(t.startState))?new Yu(this.source,0):this.map(t.changes)}map(t){return t.empty||this.explicitPos<0?this:new Yu(this.source,this.state,t.mapPos(this.explicitPos))}}class qu extends Yu{constructor(t,e,i,n,s){super(t,2,e),this.result=i,this.from=n,this.to=s}hasResult(){return!0}handleUserEvent(t,e,i){var n;let s=this.result;s.map&&!t.changes.empty&&(s=s.map(s,t.changes));let r=t.changes.mapPos(this.from),o=t.changes.mapPos(this.to,1),a=du(t.state);if((this.explicitPos<0?a<=r:a<this.from)||a>o||!s||"delete"==e&&du(t.startState)==this.from)return new Yu(this.source,"input"==e&&i.activateOnTyping?1:0);let l=this.explicitPos<0?-1:t.changes.mapPos(this.explicitPos);return function(t,e,i,n){if(!t)return!1;let s=e.sliceDoc(i,n);return"function"==typeof t?t(s,i,n,e):Ou(t,!0).test(s)}(s.validFor,t.state,r,o)?new qu(this.source,l,s,r,o):s.update&&(s=s.update(s,r,o,new hu(t.state,a,l>=0)))?new qu(this.source,l,s,s.from,null!==(n=s.to)&&void 0!==n?n:du(t.state)):new Yu(this.source,1,l)}handleChange(t){return t.changes.touchesRange(this.from,this.to)?new Yu(this.source,0):this.map(t.changes)}map(t){if(t.empty)return this;return(this.result.map?this.result.map(this.result,t):this.result)?new qu(this.source,this.explicitPos<0?-1:t.mapPos(this.explicitPos),this.result,t.mapPos(this.from),t.mapPos(this.to,1)):new Yu(this.source,0)}}const Mu=yt.define({map:(t,e)=>t.map((t=>t.map(e)))}),Eu=yt.define(),Du=K.define({create:()=>Tu.start(),update:(t,e)=>t.update(e),provide:t=>[ao.from(t,(t=>t.tooltip)),tr.contentAttributes.from(t,(t=>t.attrs))]});function ju(t,e){const i=e.completion.apply||e.completion.label;let n=t.state.field(Du).active.find((t=>t.source==e.source));return n instanceof qu&&("string"==typeof i?t.dispatch(Object.assign(Object.assign({},function(t,e,i,n){let{main:s}=t.selection,r=i-s.from,o=n-s.from;return Object.assign(Object.assign({},t.changeByRange((a=>a!=s&&i!=n&&t.sliceDoc(a.from+r,a.from+o)!=t.sliceDoc(i,n)?{range:a}:{changes:{from:a.from+r,to:n==s.from?a.to:a.from+o,insert:e},range:V.cursor(a.from+r+e.length)}))),{scrollIntoView:!0,userEvent:"input.complete"})}(t.state,i,n.from,n.to)),{annotations:pu.of(e.completion)})):i(t,e.completion,n.from,n.to),!0)}const _u=Pu(Du,ju);function Vu(t,e="option"){return i=>{let n=i.state.field(Du,!1);if(!n||!n.open||n.open.disabled||Date.now()-n.open.timestamp<i.state.facet(Qu).interactionDelay)return!1;let s,r=1;"page"==e&&(s=po(i,n.open.tooltip))&&(r=Math.max(2,Math.floor(s.dom.offsetHeight/s.dom.querySelector("li").offsetHeight)-1));let{length:o}=n.open.options,a=n.open.selected>-1?n.open.selected+r*(t?1:-1):t?0:o-1;return a<0?a="page"==e?0:o-1:a>=o&&(a="page"==e?o-1:0),i.dispatch({effects:Eu.of(a)}),!0}}class Bu{constructor(t,e){this.active=t,this.context=e,this.time=Date.now(),this.updates=[],this.done=void 0}}const zu=rn.fromClass(class{constructor(t){this.view=t,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let e of t.state.field(Du).active)1==e.state&&this.startQuery(e)}update(t){let e=t.state.field(Du);if(!t.selectionSet&&!t.docChanged&&t.startState.field(Du)==e)return;let i=t.transactions.some((t=>(t.selection||t.docChanged)&&!Ru(t)));for(let s=0;s<this.running.length;s++){let e=this.running[s];if(i||e.updates.length+t.transactions.length>50&&Date.now()-e.time>1e3){for(let t of e.context.abortListeners)try{t()}catch(tO){tn(this.view.state,tO)}e.context.abortListeners=null,this.running.splice(s--,1)}else e.updates.push(...t.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),t.transactions.some((t=>t.effects.some((t=>t.is(wu)))))&&(this.pendingStart=!0);let n=this.pendingStart?50:t.state.facet(Qu).activateOnTypingDelay;if(this.debounceUpdate=e.active.some((t=>1==t.state&&!this.running.some((e=>e.active.source==t.source))))?setTimeout((()=>this.startUpdate()),n):-1,0!=this.composing)for(let s of t.transactions)"input"==Ru(s)?this.composing=2:2==this.composing&&s.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:t}=this.view,e=t.field(Du);for(let i of e.active)1!=i.state||this.running.some((t=>t.active.source==i.source))||this.startQuery(i)}startQuery(t){let{state:e}=this.view,i=du(e),n=new hu(e,i,t.explicitPos==i),s=new Bu(t,n);this.running.push(s),Promise.resolve(t.source(n)).then((t=>{s.context.aborted||(s.done=t||null,this.scheduleAccept())}),(t=>{this.view.dispatch({effects:vu.of(null)}),tn(this.view.state,t)}))}scheduleAccept(){this.running.every((t=>void 0!==t.done))?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout((()=>this.accept()),this.view.state.facet(Qu).updateSyncTime))}accept(){var t;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let e=[],i=this.view.state.facet(Qu);for(let n=0;n<this.running.length;n++){let s=this.running[n];if(void 0===s.done)continue;if(this.running.splice(n--,1),s.done){let n=new qu(s.active.source,s.active.explicitPos,s.done,s.done.from,null!==(t=s.done.to)&&void 0!==t?t:du(s.updates.length?s.updates[0].startState:this.view.state));for(let t of s.updates)n=n.update(t,i);if(n.hasResult()){e.push(n);continue}}let r=this.view.state.field(Du).active.find((t=>t.source==s.active.source));if(r&&1==r.state)if(null==s.done){let t=new Yu(s.active.source,0);for(let e of s.updates)t=t.update(e,i);1!=t.state&&e.push(t)}else this.startQuery(r)}e.length&&this.view.dispatch({effects:Mu.of(e)})}},{eventHandlers:{blur(t){let e=this.view.state.field(Du,!1);if(e&&e.tooltip&&this.view.state.facet(Qu).closeOnBlur){let i=e.open&&po(this.view,e.open.tooltip);i&&i.dom.contains(t.relatedTarget)||setTimeout((()=>this.view.dispatch({effects:vu.of(null)})),10)}},compositionstart(){this.composing=1},compositionend(){3==this.composing&&setTimeout((()=>this.view.dispatch({effects:wu.of(!1)})),20),this.composing=0}}}),Gu="object"==typeof navigator&&/Win/.test(navigator.platform),Iu=st.highest(tr.domEventHandlers({keydown(t,e){let i=e.state.field(Du,!1);if(!i||!i.open||i.open.disabled||i.open.selected<0||t.key.length>1||t.ctrlKey&&(!Gu||!t.altKey)||t.metaKey)return!1;let n=i.open.options[i.open.selected],s=i.active.find((t=>t.source==n.source)),r=n.completion.commitCharacters||s.result.commitCharacters;return r&&r.indexOf(t.key)>-1&&ju(e,n),!1}})),Lu=tr.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class Nu{constructor(t,e,i,n){this.field=t,this.line=e,this.from=i,this.to=n}}class Uu{constructor(t,e,i){this.field=t,this.from=e,this.to=i}map(t){let e=t.mapPos(this.from,-1,X.TrackDel),i=t.mapPos(this.to,1,X.TrackDel);return null==e||null==i?null:new Uu(this.field,e,i)}}class Hu{constructor(t,e){this.lines=t,this.fieldPositions=e}instantiate(t,e){let i=[],n=[e],s=t.doc.lineAt(e),r=/^\s*/.exec(s.text)[0];for(let o of this.lines){if(i.length){let i=r,s=/^\t*/.exec(o)[0].length;for(let e=0;e<s;e++)i+=t.facet(gl);n.push(e+i.length-s),o=i+o.slice(s)}i.push(o),e+=o.length+1}return{text:i,ranges:this.fieldPositions.map((t=>new Uu(t.field,n[t.line]+t.from,n[t.line]+t.to)))}}static parse(t){let e,i=[],n=[],s=[];for(let r of t.split(/\r\n?|\n/)){for(;e=/[#$]\{(?:(\d+)(?::([^}]*))?|([^}]*))\}/.exec(r);){let t=e[1]?+e[1]:null,o=e[2]||e[3]||"",a=-1;for(let e=0;e<i.length;e++)(null!=t?i[e].seq==t:o&&i[e].name==o)&&(a=e);if(a<0){let e=0;for(;e<i.length&&(null==t||null!=i[e].seq&&i[e].seq<t);)e++;i.splice(e,0,{seq:t,name:o}),a=e;for(let t of s)t.field>=a&&t.field++}s.push(new Nu(a,n.length,e.index,e.index+o.length)),r=r.slice(0,e.index)+o+r.slice(e.index+e[0].length)}for(let t;t=/\\([{}])/.exec(r);){r=r.slice(0,t.index)+t[1]+r.slice(t.index+t[0].length);for(let e of s)e.line==n.length&&e.from>t.index&&(e.from--,e.to--)}n.push(r)}return new Hu(n,s)}}let Fu=Oi.widget({widget:new class extends ui{toDOM(){let t=document.createElement("span");return t.className="cm-snippetFieldPosition",t}ignoreEvent(){return!1}}}),Ku=Oi.mark({class:"cm-snippetField"});class Ju{constructor(t,e){this.ranges=t,this.active=e,this.deco=Oi.set(t.map((t=>(t.from==t.to?Fu:Ku).range(t.from,t.to))))}map(t){let e=[];for(let i of this.ranges){let n=i.map(t);if(!n)return null;e.push(n)}return new Ju(e,this.active)}selectionInsideField(t){return t.ranges.every((t=>this.ranges.some((e=>e.field==this.active&&e.from<=t.from&&e.to>=t.to))))}}const td=yt.define({map:(t,e)=>t&&t.map(e)}),ed=yt.define(),id=K.define({create:()=>null,update(t,e){for(let i of e.effects){if(i.is(td))return i.value;if(i.is(ed)&&t)return new Ju(t.ranges,i.value)}return t&&e.docChanged&&(t=t.map(e.changes)),t&&e.selection&&!t.selectionInsideField(e.selection)&&(t=null),t},provide:t=>tr.decorations.from(t,(t=>t?t.deco:Oi.none))});function nd(t,e){return V.create(t.filter((t=>t.field==e)).map((t=>V.range(t.from,t.to))))}function sd(t){let e=Hu.parse(t);return(t,i,n,s)=>{let{text:r,ranges:o}=e.instantiate(t.state,n),a={changes:{from:n,to:s,insert:h.of(r)},scrollIntoView:!0,annotations:i?[pu.of(i),Qt.userEvent.of("input.complete")]:void 0};if(o.length&&(a.selection=nd(o,0)),o.some((t=>t.field>0))){let e=new Ju(o,0),i=a.effects=[td.of(e)];void 0===t.state.field(id,!1)&&i.push(yt.appendConfig.of([id,ld,cd,Lu]))}t.dispatch(t.state.update(a))}}function rd(t){return({state:e,dispatch:i})=>{let n=e.field(id,!1);if(!n||t<0&&0==n.active)return!1;let s=n.active+t,r=t>0&&!n.ranges.some((e=>e.field==s+t));return i(e.update({selection:nd(n.ranges,s),effects:td.of(r?null:new Ju(n.ranges,s)),scrollIntoView:!0})),!0}}const od=[{key:"Tab",run:rd(1),shift:rd(-1)},{key:"Escape",run:({state:t,dispatch:e})=>!!t.field(id,!1)&&(e(t.update({effects:td.of(null)})),!0)}],ad=G.define({combine:t=>t.length?t[0]:od}),ld=st.highest(lr.compute([ad],(t=>t.facet(ad))));function hd(t,e){return Object.assign(Object.assign({},e),{apply:sd(t)})}const cd=tr.domEventHandlers({mousedown(t,e){let i,n=e.state.field(id,!1);if(!n||null==(i=e.posAtCoords({x:t.clientX,y:t.clientY})))return!1;let s=n.ranges.find((t=>t.from<=i&&t.to>=i));return!(!s||s.field==n.active)&&(e.dispatch({selection:nd(n.ranges,s.field),effects:td.of(n.ranges.some((t=>t.field>s.field))?new Ju(n.ranges,s.field):null),scrollIntoView:!0}),!0)}}),fd={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},ud=yt.define({map(t,e){let i=e.mapPos(t,-1,X.TrackAfter);return null==i?void 0:i}}),dd=new class extends Yt{};dd.startSide=1,dd.endSide=-1;const Od=K.define({create:()=>Dt.empty,update(t,e){if(t=t.map(e.changes),e.selection){let i=e.state.doc.lineAt(e.selection.main.head);t=t.update({filter:t=>t>=i.from&&t<=i.to})}for(let i of e.effects)i.is(ud)&&(t=t.update({add:[dd.range(i.value,i.value+1)]}));return t}});const pd="()[]{}<>";function gd(t){for(let e=0;e<pd.length;e+=2)if(pd.charCodeAt(e)==t)return pd.charAt(e+1);return C(t<128?t:t+1)}function md(t,e){return t.languageDataAt("closeBrackets",e)[0]||fd}const wd="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),vd=tr.inputHandler.of(((t,e,i,n)=>{if((wd?t.composing:t.compositionStarted)||t.state.readOnly)return!1;let s=t.state.selection.main;if(n.length>2||2==n.length&&1==T(Z(n,0))||e!=s.from||i!=s.to)return!1;let r=function(t,e){let i=md(t,t.selection.main.head),n=i.brackets||fd.brackets;for(let s of n){let r=gd(Z(s,0));if(e==s)return r==s?xd(t,s,n.indexOf(s+s+s)>-1,i):kd(t,s,r,i.before||fd.before);if(e==r&&yd(t,t.selection.main.from))return Sd(t,s,r)}return null}(t.state,n);return!!r&&(t.dispatch(r),!0)})),bd=[{key:"Backspace",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=md(t,t.selection.main.head).brackets||fd.brackets,n=null,s=t.changeByRange((e=>{if(e.empty){let n=function(t,e){let i=t.sliceString(e-2,e);return T(Z(i,0))==i.length?i:i.slice(1)}(t.doc,e.head);for(let s of i)if(s==n&&Qd(t.doc,e.head)==gd(Z(s,0)))return{changes:{from:e.head-s.length,to:e.head+s.length},range:V.cursor(e.head-s.length)}}return{range:n=e}}));return n||e(t.update(s,{scrollIntoView:!0,userEvent:"delete.backward"})),!n}}];function yd(t,e){let i=!1;return t.field(Od).between(0,t.doc.length,(t=>{t==e&&(i=!0)})),i}function Qd(t,e){let i=t.sliceString(e,e+2);return i.slice(0,T(Z(i,0)))}function kd(t,e,i,n){let s=null,r=t.changeByRange((r=>{if(!r.empty)return{changes:[{insert:e,from:r.from},{insert:i,from:r.to}],effects:ud.of(r.to+e.length),range:V.range(r.anchor+e.length,r.head+e.length)};let o=Qd(t.doc,r.head);return!o||/\s/.test(o)||n.indexOf(o)>-1?{changes:{insert:e+i,from:r.head},effects:ud.of(r.head+e.length),range:V.cursor(r.head+e.length)}:{range:s=r}}));return s?null:t.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function Sd(t,e,i){let n=null,s=t.changeByRange((e=>e.empty&&Qd(t.doc,e.head)==i?{changes:{from:e.head,to:e.head+i.length,insert:i},range:V.cursor(e.head+i.length)}:n={range:e}));return n?null:t.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function xd(t,e,i,n){let s=n.stringPrefixes||fd.stringPrefixes,r=null,o=t.changeByRange((n=>{if(!n.empty)return{changes:[{insert:e,from:n.from},{insert:e,from:n.to}],effects:ud.of(n.to+e.length),range:V.range(n.anchor+e.length,n.head+e.length)};let o,a=n.head,l=Qd(t.doc,a);if(l==e){if($d(t,a))return{changes:{insert:e+e,from:a},effects:ud.of(a+e.length),range:V.cursor(a+e.length)};if(yd(t,a)){let n=i&&t.sliceDoc(a,a+3*e.length)==e+e+e?e+e+e:e;return{changes:{from:a,to:a+n.length,insert:n},range:V.cursor(a+n.length)}}}else{if(i&&t.sliceDoc(a-2*e.length,a)==e+e&&(o=Pd(t,a-2*e.length,s))>-1&&$d(t,o))return{changes:{insert:e+e+e+e,from:a},effects:ud.of(a+e.length),range:V.cursor(a+e.length)};if(t.charCategorizer(a)(l)!=Ct.Word&&Pd(t,a,s)>-1&&!function(t,e,i,n){let s=sl(t).resolveInner(e,-1),r=n.reduce(((t,e)=>Math.max(t,e.length)),0);for(let o=0;o<5;o++){let o=t.sliceDoc(s.from,Math.min(s.to,s.from+i.length+r)),a=o.indexOf(i);if(!a||a>-1&&n.indexOf(o.slice(0,a))>-1){let e=s.firstChild;for(;e&&e.from==s.from&&e.to-e.from>i.length+a;){if(t.sliceDoc(e.to-i.length,e.to)==i)return!1;e=e.firstChild}return!0}let l=s.to==e&&s.parent;if(!l)break;s=l}return!1}(t,a,e,s))return{changes:{insert:e+e,from:a},effects:ud.of(a+e.length),range:V.cursor(a+e.length)}}return{range:r=n}}));return r?null:t.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function $d(t,e){let i=sl(t).resolveInner(e+1);return i.parent&&i.from==e}function Pd(t,e,i){let n=t.charCategorizer(e);if(n(t.sliceDoc(e-1,e))!=Ct.Word)return e;for(let s of i){let i=e-s.length;if(t.sliceDoc(i,e)==s&&n(t.sliceDoc(i-1,i))!=Ct.Word)return i}return-1}function Zd(t={}){return[Iu,Du,Qu.of(t),zu,Td,Lu]}const Cd=[{key:"Ctrl-Space",run:t=>!!t.state.field(Du,!1)&&(t.dispatch({effects:wu.of(!0)}),!0)},{key:"Escape",run:t=>{let e=t.state.field(Du,!1);return!(!e||!e.active.some((t=>0!=t.state)))&&(t.dispatch({effects:vu.of(null)}),!0)}},{key:"ArrowDown",run:Vu(!0)},{key:"ArrowUp",run:Vu(!1)},{key:"PageDown",run:Vu(!0,"page")},{key:"PageUp",run:Vu(!1,"page")},{key:"Enter",run:t=>{let e=t.state.field(Du,!1);return!(t.state.readOnly||!e||!e.open||e.open.selected<0||e.open.disabled||Date.now()-e.open.timestamp<t.state.facet(Qu).interactionDelay)&&ju(t,e.open.options[e.open.selected])}}],Td=st.highest(lr.computeN([Qu],(t=>t.facet(Qu).defaultKeymap?[Cd]:[])));class Ad{constructor(t,e,i){this.from=t,this.to=e,this.diagnostic=i}}class Xd{constructor(t,e,i){this.diagnostics=t,this.panel=e,this.selected=i}static init(t,e,i){let n=t,s=i.facet(Bd).markerFilter;s&&(n=s(n,i));let r=Oi.set(n.map((t=>t.from==t.to||t.from==t.to-1&&i.doc.lineAt(t.from).to==t.from?Oi.widget({widget:new Id(t),diagnostic:t}).range(t.from):Oi.mark({attributes:{class:"cm-lintRange cm-lintRange-"+t.severity+(t.markClass?" "+t.markClass:"")},diagnostic:t,inclusive:!0}).range(t.from,t.to))),!0);return new Xd(r,e,Wd(r))}}function Wd(t,e=null,i=0){let n=null;return t.between(i,1e9,((t,i,{spec:s})=>{if(!e||s.diagnostic==e)return n=new Ad(t,i,s.diagnostic),!1})),n}const Rd=yt.define(),Yd=yt.define(),qd=yt.define(),Md=K.define({create:()=>new Xd(Oi.none,null,null),update(t,e){if(e.docChanged){let i=t.diagnostics.map(e.changes),n=null;if(t.selected){let s=e.changes.mapPos(t.selected.from,1);n=Wd(i,t.selected.diagnostic,s)||Wd(i,null,s)}t=new Xd(i,t.panel,n)}for(let i of e.effects)i.is(Rd)?t=Xd.init(i.value,t.panel,e.state):i.is(Yd)?t=new Xd(t.diagnostics,i.value?Nd.open:null,t.selected):i.is(qd)&&(t=new Xd(t.diagnostics,t.panel,i.value));return t},provide:t=>[Qo.from(t,(t=>t.panel)),tr.decorations.from(t,(t=>t.diagnostics))]}),Ed=Oi.mark({class:"cm-lintRange cm-lintRange-active",inclusive:!0});function Dd(t,e,i){let{diagnostics:n}=t.state.field(Md),s=[],r=2e8,o=0;n.between(e-(i<0?1:0),e+(i>0?1:0),((t,n,{spec:a})=>{e>=t&&e<=n&&(t==n||(e>t||i>0)&&(e<n||i<0))&&(s.push(a.diagnostic),r=Math.min(t,r),o=Math.max(n,o))}));let a=t.state.facet(Bd).tooltipFilter;return a&&(s=a(s,t.state)),s.length?{pos:r,end:o,above:t.state.doc.lineAt(r).to<o,create:()=>({dom:jd(t,s)})}:null}function jd(t,e){return sf("ul",{class:"cm-tooltip-lint"},e.map((e=>Gd(t,e,!1))))}const _d=t=>{let e=t.state.field(Md,!1);return!(!e||!e.panel)&&(t.dispatch({effects:Yd.of(!1)}),!0)},Vd=[{key:"Mod-Shift-m",run:t=>{let e=t.state.field(Md,!1);var i,n;e&&e.panel||t.dispatch({effects:(i=t.state,n=[Yd.of(!0)],i.field(Md,!1)?n:n.concat(yt.appendConfig.of(Fd)))});let s=wo(t,Nd.open);return s&&s.dom.querySelector(".cm-panel-lint ul").focus(),!0},preventDefault:!0},{key:"F8",run:t=>{let e=t.state.field(Md,!1);if(!e)return!1;let i=t.state.selection.main,n=e.diagnostics.iter(i.to+1);return!(!n.value&&(n=e.diagnostics.iter(0),!n.value||n.from==i.from&&n.to==i.to))&&(t.dispatch({selection:{anchor:n.from,head:n.to},scrollIntoView:!0}),!0)}}],Bd=G.define({combine:t=>Object.assign({sources:t.map((t=>t.source)).filter((t=>null!=t))},Rt(t.map((t=>t.config)),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null},{needsRefresh:(t,e)=>t?e?i=>t(i)||e(i):t:e}))});function zd(t){let e=[];if(t)t:for(let{name:i}of t){for(let t=0;t<i.length;t++){let n=i[t];if(/[a-zA-Z]/.test(n)&&!e.some((t=>t.toLowerCase()==n.toLowerCase()))){e.push(n);continue t}}e.push("")}return e}function Gd(t,e,i){var n;let s=i?zd(e.actions):[];return sf("li",{class:"cm-diagnostic cm-diagnostic-"+e.severity},sf("span",{class:"cm-diagnosticText"},e.renderMessage?e.renderMessage():e.message),null===(n=e.actions)||void 0===n?void 0:n.map(((i,n)=>{let r=!1,o=n=>{if(n.preventDefault(),r)return;r=!0;let s=Wd(t.state.field(Md).diagnostics,e);s&&i.apply(t,s.from,s.to)},{name:a}=i,l=s[n]?a.indexOf(s[n]):-1,h=l<0?a:[a.slice(0,l),sf("u",a.slice(l,l+1)),a.slice(l+1)];return sf("button",{type:"button",class:"cm-diagnosticAction",onclick:o,onmousedown:o,"aria-label":` Action: ${a}${l<0?"":` (access key "${s[n]})"`}.`},h)})),e.source&&sf("div",{class:"cm-diagnosticSource"},e.source))}class Id extends ui{constructor(t){super(),this.diagnostic=t}eq(t){return t.diagnostic==this.diagnostic}toDOM(){return sf("span",{class:"cm-lintPoint cm-lintPoint-"+this.diagnostic.severity})}}class Ld{constructor(t,e){this.diagnostic=e,this.id="item_"+Math.floor(4294967295*Math.random()).toString(16),this.dom=Gd(t,e,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class Nd{constructor(t){this.view=t,this.items=[];this.list=sf("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:e=>{if(27==e.keyCode)_d(this.view),this.view.focus();else if(38==e.keyCode||33==e.keyCode)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(40==e.keyCode||34==e.keyCode)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(36==e.keyCode)this.moveSelection(0);else if(35==e.keyCode)this.moveSelection(this.items.length-1);else if(13==e.keyCode)this.view.focus();else{if(!(e.keyCode>=65&&e.keyCode<=90&&this.selectedIndex>=0))return;{let{diagnostic:i}=this.items[this.selectedIndex],n=zd(i.actions);for(let s=0;s<n.length;s++)if(n[s].toUpperCase().charCodeAt(0)==e.keyCode){let e=Wd(this.view.state.field(Md).diagnostics,i);e&&i.actions[s].apply(t,e.from,e.to)}}}e.preventDefault()},onclick:t=>{for(let e=0;e<this.items.length;e++)this.items[e].dom.contains(t.target)&&this.moveSelection(e)}}),this.dom=sf("div",{class:"cm-panel-lint"},this.list,sf("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>_d(this.view)},"×")),this.update()}get selectedIndex(){let t=this.view.state.field(Md).selected;if(!t)return-1;for(let e=0;e<this.items.length;e++)if(this.items[e].diagnostic==t.diagnostic)return e;return-1}update(){let{diagnostics:t,selected:e}=this.view.state.field(Md),i=0,n=!1,s=null;for(t.between(0,this.view.state.doc.length,((t,r,{spec:o})=>{let a,l=-1;for(let e=i;e<this.items.length;e++)if(this.items[e].diagnostic==o.diagnostic){l=e;break}l<0?(a=new Ld(this.view,o.diagnostic),this.items.splice(i,0,a),n=!0):(a=this.items[l],l>i&&(this.items.splice(i,l-i),n=!0)),e&&a.diagnostic==e.diagnostic?a.dom.hasAttribute("aria-selected")||(a.dom.setAttribute("aria-selected","true"),s=a):a.dom.hasAttribute("aria-selected")&&a.dom.removeAttribute("aria-selected"),i++}));i<this.items.length&&!(1==this.items.length&&this.items[0].diagnostic.from<0);)n=!0,this.items.pop();0==this.items.length&&(this.items.push(new Ld(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),n=!0),s?(this.list.setAttribute("aria-activedescendant",s.id),this.view.requestMeasure({key:this,read:()=>({sel:s.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:t,panel:e})=>{let i=e.height/this.list.offsetHeight;t.top<e.top?this.list.scrollTop-=(e.top-t.top)/i:t.bottom>e.bottom&&(this.list.scrollTop+=(t.bottom-e.bottom)/i)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),n&&this.sync()}sync(){let t=this.list.firstChild;function e(){let e=t;t=e.nextSibling,e.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;t!=i.dom;)e();t=i.dom.nextSibling}else this.list.insertBefore(i.dom,t);for(;t;)e()}moveSelection(t){if(this.selectedIndex<0)return;let e=Wd(this.view.state.field(Md).diagnostics,this.items[t].diagnostic);e&&this.view.dispatch({selection:{anchor:e.from,head:e.to},scrollIntoView:!0,effects:qd.of(e)})}static open(t){return new Nd(t)}}function Ud(t){return function(t,e='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${e}>${encodeURIComponent(t)}</svg>')`}(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${t}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const Hd=tr.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:Ud("#d11")},".cm-lintRange-warning":{backgroundImage:Ud("orange")},".cm-lintRange-info":{backgroundImage:Ud("#999")},".cm-lintRange-hint":{backgroundImage:Ud("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}}),Fd=[Md,tr.decorations.compute([Md],(t=>{let{selected:e,panel:i}=t.field(Md);return e&&i&&e.from!=e.to?Oi.set([Ed.range(e.from,e.to)]):Oi.none})),Oo(Dd,{hideOn:function(t,e){let i=t.startState.doc.lineAt(e.pos);return!(!t.effects.some((t=>t.is(Rd)))&&!t.changes.touchesRange(i.from,i.to))}}),Hd],Kd=(()=>[_o(),zo,Dr(),Vh(),Kl(),Qr(),[Tr,Ar],Wt.allowMultipleSelections.of(!0),Wt.transactionFilter.of((t=>{if(!t.docChanged||!t.isUserEvent("input.type")&&!t.isUserEvent("input.complete"))return t;let e=t.startState.languageDataAt("indentOnInput",t.startState.selection.main.head);if(!e.length)return t;let i=t.newDoc,{head:n}=t.newSelection.main,s=i.lineAt(n);if(n>s.from+200)return t;let r=i.sliceString(s.from,n);if(!e.some((t=>t.test(r))))return t;let{state:o}=t,a=-1,l=[];for(let{head:h}of o.selection.ranges){let t=o.doc.lineAt(h);if(t.from==a)continue;a=t.from;let e=vl(o,t.from);if(null==e)continue;let i=/^\s*/.exec(t.text)[0],n=wl(o,e);i!=n&&l.push({from:t.from,to:t.from+i.length,insert:n})}return l.length?[t,{changes:l,sequential:!0}]:t})),sh(ah,{fallback:!0}),gh(),[vd,Od],Zd(),Ur(),Kr(),zr,yf(),lr.of([...bd,...ef,...eu,...rc,...Bl,...Cd,...Vd])])();
/*!
* VueCodemirror v6.1.1
* Copyright (c) Surmon. All rights reserved.
* Released under the MIT License.
* Surmon
*/
var Jd,tO,eO=Object.freeze({autofocus:!1,disabled:!1,indentWithTab:!0,tabSize:2,placeholder:"",autoDestroy:!0,extensions:[Kd]}),iO=Symbol("vue-codemirror-global-config"),nO=function(t){var e=new ot;return{compartment:e,run:function(i){e.get(t.state)?t.dispatch({effects:e.reconfigure(i)}):t.dispatch({effects:yt.appendConfig.of(e.of(i))})}}},sO=function(t,e){var i=nO(t),n=i.compartment,s=i.run;return function(i){var r=n.get(t.state);s((null!=i?i:r!==e)?e:[])}},rO={type:Boolean,default:void 0},oO={autofocus:rO,disabled:rO,indentWithTab:rO,tabSize:Number,placeholder:String,style:Object,autoDestroy:rO,phrases:Object,root:Object,extensions:Array,selection:Object},aO={modelValue:{type:String,default:""}},lO=Object.assign(Object.assign({},oO),aO);(tO=Jd||(Jd={})).Change="change",tO.Update="update",tO.Focus="focus",tO.Blur="blur",tO.Ready="ready",tO.ModelUpdate="update:modelValue";var hO={};hO[Jd.Change]=function(t,e){return!0},hO[Jd.Update]=function(t){return!0},hO[Jd.Focus]=function(t){return!0},hO[Jd.Blur]=function(t){return!0},hO[Jd.Ready]=function(t){return!0};var cO={};cO[Jd.ModelUpdate]=hO[Jd.Change];var fO=Object.assign(Object.assign({},hO),cO),uO=t({name:"VueCodemirror",props:Object.assign({},lO),emits:Object.assign({},fO),setup:function(t,h){var c=e(),f=e(),u=e(),d=Object.assign(Object.assign({},eO),i(iO,{})),O=n((function(){var e={};return Object.keys(l(t)).forEach((function(i){var n;"modelValue"!==i&&(e[i]=null!==(n=t[i])&&void 0!==n?n:d[i])})),e}));return s((function(){var e,i;f.value=function(t){var e=t.onUpdate,i=t.onChange,n=t.onFocus,s=t.onBlur,r=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(n=Object.getOwnPropertySymbols(t);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(t,n[s])&&(i[n[s]]=t[n[s]])}return i}(t,["onUpdate","onChange","onFocus","onBlur"]);return Wt.create({doc:r.doc,selection:r.selection,extensions:(Array.isArray(r.extensions)?r.extensions:[r.extensions]).concat([tr.updateListener.of((function(t){e(t),t.docChanged&&i(t.state.doc.toString(),t),t.focusChanged&&(t.view.hasFocus?n(t):s(t))}))])})}({doc:t.modelValue,selection:O.value.selection,extensions:null!==(e=d.extensions)&&void 0!==e?e:[],onFocus:function(t){return h.emit(Jd.Focus,t)},onBlur:function(t){return h.emit(Jd.Blur,t)},onUpdate:function(t){return h.emit(Jd.Update,t)},onChange:function(e,i){e!==t.modelValue&&(h.emit(Jd.Change,e,i),h.emit(Jd.ModelUpdate,e,i))}}),u.value=(i={state:f.value,parent:c.value,root:O.value.root},new tr(Object.assign({},i)));var n=function(t){var e=function(){return t.state.doc.toString()},i=nO(t).run,n=sO(t,[tr.editable.of(!1),Wt.readOnly.of(!0)]),s=sO(t,lr.of([nf])),r=nO(t).run,o=nO(t).run,a=nO(t).run,l=nO(t).run;return{focus:function(){return t.focus()},getDoc:e,setDoc:function(i){i!==e()&&t.dispatch({changes:{from:0,to:t.state.doc.length,insert:i}})},reExtensions:i,toggleDisabled:n,toggleIndentWithTab:s,setTabSize:function(t){r([Wt.tabSize.of(t),gl.of(" ".repeat(t))])},setPhrases:function(t){o([Wt.phrases.of(t)])},setPlaceholder:function(t){var e;a((e=t,rn.fromClass(class{constructor(t){this.view=t,this.placeholder=e?Oi.set([Oi.widget({widget:new Gr(e),side:1}).range(0)]):Oi.none}get decorations(){return this.view.state.doc.length?Oi.none:this.placeholder}},{decorations:t=>t.decorations})))},setStyle:function(t){void 0===t&&(t={}),l(tr.theme({"&":Object.assign({},t)}))}}}(u.value);r((function(){return t.modelValue}),(function(t){t!==n.getDoc()&&n.setDoc(t)})),r((function(){return t.extensions}),(function(t){return n.reExtensions(t||[])}),{immediate:!0}),r((function(){return O.value.disabled}),(function(t){return n.toggleDisabled(t)}),{immediate:!0}),r((function(){return O.value.indentWithTab}),(function(t){return n.toggleIndentWithTab(t)}),{immediate:!0}),r((function(){return O.value.tabSize}),(function(t){return n.setTabSize(t)}),{immediate:!0}),r((function(){return O.value.phrases}),(function(t){return n.setPhrases(t||{})}),{immediate:!0}),r((function(){return O.value.placeholder}),(function(t){return n.setPlaceholder(t)}),{immediate:!0}),r((function(){return O.value.style}),(function(t){return n.setStyle(t)}),{immediate:!0}),O.value.autofocus&&n.focus(),h.emit(Jd.Ready,{state:f.value,view:u.value,container:c.value})})),o((function(){O.value.autoDestroy&&u.value&&function(t){t.destroy()}(u.value)})),function(){return a("div",{class:"v-codemirror",style:{display:"contents"},ref:c})}}}),dO=uO,OO={};class pO{constructor(t,e,i,n,s,r,o,a,l,h=0,c){this.p=t,this.stack=e,this.state=i,this.reducePos=n,this.pos=s,this.score=r,this.buffer=o,this.bufferBase=a,this.curContext=l,this.lookAhead=h,this.parent=c}toString(){return`[${this.stack.filter(((t,e)=>e%3==0)).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(t,e,i=0){let n=t.parser.context;return new pO(t,[],e,i,i,0,[],0,n?new gO(n,n.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(t,e){this.stack.push(this.state,e,this.bufferBase+this.buffer.length),this.state=t}reduce(t){var e;let i=t>>19,n=65535&t,{parser:s}=this.p,r=s.dynamicPrecedence(n);if(r&&(this.score+=r),0==i)return this.pushState(s.getGoto(this.state,n,!0),this.reducePos),n<s.minRepeatTerm&&this.storeNode(n,this.reducePos,this.reducePos,4,!0),void this.reduceContext(n,this.reducePos);let o=this.stack.length-3*(i-1)-(262144&t?6:0),a=o?this.stack[o-2]:this.p.ranges[0].from,l=this.reducePos-a;l>=2e3&&!(null===(e=this.p.parser.nodeSet.types[n])||void 0===e?void 0:e.isAnonymous)&&(a==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=l):this.p.lastBigReductionSize<l&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=a,this.p.lastBigReductionSize=l));let h=o?this.stack[o-1]:0,c=this.bufferBase+this.buffer.length-h;if(n<s.minRepeatTerm||131072&t){let t=s.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(n,a,t,c+4,!0)}if(262144&t)this.state=this.stack[o];else{let t=this.stack[o-3];this.state=s.getGoto(t,n,!0)}for(;this.stack.length>o;)this.stack.pop();this.reduceContext(n,a)}storeNode(t,e,i,n=4,s=!1){if(0==t&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let t=this,n=this.buffer.length;if(0==n&&t.parent&&(n=t.bufferBase-t.parent.bufferBase,t=t.parent),n>0&&0==t.buffer[n-4]&&t.buffer[n-1]>-1){if(e==i)return;if(t.buffer[n-2]>=e)return void(t.buffer[n-2]=i)}}if(s&&this.pos!=i){let s=this.buffer.length;if(s>0&&0!=this.buffer[s-4])for(;s>0&&this.buffer[s-2]>i;)this.buffer[s]=this.buffer[s-4],this.buffer[s+1]=this.buffer[s-3],this.buffer[s+2]=this.buffer[s-2],this.buffer[s+3]=this.buffer[s-1],s-=4,n>4&&(n-=4);this.buffer[s]=t,this.buffer[s+1]=e,this.buffer[s+2]=i,this.buffer[s+3]=n}else this.buffer.push(t,e,i,n)}shift(t,e,i,n){if(131072&t)this.pushState(65535&t,this.pos);else if(262144&t)this.pos=n,this.shiftContext(e,i),e<=this.p.parser.maxNode&&this.buffer.push(e,i,n,4);else{let s=t,{parser:r}=this.p;(n>this.pos||e<=r.maxNode)&&(this.pos=n,r.stateFlag(s,1)||(this.reducePos=n)),this.pushState(s,i),this.shiftContext(e,i),e<=r.maxNode&&this.buffer.push(e,i,n,4)}}apply(t,e,i,n){65536&t?this.reduce(t):this.shift(t,e,i,n)}useNode(t,e){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=t)&&(this.p.reused.push(t),i++);let n=this.pos;this.reducePos=this.pos=n+t.length,this.pushState(e,n),this.buffer.push(i,n,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,t,this,this.p.stream.reset(this.pos-t.length)))}split(){let t=this,e=t.buffer.length;for(;e>0&&t.buffer[e-2]>t.reducePos;)e-=4;let i=t.buffer.slice(e),n=t.bufferBase+e;for(;t&&n==t.bufferBase;)t=t.parent;return new pO(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,n,this.curContext,this.lookAhead,t)}recoverByDelete(t,e){let i=t<=this.p.parser.maxNode;i&&this.storeNode(t,this.pos,e,4),this.storeNode(0,this.pos,e,i?8:4),this.pos=this.reducePos=e,this.score-=190}canShift(t){for(let e=new mO(this);;){let i=this.p.parser.stateSlot(e.state,4)||this.p.parser.hasAction(e.state,t);if(0==i)return!1;if(!(65536&i))return!0;e.reduce(i)}}recoverByInsert(t){if(this.stack.length>=300)return[];let e=this.p.parser.nextStates(this.state);if(e.length>8||this.stack.length>=120){let i=[];for(let n,s=0;s<e.length;s+=2)(n=e[s+1])!=this.state&&this.p.parser.hasAction(n,t)&&i.push(e[s],n);if(this.stack.length<120)for(let t=0;i.length<8&&t<e.length;t+=2){let n=e[t+1];i.some(((t,e)=>1&e&&t==n))||i.push(e[t],n)}e=i}let i=[];for(let n=0;n<e.length&&i.length<4;n+=2){let t=e[n+1];if(t==this.state)continue;let s=this.split();s.pushState(t,this.pos),s.storeNode(0,s.pos,s.pos,4,!0),s.shiftContext(e[n],this.pos),s.reducePos=this.pos,s.score-=200,i.push(s)}return i}forceReduce(){let{parser:t}=this.p,e=t.stateSlot(this.state,5);if(!(65536&e))return!1;if(!t.validAction(this.state,e)){let i=e>>19,n=65535&e,s=this.stack.length-3*i;if(s<0||t.getGoto(this.stack[s],n,!1)<0){let t=this.findForcedReduction();if(null==t)return!1;e=t}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(e),!0}findForcedReduction(){let{parser:t}=this.p,e=[],i=(n,s)=>{if(!e.includes(n))return e.push(n),t.allActions(n,(e=>{if(393216&e);else if(65536&e){let i=(e>>19)-s;if(i>1){let n=65535&e,s=this.stack.length-3*i;if(s>=0&&t.getGoto(this.stack[s],n,!1)>=0)return i<<19|65536|n}}else{let t=i(e,s+1);if(null!=t)return t}}))};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(3!=this.stack.length)return!1;let{parser:t}=this.p;return 65535==t.data[t.stateSlot(this.state,1)]&&!t.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(t){if(this.state!=t.state||this.stack.length!=t.stack.length)return!1;for(let e=0;e<this.stack.length;e+=3)if(this.stack[e]!=t.stack[e])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(t){return this.p.parser.dialect.flags[t]}shiftContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,t,this,this.p.stream.reset(e)))}reduceContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,t,this,this.p.stream.reset(e)))}emitContext(){let t=this.buffer.length-1;(t<0||-3!=this.buffer[t])&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let t=this.buffer.length-1;(t<0||-4!=this.buffer[t])&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(t){if(t!=this.curContext.context){let e=new gO(this.curContext.tracker,t);e.hash!=this.curContext.hash&&this.emitContext(),this.curContext=e}}setLookAhead(t){t>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=t)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class gO{constructor(t,e){this.tracker=t,this.context=e,this.hash=t.strict?t.hash(e):0}}class mO{constructor(t){this.start=t,this.state=t.state,this.stack=t.stack,this.base=this.stack.length}reduce(t){let e=65535&t,i=t>>19;0==i?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=3*(i-1);let n=this.start.p.parser.getGoto(this.stack[this.base-3],e,!0);this.state=n}}class wO{constructor(t,e,i){this.stack=t,this.pos=e,this.index=i,this.buffer=t.buffer,0==this.index&&this.maybeNext()}static create(t,e=t.bufferBase+t.buffer.length){return new wO(t,e,e-t.bufferBase)}maybeNext(){let t=this.stack.parent;null!=t&&(this.index=this.stack.bufferBase-t.bufferBase,this.stack=t,this.buffer=t.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,0==this.index&&this.maybeNext()}fork(){return new wO(this.stack,this.pos,this.index)}}function vO(t,e=Uint16Array){if("string"!=typeof t)return t;let i=null;for(let n=0,s=0;n<t.length;){let r=0;for(;;){let e=t.charCodeAt(n++),i=!1;if(126==e){r=65535;break}e>=92&&e--,e>=34&&e--;let s=e-32;if(s>=46&&(s-=46,i=!0),r+=s,i)break;r*=46}i?i[s++]=r:i=new e(r)}return i}class bO{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const yO=new bO;class QO{constructor(t,e){this.input=t,this.ranges=e,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=yO,this.rangeIndex=0,this.pos=this.chunkPos=e[0].from,this.range=e[0],this.end=e[e.length-1].to,this.readNext()}resolveOffset(t,e){let i=this.range,n=this.rangeIndex,s=this.pos+t;for(;s<i.from;){if(!n)return null;let t=this.ranges[--n];s-=i.from-t.to,i=t}for(;e<0?s>i.to:s>=i.to;){if(n==this.ranges.length-1)return null;let t=this.ranges[++n];s+=t.from-i.to,i=t}return s}clipPos(t){if(t>=this.range.from&&t<this.range.to)return t;for(let e of this.ranges)if(e.to>t)return Math.max(t,e.from);return this.end}peek(t){let e,i,n=this.chunkOff+t;if(n>=0&&n<this.chunk.length)e=this.pos+t,i=this.chunk.charCodeAt(n);else{let n=this.resolveOffset(t,1);if(null==n)return-1;if(e=n,e>=this.chunk2Pos&&e<this.chunk2Pos+this.chunk2.length)i=this.chunk2.charCodeAt(e-this.chunk2Pos);else{let t=this.rangeIndex,n=this.range;for(;n.to<=e;)n=this.ranges[++t];this.chunk2=this.input.chunk(this.chunk2Pos=e),e+this.chunk2.length>n.to&&(this.chunk2=this.chunk2.slice(0,n.to-e)),i=this.chunk2.charCodeAt(0)}}return e>=this.token.lookAhead&&(this.token.lookAhead=e+1),i}acceptToken(t,e=0){let i=e?this.resolveOffset(e,-1):this.pos;if(null==i||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=t,this.token.end=i}acceptTokenTo(t,e){this.token.value=t,this.token.end=e}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:t,chunkPos:e}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=t,this.chunk2Pos=e,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let t=this.input.chunk(this.pos),e=this.pos+t.length;this.chunk=e>this.range.to?t.slice(0,this.range.to-this.pos):t,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(t=1){for(this.chunkOff+=t;this.pos+t>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();t-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=t,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(t,e){if(e?(this.token=e,e.start=t,e.lookAhead=t+1,e.value=e.extended=-1):this.token=yO,this.pos!=t){if(this.pos=t,t==this.end)return this.setDone(),this;for(;t<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;t>=this.range.to;)this.range=this.ranges[++this.rangeIndex];t>=this.chunkPos&&t<this.chunkPos+this.chunk.length?this.chunkOff=t-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(t,e){if(t>=this.chunkPos&&e<=this.chunkPos+this.chunk.length)return this.chunk.slice(t-this.chunkPos,e-this.chunkPos);if(t>=this.chunk2Pos&&e<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(t-this.chunk2Pos,e-this.chunk2Pos);if(t>=this.range.from&&e<=this.range.to)return this.input.read(t,e);let i="";for(let n of this.ranges){if(n.from>=e)break;n.to>t&&(i+=this.input.read(Math.max(n.from,t),Math.min(n.to,e)))}return i}}class kO{constructor(t,e){this.data=t,this.id=e}token(t,e){let{parser:i}=e.p;$O(this.data,t,e,this.id,i.data,i.tokenPrecTable)}}kO.prototype.contextual=kO.prototype.fallback=kO.prototype.extend=!1;class SO{constructor(t,e,i){this.precTable=e,this.elseToken=i,this.data="string"==typeof t?vO(t):t}token(t,e){let i=t.pos,n=0;for(;;){let i=t.next<0,s=t.resolveOffset(1,1);if($O(this.data,t,e,0,this.data,this.precTable),t.token.value>-1)break;if(null==this.elseToken)return;if(i||n++,null==s)break;t.reset(s,t.token)}n&&(t.reset(i,t.token),t.acceptToken(this.elseToken,n))}}SO.prototype.contextual=kO.prototype.fallback=kO.prototype.extend=!1;class xO{constructor(t,e={}){this.token=t,this.contextual=!!e.contextual,this.fallback=!!e.fallback,this.extend=!!e.extend}}function $O(t,e,i,n,s,r){let o=0,a=1<<n,{dialect:l}=i.p.parser;t:for(;a&t[o];){let i=t[o+1];for(let f=o+3;f<i;f+=2)if((t[f+1]&a)>0){let i=t[f];if(l.allows(i)&&(-1==e.token.value||e.token.value==i||ZO(i,e.token.value,s,r))){e.acceptToken(i);break}}let n=e.next,h=0,c=t[o+2];if(!(e.next<0&&c>h&&65535==t[i+3*c-3])){for(;h<c;){let s=h+c>>1,r=i+s+(s<<1),a=t[r],l=t[r+1]||65536;if(n<a)c=s;else{if(!(n>=l)){o=t[r+2],e.advance();continue t}h=s+1}}break}o=t[i+3*c-1]}}function PO(t,e,i){for(let n,s=e;65535!=(n=t[s]);s++)if(n==i)return s-e;return-1}function ZO(t,e,i,n){let s=PO(i,n,e);return s<0||PO(i,n,t)<s}const CO="undefined"!=typeof process&&OO&&/\bparse\b/.test(OO.LOG);let TO=null;function AO(t,e,i){let n=t.cursor(ea.IncludeAnonymous);for(n.moveTo(e);;)if(!(i<0?n.childBefore(e):n.childAfter(e)))for(;;){if((i<0?n.to<e:n.from>e)&&!n.type.isError)return i<0?Math.max(0,Math.min(n.to-1,e-25)):Math.min(t.length,Math.max(n.from+1,e+25));if(i<0?n.prevSibling():n.nextSibling())break;if(!n.parent())return i<0?0:t.length}}class XO{constructor(t,e){this.fragments=t,this.nodeSet=e,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let t=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(t){for(this.safeFrom=t.openStart?AO(t.tree,t.from+t.offset,1)-t.offset:t.from,this.safeTo=t.openEnd?AO(t.tree,t.to+t.offset,-1)-t.offset:t.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(t.tree),this.start.push(-t.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(t){if(t<this.nextStart)return null;for(;this.fragment&&this.safeTo<=t;)this.nextFragment();if(!this.fragment)return null;for(;;){let e=this.trees.length-1;if(e<0)return this.nextFragment(),null;let i=this.trees[e],n=this.index[e];if(n==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let s=i.children[n],r=this.start[e]+i.positions[n];if(r>t)return this.nextStart=r,null;if(s instanceof na){if(r==t){if(r<this.safeFrom)return null;let t=r+s.length;if(t<=this.safeTo){let e=s.prop(No.lookAhead);if(!e||t+e<this.fragment.to)return s}}this.index[e]++,r+s.length>=Math.max(this.safeFrom,t)&&(this.trees.push(s),this.start.push(r),this.index.push(0))}else this.index[e]++,this.nextStart=r+s.length}}}class WO{constructor(t,e){this.stream=e,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=t.tokenizers.map((t=>new bO))}getActions(t){let e=0,i=null,{parser:n}=t.p,{tokenizers:s}=n,r=n.stateSlot(t.state,3),o=t.curContext?t.curContext.hash:0,a=0;for(let l=0;l<s.length;l++){if(!(1<<l&r))continue;let n=s[l],h=this.tokens[l];if((!i||n.fallback)&&((n.contextual||h.start!=t.pos||h.mask!=r||h.context!=o)&&(this.updateCachedToken(h,n,t),h.mask=r,h.context=o),h.lookAhead>h.end+25&&(a=Math.max(h.lookAhead,a)),0!=h.value)){let s=e;if(h.extended>-1&&(e=this.addActions(t,h.extended,h.end,e)),e=this.addActions(t,h.value,h.end,e),!n.extend&&(i=h,e>s))break}}for(;this.actions.length>e;)this.actions.pop();return a&&t.setLookAhead(a),i||t.pos!=this.stream.end||(i=new bO,i.value=t.p.parser.eofTerm,i.start=i.end=t.pos,e=this.addActions(t,i.value,i.end,e)),this.mainToken=i,this.actions}getMainToken(t){if(this.mainToken)return this.mainToken;let e=new bO,{pos:i,p:n}=t;return e.start=i,e.end=Math.min(i+1,n.stream.end),e.value=i==n.stream.end?n.parser.eofTerm:0,e}updateCachedToken(t,e,i){let n=this.stream.clipPos(i.pos);if(e.token(this.stream.reset(n,t),i),t.value>-1){let{parser:e}=i.p;for(let n=0;n<e.specialized.length;n++)if(e.specialized[n]==t.value){let s=e.specializers[n](this.stream.read(t.start,t.end),i);if(s>=0&&i.p.parser.dialect.allows(s>>1)){1&s?t.extended=s>>1:t.value=s>>1;break}}}else t.value=0,t.end=this.stream.clipPos(n+1)}putAction(t,e,i,n){for(let s=0;s<n;s+=3)if(this.actions[s]==t)return n;return this.actions[n++]=t,this.actions[n++]=e,this.actions[n++]=i,n}addActions(t,e,i,n){let{state:s}=t,{parser:r}=t.p,{data:o}=r;for(let a=0;a<2;a++)for(let t=r.stateSlot(s,a?2:1);;t+=3){if(65535==o[t]){if(1!=o[t+1]){0==n&&2==o[t+1]&&(n=this.putAction(DO(o,t+2),e,i,n));break}t=DO(o,t+2)}o[t]==e&&(n=this.putAction(DO(o,t+1),e,i,n))}return n}}class RO{constructor(t,e,i,n){this.parser=t,this.input=e,this.ranges=n,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new QO(e,n),this.tokens=new WO(t,this.stream),this.topTerm=t.top[1];let{from:s}=n[0];this.stacks=[pO.start(this,t.top[0],s)],this.fragments=i.length&&this.stream.end-s>4*t.bufferLength?new XO(i,t.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let t,e,i=this.stacks,n=this.minStackPos,s=this.stacks=[];if(this.bigReductionCount>300&&1==i.length){let[t]=i;for(;t.forceReduce()&&t.stack.length&&t.stack[t.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let r=0;r<i.length;r++){let o=i[r];for(;;){if(this.tokens.mainToken=null,o.pos>n)s.push(o);else{if(this.advanceStack(o,s,i))continue;{t||(t=[],e=[]),t.push(o);let i=this.tokens.getMainToken(o);e.push(i.value,i.end)}}break}}if(!s.length){let e=t&&function(t){let e=null;for(let i of t){let t=i.p.stoppedAt;(i.pos==i.p.stream.end||null!=t&&i.pos>t)&&i.p.parser.stateFlag(i.state,2)&&(!e||e.score<i.score)&&(e=i)}return e}(t);if(e)return this.stackToTree(e);if(this.parser.strict)throw new SyntaxError("No parse at "+n);this.recovering||(this.recovering=5)}if(this.recovering&&t){let i=null!=this.stoppedAt&&t[0].pos>this.stoppedAt?t[0]:this.runRecovery(t,e,s);if(i)return this.stackToTree(i.forceAll())}if(this.recovering){let t=1==this.recovering?1:3*this.recovering;if(s.length>t)for(s.sort(((t,e)=>e.score-t.score));s.length>t;)s.pop();s.some((t=>t.reducePos>n))&&this.recovering--}else if(s.length>1){t:for(let t=0;t<s.length-1;t++){let e=s[t];for(let i=t+1;i<s.length;i++){let n=s[i];if(e.sameState(n)||e.buffer.length>500&&n.buffer.length>500){if(!((e.score-n.score||e.buffer.length-n.buffer.length)>0)){s.splice(t--,1);continue t}s.splice(i--,1)}}}s.length>12&&s.splice(12,s.length-12)}this.minStackPos=s[0].pos;for(let r=1;r<s.length;r++)s[r].pos<this.minStackPos&&(this.minStackPos=s[r].pos);return null}stopAt(t){if(null!=this.stoppedAt&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}advanceStack(t,e,i){let n=t.pos,{parser:s}=this;CO&&this.stackID(t);if(null!=this.stoppedAt&&n>this.stoppedAt)return t.forceReduce()?t:null;if(this.fragments){let e=t.curContext&&t.curContext.tracker.strict,i=e?t.curContext.hash:0;for(let r=this.fragments.nodeAt(n);r;){let n=this.parser.nodeSet.types[r.type.id]==r.type?s.getGoto(t.state,r.type.id):-1;if(n>-1&&r.length&&(!e||(r.prop(No.contextHash)||0)==i))return t.useNode(r,n),!0;if(!(r instanceof na)||0==r.children.length||r.positions[0]>0)break;let o=r.children[0];if(!(o instanceof na&&0==r.positions[0]))break;r=o}}let r=s.stateSlot(t.state,4);if(r>0)return t.reduce(r),!0;if(t.stack.length>=8400)for(;t.stack.length>6e3&&t.forceReduce(););let o=this.tokens.getActions(t);for(let a=0;a<o.length;){let s=o[a++],r=o[a++],l=o[a++],h=a==o.length||!i,c=h?t:t.split(),f=this.tokens.mainToken;if(c.apply(s,r,f?f.start:c.pos,l),h)return!0;c.pos>n?e.push(c):i.push(c)}return!1}advanceFully(t,e){let i=t.pos;for(;;){if(!this.advanceStack(t,null,null))return!1;if(t.pos>i)return YO(t,e),!0}}runRecovery(t,e,i){let n=null,s=!1;for(let r=0;r<t.length;r++){let o=t[r],a=e[r<<1],l=e[1+(r<<1)],h=CO?this.stackID(o)+" -> ":"";if(o.deadEnd){if(s)continue;if(s=!0,o.restart(),this.advanceFully(o,i))continue}let c=o.split(),f=h;for(let t=0;c.forceReduce()&&t<10;t++){if(this.advanceFully(c,i))break;CO&&(f=this.stackID(c)+" -> ")}for(let t of o.recoverByInsert(a))this.advanceFully(t,i);this.stream.end>o.pos?(l==o.pos&&(l++,a=0),o.recoverByDelete(a,l),YO(o,i)):(!n||n.score<o.score)&&(n=o)}return n}stackToTree(t){return t.close(),na.build({buffer:wO.create(t),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:t.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(t){let e=(TO||(TO=new WeakMap)).get(t);return e||TO.set(t,e=String.fromCodePoint(this.nextStackID++)),e+t}}function YO(t,e){for(let i=0;i<e.length;i++){let n=e[i];if(n.pos==t.pos&&n.sameState(t))return void(e[i].score<t.score&&(e[i]=t))}e.push(t)}class qO{constructor(t,e,i){this.source=t,this.flags=e,this.disabled=i}allows(t){return!this.disabled||0==this.disabled[t]}}const MO=t=>t;class EO extends ka{constructor(t){if(super(),this.wrappers=[],14!=t.version)throw new RangeError(`Parser version (${t.version}) doesn't match runtime version (14)`);let e=t.nodeNames.split(" ");this.minRepeatTerm=e.length;for(let o=0;o<t.repeatNodeCount;o++)e.push("");let i=Object.keys(t.topRules).map((e=>t.topRules[e][1])),n=[];for(let o=0;o<e.length;o++)n.push([]);function s(t,e,i){n[t].push([e,e.deserialize(String(i))])}if(t.nodeProps)for(let o of t.nodeProps){let t=o[0];"string"==typeof t&&(t=No[t]);for(let e=1;e<o.length;){let i=o[e++];if(i>=0)s(i,t,o[e++]);else{let n=o[e+-i];for(let r=-i;r>0;r--)s(o[e++],t,n);e++}}}this.nodeSet=new Ko(e.map(((e,s)=>Fo.define({name:s>=this.minRepeatTerm?void 0:e,id:s,props:n[s],top:i.indexOf(s)>-1,error:0==s,skipped:t.skippedNodes&&t.skippedNodes.indexOf(s)>-1})))),t.propSources&&(this.nodeSet=this.nodeSet.extend(...t.propSources)),this.strict=!1,this.bufferLength=Go;let r=vO(t.tokenData);this.context=t.context,this.specializerSpecs=t.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let o=0;o<this.specializerSpecs.length;o++)this.specialized[o]=this.specializerSpecs[o].term;this.specializers=this.specializerSpecs.map(jO),this.states=vO(t.states,Uint32Array),this.data=vO(t.stateData),this.goto=vO(t.goto),this.maxTerm=t.maxTerm,this.tokenizers=t.tokenizers.map((t=>"number"==typeof t?new kO(r,t):t)),this.topRules=t.topRules,this.dialects=t.dialects||{},this.dynamicPrecedences=t.dynamicPrecedences||null,this.tokenPrecTable=t.tokenPrec,this.termNames=t.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(t,e,i){let n=new RO(this,t,e,i);for(let s of this.wrappers)n=s(n,t,e,i);return n}getGoto(t,e,i=!1){let n=this.goto;if(e>=n[0])return-1;for(let s=n[e+1];;){let e=n[s++],r=1&e,o=n[s++];if(r&&i)return o;for(let i=s+(e>>1);s<i;s++)if(n[s]==t)return o;if(r)return-1}}hasAction(t,e){let i=this.data;for(let n=0;n<2;n++)for(let s,r=this.stateSlot(t,n?2:1);;r+=3){if(65535==(s=i[r])){if(1!=i[r+1]){if(2==i[r+1])return DO(i,r+2);break}s=i[r=DO(i,r+2)]}if(s==e||0==s)return DO(i,r+1)}return 0}stateSlot(t,e){return this.states[6*t+e]}stateFlag(t,e){return(this.stateSlot(t,0)&e)>0}validAction(t,e){return!!this.allActions(t,(t=>t==e||null))}allActions(t,e){let i=this.stateSlot(t,4),n=i?e(i):void 0;for(let s=this.stateSlot(t,1);null==n;s+=3){if(65535==this.data[s]){if(1!=this.data[s+1])break;s=DO(this.data,s+2)}n=e(DO(this.data,s+1))}return n}nextStates(t){let e=[];for(let i=this.stateSlot(t,1);;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=DO(this.data,i+2)}if(!(1&this.data[i+2])){let t=this.data[i+1];e.some(((e,i)=>1&i&&e==t))||e.push(this.data[i],t)}}return e}configure(t){let e=Object.assign(Object.create(EO.prototype),this);if(t.props&&(e.nodeSet=this.nodeSet.extend(...t.props)),t.top){let i=this.topRules[t.top];if(!i)throw new RangeError(`Invalid top rule name ${t.top}`);e.top=i}return t.tokenizers&&(e.tokenizers=this.tokenizers.map((e=>{let i=t.tokenizers.find((t=>t.from==e));return i?i.to:e}))),t.specializers&&(e.specializers=this.specializers.slice(),e.specializerSpecs=this.specializerSpecs.map(((i,n)=>{let s=t.specializers.find((t=>t.from==i.external));if(!s)return i;let r=Object.assign(Object.assign({},i),{external:s.to});return e.specializers[n]=jO(r),r}))),t.contextTracker&&(e.context=t.contextTracker),t.dialect&&(e.dialect=this.parseDialect(t.dialect)),null!=t.strict&&(e.strict=t.strict),t.wrap&&(e.wrappers=e.wrappers.concat(t.wrap)),null!=t.bufferLength&&(e.bufferLength=t.bufferLength),e}hasWrappers(){return this.wrappers.length>0}getName(t){return this.termNames?this.termNames[t]:String(t<=this.maxNode&&this.nodeSet.types[t].name||t)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(t){let e=this.dynamicPrecedences;return null==e?0:e[t]||0}parseDialect(t){let e=Object.keys(this.dialects),i=e.map((()=>!1));if(t)for(let s of t.split(" ")){let t=e.indexOf(s);t>=0&&(i[t]=!0)}let n=null;for(let s=0;s<e.length;s++)if(!i[s])for(let t,i=this.dialects[e[s]];65535!=(t=this.data[i++]);)(n||(n=new Uint8Array(this.maxTerm+1)))[t]=1;return new qO(t,i,n)}static deserialize(t){return new EO(t)}}function DO(t,e){return t[e]|t[e+1]<<16}function jO(t){if(t.external){let e=t.extend?1:0;return(i,n)=>t.external(i,n)<<1|e}return t.get}const _O=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],VO=new class{constructor(t){this.start=t.start,this.shift=t.shift||MO,this.reduce=t.reduce||MO,this.reuse=t.reuse||MO,this.hash=t.hash||(()=>0),this.strict=!1!==t.strict}}({start:!1,shift:(t,e)=>4==e||5==e||312==e?t:313==e,strict:!1}),BO=new xO(((t,e)=>{let{next:i}=t;(125==i||-1==i||e.context)&&t.acceptToken(310)}),{contextual:!0,fallback:!0}),zO=new xO(((t,e)=>{let i,{next:n}=t;_O.indexOf(n)>-1||(47!=n||47!=(i=t.peek(1))&&42!=i)&&(125==n||59==n||-1==n||e.context||t.acceptToken(309))}),{contextual:!0}),GO=new xO(((t,e)=>{let{next:i}=t;if((43==i||45==i)&&(t.advance(),i==t.next)){t.advance();let i=!e.context&&e.canShift(1);t.acceptToken(i?1:2)}}),{contextual:!0});function IO(t,e){return t>=65&&t<=90||t>=97&&t<=122||95==t||t>=192||!e&&t>=48&&t<=57}const LO=new xO(((t,e)=>{if(60!=t.next||!e.dialectEnabled(0))return;if(t.advance(),47==t.next)return;let i=0;for(;_O.indexOf(t.next)>-1;)t.advance(),i++;if(IO(t.next,!0)){for(t.advance(),i++;IO(t.next,!1);)t.advance(),i++;for(;_O.indexOf(t.next)>-1;)t.advance(),i++;if(44==t.next)return;for(let e=0;;e++){if(7==e){if(!IO(t.next,!0))return;break}if(t.next!="extends".charCodeAt(e))break;t.advance(),i++}}t.acceptToken(3,-i)})),NO=Ca({"get set async static":Ha.modifier,"for while do if else switch try catch finally return throw break continue default case":Ha.controlKeyword,"in of await yield void typeof delete instanceof":Ha.operatorKeyword,"let var const using function class extends":Ha.definitionKeyword,"import export from":Ha.moduleKeyword,"with debugger as new":Ha.keyword,TemplateString:Ha.special(Ha.string),super:Ha.atom,BooleanLiteral:Ha.bool,this:Ha.self,null:Ha.null,Star:Ha.modifier,VariableName:Ha.variableName,"CallExpression/VariableName TaggedTemplateExpression/VariableName":Ha.function(Ha.variableName),VariableDefinition:Ha.definition(Ha.variableName),Label:Ha.labelName,PropertyName:Ha.propertyName,PrivatePropertyName:Ha.special(Ha.propertyName),"CallExpression/MemberExpression/PropertyName":Ha.function(Ha.propertyName),"FunctionDeclaration/VariableDefinition":Ha.function(Ha.definition(Ha.variableName)),"ClassDeclaration/VariableDefinition":Ha.definition(Ha.className),PropertyDefinition:Ha.definition(Ha.propertyName),PrivatePropertyDefinition:Ha.definition(Ha.special(Ha.propertyName)),UpdateOp:Ha.updateOperator,"LineComment Hashbang":Ha.lineComment,BlockComment:Ha.blockComment,Number:Ha.number,String:Ha.string,Escape:Ha.escape,ArithOp:Ha.arithmeticOperator,LogicOp:Ha.logicOperator,BitOp:Ha.bitwiseOperator,CompareOp:Ha.compareOperator,RegExp:Ha.regexp,Equals:Ha.definitionOperator,Arrow:Ha.function(Ha.punctuation),": Spread":Ha.punctuation,"( )":Ha.paren,"[ ]":Ha.squareBracket,"{ }":Ha.brace,"InterpolationStart InterpolationEnd":Ha.special(Ha.brace),".":Ha.derefOperator,", ;":Ha.separator,"@":Ha.meta,TypeName:Ha.typeName,TypeDefinition:Ha.definition(Ha.typeName),"type enum interface implements namespace module declare":Ha.definitionKeyword,"abstract global Privacy readonly override":Ha.modifier,"is keyof unique infer":Ha.operatorKeyword,JSXAttributeValue:Ha.attributeValue,JSXText:Ha.content,"JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag":Ha.angleBracket,"JSXIdentifier JSXNameSpacedName":Ha.tagName,"JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName":Ha.attributeName,"JSXBuiltin/JSXIdentifier":Ha.standard(Ha.tagName)}),UO={__proto__:null,export:18,as:23,from:31,default:34,async:39,function:40,extends:52,this:56,true:64,false:64,null:76,void:80,typeof:84,super:102,new:136,delete:152,yield:161,await:165,class:170,public:227,private:227,protected:227,readonly:229,instanceof:248,satisfies:251,in:252,const:254,import:286,keyof:339,unique:343,infer:349,is:385,abstract:405,implements:407,type:409,let:412,var:414,using:417,interface:423,enum:427,namespace:433,module:435,declare:439,global:443,for:462,of:471,while:474,with:478,do:482,if:486,else:488,switch:492,case:498,try:504,catch:508,finally:512,return:516,throw:520,break:524,continue:528,debugger:532},HO={__proto__:null,async:123,get:125,set:127,declare:187,public:189,private:189,protected:189,static:191,abstract:193,override:195,readonly:201,accessor:203,new:389},FO={__proto__:null,"<":143},KO=EO.deserialize({version:14,states:"$=WO%TQ^OOO%[Q^OOO'_Q`OOP(lOWOOO*zQ08SO'#ChO+RO!bO'#CiO+aO#tO'#CiO+oO?MpO'#D^O.QQ^O'#DdO.bQ^O'#DoO%[Q^O'#DyO0fQ^O'#EROOQ07b'#EZ'#EZO1PQWO'#EWOOQO'#El'#ElOOQO'#Ie'#IeO1XQWO'#GmO1dQWO'#EkO1iQWO'#EkO3kQ08SO'#JiO6[Q08SO'#JjO6xQWO'#FZO6}Q&jO'#FqOOQ07b'#Fc'#FcO7YO,YO'#FcO7hQ7[O'#FxO9UQWO'#FwOOQ07b'#Jj'#JjOOQ07`'#Ji'#JiO9ZQWO'#GqOOQU'#KV'#KVO9fQWO'#IRO9kQ07hO'#ISOOQU'#JW'#JWOOQU'#IW'#IWQ`Q^OOO`Q^OOO%[Q^O'#DqO9sQ^O'#D}O9zQ^O'#EPO9aQWO'#GmO:RQ7[O'#CnO:aQWO'#EjO:lQWO'#EuO:qQ7[O'#FbO;`QWO'#GmOOQO'#KW'#KWO;eQWO'#KWO;sQWO'#GuO;sQWO'#GvO;sQWO'#GxO9aQWO'#G{O<jQWO'#HOO>RQWO'#CdO>cQWO'#H[O>kQWO'#HbO>kQWO'#HdO`Q^O'#HfO>kQWO'#HhO>kQWO'#HkO>pQWO'#HqO>uQ07iO'#HwO%[Q^O'#HyO?QQ07iO'#H{O?]Q07iO'#H}O9kQ07hO'#IPO?hQ08SO'#ChO@jQ`O'#DiQOQWOOO%[Q^O'#EPOAQQWO'#ESO:RQ7[O'#EjOA]QWO'#EjOAhQpO'#FbOOQU'#Cf'#CfOOQ07`'#Dn'#DnOOQ07`'#Jm'#JmO%[Q^O'#JmOOQO'#Jq'#JqOOQO'#Ib'#IbOBhQ`O'#EcOOQ07`'#Eb'#EbOOQ07`'#Jt'#JtOCdQ07pO'#EcOCnQ`O'#EVOOQO'#Jp'#JpODSQ`O'#JqOEaQ`O'#EVOCnQ`O'#EcPEnO!0LbO'#CaPOOO)CDu)CDuOOOO'#IX'#IXOEyO!bO,59TOOQ07b,59T,59TOOOO'#IY'#IYOFXO#tO,59TO%[Q^O'#D`OOOO'#I['#I[OFgO?MpO,59xOOQ07b,59x,59xOFuQ^O'#I]OGYQWO'#JkOI[QrO'#JkO+}Q^O'#JkOIcQWO,5:OOIyQWO'#ElOJWQWO'#JzOJcQWO'#JyOJcQWO'#JyOJkQWO,5;YOJpQWO'#JxOOQ07f,5:Z,5:ZOJwQ^O,5:ZOLxQ08SO,5:eOMiQWO,5:mONSQ07hO'#JwONZQWO'#JvO9ZQWO'#JvONoQWO'#JvONwQWO,5;XON|QWO'#JvO!#UQrO'#JjOOQ07b'#Ch'#ChO%[Q^O'#ERO!#tQpO,5:rOOQO'#Jr'#JrOOQO-E<c-E<cO9aQWO,5=XO!$[QWO,5=XO!$aQ^O,5;VO!&dQ7[O'#EgO!'}QWO,5;VO!)mQ7[O'#DsO!)tQ^O'#DxO!*OQ`O,5;`O!*WQ`O,5;`O%[Q^O,5;`OOQU'#FR'#FROOQU'#FT'#FTO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aO%[Q^O,5;aOOQU'#FX'#FXO!*fQ^O,5;rOOQ07b,5;w,5;wOOQ07b,5;x,5;xO!,iQWO,5;xOOQ07b,5;y,5;yO%[Q^O'#IiO!,qQ07hO,5<eO!&dQ7[O,5;aO!-`Q7[O,5;aO%[Q^O,5;uO!-gQ&jO'#FgO!.dQ&jO'#KOO!.OQ&jO'#KOO!.kQ&jO'#KOOOQO'#KO'#KOO!/PQ&jO,5<POOOS,5<],5<]O!/bQ^O'#FsOOOS'#Ih'#IhO7YO,YO,5;}O!/iQ&jO'#FuOOQ07b,5;},5;}O!0YQMhO'#CuOOQ07b'#Cy'#CyO!0mQWO'#CyO!0rO?MpO'#C}O!1`Q7[O,5<bO!1gQWO,5<dO!3SQ!LQO'#GSO!3aQWO'#GTO!3fQWO'#GTO!5UQ!LQO'#GXO!6QQ`O'#G]OOQO'#Gh'#GhO!(SQ7[O'#GgOOQO'#Gj'#GjO!(SQ7[O'#GiO!6sQMhO'#JdOOQ07b'#Jd'#JdO!6}QWO'#JcO!7]QWO'#JbO!7eQWO'#CtOOQ07b'#Cw'#CwOOQ07b'#DR'#DROOQ07b'#DT'#DTO1SQWO'#DVO!(SQ7[O'#FzO!(SQ7[O'#F|O!7mQWO'#GOO!7rQWO'#GPO!3fQWO'#GVO!(SQ7[O'#G[O!7wQWO'#EmO!8fQWO,5<cOOQ07`'#Cq'#CqO!8nQWO'#EnO!9hQ`O'#EoOOQ07`'#Jx'#JxO!9oQ07hO'#KXO9kQ07hO,5=]O`Q^O,5>mOOQU'#J`'#J`OOQU,5>n,5>nOOQU-E<U-E<UO!;qQ08SO,5:]O!>_Q08SO,5:iO%[Q^O,5:iO!@xQ08SO,5:kOOQO,5@r,5@rO!AiQ7[O,5=XO!AwQ07hO'#JaO9UQWO'#JaO!BYQ07hO,59YO!BeQ`O,59YO!BmQ7[O,59YO:RQ7[O,59YO!BxQWO,5;VO!CQQWO'#HZO!CfQWO'#K[O%[Q^O,5;zO!9cQ`O,5;|O!CnQWO,5=tO!CsQWO,5=tO!CxQWO,5=tO9kQ07hO,5=tO;sQWO,5=dOOQO'#Cu'#CuO!DWQ`O,5=aO!D`Q7[O,5=bO!DkQWO,5=dO!DpQpO,5=gO!DxQWO'#KWO>pQWO'#HQO9aQWO'#HSO!D}QWO'#HSO:RQ7[O'#HUO!ESQWO'#HUOOQU,5=j,5=jO!EXQWO'#HVO!EjQWO'#CnO!EoQWO,59OO!EyQWO,59OO!HOQ^O,59OOOQU,59O,59OO!H`Q07hO,59OO%[Q^O,59OO!JkQ^O'#H^OOQU'#H_'#H_OOQU'#H`'#H`O`Q^O,5=vO!KRQWO,5=vO`Q^O,5=|O`Q^O,5>OO!KWQWO,5>QO`Q^O,5>SO!K]QWO,5>VO!KbQ^O,5>]OOQU,5>c,5>cO%[Q^O,5>cO9kQ07hO,5>eOOQU,5>g,5>gO# lQWO,5>gOOQU,5>i,5>iO# lQWO,5>iOOQU,5>k,5>kO#!YQ`O'#D[O%[Q^O'#JmO#!dQ`O'#JmO##RQ`O'#DjO##dQ`O'#DjO#%uQ^O'#DjO#%|QWO'#JlO#&UQWO,5:TO#&ZQWO'#EpO#&iQWO'#J{O#&qQWO,5;ZO#&vQ`O'#DjO#'TQ`O'#EUOOQ07b,5:n,5:nO%[Q^O,5:nO#'[QWO,5:nO>pQWO,5;UO!BeQ`O,5;UO!BmQ7[O,5;UO:RQ7[O,5;UO#'dQWO,5@XO#'iQ$ISO,5:rOOQO-E<`-E<`O#(oQ07pO,5:}OCnQ`O,5:qO#(yQ`O,5:qOCnQ`O,5:}O!BYQ07hO,5:qOOQ07`'#Ef'#EfOOQO,5:},5:}O%[Q^O,5:}O#)WQ07hO,5:}O#)cQ07hO,5:}O!BeQ`O,5:qOOQO,5;T,5;TO#)qQ07hO,5:}POOO'#IV'#IVP#*VO!0LbO,58{POOO,58{,58{OOOO-E<V-E<VOOQ07b1G.o1G.oOOOO-E<W-E<WO#*bQpO,59zOOOO-E<Y-E<YOOQ07b1G/d1G/dO#*gQrO,5>wO+}Q^O,5>wOOQO,5>},5>}O#*qQ^O'#I]OOQO-E<Z-E<ZO#+OQWO,5@VO#+WQrO,5@VO#+_QWO,5@eOOQ07b1G/j1G/jO%[Q^O,5@fO#+gQWO'#IcOOQO-E<a-E<aO#+_QWO,5@eOOQ07`1G0t1G0tOOQ07f1G/u1G/uOOQ07f1G0X1G0XO%[Q^O,5@cO#+{Q07hO,5@cO#,^Q07hO,5@cO#,eQWO,5@bO9ZQWO,5@bO#,mQWO,5@bO#,{QWO'#IfO#,eQWO,5@bOOQ07`1G0s1G0sO!*OQ`O,5:tO!*ZQ`O,5:tOOQO,5:v,5:vO#-mQWO,5:vO#-uQ7[O1G2sO9aQWO1G2sOOQ07b1G0q1G0qO#.TQ08SO1G0qO#/YQ08QO,5;ROOQ07b'#GR'#GRO#/vQ08SO'#JdO!$aQ^O1G0qO#2OQ7[O'#JnO#2YQWO,5:_O#2_QrO'#JoO%[Q^O'#JoO#2iQWO,5:dOOQ07b'#D['#D[OOQ07b1G0z1G0zO%[Q^O1G0zOOQ07b1G1d1G1dO#2nQWO1G0zO#5VQ08SO1G0{O#5^Q08SO1G0{O#7wQ08SO1G0{O#8OQ08SO1G0{O#:YQ08SO1G0{O#:pQ08SO1G0{O#=jQ08SO1G0{O#=qQ08SO1G0{O#@UQ08SO1G0{O#@cQ08SO1G0{O#BaQ08SO1G0{O#EaQ(CYO'#ChO#G_Q(CYO1G1^O#GfQ(CYO'#JjO!,lQWO1G1dO#GvQ08SO,5?TOOQ07`-E<g-E<gO#HjQ08SO1G0{OOQ07b1G0{1G0{O#JuQ08SO1G1aO#KiQ&jO,5<TO#KqQ&jO,5<UO#KyQ&jO'#FlO#LbQWO'#FkOOQO'#KP'#KPOOQO'#Ig'#IgO#LgQ&jO1G1kOOQ07b1G1k1G1kOOOS1G1v1G1vO#LxQ(CYO'#JiO#MSQWO,5<_O!*fQ^O,5<_OOOS-E<f-E<fOOQ07b1G1i1G1iO#MXQ`O'#KOOOQ07b,5<a,5<aO#MaQ`O,5<aOOQ07b,59e,59eO!&dQ7[O'#DPOOOO'#IZ'#IZO#MfO?MpO,59iOOQ07b,59i,59iO%[Q^O1G1|O!7rQWO'#IkO#MqQ7[O,5<uOOQ07b,5<r,5<rO!(SQ7[O'#InO#NaQ7[O,5=RO!(SQ7[O'#IpO$ SQ7[O,5=TO!&dQ7[O,5=VOOQO1G2O1G2OO$ ^QpO'#CqO$ qQ!LQO'#EnO$!pQ`O'#G]O$#^QpO,5<nO$#eQWO'#KSO9ZQWO'#KSO$#sQWO,5<pO!(SQ7[O,5<oO$#xQWO'#GUO$$ZQWO,5<oO$$`QpO'#GRO$$mQpO'#KTO$$wQWO'#KTO!&dQ7[O'#KTO$$|QWO,5<sO$%RQ`O'#G^O!5{Q`O'#G^O$%dQWO'#G`O$%iQWO'#GbO!3fQWO'#GeO$%nQ07hO'#ImO$%yQ`O,5<wOOQ07f,5<w,5<wO$&QQ`O'#G^O$&`Q`O'#G_O$&hQ`O'#G_O$&mQ7[O,5=RO$&}Q7[O,5=TOOQ07b,5=W,5=WO!(SQ7[O,5?}O!(SQ7[O,5?}O$'_QWO'#IrO$'jQWO,5?|O$'rQWO,59`O$(cQ7[O,59qOOQ07b,59q,59qO$)UQ7[O,5<fO$)wQ7[O,5<hO@bQWO,5<jOOQ07b,5<k,5<kO$*RQWO,5<qO$*WQ7[O,5<vO$*hQWO'#JvO!$aQ^O1G1}O$*mQWO1G1}O9ZQWO'#JyO9ZQWO'#EpO%[Q^O'#EpO9ZQWO'#ItO$*rQ07hO,5@sOOQU1G2w1G2wOOQU1G4X1G4XOOQ07b1G/w1G/wO!,iQWO1G/wO$,wQ08SO1G0TOOQU1G2s1G2sO!&dQ7[O1G2sO%[Q^O1G2sO#-xQWO1G2sO$.{Q7[O'#EgOOQ07`,5?{,5?{O$/VQ07hO,5?{OOQU1G.t1G.tO!BYQ07hO1G.tO!BeQ`O1G.tO!BmQ7[O1G.tO$/hQWO1G0qO$/mQWO'#ChO$/xQWO'#K]O$0QQWO,5=uO$0VQWO'#K]O$0[QWO'#K]O$0jQWO'#IzO$0xQWO,5@vO$1QQrO1G1fOOQ07b1G1h1G1hO9aQWO1G3`O@bQWO1G3`O$1XQWO1G3`O$1^QWO1G3`OOQU1G3`1G3`O!DkQWO1G3OO!&dQ7[O1G2{O$1cQWO1G2{OOQU1G2|1G2|O!&dQ7[O1G2|O$1hQWO1G2|O$1pQ`O'#GzOOQU1G3O1G3OO!5{Q`O'#IvO!DpQpO1G3ROOQU1G3R1G3ROOQU,5=l,5=lO$1xQ7[O,5=nO9aQWO,5=nO$%iQWO,5=pO9UQWO,5=pO!BeQ`O,5=pO!BmQ7[O,5=pO:RQ7[O,5=pO$2WQWO'#KZO$2cQWO,5=qOOQU1G.j1G.jO$<EMAIL>$2sQWO1G.jO9kQ07hO1G.jO$4xQrO,5@xO$5YQWO,5@xO9ZQWO,5@xO$5eQ^O,5=xO$5lQWO,5=xOOQU1G3b1G3bO`Q^O1G3bOOQU1G3h1G3hOOQU1G3j1G3jO>kQWO1G3lO$5qQ^O1G3nO$9uQ^O'#HmOOQU1G3q1G3qO$:SQWO'#HsO>pQWO'#HuOOQU1G3w1G3wO$:[Q^O1G3wO9kQ07hO1G3}OOQU1G4P1G4POOQ07`'#GY'#GYO9kQ07hO1G4RO9kQ07hO1G4TO$>cQWO,5@XO!*fQ^O,5;[O9ZQWO,5;[O>pQWO,5:UO!*fQ^O,5:UO!BeQ`O,5:UO$>hQ(CYO,5:UOOQO,5;[,5;[O$>rQ`O'#I^O$?YQWO,5@WOOQ07b1G/o1G/oO$?bQ`O'#IdO$?lQWO,5@gOOQ07`1G0u1G0uO##dQ`O,5:UOOQO'#Ia'#IaO$?tQ`O,5:pOOQ07f,5:p,5:pO#'_QWO1G0YOOQ07b1G0Y1G0YO%[Q^O1G0YOOQ07b1G0p1G0pO>pQWO1G0pO!BeQ`O1G0pO!BmQ7[O1G0pOOQ07`1G5s1G5sO!BYQ07hO1G0]OOQO1G0i1G0iO%[Q^O1G0iO$?{Q07hO1G0iO$@WQ07hO1G0iO!BeQ`O1G0]OCnQ`O1G0]O$@fQ07hO1G0iOOQO1G0]1G0]O$@zQ08SO1G0iPOOO-E<T-E<TPOOO1G.g1G.gOOOO1G/f1G/fO$AUQpO,5<eO$A^QrO1G4cOOQO1G4i1G4iO%[Q^O,5>wO$AhQWO1G5qO$ApQWO1G6PO$AxQrO1G6QO9ZQWO,5>}O$BSQ08SO1G5}O%[Q^O1G5}O$BdQ07hO1G5}O$BuQWO1G5|O$BuQWO1G5|O9ZQWO1G5|O$B}QWO,5?QO9ZQWO,5?QOOQO,5?Q,5?QO$CcQWO,5?QO$*hQWO,5?QOOQO-E<d-E<dOOQO1G0`1G0`OOQO1G0b1G0bO!,lQWO1G0bOOQU7+(_7+(_O!&dQ7[O7+(_O%[Q^O7+(_O$CqQWO7+(_O$C|Q7[O7+(_O$D[Q08SO,5=RO$FgQ08SO,5=TO$HrQ08SO,5=RO$KTQ08SO,5=TO$MfQ08SO,59qO% nQ08SO,5<fO%#yQ08SO,5<hO%&UQ08SO,5<vOOQ07b7+&]7+&]O%(gQ08SO7+&]O%)ZQ7[O'#I_O%)eQWO,5@YOOQ07b1G/y1G/yO%)mQ^O'#I`O%)zQWO,5@ZO%*SQrO,5@ZOOQ07b1G0O1G0OO%*^QWO7+&fOOQ07b7+&f7+&fO%*cQ(CYO,5:eO%[Q^O7+&xO%*mQ(CYO,5:]O%*zQ(CYO,5:iO%+UQ(CYO,5:kOOQ07b7+'O7+'OOOQO1G1o1G1oOOQO1G1p1G1pO%+`QtO,5<WO!*fQ^O,5<VOOQO-E<e-E<eOOQ07b7+'V7+'VOOOS7+'b7+'bOOOS1G1y1G1yO%+kQWO1G1yOOQ07b1G1{1G1{O%+pQpO,59kOOOO-E<X-E<XOOQ07b1G/T1G/TO%+wQ08SO7+'hOOQ07b,5?V,5?VO%,kQpO,5?VOOQ07b1G2a1G2aP!&dQ7[O'#IkPOQ07b-E<i-E<iO%-ZQ7[O,5?YOOQ07b-E<l-E<lO%-|Q7[O,5?[OOQ07b-E<n-E<nO%.WQpO1G2qO%._QpO'#CqO%.uQ7[O'#JyO%.|Q^O'#EpOOQ07b1G2Y1G2YO%/WQWO'#IjO%/lQWO,5@nO%/lQWO,5@nO%/tQWO,5@nO%0PQWO,5@nOOQO1G2[1G2[O%0_Q7[O1G2ZO!(SQ7[O1G2ZO%0oQ!LQO'#IlO%0|QWO,5@oO!&dQ7[O,5@oO%1UQpO,5@oOOQ07b1G2_1G2_OOQ07`,5<x,5<xOOQ07`,5<y,5<yO$*hQWO,5<yOC_QWO,5<yO!BeQ`O,5<xOOQO'#Ga'#GaO%1`QWO,5<zOOQ07`,5<|,5<|O$*hQWO,5=POOQO,5?X,5?XOOQO-E<k-E<kOOQ07f1G2c1G2cO!5{Q`O,5<xO%1hQWO,5<yO$%dQWO,5<zO!5{Q`O,5<yO!(SQ7[O'#InO%2[Q7[O1G2mO!(SQ7[O'#IpO%2}Q7[O1G2oO%3XQ7[O1G5iO%3cQ7[O1G5iOOQO,5?^,5?^OOQO-E<p-E<pOOQO1G.z1G.zO!9cQ`O,59sO%[Q^O,59sO%3pQWO1G2UO!(SQ7[O1G2]O%3uQ08SO7+'iOOQ07b7+'i7+'iO!$aQ^O7+'iO%4iQWO,5;[OOQ07`,5?`,5?`OOQ07`-E<r-E<rOOQ07b7+%c7+%cO%4nQpO'#KUO#'_QWO7+(_O%4xQrO7+(_O$CtQWO7+(_O%5PQ08QO'#ChO%5dQ08QO,5<}O%6UQWO,5<}OOQ07`1G5g1G5gOOQU7+$`7+$`O!BYQ07hO7+$`O!BeQ`O7+$`O!$aQ^O7+&]O%6ZQWO'#IyO%6rQWO,5@wOOQO1G3a1G3aO9aQWO,5@wO%6rQWO,5@wO%6zQWO,5@wOOQO,5?f,5?fOOQO-E<x-E<xOOQ07b7+'Q7+'QO%7PQWO7+(zO9kQ07hO7+(zO9aQWO7+(zO@bQWO7+(zOOQU7+(j7+(jO%7UQ08QO7+(gO!&dQ7[O7+(gO%7`QpO7+(hOOQU7+(h7+(hO!&dQ7[O7+(hO%7gQWO'#KYO%7rQWO,5=fOOQO,5?b,5?bOOQO-E<t-E<tOOQU7+(m7+(mO%9RQ`O'#HTOOQU1G3Y1G3YO!&dQ7[O1G3YO%[Q^O1G3YO%9YQWO1G3YO%9eQ7[O1G3YO9kQ07hO1G3[O$%iQWO1G3[O9UQWO1G3[O!BeQ`O1G3[O!BmQ7[O1G3[O%9sQWO'#IxO%:XQWO,5@uO%:aQ`O,5@uOOQ07`1G3]1G3]OOQU7+$U7+$UO@bQWO7+$UO9kQ07hO7+$UO%:lQWO7+$UO%[Q^O1G6dO%[Q^O1G6eO%:qQ07hO1G6dO%:{Q^O1G3dO%;SQWO1G3dO%;XQ^O1G3dOOQU7+(|7+(|O9kQ07hO7+)WO`Q^O7+)YOOQU'#K`'#K`OOQU'#I{'#I{O%;`Q^O,5>XOOQU,5>X,5>XO%[Q^O'#HnO%;mQWO'#HpOOQU,5>_,5>_O9ZQWO,5>_OOQU,5>a,5>aOOQU7+)c7+)cOOQU7+)i7+)iOOQU7+)m7+)mOOQU7+)o7+)oO%;rQ`O1G5sO%<WQ(CYO1G0vO%<bQWO1G0vOOQO1G/p1G/pO%<mQ(CYO1G/pO>pQWO1G/pO!*fQ^O'#DjOOQO,5>x,5>xOOQO-E<[-E<[OOQO,5?O,5?OOOQO-E<b-E<bO!BeQ`O1G/pOOQO-E<_-E<_OOQ07f1G0[1G0[OOQ07b7+%t7+%tO#'_QWO7+%tOOQ07b7+&[7+&[O>pQWO7+&[O!BeQ`O7+&[OOQO7+%w7+%wO$@zQ08SO7+&TOOQO7+&T7+&TO%[Q^O7+&TO%<wQ07hO7+&TO!BYQ07hO7+%wO!BeQ`O7+%wO%=SQ07hO7+&TO%=bQ08SO7++iO%[Q^O7++iO%=rQWO7++hO%=rQWO7++hOOQO1G4l1G4lO9ZQWO1G4lO%=zQWO1G4lOOQO7+%|7+%|O#'_QWO<<KyO%4xQrO<<KyO%>YQWO<<KyOOQU<<Ky<<KyO!&dQ7[O<<KyO%[Q^O<<KyO%>bQWO<<KyO%>mQ08SO,5?YO%@xQ08SO,5?[O%CTQ08SO1G2ZO%EfQ08SO1G2mO%GqQ08SO1G2oO%I|Q7[O,5>yOOQO-E<]-E<]O%JWQrO,5>zO%[Q^O,5>zOOQO-E<^-E<^O%JbQWO1G5uOOQ07b<<JQ<<JQO%JjQ(CYO1G0qO%LtQ(CYO1G0{O%L{Q(CYO1G0{O& PQ(CYO1G0{O& WQ(CYO1G0{O&!{Q(CYO1G0{O&#cQ(CYO1G0{O&%vQ(CYO1G0{O&%}Q(CYO1G0{O&'{Q(CYO1G0{O&(YQ(CYO1G0{O&*WQ(CYO1G0{O&*kQ08SO<<JdO&+pQ(CYO1G0{O&-fQ(CYO'#JdO&/iQ(CYO1G1aO&/vQ(CYO1G0TO!*fQ^O'#FnOOQO'#KQ'#KQOOQO1G1r1G1rO&0QQWO1G1qO&0VQ(CYO,5?TOOOS7+'e7+'eOOOO1G/V1G/VOOQ07b1G4q1G4qO!(SQ7[O7+(]O&2gQrO'#ChO&2qQWO,5?UO9ZQWO,5?UOOQO-E<h-E<hO&3PQWO1G6YO&3PQWO1G6YO&3XQWO1G6YO&3dQ7[O7+'uO&3tQpO,5?WO&4OQWO,5?WO!&dQ7[O,5?WOOQO-E<j-E<jO&4TQpO1G6ZO&4_QWO1G6ZOOQ07`1G2e1G2eO$*hQWO1G2eOOQ07`1G2d1G2dO&4gQWO1G2fO!&dQ7[O1G2fOOQ07`1G2k1G2kO!BeQ`O1G2dOC_QWO1G2eO&4lQWO1G2fO&4tQWO1G2eO&5hQ7[O,5?YOOQ07b-E<m-E<mO&6ZQ7[O,5?[OOQ07b-E<o-E<oO!(SQ7[O7++TOOQ07b1G/_1G/_O&6eQWO1G/_OOQ07b7+'p7+'pO&6jQ7[O7+'wO&6zQ08SO<<KTOOQ07b<<KT<<KTO&7nQWO1G0vO!&dQ7[O'#IsO&7sQWO,5@pO!&dQ7[O1G2iOOQU<<Gz<<GzO!BYQ07hO<<GzO&7{Q08SO<<IwOOQ07b<<Iw<<IwOOQO,5?e,5?eO&8oQWO,5?eO&8tQWO,5?eOOQO-E<w-E<wO&9SQWO1G6cO&9SQWO1G6cO9aQWO1G6cO@bQWO<<LfOOQU<<Lf<<LfO&9[QWO<<LfO9kQ07hO<<LfOOQU<<LR<<LRO%7UQ08QO<<LROOQU<<LS<<LSO%7`QpO<<LSO&9aQ`O'#IuO&9lQWO,5@tO!*fQ^O,5@tOOQU1G3Q1G3QO%.|Q^O'#JmOOQO'#Iw'#IwO9kQ07hO'#IwO&9tQ`O,5=oOOQU,5=o,5=oO&9{Q`O'#EcO&:aQWO7+(tO&:fQWO7+(tOOQU7+(t7+(tO!&dQ7[O7+(tO%[Q^O7+(tO&:nQWO7+(tOOQU7+(v7+(vO9kQ07hO7+(vO$%iQWO7+(vO9UQWO7+(vO!BeQ`O7+(vO&:yQWO,5?dOOQO-E<v-E<vOOQO'#HW'#HWO&;UQWO1G6aO9kQ07hO<<GpOOQU<<Gp<<GpO@bQWO<<GpO&;^QWO7+,OO&;cQWO7+,PO%[Q^O7+,OO%[Q^O7+,POOQU7+)O7+)OO&;hQWO7+)OO&;mQ^O7+)OO&;tQWO7+)OOOQU<<Lr<<LrOOQU<<Lt<<LtOOQU-E<y-E<yOOQU1G3s1G3sO&;yQWO,5>YOOQU,5>[,5>[O&<OQWO1G3yO9ZQWO7+&bO!*fQ^O7+&bOOQO7+%[7+%[O&<TQ(CYO1G6QO>pQWO7+%[OOQ07b<<I`<<I`OOQ07b<<Iv<<IvO>pQWO<<IvOOQO<<Io<<IoO$@zQ08SO<<IoO%[Q^O<<IoOOQO<<Ic<<IcO!BYQ07hO<<IcO&<_Q07hO<<IoO&<jQ08SO<= TO&<zQWO<= SOOQO7+*W7+*WO9ZQWO7+*WOOQUANAeANAeO&=SQWOANAeO!&dQ7[OANAeO#'_QWOANAeO%4xQrOANAeO%[Q^OANAeO&=[Q08SO7+'uO&?mQ08SO,5?YO&AxQ08SO,5?[O&DTQ08SO7+'wO&FfQrO1G4fO&FpQ(CYO7+&]O&HtQ(CYO,5=RO&J{Q(CYO,5=TO&K]Q(CYO,5=RO&KmQ(CYO,5=TO&K}Q(CYO,59qO&NQQ(CYO,5<fO'!TQ(CYO,5<hO'$WQ(CYO,5<vO'%|Q(CYO7+'hO'&ZQ(CYO7+'iO'&hQWO,5<YOOQO7+']7+']O'&mQ7[O<<KwOOQO1G4p1G4pO'&tQWO1G4pO''PQWO1G4pO''_QWO7++tO''_QWO7++tO!&dQ7[O1G4rO''gQpO1G4rO''qQWO7++uOOQ07`7+(P7+(PO$*hQWO7+(QO''yQpO7+(QOOQ07`7+(O7+(OO$*hQWO7+(PO'(QQWO7+(QO!&dQ7[O7+(QOC_QWO7+(PO'(VQ7[O<<NoOOQ07b7+$y7+$yO'(aQpO,5?_OOQO-E<q-E<qO'(kQ08QO7+(TOOQUAN=fAN=fO9aQWO1G5POOQO1G5P1G5PO'({QWO1G5PO')QQWO7++}O')QQWO7++}O9kQ07hOANBQO@bQWOANBQOOQUANBQANBQOOQUANAmANAmOOQUANAnANAnO')YQWO,5?aOOQO-E<s-E<sO')eQ(CYO1G6`OOQO,5?c,5?cOOQO-E<u-E<uOOQU1G3Z1G3ZO%.|Q^O,5<zOOQU<<L`<<L`O!&dQ7[O<<L`O&:aQWO<<L`O')oQWO<<L`O%[Q^O<<L`OOQU<<Lb<<LbO9kQ07hO<<LbO$%iQWO<<LbO9UQWO<<LbO')wQ`O1G5OO'*SQWO7++{OOQUAN=[AN=[O9kQ07hOAN=[OOQU<= j<= jOOQU<= k<= kO'*[QWO<= jO'*aQWO<= kOOQU<<Lj<<LjO'*fQWO<<LjO'*kQ^O<<LjOOQU1G3t1G3tO>pQWO7+)eO'*rQWO<<I|O'*}Q(CYO<<I|OOQO<<Hv<<HvOOQ07bAN?bAN?bOOQOAN?ZAN?ZO$@zQ08SOAN?ZOOQOAN>}AN>}O%[Q^OAN?ZOOQO<<Mr<<MrOOQUG27PG27PO!&dQ7[OG27PO#'_QWOG27PO'+XQWOG27PO%4xQrOG27PO'+aQ(CYO<<JdO'+nQ(CYO1G2ZO'-dQ(CYO,5?YO'/gQ(CYO,5?[O'1jQ(CYO1G2mO'3mQ(CYO1G2oO'5pQ(CYO<<KTO'5}Q(CYO<<IwOOQO1G1t1G1tO!(SQ7[OANAcOOQO7+*[7+*[O'6[QWO7+*[O'6gQWO<= `O'6oQpO7+*^OOQ07`<<Kl<<KlO$*hQWO<<KlOOQ07`<<Kk<<KkO'6yQpO<<KlO$*hQWO<<KkOOQO7+*k7+*kO9aQWO7+*kO'7QQWO<= iOOQUG27lG27lO9kQ07hOG27lO!*fQ^O1G4{O'7YQWO7++zO&:aQWOANAzOOQUANAzANAzO!&dQ7[OANAzO'7bQWOANAzOOQUANA|ANA|O9kQ07hOANA|O$%iQWOANA|OOQO'#HX'#HXOOQO7+*j7+*jOOQUG22vG22vOOQUANEUANEUOOQUANEVANEVOOQUANBUANBUO'7jQWOANBUOOQU<<MP<<MPO!*fQ^OAN?hOOQOG24uG24uO$@zQ08SOG24uO#'_QWOLD,kOOQULD,kLD,kO!&dQ7[OLD,kO'7oQWOLD,kO'7wQ(CYO7+'uO'9mQ(CYO,5?YO';pQ(CYO,5?[O'=sQ(CYO7+'wO'?iQ7[OG26}OOQO<<Mv<<MvOOQ07`ANAWANAWO$*hQWOANAWOOQ07`ANAVANAVOOQO<<NV<<NVOOQULD-WLD-WO'?yQ(CYO7+*gOOQUG27fG27fO&:aQWOG27fO!&dQ7[OG27fOOQUG27hG27hO9kQ07hOG27hOOQUG27pG27pO'@TQ(CYOG25SOOQOLD*aLD*aOOQU!$(!V!$(!VO#'_QWO!$(!VO!&dQ7[O!$(!VO'@_Q08SOG26}OOQ07`G26rG26rOOQULD-QLD-QO&:aQWOLD-QOOQULD-SLD-SOOQU!)9Eq!)9EqO#'_QWO!)9EqOOQU!$(!l!$(!lOOQU!.K;]!.K;]O'BpQ(CYOG26}O!*fQ^O'#DyO1PQWO'#EWO'DfQrO'#JiO!*fQ^O'#DqO'DmQ^O'#D}O'DtQrO'#ChO'G[QrO'#ChO!*fQ^O'#EPO'GlQ^O,5;VO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O,5;aO!*fQ^O'#IiO'IoQWO,5<eO'IwQ7[O,5;aO'KbQ7[O,5;aO!*fQ^O,5;uO!&dQ7[O'#GgO'IwQ7[O'#GgO!&dQ7[O'#GiO'IwQ7[O'#GiO1SQWO'#DVO1SQWO'#DVO!&dQ7[O'#FzO'IwQ7[O'#FzO!&dQ7[O'#F|O'IwQ7[O'#F|O!&dQ7[O'#G[O'IwQ7[O'#G[O!*fQ^O,5:iO'KiQ`O'#D[O!*fQ^O,5@fO'GlQ^O1G0qO'KsQ(CYO'#ChO!*fQ^O1G1|O!&dQ7[O'#InO'IwQ7[O'#InO!&dQ7[O'#IpO'IwQ7[O'#IpO'K}QpO'#CqO!&dQ7[O,5<oO'IwQ7[O,5<oO'GlQ^O1G1}O!*fQ^O7+&xO!&dQ7[O1G2ZO'IwQ7[O1G2ZO!&dQ7[O'#InO'IwQ7[O'#InO!&dQ7[O'#IpO'IwQ7[O'#IpO!&dQ7[O1G2]O'IwQ7[O1G2]O'GlQ^O7+'iO'GlQ^O7+&]O!&dQ7[OANAcO'IwQ7[OANAcO'LbQWO'#EkO'LgQWO'#EkO'LoQWO'#FZO'LtQWO'#EuO'LyQWO'#JzO'MUQWO'#JxO'MaQWO,5;VO'MfQ7[O,5<bO'MmQWO'#GTO'MrQWO'#GTO'MwQWO,5<cO'NPQWO,5;VO'NXQ(CYO1G1^O'N`QWO,5<oO'NeQWO,5<oO'NjQWO,5<qO'NoQWO,5<qO'NtQWO1G1}O'NyQWO1G0qO( OQ7[O<<KwO( VQ7[O<<KwO7hQ7[O'#FxO9UQWO'#FwOA]QWO'#EjO!*fQ^O,5;rO!3fQWO'#GTO!3fQWO'#GTO!3fQWO'#GVO!3fQWO'#GVO!(SQ7[O7+(]O!(SQ7[O7+(]O%.WQpO1G2qO%.WQpO1G2qO!&dQ7[O,5=VO!&dQ7[O,5=V",stateData:"(!Z~O'tOS'uOSSOS'vRQ~OPYOQYORfOX!VO`qOczOdyOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![XO!fuO!kZO!nYO!oYO!pYO!rvO!twO!wxO!{]O#s!PO$T|O%b}O%d!QO%f!OO%g!OO%h!OO%k!RO%m!SO%p!TO%q!TO%s!UO&P!WO&V!XO&X!YO&Z!ZO&]![O&`!]O&f!^O&l!_O&n!`O&p!aO&r!bO&t!cO'{SO'}TO(QUO(XVO(g[O(uiO~OVtO~P`OPYOQYORfOc!jOd!iOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![!eO!fuO!kZO!nYO!oYO!pYO!rvO!t!gO!w!hO$T!kO'{!dO'}TO(QUO(XVO(g[O(uiO~O`!wOo!nO!P!oO!_!yO!`!vO!a!vO!{:jO#P!pO#Q!pO#R!xO#S!pO#T!pO#W!zO#X!zO'|!lO'}TO(QUO([!mO(g!sO~O'v!{O~OP[XZ[X`[Xn[X|[X}[X!P[X!Y[X!h[X!i[X!k[X!o[X#[[X#geX#j[X#k[X#l[X#m[X#n[X#o[X#p[X#q[X#r[X#t[X#v[X#x[X#y[X$O[X'r[X(X[X(i[X(p[X(q[X~O!d$|X~P(qO^!}O'}#PO(O!}O(P#PO~O^#QO(P#PO(Q#PO(R#QO~Ot#SO!R#TO(Y#TO(Z#VO~OPYOQYORfOc!jOd!iOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![!eO!fuO!kZO!nYO!oYO!pYO!rvO!t!gO!w!hO$T!kO'{:nO'}TO(QUO(XVO(g[O(uiO~O!X#ZO!Y#WO!V(_P!V(mP~P+}O!Z#cO~P`OPYOQYORfOc!jOd!iOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![!eO!fuO!kZO!nYO!oYO!pYO!rvO!t!gO!w!hO$T!kO'}TO(QUO(XVO(g[O(uiO~Ol#mO!X#iO!{]O#e#lO#f#iO'{:oO!j(jP~P.iO!k#oO'{#nO~O!w#sO!{]O%b#tO~O#g#uO~O!d#vO#g#uO~OP$^OZ$eOn$RO|#zO}#{O!P#|O!Y$bO!h$TO!i#xO!k#yO!o$^O#j$PO#k$QO#l$QO#m$QO#n$SO#o$TO#p$TO#q$dO#r$TO#t$UO#v$WO#x$YO#y$ZO(XVO(i$[O(p#}O(q$OO~O`(]X'r(]X'p(]X!j(]X!V(]X![(]X%c(]X!d(]X~P1qO#[$fO$O$fOP(^XZ(^Xn(^X|(^X}(^X!P(^X!Y(^X!h(^X!k(^X!o(^X#j(^X#k(^X#l(^X#m(^X#n(^X#o(^X#p(^X#q(^X#r(^X#t(^X#v(^X#x(^X#y(^X(X(^X(i(^X(p(^X(q(^X![(^X%c(^X~O`(^X!i(^X'r(^X'p(^X!V(^X!j(^Xr(^X!d(^X~P4XO#[$fO~O$Y$hO$[$gO$c$mO~ORfO![$nO$f$oO$h$qO~Og%WOl%XOn$uOo$tOp$tOv%YOx%ZOz%[O!P$|O![$}O!f%aO!k$yO#f%bO$T%_O$o%]O$q%^O$t%`O'{$sO'}TO(QUO(X$vO(p%OO(q%QOf(UP~O!k%cO~O!P%fO![%gO'{%eO~O!d%kO~O`%lO'r%lO~O'|!lO~P%[O%h%sO~P%[Og%WO!k%cO'{%eO'|!lO~Od%zO!k%cO'{%eO~O#r$TO~O|&PO![%|O!k&OO%d&SO'{%eO'|!lO'}TO(QUO_)OP~O!w#sO~O%m&UO!P(zX![(zX'{(zX~O'{&VO~O!t&[O#s!PO%d!QO%f!OO%g!OO%h!OO%k!RO%m!SO%p!TO%q!TO~Oc&aOd&`O!w&^O%b&_O%u&]O~P;xOc&dOdyO![&cO!t&[O!wxO!{]O#s!PO%b}O%f!OO%g!OO%h!OO%k!RO%m!SO%p!TO%q!TO%s!UO~Oa&gO#[&jO%d&eO'|!lO~P<}O!k&kO!t&oO~O!k#oO~O![XO~O`%lO'q&wO'r%lO~O`%lO'q&zO'r%lO~O`%lO'q&|O'r%lO~O'p[X!V[Xr[X!j[X&T[X![[X%c[X!d[X~P(qO!_'ZO!`'SO!a'SO'|!lO'}TO(QUO~Oo'QO!P'PO!X'TO(['OO!Z(`P!Z(oP~P@UOj'^O!['[O'{%eO~Od'cO!k%cO'{%eO~O|&PO!k&OO~Oo!nO!P!oO!{:jO#P!pO#Q!pO#S!pO#T!pO'|!lO'}TO(QUO([!mO(g!sO~O!_'iO!`'hO!a'hO#R!pO#W'jO#X'jO~PApO`%lOg%WO!d#vO!k%cO'r%lO(i'lO~O!o'pO#['nO~PCOOo!nO!P!oO'}TO(QUO([!mO(g!sO~O![XOo(eX!P(eX!_(eX!`(eX!a(eX!{(eX#P(eX#Q(eX#R(eX#S(eX#T(eX#W(eX#X(eX'|(eX'}(eX(Q(eX([(eX(g(eX~O!`'hO!a'hO'|!lO~PCnO'w'tO'x'tO'y'vO~O^!}O'}'xO(O!}O(P'xO~O^#QO(P'xO(Q'xO(R#QO~Ot#SO!R#TO(Y#TO(Z'|O~O!X(OO!V'PX!V'VX!Y'PX!Y'VX~P+}O!Y(QO!V(_X~OP$^OZ$eOn$RO|#zO}#{O!P#|O!Y(QO!h$TO!i#xO!k#yO!o$^O#j$PO#k$QO#l$QO#m$QO#n$SO#o$TO#p$TO#q$dO#r$TO#t$UO#v$WO#x$YO#y$ZO(XVO(i$[O(p#}O(q$OO~O!V(_X~PGbO!V(VO~O!V(lX!Y(lX!d(lX!j(lX(i(lX~O#[(lX#g#`X!Z(lX~PIhO#[(WO!V(nX!Y(nX~O!Y(XO!V(mX~O!V([O~O#[$fO~PIhO!Z(]O~P`O|#zO}#{O!P#|O!i#xO!k#yO(XVOP!maZ!man!ma!Y!ma!h!ma!o!ma#j!ma#k!ma#l!ma#m!ma#n!ma#o!ma#p!ma#q!ma#r!ma#t!ma#v!ma#x!ma#y!ma(i!ma(p!ma(q!ma~O`!ma'r!ma'p!ma!V!ma!j!mar!ma![!ma%c!ma!d!ma~PKOO!j(^O~O!d#vO#[(_O(i'lO!Y(kX`(kX'r(kX~O!j(kX~PMnO!P%fO![%gO!{]O#e(dO#f(cO'{%eO~O!Y(eO!j(jX~O!j(gO~O!P%fO![%gO#f(cO'{%eO~OP(^XZ(^Xn(^X|(^X}(^X!P(^X!Y(^X!h(^X!i(^X!k(^X!o(^X#j(^X#k(^X#l(^X#m(^X#n(^X#o(^X#p(^X#q(^X#r(^X#t(^X#v(^X#x(^X#y(^X(X(^X(i(^X(p(^X(q(^X~O!d#vO!j(^X~P! [O|(hO}(iO!i#xO!k#yO!{!za!P!za~O!w!za%b!za![!za#e!za#f!za'{!za~P!#`O!w(mO~OPYOQYORfOc!jOd!iOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![XO!fuO!kZO!nYO!oYO!pYO!rvO!t!gO!w!hO$T!kO'{!dO'}TO(QUO(XVO(g[O(uiO~Og%WOl%XOn$uOo$tOp$tOv%YOx%ZOz;WO!P$|O![$}O!f<hO!k$yO#f;^O$T%_O$o;YO$q;[O$t%`O'{(qO'}TO(QUO(X$vO(p%OO(q%QO~O#g(sO~Og%WOl%XOn$uOo$tOp$tOv%YOx%ZOz%[O!P$|O![$}O!f%aO!k$yO#f%bO$T%_O$o%]O$q%^O$t%`O'{(qO'}TO(QUO(X$vO(p%OO(q%QO~Of(bP~P!(SO!X(wO!j(cP~P%[O([(yO(g[O~O!P({O!k#yO([(yO(g[O~OP:iOQ:iORfOc<dOd!iOlkOn:iOokOpkOvkOx:iOz:iO!PWO!TkO!UkO![!eO!f:lO!kZO!n:iO!o:iO!p:iO!r:mO!t:pO!w!hO$T!kO'{)ZO'}TO(QUO(XVO(g[O(u<bO~O})^O!k#yO~O!Y$bO`$ma'r$ma'p$ma!j$ma!V$ma![$ma%c$ma!d$ma~O#s)bO~P!&dO|)eO!d)dO![$ZX$W$ZX$Y$ZX$[$ZX$c$ZX~O!d)dO![(rX$W(rX$Y(rX$[(rX$c(rX~O|)eO~P!.OO|)eO![(rX$W(rX$Y(rX$[(rX$c(rX~O![)gO$W)kO$Y)fO$[)fO$c)lO~O!X)oO~P!*fO$Y$hO$[$gO$c)sO~Oj$uX|$uX!P$uX!i$uX(p$uX(q$uX~OfiXf$uXjiX!YiX#[iX~P!/tOo)uO~Ot)vO(Y)wO(Z)yO~Oj*SO|){O!P)|O(p%OO(q%QO~Of)zO~P!0}Of*TO~Og%WOl%XOn$uOo$tOp$tOv%YOx%ZOz;WO!P*VO![*WO!f<hO!k$yO#f;^O$T%_O$o;YO$q;[O$t%`O'}TO(QUO(X$vO(p%OO(q%QO~O!X*ZO'{*UO!j(vP~P!1lO#g*]O~O!k*^O~Og%WOl%XOn$uOo$tOp$tOv%YOx%ZOz;WO!P$|O![$}O!f<hO!k$yO#f;^O$T%_O$o;YO$q;[O$t%`O'{*`O'}TO(QUO(X$vO(p%OO(q%QO~O!X*cO!V(wP~P!3kOn*oO!P*gO!_*mO!`*fO!a*fO!k*^O#W*nO%Y*iO'|!lO([!mO~O!Z*lO~P!5`O!i#xOj(WX|(WX!P(WX(p(WX(q(WX!Y(WX#[(WX~Of(WX#|(WX~P!6XOj*tO#[*sOf(VX!Y(VX~O!Y*uOf(UX~O'{&VOf(UP~O!k*|O~O'{(qO~Ol+QO!P%fO!X#iO![%gO!{]O#e#lO#f#iO'{%eO!j(jP~O!d#vO#g+RO~O!P%fO!X+TO!Y(XO![%gO'{%eO!V(mP~Oo'WO!P+VO!X+UO'}TO(QUO([(yO~O!Z(oP~P!9SO!Y+WO`({X'r({X~OP$^OZ$eOn$RO|#zO}#{O!P#|O!h$TO!i#xO!k#yO!o$^O#j$PO#k$QO#l$QO#m$QO#n$SO#o$TO#p$TO#q$dO#r$TO#t$UO#v$WO#x$YO#y$ZO(XVO(i$[O(p#}O(q$OO~O`!ea!Y!ea'r!ea'p!ea!V!ea!j!ear!ea![!ea%c!ea!d!ea~P!9zO|#zO}#{O!P#|O!i#xO!k#yO(XVOP!qaZ!qan!qa!Y!qa!h!qa!o!qa#j!qa#k!qa#l!qa#m!qa#n!qa#o!qa#p!qa#q!qa#r!qa#t!qa#v!qa#x!qa#y!qa(i!qa(p!qa(q!qa~O`!qa'r!qa'p!qa!V!qa!j!qar!qa![!qa%c!qa!d!qa~P!<eO|#zO}#{O!P#|O!i#xO!k#yO(XVOP!saZ!san!sa!Y!sa!h!sa!o!sa#j!sa#k!sa#l!sa#m!sa#n!sa#o!sa#p!sa#q!sa#r!sa#t!sa#v!sa#x!sa#y!sa(i!sa(p!sa(q!sa~O`!sa'r!sa'p!sa!V!sa!j!sar!sa![!sa%c!sa!d!sa~P!?OOg%WOj+aO!['[O%c+`O~O!d+cO`(TX![(TX'r(TX!Y(TX~O`%lO![XO'r%lO~Og%WO!k%cO~Og%WO!k%cO'{%eO~O!d#vO#g(sO~Oa+nO%d+oO'{+kO'}TO(QUO!Z)PP~O!Y+pO_)OX~OZ+tO~O_+uO~O![%|O'{%eO'|!lO_)OP~Og%WO#[+zO~Og%WOj+}O![$}O~O![,PO~O|,RO![XO~O%h%sO~O!w,WO~Od,]O~Oa,^O'{#nO'}TO(QUO!Z(}P~Od%zO~O%d!QO'{&VO~P<}OZ,cO_,bO~OPYOQYORfOczOdyOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO!fuO!kZO!nYO!oYO!pYO!rvO!wxO!{]O%b}O'}TO(QUO(XVO(g[O(uiO~O![!eO!t!gO$T!kO'{!dO~P!FRO_,bO`%lO'r%lO~OPYOQYORfOc!jOd!iOlkOnYOokOpkOvkOxYOzYO!PWO!TkO!UkO![!eO!fuO!kZO!nYO!oYO!pYO!rvO!w!hO$T!kO'{!dO'}TO(QUO(XVO(g[O(uiO~O`,hO!twO#s!OO%f!OO%g!OO%h!OO~P!HkO!k&kO~O&V,nO~O![,pO~O&h,rO&j,sOP&eaQ&eaR&eaX&ea`&eac&ead&eal&ean&eao&eap&eav&eax&eaz&ea!P&ea!T&ea!U&ea![&ea!f&ea!k&ea!n&ea!o&ea!p&ea!r&ea!t&ea!w&ea!{&ea#s&ea$T&ea%b&ea%d&ea%f&ea%g&ea%h&ea%k&ea%m&ea%p&ea%q&ea%s&ea&P&ea&V&ea&X&ea&Z&ea&]&ea&`&ea&f&ea&l&ea&n&ea&p&ea&r&ea&t&ea'p&ea'{&ea'}&ea(Q&ea(X&ea(g&ea(u&ea!Z&ea&^&eaa&ea&c&ea~O'{,xO~Og!bX!Y!OX!Z!OX!d!OX!d!bX!k!bX#[!OX~O!Y!bX!Z!bX~P# qO!d,}O#[,|Og(aX!Y#dX!Y(aX!Z#dX!Z(aX!d(aX!k(aX~Og%WO!d-PO!k%cO!Y!^X!Z!^X~Oo!nO!P!oO'}TO(QUO([!mO~OP:iOQ:iORfOc<dOd!iOlkOn:iOokOpkOvkOx:iOz:iO!PWO!TkO!UkO![!eO!f:lO!kZO!n:iO!o:iO!p:iO!r:mO!t:pO!w!hO$T!kO'}TO(QUO(XVO(g[O(u<bO~O'{;dO~P##uO!Y-TO!Z(`X~O!Z-VO~O!d,}O#[,|O!Y#dX!Z#dX~O!Y-WO!Z(oX~O!Z-YO~O!`-ZO!a-ZO'|!lO~P##dO!Z-^O~P'_Oj-aO!['[O~O!V-fO~Oo!za!_!za!`!za!a!za#P!za#Q!za#R!za#S!za#T!za#W!za#X!za'|!za'}!za(Q!za([!za(g!za~P!#`O!o-kO#[-iO~PCOO!`-mO!a-mO'|!lO~PCnO`%lO#[-iO'r%lO~O`%lO!d#vO#[-iO'r%lO~O`%lO!d#vO!o-kO#[-iO'r%lO(i'lO~O'w'tO'x'tO'y-rO~Or-sO~O!V'Pa!Y'Pa~P!9zO!X-wO!V'PX!Y'PX~P%[O!Y(QO!V(_a~O!V(_a~PGbO!Y(XO!V(ma~O!P%fO!X-{O![%gO'{%eO!V'VX!Y'VX~O#[-}O!Y(ka!j(ka`(ka'r(ka~O!d#vO~P#+{O!Y(eO!j(ja~O!P%fO![%gO#f.RO'{%eO~Ol.WO!P%fO!X.TO![%gO!{]O#e.VO#f.TO'{%eO!Y'YX!j'YX~O}.[O!k#yO~Og%WOj._O!['[O%c.^O~O`#_i!Y#_i'r#_i'p#_i!V#_i!j#_ir#_i![#_i%c#_i!d#_i~P!9zOj<nO|){O!P)|O(p%OO(q%QO~O#g#Za`#Za#[#Za'r#Za!Y#Za!j#Za![#Za!V#Za~P#.wO#g(WXP(WXZ(WX`(WXn(WX}(WX!h(WX!k(WX!o(WX#j(WX#k(WX#l(WX#m(WX#n(WX#o(WX#p(WX#q(WX#r(WX#t(WX#v(WX#x(WX#y(WX'r(WX(X(WX(i(WX!j(WX!V(WX'p(WXr(WX![(WX%c(WX!d(WX~P!6XO!Y.lOf(bX~P!0}Of.nO~O!Y.oO!j(cX~P!9zO!j.rO~O!V.tO~OP$^O|#zO}#{O!P#|O!i#xO!k#yO!o$^O(XVOZ#ii`#iin#ii!Y#ii!h#ii#k#ii#l#ii#m#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii'r#ii(i#ii(p#ii(q#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~O#j#ii~P#2sO#j$PO~P#2sOP$^O|#zO}#{O!P#|O!i#xO!k#yO!o$^O#j$PO#k$QO#l$QO#m$QO(XVOZ#ii`#ii!Y#ii!h#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii'r#ii(i#ii(p#ii(q#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~On#ii~P#5eOn$RO~P#5eOP$^On$RO|#zO}#{O!P#|O!i#xO!k#yO!o$^O#j$PO#k$QO#l$QO#m$QO#n$SO(XVO`#ii!Y#ii#t#ii#v#ii#x#ii#y#ii'r#ii(i#ii(p#ii(q#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~OZ#ii!h#ii#o#ii#p#ii#q#ii#r#ii~P#8VOZ$eO!h$TO#o$TO#p$TO#q$dO#r$TO~P#8VOP$^OZ$eOn$RO|#zO}#{O!P#|O!h$TO!i#xO!k#yO!o$^O#j$PO#k$QO#l$QO#m$QO#n$SO#o$TO#p$TO#q$dO#r$TO#t$UO(XVO(q$OO`#ii!Y#ii#x#ii#y#ii'r#ii(i#ii(p#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~O#v$WO~P#;WO#v#ii~P#;WOP$^OZ$eOn$RO|#zO}#{O!P#|O!h$TO!i#xO!k#yO!o$^O#j$PO#k$QO#l$QO#m$QO#n$SO#o$TO#p$TO#q$dO#r$TO#t$UO(XVO`#ii!Y#ii#x#ii#y#ii'r#ii(i#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~O#v#ii(p#ii(q#ii~P#=xO#v$WO(p#}O(q$OO~P#=xOP$^OZ$eOn$RO|#zO}#{O!P#|O!h$TO!i#xO!k#yO!o$^O#j$PO#k$QO#l$QO#m$QO#n$SO#o$TO#p$TO#q$dO#r$TO#t$UO#v$WO#x$YO(XVO(p#}O(q$OO~O`#ii!Y#ii#y#ii'r#ii(i#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~P#@pOP[XZ[Xn[X|[X}[X!P[X!h[X!i[X!k[X!o[X#[[X#geX#j[X#k[X#l[X#m[X#n[X#o[X#p[X#q[X#r[X#t[X#v[X#x[X#y[X$O[X(X[X(i[X(p[X(q[X!Y[X!Z[X~O#|[X~P#CZOP$^OZ;QOn:tO|#zO}#{O!P#|O!h:vO!i#xO!k#yO!o$^O#j:rO#k:sO#l:sO#m:sO#n:uO#o:vO#p:vO#q;PO#r:vO#t:wO#v:yO#x:{O#y:|O(XVO(i$[O(p#}O(q$OO~O#|.vO~P#EhO#[;RO$O;RO#|(^X!Z(^X~P! [O`']a!Y']a'r']a'p']a!j']a!V']ar']a![']a%c']a!d']a~P!9zOP#iiZ#ii`#iin#ii}#ii!Y#ii!h#ii!i#ii!k#ii!o#ii#j#ii#k#ii#l#ii#m#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii'r#ii(X#ii(i#ii'p#ii!V#ii!j#iir#ii![#ii%c#ii!d#ii~P#.wO`#}i!Y#}i'r#}i'p#}i!V#}i!j#}ir#}i![#}i%c#}i!d#}i~P!9zO$Y.{O$[.{O~O$Y.|O$[.|O~O!d)dO#[.}O![$`X$W$`X$Y$`X$[$`X$c$`X~O!X/OO~O![)gO$W/QO$Y)fO$[)fO$c/RO~O!Y:}O!Z(]X~P#EhO!Z/SO~O!d)dO$c(rX~O$c/UO~Ot)vO(Y)wO(Z/XO~O!V/]O~P!&dO(p%OOj%Za|%Za!P%Za(q%Za!Y%Za#[%Za~Of%Za#|%Za~P#MxO(q%QOj%]a|%]a!P%]a(p%]a!Y%]a#[%]a~Of%]a#|%]a~P#NkO!YeX!deX!jeX!j$uX(ieX~P!/tO!X/fO!Y(XO'{/eO!V(mP!V(wP~P!1lOn*oO!_*mO!`*fO!a*fO!k*^O#W*nO%Y*iO'|!lO~Oo'WO!P/gO!X+UO!Z*lO'}TO(QUO([;aO!Z(oP~P$!UO!j/hO~P#.wO!Y/iO!d#vO(i'lO!j(vX~O!j/nO~O!P%fO!X*ZO![%gO'{%eO!j(vP~O#g/pO~O!V$uX!Y$uX!d$|X~P!/tO!Y/qO!V(wX~P#.wO!d/sO~O!V/uO~Og%WOn/yO!d#vO!k%cO(i'lO~O'{/{O~O!d+cO~O`%lO!Y0PO'r%lO~O!Z0RO~P!5`O!`0SO!a0SO'|!lO([!mO~O!P0UO([!mO~O#W0VO~Of%Za!Y%Za#[%Za#|%Za~P!0}Of%]a!Y%]a#[%]a#|%]a~P!0}O'{&VOf'fX!Y'fX~O!Y*uOf(Ua~Of0`O~O|0aO}0aO!P0bOjya(pya(qya!Yya#[ya~Ofya#|ya~P$'wO|){O!P)|Oj$na(p$na(q$na!Y$na#[$na~Of$na#|$na~P$(mO|){O!P)|Oj$pa(p$pa(q$pa!Y$pa#[$pa~Of$pa#|$pa~P$)`O#g0dO~Of%Oa!Y%Oa#[%Oa#|%Oa~P!0}O!d#vO~O#g0gO~O!Y+WO`({a'r({a~O|#zO}#{O!P#|O!i#xO!k#yO(XVOP!qiZ!qin!qi!Y!qi!h!qi!o!qi#j!qi#k!qi#l!qi#m!qi#n!qi#o!qi#p!qi#q!qi#r!qi#t!qi#v!qi#x!qi#y!qi(i!qi(p!qi(q!qi~O`!qi'r!qi'p!qi!V!qi!j!qir!qi![!qi%c!qi!d!qi~P$*}Og%WOn$uOo$tOp$tOv%YOx%ZOz;WO!P$|O![$}O!f<hO!k$yO#f;^O$T%_O$o;YO$q;[O$t%`O'}TO(QUO(X$vO(p%OO(q%QO~Ol0qO'{0pO~P$-hO!d+cO`(Ta![(Ta'r(Ta!Y(Ta~O#g0wO~OZ[X!YeX!ZeX~O!Y0xO!Z)PX~O!Z0zO~OZ0{O~Oa0}O'{+kO'}TO(QUO~O![%|O'{%eO_'nX!Y'nX~O!Y+pO_)Oa~O!j1QO~P!9zOZ1TO~O_1UO~O#[1XO~Oj1[O![$}O~O([(yO!Z(|P~Og%WOj1eO![1bO%c1dO~OZ1oO!Y1mO!Z(}X~O!Z1pO~O_1rO`%lO'r%lO~O'{#nO'}TO(QUO~O#[$fO$O$fOP(^XZ(^Xn(^X|(^X}(^X!P(^X!Y(^X!h(^X!k(^X!o(^X#j(^X#k(^X#l(^X#m(^X#n(^X#o(^X#p(^X#q(^X#t(^X#v(^X#x(^X#y(^X(X(^X(i(^X(p(^X(q(^X~O#r1uO&T1vO`(^X!i(^X~P$3OO#[$fO#r1uO&T1vO~O`1xO~P%[O`1zO~O&^1}OP&[iQ&[iR&[iX&[i`&[ic&[id&[il&[in&[io&[ip&[iv&[ix&[iz&[i!P&[i!T&[i!U&[i![&[i!f&[i!k&[i!n&[i!o&[i!p&[i!r&[i!t&[i!w&[i!{&[i#s&[i$T&[i%b&[i%d&[i%f&[i%g&[i%h&[i%k&[i%m&[i%p&[i%q&[i%s&[i&P&[i&V&[i&X&[i&Z&[i&]&[i&`&[i&f&[i&l&[i&n&[i&p&[i&r&[i&t&[i'p&[i'{&[i'}&[i(Q&[i(X&[i(g&[i(u&[i!Z&[ia&[i&c&[i~Oa2TO!Z2RO&c2SO~P`O![XO!k2VO~O&j,sOP&eiQ&eiR&eiX&ei`&eic&eid&eil&ein&eio&eip&eiv&eix&eiz&ei!P&ei!T&ei!U&ei![&ei!f&ei!k&ei!n&ei!o&ei!p&ei!r&ei!t&ei!w&ei!{&ei#s&ei$T&ei%b&ei%d&ei%f&ei%g&ei%h&ei%k&ei%m&ei%p&ei%q&ei%s&ei&P&ei&V&ei&X&ei&Z&ei&]&ei&`&ei&f&ei&l&ei&n&ei&p&ei&r&ei&t&ei'p&ei'{&ei'}&ei(Q&ei(X&ei(g&ei(u&ei!Z&ei&^&eia&ei&c&ei~O!V2]O~O!Y!^a!Z!^a~P#EhOo!nO!P!oO!X2cO([!mO!Y'QX!Z'QX~P@UO!Y-TO!Z(`a~O!Y'WX!Z'WX~P!9SO!Y-WO!Z(oa~O!Z2jO~P'_O`%lO#[2sO'r%lO~O`%lO!d#vO#[2sO'r%lO~O`%lO!d#vO!o2wO#[2sO'r%lO(i'lO~O`%lO'r%lO~P!9zO!Y$bOr$ma~O!V'Pi!Y'Pi~P!9zO!Y(QO!V(_i~O!Y(XO!V(mi~O!V(ni!Y(ni~P!9zO!Y(ki!j(ki`(ki'r(ki~P!9zO#[2yO!Y(ki!j(ki`(ki'r(ki~O!Y(eO!j(ji~O!P%fO![%gO!{]O#e3OO#f2}O'{%eO~O!P%fO![%gO#f2}O'{%eO~Oj3VO!['[O%c3UO~Og%WOj3VO!['[O%c3UO~O#g%ZaP%ZaZ%Za`%Zan%Za}%Za!h%Za!i%Za!k%Za!o%Za#j%Za#k%Za#l%Za#m%Za#n%Za#o%Za#p%Za#q%Za#r%Za#t%Za#v%Za#x%Za#y%Za'r%Za(X%Za(i%Za!j%Za!V%Za'p%Zar%Za![%Za%c%Za!d%Za~P#MxO#g%]aP%]aZ%]a`%]an%]a}%]a!h%]a!i%]a!k%]a!o%]a#j%]a#k%]a#l%]a#m%]a#n%]a#o%]a#p%]a#q%]a#r%]a#t%]a#v%]a#x%]a#y%]a'r%]a(X%]a(i%]a!j%]a!V%]a'p%]ar%]a![%]a%c%]a!d%]a~P#NkO#g%ZaP%ZaZ%Za`%Zan%Za}%Za!Y%Za!h%Za!i%Za!k%Za!o%Za#j%Za#k%Za#l%Za#m%Za#n%Za#o%Za#p%Za#q%Za#r%Za#t%Za#v%Za#x%Za#y%Za'r%Za(X%Za(i%Za!j%Za!V%Za'p%Za#[%Zar%Za![%Za%c%Za!d%Za~P#.wO#g%]aP%]aZ%]a`%]an%]a}%]a!Y%]a!h%]a!i%]a!k%]a!o%]a#j%]a#k%]a#l%]a#m%]a#n%]a#o%]a#p%]a#q%]a#r%]a#t%]a#v%]a#x%]a#y%]a'r%]a(X%]a(i%]a!j%]a!V%]a'p%]a#[%]ar%]a![%]a%c%]a!d%]a~P#.wO#gyaPyaZya`yanya!hya!iya!kya!oya#jya#kya#lya#mya#nya#oya#pya#qya#rya#tya#vya#xya#yya'rya(Xya(iya!jya!Vya'pyarya![ya%cya!dya~P$'wO#g$naP$naZ$na`$nan$na}$na!h$na!i$na!k$na!o$na#j$na#k$na#l$na#m$na#n$na#o$na#p$na#q$na#r$na#t$na#v$na#x$na#y$na'r$na(X$na(i$na!j$na!V$na'p$nar$na![$na%c$na!d$na~P$(mO#g$paP$paZ$pa`$pan$pa}$pa!h$pa!i$pa!k$pa!o$pa#j$pa#k$pa#l$pa#m$pa#n$pa#o$pa#p$pa#q$pa#r$pa#t$pa#v$pa#x$pa#y$pa'r$pa(X$pa(i$pa!j$pa!V$pa'p$par$pa![$pa%c$pa!d$pa~P$)`O#g%OaP%OaZ%Oa`%Oan%Oa}%Oa!Y%Oa!h%Oa!i%Oa!k%Oa!o%Oa#j%Oa#k%Oa#l%Oa#m%Oa#n%Oa#o%Oa#p%Oa#q%Oa#r%Oa#t%Oa#v%Oa#x%Oa#y%Oa'r%Oa(X%Oa(i%Oa!j%Oa!V%Oa'p%Oa#[%Oar%Oa![%Oa%c%Oa!d%Oa~P#.wO`#_q!Y#_q'r#_q'p#_q!V#_q!j#_qr#_q![#_q%c#_q!d#_q~P!9zOf'RX!Y'RX~P!(SO!Y.lOf(ba~O!X3aO!Y'SX!j'SX~P%[O!Y.oO!j(ca~O!Y.oO!j(ca~P!9zO!V3dO~O#|!ma!Z!ma~PKOO#|!ea!Y!ea!Z!ea~P#EhO#|!qa!Z!qa~P!<eO#|!sa!Z!sa~P!?OORfO![3vO$a3wO~O!Z3{O~Or3|O~P#.wO`$jq!Y$jq'r$jq'p$jq!V$jq!j$jqr$jq![$jq%c$jq!d$jq~P!9zO!V3}O~P#.wO|){O!P)|O(q%QOj'ba(p'ba!Y'ba#['ba~Of'ba#|'ba~P%,rO|){O!P)|Oj'da(p'da(q'da!Y'da#['da~Of'da#|'da~P%-eO(i$[O~P#.wO!VeX!V$uX!YeX!Y$uX!d$|X#[eX~P!/tO'{;jO~P!1lOlkO'{4PO~P.iO!P%fO!X4RO![%gO'{%eO!Y'^X!j'^X~O!Y/iO!j(va~O!Y/iO!d#vO!j(va~O!Y/iO!d#vO(i'lO!j(va~Of$wi!Y$wi#[$wi#|$wi~P!0}O!X4ZO!V'`X!Y'`X~P!3kO!Y/qO!V(wa~O!Y/qO!V(wa~P#.wO!d#vO#r4cO~On4fO!d#vO(i'lO~O(p%OOj%Zi|%Zi!P%Zi(q%Zi!Y%Zi#[%Zi~Of%Zi#|%Zi~P%1sO(q%QOj%]i|%]i!P%]i(p%]i!Y%]i#[%]i~Of%]i#|%]i~P%2fOf(Vi!Y(Vi~P!0}O#[4mOf(Vi!Y(Vi~P!0}O!j4pO~O`$kq!Y$kq'r$kq'p$kq!V$kq!j$kqr$kq![$kq%c$kq!d$kq~P!9zO!V4tO~O!Y4uO![(xX~P#.wO!i#xO~P4XO`$uX![$uX%W[X'r$uX!Y$uX~P!/tO%W4wO`kXjkX|kX!PkX![kX'rkX(pkX(qkX!YkX~O%W4wO~Oa4}O%d5OO'{+kO'}TO(QUO!Y'mX!Z'mX~O!Y0xO!Z)Pa~OZ5SO~O_5TO~O`%lO'r%lO~P#.wO![$}O~P#.wO!Y5]O#[5_O!Z(|X~O!Z5`O~Oo!nO!P5aO!_!yO!`!vO!a!vO!{:jO#P!pO#Q!pO#R!pO#S!pO#T!pO#W5fO#X!zO'|!lO'}TO(QUO([!mO(g!sO~O!Z5eO~P%7wOj5kO![1bO%c5jO~Og%WOj5kO![1bO%c5jO~Oa5rO'{#nO'}TO(QUO!Y'lX!Z'lX~O!Y1mO!Z(}a~O'}TO(QUO([5tO~O_5xO~O#r5{O&T5|O~PMnO!j5}O~P%[O`6PO~O`6PO~P%[Oa2TO!Z6UO&c2SO~P`O!d6WO~O!d6YOg(ai!Y(ai!Z(ai!d(ai!k(ai~O!Y#di!Z#di~P#EhO#[6ZO!Y#di!Z#di~O!Y!^i!Z!^i~P#EhO`%lO#[6dO'r%lO~O`%lO!d#vO#[6dO'r%lO~O!Y(kq!j(kq`(kq'r(kq~P!9zO!Y(eO!j(jq~O!P%fO![%gO#f6kO'{%eO~O!['[O%c6nO~Oj6qO!['[O%c6nO~O#g'baP'baZ'ba`'ban'ba}'ba!h'ba!i'ba!k'ba!o'ba#j'ba#k'ba#l'ba#m'ba#n'ba#o'ba#p'ba#q'ba#r'ba#t'ba#v'ba#x'ba#y'ba'r'ba(X'ba(i'ba!j'ba!V'ba'p'bar'ba!['ba%c'ba!d'ba~P%,rO#g'daP'daZ'da`'dan'da}'da!h'da!i'da!k'da!o'da#j'da#k'da#l'da#m'da#n'da#o'da#p'da#q'da#r'da#t'da#v'da#x'da#y'da'r'da(X'da(i'da!j'da!V'da'p'dar'da!['da%c'da!d'da~P%-eO#g$wiP$wiZ$wi`$win$wi}$wi!Y$wi!h$wi!i$wi!k$wi!o$wi#j$wi#k$wi#l$wi#m$wi#n$wi#o$wi#p$wi#q$wi#r$wi#t$wi#v$wi#x$wi#y$wi'r$wi(X$wi(i$wi!j$wi!V$wi'p$wi#[$wir$wi![$wi%c$wi!d$wi~P#.wO#g%ZiP%ZiZ%Zi`%Zin%Zi}%Zi!h%Zi!i%Zi!k%Zi!o%Zi#j%Zi#k%Zi#l%Zi#m%Zi#n%Zi#o%Zi#p%Zi#q%Zi#r%Zi#t%Zi#v%Zi#x%Zi#y%Zi'r%Zi(X%Zi(i%Zi!j%Zi!V%Zi'p%Zir%Zi![%Zi%c%Zi!d%Zi~P%1sO#g%]iP%]iZ%]i`%]in%]i}%]i!h%]i!i%]i!k%]i!o%]i#j%]i#k%]i#l%]i#m%]i#n%]i#o%]i#p%]i#q%]i#r%]i#t%]i#v%]i#x%]i#y%]i'r%]i(X%]i(i%]i!j%]i!V%]i'p%]ir%]i![%]i%c%]i!d%]i~P%2fOf'Ra!Y'Ra~P!0}O!Y'Sa!j'Sa~P!9zO!Y.oO!j(ci~O#|#_i!Y#_i!Z#_i~P#EhOP$^O|#zO}#{O!P#|O!i#xO!k#yO!o$^O(XVOZ#iin#ii!h#ii#k#ii#l#ii#m#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii#|#ii(i#ii(p#ii(q#ii!Y#ii!Z#ii~O#j#ii~P%JwO#j:rO~P%JwOP$^O|#zO}#{O!P#|O!i#xO!k#yO!o$^O#j:rO#k:sO#l:sO#m:sO(XVOZ#ii!h#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii#|#ii(i#ii(p#ii(q#ii!Y#ii!Z#ii~On#ii~P%MSOn:tO~P%MSOP$^On:tO|#zO}#{O!P#|O!i#xO!k#yO!o$^O#j:rO#k:sO#l:sO#m:sO#n:uO(XVO#t#ii#v#ii#x#ii#y#ii#|#ii(i#ii(p#ii(q#ii!Y#ii!Z#ii~OZ#ii!h#ii#o#ii#p#ii#q#ii#r#ii~P& _OZ;QO!h:vO#o:vO#p:vO#q;PO#r:vO~P& _OP$^OZ;QOn:tO|#zO}#{O!P#|O!h:vO!i#xO!k#yO!o$^O#j:rO#k:sO#l:sO#m:sO#n:uO#o:vO#p:vO#q;PO#r:vO#t:wO(XVO(q$OO#x#ii#y#ii#|#ii(i#ii(p#ii!Y#ii!Z#ii~O#v:yO~P&#yO#v#ii~P&#yOP$^OZ;QOn:tO|#zO}#{O!P#|O!h:vO!i#xO!k#yO!o$^O#j:rO#k:sO#l:sO#m:sO#n:uO#o:vO#p:vO#q;PO#r:vO#t:wO(XVO#x#ii#y#ii#|#ii(i#ii!Y#ii!Z#ii~O#v#ii(p#ii(q#ii~P&&UO#v:yO(p#}O(q$OO~P&&UOP$^OZ;QOn:tO|#zO}#{O!P#|O!h:vO!i#xO!k#yO!o$^O#j:rO#k:sO#l:sO#m:sO#n:uO#o:vO#p:vO#q;PO#r:vO#t:wO#v:yO#x:{O(XVO(p#}O(q$OO~O#y#ii#|#ii(i#ii!Y#ii!Z#ii~P&(gO`#zy!Y#zy'r#zy'p#zy!V#zy!j#zyr#zy![#zy%c#zy!d#zy~P!9zOj<oO|){O!P)|O(p%OO(q%QO~OP#iiZ#iin#ii}#ii!h#ii!i#ii!k#ii!o#ii#j#ii#k#ii#l#ii#m#ii#n#ii#o#ii#p#ii#q#ii#r#ii#t#ii#v#ii#x#ii#y#ii#|#ii(X#ii(i#ii!Y#ii!Z#ii~P&+_O!i#xOP(WXZ(WXj(WXn(WX|(WX}(WX!P(WX!h(WX!k(WX!o(WX#j(WX#k(WX#l(WX#m(WX#n(WX#o(WX#p(WX#q(WX#r(WX#t(WX#v(WX#x(WX#y(WX#|(WX(X(WX(i(WX(p(WX(q(WX!Y(WX!Z(WX~O#|#}i!Y#}i!Z#}i~P#EhO#|!qi!Z!qi~P$*}O!Z7TO~O!Y']a!Z']a~P#EhOP[XZ[Xn[X|[X}[X!P[X!V[X!Y[X!h[X!i[X!k[X!o[X#[[X#geX#j[X#k[X#l[X#m[X#n[X#o[X#p[X#q[X#r[X#t[X#v[X#x[X#y[X$O[X(X[X(i[X(p[X(q[X~O!d%TX#r%TX~P&0aO!d#vO(i'lO!Y'^a!j'^a~O!Y/iO!j(vi~O!Y/iO!d#vO!j(vi~Of$wq!Y$wq#[$wq#|$wq~P!0}O!V'`a!Y'`a~P#.wO!d7[O~O!Y/qO!V(wi~P#.wO!Y/qO!V(wi~O!V7`O~O!d#vO#r7eO~On7fO!d#vO(i'lO~O|){O!P)|O(q%QOj'ca(p'ca!Y'ca#['ca~Of'ca#|'ca~P&5PO|){O!P)|Oj'ea(p'ea(q'ea!Y'ea#['ea~Of'ea#|'ea~P&5rO!V7hO~Of$yq!Y$yq#[$yq#|$yq~P!0}O`$ky!Y$ky'r$ky'p$ky!V$ky!j$kyr$ky![$ky%c$ky!d$ky~P!9zO!d6YO~O!Y4uO![(xa~O`#_y!Y#_y'r#_y'p#_y!V#_y!j#_yr#_y![#_y%c#_y!d#_y~P!9zOZ7mO~Oa7oO'{+kO'}TO(QUO~O!Y0xO!Z)Pi~O_7sO~O([(yO!Y'iX!Z'iX~O!Y5]O!Z(|a~O!Z7|O~P%7wOo!nO!P7}O'}TO(QUO([!mO(g!sO~O![1bO~O![1bO%c8PO~Oj8SO![1bO%c8PO~OZ8XO!Y'la!Z'la~O!Y1mO!Z(}i~O!j8]O~O!j8^O~O!j8aO~O!j8aO~P%[O`8cO~O!d8dO~O!j8eO~O!Y(ni!Z(ni~P#EhO`%lO#[8mO'r%lO~O!Y(ky!j(ky`(ky'r(ky~P!9zO!Y(eO!j(jy~O!['[O%c8pO~O#g$wqP$wqZ$wq`$wqn$wq}$wq!Y$wq!h$wq!i$wq!k$wq!o$wq#j$wq#k$wq#l$wq#m$wq#n$wq#o$wq#p$wq#q$wq#r$wq#t$wq#v$wq#x$wq#y$wq'r$wq(X$wq(i$wq!j$wq!V$wq'p$wq#[$wqr$wq![$wq%c$wq!d$wq~P#.wO#g'caP'caZ'ca`'can'ca}'ca!h'ca!i'ca!k'ca!o'ca#j'ca#k'ca#l'ca#m'ca#n'ca#o'ca#p'ca#q'ca#r'ca#t'ca#v'ca#x'ca#y'ca'r'ca(X'ca(i'ca!j'ca!V'ca'p'car'ca!['ca%c'ca!d'ca~P&5PO#g'eaP'eaZ'ea`'ean'ea}'ea!h'ea!i'ea!k'ea!o'ea#j'ea#k'ea#l'ea#m'ea#n'ea#o'ea#p'ea#q'ea#r'ea#t'ea#v'ea#x'ea#y'ea'r'ea(X'ea(i'ea!j'ea!V'ea'p'ear'ea!['ea%c'ea!d'ea~P&5rO#g$yqP$yqZ$yq`$yqn$yq}$yq!Y$yq!h$yq!i$yq!k$yq!o$yq#j$yq#k$yq#l$yq#m$yq#n$yq#o$yq#p$yq#q$yq#r$yq#t$yq#v$yq#x$yq#y$yq'r$yq(X$yq(i$yq!j$yq!V$yq'p$yq#[$yqr$yq![$yq%c$yq!d$yq~P#.wO!Y'Si!j'Si~P!9zO#|#_q!Y#_q!Z#_q~P#EhO(p%OOP%ZaZ%Zan%Za}%Za!h%Za!i%Za!k%Za!o%Za#j%Za#k%Za#l%Za#m%Za#n%Za#o%Za#p%Za#q%Za#r%Za#t%Za#v%Za#x%Za#y%Za#|%Za(X%Za(i%Za!Y%Za!Z%Za~Oj%Za|%Za!P%Za(q%Za~P&F}O(q%QOP%]aZ%]an%]a}%]a!h%]a!i%]a!k%]a!o%]a#j%]a#k%]a#l%]a#m%]a#n%]a#o%]a#p%]a#q%]a#r%]a#t%]a#v%]a#x%]a#y%]a#|%]a(X%]a(i%]a!Y%]a!Z%]a~Oj%]a|%]a!P%]a(p%]a~P&IUOj<oO|){O!P)|O(q%QO~P&F}Oj<oO|){O!P)|O(p%OO~P&IUO|0aO}0aO!P0bOPyaZyajyanya!hya!iya!kya!oya#jya#kya#lya#mya#nya#oya#pya#qya#rya#tya#vya#xya#yya#|ya(Xya(iya(pya(qya!Yya!Zya~O|){O!P)|OP$naZ$naj$nan$na}$na!h$na!i$na!k$na!o$na#j$na#k$na#l$na#m$na#n$na#o$na#p$na#q$na#r$na#t$na#v$na#x$na#y$na#|$na(X$na(i$na(p$na(q$na!Y$na!Z$na~O|){O!P)|OP$paZ$paj$pan$pa}$pa!h$pa!i$pa!k$pa!o$pa#j$pa#k$pa#l$pa#m$pa#n$pa#o$pa#p$pa#q$pa#r$pa#t$pa#v$pa#x$pa#y$pa#|$pa(X$pa(i$pa(p$pa(q$pa!Y$pa!Z$pa~OP%OaZ%Oan%Oa}%Oa!h%Oa!i%Oa!k%Oa!o%Oa#j%Oa#k%Oa#l%Oa#m%Oa#n%Oa#o%Oa#p%Oa#q%Oa#r%Oa#t%Oa#v%Oa#x%Oa#y%Oa#|%Oa(X%Oa(i%Oa!Y%Oa!Z%Oa~P&+_O#|$jq!Y$jq!Z$jq~P#EhO#|$kq!Y$kq!Z$kq~P#EhO!Z8|O~O#|8}O~P!0}O!d#vO!Y'^i!j'^i~O!d#vO(i'lO!Y'^i!j'^i~O!Y/iO!j(vq~O!V'`i!Y'`i~P#.wO!Y/qO!V(wq~O!V9TO~P#.wO!V9TO~Of(Vy!Y(Vy~P!0}O!Y'ga!['ga~P#.wO`%Vq![%Vq'r%Vq!Y%Vq~P#.wOZ9YO~O!Y0xO!Z)Pq~O#[9^O!Y'ia!Z'ia~O!Y5]O!Z(|i~P#EhO![1bO%c9bO~O'}TO(QUO([9gO~O!Y1mO!Z(}q~O!j9jO~O!j9kO~O!j9lO~O!j9lO~P%[O#[9oO!Y#dy!Z#dy~O!Y#dy!Z#dy~P#EhO!['[O%c9tO~O#|#zy!Y#zy!Z#zy~P#EhOP$wiZ$win$wi}$wi!h$wi!i$wi!k$wi!o$wi#j$wi#k$wi#l$wi#m$wi#n$wi#o$wi#p$wi#q$wi#r$wi#t$wi#v$wi#x$wi#y$wi#|$wi(X$wi(i$wi!Y$wi!Z$wi~P&+_O|){O!P)|O(q%QOP'baZ'baj'ban'ba}'ba!h'ba!i'ba!k'ba!o'ba#j'ba#k'ba#l'ba#m'ba#n'ba#o'ba#p'ba#q'ba#r'ba#t'ba#v'ba#x'ba#y'ba#|'ba(X'ba(i'ba(p'ba!Y'ba!Z'ba~O|){O!P)|OP'daZ'daj'dan'da}'da!h'da!i'da!k'da!o'da#j'da#k'da#l'da#m'da#n'da#o'da#p'da#q'da#r'da#t'da#v'da#x'da#y'da#|'da(X'da(i'da(p'da(q'da!Y'da!Z'da~O(p%OOP%ZiZ%Zij%Zin%Zi|%Zi}%Zi!P%Zi!h%Zi!i%Zi!k%Zi!o%Zi#j%Zi#k%Zi#l%Zi#m%Zi#n%Zi#o%Zi#p%Zi#q%Zi#r%Zi#t%Zi#v%Zi#x%Zi#y%Zi#|%Zi(X%Zi(i%Zi(q%Zi!Y%Zi!Z%Zi~O(q%QOP%]iZ%]ij%]in%]i|%]i}%]i!P%]i!h%]i!i%]i!k%]i!o%]i#j%]i#k%]i#l%]i#m%]i#n%]i#o%]i#p%]i#q%]i#r%]i#t%]i#v%]i#x%]i#y%]i#|%]i(X%]i(i%]i(p%]i!Y%]i!Z%]i~O#|$ky!Y$ky!Z$ky~P#EhO#|#_y!Y#_y!Z#_y~P#EhO!d#vO!Y'^q!j'^q~O!Y/iO!j(vy~O!V'`q!Y'`q~P#.wO!V9}O~P#.wO!Y0xO!Z)Py~O!Y5]O!Z(|q~O![1bO%c:UO~O!j:XO~O!['[O%c:^O~OP$wqZ$wqn$wq}$wq!h$wq!i$wq!k$wq!o$wq#j$wq#k$wq#l$wq#m$wq#n$wq#o$wq#p$wq#q$wq#r$wq#t$wq#v$wq#x$wq#y$wq#|$wq(X$wq(i$wq!Y$wq!Z$wq~P&+_O|){O!P)|O(q%QOP'caZ'caj'can'ca}'ca!h'ca!i'ca!k'ca!o'ca#j'ca#k'ca#l'ca#m'ca#n'ca#o'ca#p'ca#q'ca#r'ca#t'ca#v'ca#x'ca#y'ca#|'ca(X'ca(i'ca(p'ca!Y'ca!Z'ca~O|){O!P)|OP'eaZ'eaj'ean'ea}'ea!h'ea!i'ea!k'ea!o'ea#j'ea#k'ea#l'ea#m'ea#n'ea#o'ea#p'ea#q'ea#r'ea#t'ea#v'ea#x'ea#y'ea#|'ea(X'ea(i'ea(p'ea(q'ea!Y'ea!Z'ea~OP$yqZ$yqn$yq}$yq!h$yq!i$yq!k$yq!o$yq#j$yq#k$yq#l$yq#m$yq#n$yq#o$yq#p$yq#q$yq#r$yq#t$yq#v$yq#x$yq#y$yq#|$yq(X$yq(i$yq!Y$yq!Z$yq~P&+_Of%_!Z!Y%_!Z#[%_!Z#|%_!Z~P!0}O!Y'iq!Z'iq~P#EhO!Y#d!Z!Z#d!Z~P#EhO#g%_!ZP%_!ZZ%_!Z`%_!Zn%_!Z}%_!Z!Y%_!Z!h%_!Z!i%_!Z!k%_!Z!o%_!Z#j%_!Z#k%_!Z#l%_!Z#m%_!Z#n%_!Z#o%_!Z#p%_!Z#q%_!Z#r%_!Z#t%_!Z#v%_!Z#x%_!Z#y%_!Z'r%_!Z(X%_!Z(i%_!Z!j%_!Z!V%_!Z'p%_!Z#[%_!Zr%_!Z![%_!Z%c%_!Z!d%_!Z~P#.wOP%_!ZZ%_!Zn%_!Z}%_!Z!h%_!Z!i%_!Z!k%_!Z!o%_!Z#j%_!Z#k%_!Z#l%_!Z#m%_!Z#n%_!Z#o%_!Z#p%_!Z#q%_!Z#r%_!Z#t%_!Z#v%_!Z#x%_!Z#y%_!Z#|%_!Z(X%_!Z(i%_!Z!Y%_!Z!Z%_!Z~P&+_Or(]X~P1qO'|!lO~P!*fO!VeX!YeX#[eX~P&0aOP[XZ[Xn[X|[X}[X!P[X!Y[X!YeX!h[X!i[X!k[X!o[X#[[X#[eX#geX#j[X#k[X#l[X#m[X#n[X#o[X#p[X#q[X#r[X#t[X#v[X#x[X#y[X$O[X(X[X(i[X(p[X(q[X~O!deX!j[X!jeX(ieX~P'EROP:iOQ:iORfOc<dOd!iOlkOn:iOokOpkOvkOx:iOz:iO!PWO!TkO!UkO![XO!f:lO!kZO!n:iO!o:iO!p:iO!r:mO!t:pO!w!hO$T!kO'{)ZO'}TO(QUO(XVO(g[O(u<bO~O!Y:}O!Z$ma~Og%WOl%XOn$uOo$tOp$tOv%YOx%ZOz;XO!P$|O![$}O!f<iO!k$yO#f;_O$T%_O$o;ZO$q;]O$t%`O'{(qO'}TO(QUO(X$vO(p%OO(q%QO~O#s)bO~P'IwOn!bX(i!bX~P# qO!Z[X!ZeX~P'ERO!VeX!V$uX!YeX!Y$uX#[eX~P!/tO#g:qO~O!d#vO#g:qO~O#[;RO~O#r:vO~O#[;bO!Y(nX!Z(nX~O#[;RO!Y(lX!Z(lX~O#g;cO~Of;eO~P!0}O#g;kO~O#g;lO~O!d#vO#g;mO~O!d#vO#g;cO~O#|;nO~P#EhO#g;oO~O#g;pO~O#g;uO~O#g;vO~O#g;wO~O#g;xO~O#|;yO~P!0}O#|;zO~P!0}O!i#P#Q#S#T#W#e#f#q(u$o$q$t%W%b%c%d%k%m%p%q%s%u~'vS#k!U't'|#lo#j#mn|'u$Y'u'{$[([~",goto:"$4`)TPPPPP)UPP)XP)jP*z/PPPPP5wPP6_PP<U?kP@OP@OPPP@OPBOP@OP@OP@OPBSPPBXPBsPGlPPPGpPPPPGpJrPPPJxKtPGpPNSPPPP!!bGpPPPGpPGpP!$pGpP!(V!)X!)bP!*U!*Y!*UPPPPP!-f!)XPP!-v!.pP!1dGpGp!1i!4t!9[!9[!=YPPP!=bGpPPPPPPPPPPP!@pP!A}PPGp!C`PGpPGpGpGpGpPGp!DrP!G{P!KQP!KU!K`!Kd!KdP!GxP!Kh!KhP!NmP!NqGpGp!Nw##{@OP@OP@O@OP#%X@O@O#'c@O#*R@O#,V@O@O#,u#/R#/R#/W#/a#/R#/jP#/RP@O#0S@O#3s@O@O5wPPP#7jPPP#8T#8TP#8TP#8k#8TPP#8qP#8hP#8h#9U#8h#9p#9v5t)X#9y)XP#:Q#:Q#:QP)XP)XP)XP)XPP)XP#:W#:ZP#:Z)XP#:_P#:bP)XP)XP)XP)XP)XP)X)XPP#:h#:n#:y#;P#;V#;]#;c#;q#;w#;}#<X#<_#<i#<y#=P#=q#>T#>Z#>a#>o#?U#@s#AR#AY#Bn#B|#Dh#Dv#D|#ES#EY#Ed#Ej#Ep#Ez#F^#FdPPPPPPPPPP#FjPPPPPPP#G_#Jf#Ku#K|#LUPPPP$#[$&S$,l$,o$,r$-_$-b$-e$-l$-tP$-zP$.h$.l$/d$0r$0w$1_PP$1d$1j$1nP$1q$1u$1y$2o$3W$3o$3s$3v$3y$4P$4S$4W$4[R!|RoqOXst!Z#d%k&n&p&q&s,k,p1}2QY!vQ'[-]1b5dQ%qvQ%yyQ&Q|Q&f!VS'S!e-TQ'b!iS'h!r!yU*f$}*W*kQ+i%zQ+v&SQ,[&`Q-Z'ZQ-e'cQ-m'iQ0S*mQ1l,]R;`:m%QdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$b$f%k%q&O&g&j&n&p&q&s&w'P'^'n(O(Q(W(_(s(w({)z+R+V,h,k,p-a-i-w-}.o.v/g0b0g0w1e1u1v1x1z1}2Q2S2s2y3a5a5k5{5|6P6d7}8S8c8mS#q]:j!r)]$]$n'T)o,|-P/O2c3v5_6Z9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eQ*x%[Q+n%|Q,^&cQ,e&kQ.f;WQ0n+aQ0r+cQ0}+oQ1t,cQ3R._Q4}0xQ5r1mQ6p3VQ6|;XQ7o5OR8s6q'OkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$]$b$f$n%k%q&O&g&j&k&n&p&q&s&w'P'T'^'n(O(Q(W(_(s(w({)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.o.v/O/g0b0g0w1e1u1v1x1z1}2Q2S2c2s2y3V3a3v5_5a5k5{5|6P6Z6d6q7}8S8c8m9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<et!nQ!r!v!y!z'S'Z'['h'i'j-T-Z-]-m1b5d5f$z$ti#v#x$d$e$y$|%P%R%]%^%b)v)|*O*Q*S*V*]*c*s*t+`+c+z+}.^.l/^/f/p/q/s0W0Y0d1X1[1d3U4O4Z4c4m4u4w5j6n7[7e8P8p8}9b9t:U:^;P;Q;S;T;U;V;Y;Z;[;];^;_;f;g;h;i;k;l;o;p;q;r;s;t;u;v;y;z<b<j<k<n<oQ&T|Q'Q!eU'W%g*W-WQ+n%|Q,^&cQ0c*|Q0}+oQ1S+uQ1s,bQ1t,cQ4}0xQ5W1UQ5r1mQ5u1oQ5v1rQ7o5OQ7r5TQ8[5xQ9]7sR9h8XrnOXst!V!Z#d%k&e&n&p&q&s,k,p1}2QR,`&g&x^OPXYstuvwz!Z!`!g!j!o#S#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$]$b$f$n%k%q&O&g&j&k&n&p&q&s&w'P'^'n(Q(W(_(s(w({)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.o.v/O/g0b0g0w1e1u1v1x1z1}2Q2S2c2s2y3V3a3v5_5a5k5{5|6P6Z6d6q7}8S8c8m9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<d<e[#]WZ#W#Z'T(O!b%hm#h#i#l$y%c%f(X(c(d(e*V*Z*^+T+U+W,g,}-{.R.S.T.V/f/i2V2}3O4R6Y6kQ%txQ%xyS%}|&SQ&Z!TQ'_!hQ'a!iQ(l#sS+h%y%zQ+l%|Q,V&^Q,Z&`S-d'b'cQ.a(mQ0v+iQ0|+oQ1O+pQ1R+tQ1g,WS1k,[,]Q2o-eQ4|0xQ5Q0{Q5V1TQ5q1lQ7n5OQ7q5SQ9X7mR:P9Y!O${i#x%P%R%]%^%b*O*Q*]*s*t.l/p0W0Y0d4O4m8}<b<j<k!S%vy!i!u%x%y%z'R'a'b'c'g'q*e+h+i-Q-d-e-l/z0v2h2o2v4eQ+b%tQ+{&WQ,O&XQ,Y&`Q.`(lQ1f,VU1j,Z,[,]Q3W.aQ5l1gS5p1k1lQ8W5q#[<f#v$d$e$y$|)v)|*S*V*c+`+c+z+}.^/^/f/q/s1X1[1d3U4Z4c4u4w5j6n7[7e8P8p9b9t:U:^;S;U;Y;[;^;f;h;k;o;q;s;u;y<n<oo<g;P;Q;T;V;Z;];_;g;i;l;p;r;t;v;zW%Ui%W*u<bS&W!Q&eQ&X!RQ&Y!SR+y&U${%Ti#v#x$d$e$y$|%P%R%]%^%b)v)|*O*Q*S*V*]*c*s*t+`+c+z+}.^.l/^/f/p/q/s0W0Y0d1X1[1d3U4O4Z4c4m4u4w5j6n7[7e8P8p8}9b9t:U:^;P;Q;S;T;U;V;Y;Z;[;];^;_;f;g;h;i;k;l;o;p;q;r;s;t;u;v;y;z<b<j<k<n<oT)w$v)xV*y%[;W;XW'W!e%g*W-WS(z#z#{Q+s&PS.Y(h(iQ1],PQ4n0aR7w5]'OkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$]$b$f$n%k%q&O&g&j&k&n&p&q&s&w'P'T'^'n(O(Q(W(_(s(w({)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.o.v/O/g0b0g0w1e1u1v1x1z1}2Q2S2c2s2y3V3a3v5_5a5k5{5|6P6Z6d6q7}8S8c8m9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<e$i$ac#Y#e%o%p%r'}(T(o(v)O)P)Q)R)S)T)U)V)W)X)Y)[)_)c)m+^+r-R-p-u-z-|.k.q.u.w.x.y/Y0e2^2a2q2x3`3e3f3g3h3i3j3k3l3m3n3o3p3q3t3u3z4r4z6]6c6h6v6w7Q7R7y8g8k8t8z8{9q:R:Y:k<XT#TV#U'PkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$]$b$f$n%k%q&O&g&j&k&n&p&q&s&w'P'T'^'n(O(Q(W(_(s(w({)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.o.v/O/g0b0g0w1e1u1v1x1z1}2Q2S2c2s2y3V3a3v5_5a5k5{5|6P6Z6d6q7}8S8c8m9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eQ'U!eR2d-Tv!nQ!e!r!v!y!z'S'Z'['h'i'j-T-Z-]-m1b5d5fU*e$}*W*kS/z*f*mQ0T*nQ1_,RQ4e0SR4h0VnqOXst!Z#d%k&n&p&q&s,k,p1}2QQ&u!^Q'r!xS(n#u:qQ+f%wQ,T&ZQ,U&]Q-b'`Q-o'kS.j(s;cS0f+R;mQ0t+gQ1a,SQ2U,rQ2W,sQ2`-OQ2m-cQ2p-gS4s0g;wQ4x0uS4{0w;xQ6[2bQ6`2nQ6e2uQ7l4yQ8h6^Q8i6aQ8l6fR9n8e$d$`c#Y#e%p%r'}(T(o(v)O)P)Q)R)S)T)U)V)W)X)Y)[)_)c)m+^+r-R-p-u-z-|.k.q.u.x.y/Y0e2^2a2q2x3`3e3f3g3h3i3j3k3l3m3n3o3p3q3t3u3z4r4z6]6c6h6v6w7Q7R7y8g8k8t8z8{9q:R:Y:k<XS(k#p'eU*r%S(r3sS+]%o.wQ3S0nQ6m3RQ8r6pR9u8s$d$_c#Y#e%p%r'}(T(o(v)O)P)Q)R)S)T)U)V)W)X)Y)[)_)c)m+^+r-R-p-u-z-|.k.q.u.x.y/Y0e2^2a2q2x3`3e3f3g3h3i3j3k3l3m3n3o3p3q3t3u3z4r4z6]6c6h6v6w7Q7R7y8g8k8t8z8{9q:R:Y:k<XS(j#p'eS(|#{$`S+[%o.wS.Z(i(kQ.z)^Q0k+]R3P.['OkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$]$b$f$n%k%q&O&g&j&k&n&p&q&s&w'P'T'^'n(O(Q(W(_(s(w({)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.o.v/O/g0b0g0w1e1u1v1x1z1}2Q2S2c2s2y3V3a3v5_5a5k5{5|6P6Z6d6q7}8S8c8m9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eS#q]:jQ&p!XQ&q!YQ&s![Q&t!]R1|,nQ']!hQ+_%tQ-`'_S.](l+bQ2k-_W3T.`.a0m0oQ6_2lU6l3Q3S3WS8o6m6oS9s8q8rS:[9r9uQ:d:]R:g:eU!wQ'[-]T5b1b5d!Q_OXZ`st!V!Z#d#h%c%k&e&g&n&p&q&s(e,k,p.S1}2Q]!pQ!r'[-]1b5dT#q]:j%[{OPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$b$f%k%q&O&g&j&k&n&p&q&s&w'P'^'n(O(Q(W(_(s(w({)z+R+V+a,h,k,p-a-i-w-}._.o.v/g0b0g0w1e1u1v1x1z1}2Q2S2s2y3V3a5a5k5{5|6P6d6q7}8S8c8mS(z#z#{S.Y(h(i!s<O$]$n'T)o,|-P/O2c3v5_6Z9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<em!tQ!r!v!y!z'['h'i'j-]-m1b5d5fQ'p!uS(a#g1wS-k'g'sQ/l*YQ/x*eQ2w-nQ4V/mS4`/y0TQ7W4QS7c4f4hQ9P7XR9W7fQ#wbQ'o!uS(`#g1wS(b#m+QQ+S%dQ+d%uQ+j%{U-j'g'p'sQ.O(aQ/k*YQ/w*eQ/}*hQ0s+eQ1h,XS2t-k-nQ2|.WS4U/l/mS4_/x0TQ4b/|Q4d0OQ5n1iQ6g2wQ7V4QQ7Z4VS7_4`4hQ7d4gQ8U5oS9O7W7XQ9S7`Q9U7cQ9e8VQ9{9PQ9|9TQ:O9WQ:W9fQ:`9}Q<R;|Q<^<VR<_<WV!wQ'[-]%[aOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$b$f%k%q&O&g&j&k&n&p&q&s&w'P'^'n(O(Q(W(_(s(w({)z+R+V+a,h,k,p-a-i-w-}._.o.v/g0b0g0w1e1u1v1x1z1}2Q2S2s2y3V3a5a5k5{5|6P6d6q7}8S8c8mS#wz!j!r;{$]$n'T)o,|-P/O2c3v5_6Z9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eR<R<d%[bOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$b$f%k%q&O&g&j&k&n&p&q&s&w'P'^'n(O(Q(W(_(s(w({)z+R+V+a,h,k,p-a-i-w-}._.o.v/g0b0g0w1e1u1v1x1z1}2Q2S2s2y3V3a5a5k5{5|6P6d6q7}8S8c8mQ%dj!S%uy!i!u%x%y%z'R'a'b'c'g'q*e+h+i-Q-d-e-l/z0v2h2o2v4eS%{z!jQ+e%vQ,X&`W1i,Y,Z,[,]U5o1j1k1lS8V5p5qQ9f8W!r;|$]$n'T)o,|-P/O2c3v5_6Z9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eQ<V<cR<W<d%OeOPXYstuvw!Z!`!g!o#S#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$b$f%k%q&O&g&j&n&p&q&s&w'P'^'n(Q(W(_(s(w({)z+R+V+a,h,k,p-a-i-w-}._.o.v/g0b0g0w1e1u1v1x1z1}2Q2S2s2y3V3a5a5k5{5|6P6d6q7}8S8c8mY#bWZ#W#Z(O!b%hm#h#i#l$y%c%f(X(c(d(e*V*Z*^+T+U+W,g,}-{.R.S.T.V/f/i2V2}3O4R6Y6kQ,f&k!p;}$]$n)o,|-P/O2c3v5_6Z9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eR<Q'TU'X!e%g*WR2f-W%QdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$b$f%k%q&O&g&j&n&p&q&s&w'P'^'n(O(Q(W(_(s(w({)z+R+V,h,k,p-a-i-w-}.o.v/g0b0g0w1e1u1v1x1z1}2Q2S2s2y3a5a5k5{5|6P6d7}8S8c8m!r)]$]$n'T)o,|-P/O2c3v5_6Z9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eQ,e&kQ0n+aQ3R._Q6p3VR8s6q!b$Vc#Y%o'}(T(o(v)X)Y)_)c+r-p-u-z-|.k.q/Y0e2q2x3`3q4r4z6c6h6v8k9q:k!P:x)[)m-R.w2^2a3e3o3p3t3z6]6w7Q7R7y8g8t8z8{:R:Y<X!f$Xc#Y%o'}(T(o(v)U)V)X)Y)_)c+r-p-u-z-|.k.q/Y0e2q2x3`3q4r4z6c6h6v8k9q:k!T:z)[)m-R.w2^2a3e3l3m3o3p3t3z6]6w7Q7R7y8g8t8z8{:R:Y<X!^$]c#Y%o'}(T(o(v)_)c+r-p-u-z-|.k.q/Y0e2q2x3`3q4r4z6c6h6v8k9q:kQ4O/dz<e)[)m-R.w2^2a3e3t3z6]6w7Q7R7y8g8t8z8{:R:Y<XQ<j<lR<k<m'OkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$]$b$f$n%k%q&O&g&j&k&n&p&q&s&w'P'T'^'n(O(Q(W(_(s(w({)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.o.v/O/g0b0g0w1e1u1v1x1z1}2Q2S2c2s2y3V3a3v5_5a5k5{5|6P6Z6d6q7}8S8c8m9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eS$oh$pR3w.}'VgOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$]$b$f$n$p%k%q&O&g&j&k&n&p&q&s&w'P'T'^'n(O(Q(W(_(s(w({)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.o.v.}/O/g0b0g0w1e1u1v1x1z1}2Q2S2c2s2y3V3a3v5_5a5k5{5|6P6Z6d6q7}8S8c8m9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eT$kf$qQ$ifS)f$l)jR)r$qT$jf$qT)h$l)j'VhOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$]$b$f$n$p%k%q&O&g&j&k&n&p&q&s&w'P'T'^'n(O(Q(W(_(s(w({)o)z+R+V+a,h,k,p,|-P-a-i-w-}._.o.v.}/O/g0b0g0w1e1u1v1x1z1}2Q2S2c2s2y3V3a3v5_5a5k5{5|6P6Z6d6q7}8S8c8m9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<eT$oh$pQ$rhR)q$p%[jOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#y#|$P$Q$R$S$T$U$V$W$X$Y$Z$b$f%k%q&O&g&j&k&n&p&q&s&w'P'^'n(O(Q(W(_(s(w({)z+R+V+a,h,k,p-a-i-w-}._.o.v/g0b0g0w1e1u1v1x1z1}2Q2S2s2y3V3a5a5k5{5|6P6d6q7}8S8c8m!s<c$]$n'T)o,|-P/O2c3v5_6Z9^9o:i:l:m:p:q:r:s:t:u:v:w:x:y:z:{:|:};R;`;b;c;e;m;n;w;x<e#elOPXZst!Z!`!o#S#d#o#|$n%k&g&j&k&n&p&q&s&w'P'^({)o+V+a,h,k,p-a._/O/g0b1e1u1v1x1z1}2Q2S3V3v5a5k5{5|6P6q7}8S8c!O%Si#x%P%R%]%^%b*O*Q*]*s*t.l/p0W0Y0d4O4m8}<b<j<k#[(r#v$d$e$y$|)v)|*S*V*c+`+c+z+}.^/^/f/q/s1X1[1d3U4Z4c4u4w5j6n7[7e8P8p9b9t:U:^;S;U;Y;[;^;f;h;k;o;q;s;u;y<n<oQ*}%`Q/Z){o3s;P;Q;T;V;Z;];_;g;i;l;p;r;t;v;z!O$zi#x%P%R%]%^%b*O*Q*]*s*t.l/p0W0Y0d4O4m8}<b<j<kQ*_${U*h$}*W*kQ+O%aQ0O*i#[<T#v$d$e$y$|)v)|*S*V*c+`+c+z+}.^/^/f/q/s1X1[1d3U4Z4c4u4w5j6n7[7e8P8p9b9t:U:^;S;U;Y;[;^;f;h;k;o;q;s;u;y<n<on<U;P;Q;T;V;Z;];_;g;i;l;p;r;t;v;zQ<Y<fQ<Z<gQ<[<hR<]<i!O%Si#x%P%R%]%^%b*O*Q*]*s*t.l/p0W0Y0d4O4m8}<b<j<k#[(r#v$d$e$y$|)v)|*S*V*c+`+c+z+}.^/^/f/q/s1X1[1d3U4Z4c4u4w5j6n7[7e8P8p9b9t:U:^;S;U;Y;[;^;f;h;k;o;q;s;u;y<n<oo3s;P;Q;T;V;Z;];_;g;i;l;p;r;t;v;znoOXst!Z#d%k&n&p&q&s,k,p1}2QS*b$|*VQ,y&zQ,z&|R4Y/q$z%Ti#v#x$d$e$y$|%P%R%]%^%b)v)|*O*Q*S*V*]*c*s*t+`+c+z+}.^.l/^/f/p/q/s0W0Y0d1X1[1d3U4O4Z4c4m4u4w5j6n7[7e8P8p8}9b9t:U:^;P;Q;S;T;U;V;Y;Z;[;];^;_;f;g;h;i;k;l;o;p;q;r;s;t;u;v;y;z<b<j<k<n<oQ+|&XQ1Z,OQ5Z1YR7v5[V*j$}*W*kU*j$}*W*kT5c1b5dU/|*g/g5aT4g0U7}Q+d%uQ/}*hQ0s+eQ1h,XQ5n1iQ8U5oQ9e8VR:W9f!O%Pi#x%P%R%]%^%b*O*Q*]*s*t.l/p0W0Y0d4O4m8}<b<j<kr*O$w(t*P*q+P/o0[0]3^4W4q7U7g9z<S<`<aS0W*p0X#[;S#v$d$e$y$|)v)|*S*V*c+`+c+z+}.^/^/f/q/s1X1[1d3U4Z4c4u4w5j6n7[7e8P8p9b9t:U:^;S;U;Y;[;^;f;h;k;o;q;s;u;y<n<on;T;P;Q;T;V;Z;];_;g;i;l;p;r;t;v;z!^;f(p)a*X*a.b.e.i/V/[/d/t0l1W1Y3Z4X4]5Y5[6r6u7]7a7i7k9R9V:_<l<m`;g3r6x6{7P8u9v9y:hS;q.d3[T;r6z8x!O%Ri#x%P%R%]%^%b*O*Q*]*s*t.l/p0W0Y0d4O4m8}<b<j<kv*Q$w(t*R*p+P/`/o0[0]3^4W4i4q7U7g9z<S<`<aS0Y*q0Z#[;U#v$d$e$y$|)v)|*S*V*c+`+c+z+}.^/^/f/q/s1X1[1d3U4Z4c4u4w5j6n7[7e8P8p9b9t:U:^;S;U;Y;[;^;f;h;k;o;q;s;u;y<n<on;V;P;Q;T;V;Z;];_;g;i;l;p;r;t;v;z!b;h(p)a*X*a.c.d.i/V/[/d/t0l1W1Y3X3Z4X4]5Y5[6r6s6u7]7a7i7k9R9V:_<l<md;i3r6y6z7P8u8v9v9w9y:hS;s.e3]T;t6{8yrnOXst!V!Z#d%k&e&n&p&q&s,k,p1}2QQ&b!UR,h&krnOXst!V!Z#d%k&e&n&p&q&s,k,p1}2QR&b!UQ,Q&YR1V+ysnOXst!V!Z#d%k&e&n&p&q&s,k,p1}2QQ1c,VS5i1f1gU8O5g5h5lS9a8Q8RS:S9`9cQ:a:TR:f:bQ&i!VR,a&eR5u1oS%}|&SR1O+pQ&n!WR,k&oR,q&tT2O,p2QR,u&uQ,t&uR2X,uQ'u!{R-q'uSsOtQ#dXT%ns#dQ#OTR'w#OQ#RUR'y#RQ)x$vR/W)xQ#UVR'{#UQ#XWU(R#X(S-xQ(S#YR-x(TQ-U'UR2e-UQ.m(tR3_.mQ.p(vS3b.p3cR3c.qQ-]'[R2i-]Y!rQ'[-]1b5dR'f!rU#_W%f*VU(Y#_(Z-yQ(Z#`R-y(UQ-X'XR2g-Xt`OXst!V!Z#d%k&e&g&n&p&q&s,k,p1}2QS#hZ%cU#r`#h.SR.S(eQ(f#jQ.P(bW.X(f.P2z6iQ2z.QR6i2{Q)j$lR/P)jQ$phR)p$pQ$ccU)`$c-t;OQ-t:kR;O)mQ/j*YW4S/j4T7Y9QU4T/k/l/mS7Y4U4VR9Q7Z$X)}$w(p(t)a*X*a*p*q*z*{+P.d.e.g.h.i/V/[/`/b/d/o/t0[0]0l1W1Y3X3Y3Z3^3r4W4X4]4i4k4q5Y5[6r6s6t6u6z6{6}7O7P7U7]7a7g7i7k8u8v8w9R9V9v9w9x9y9z:_:h<S<`<a<l<mQ/r*aU4[/r4^7^Q4^/tR7^4]S*k$}*WR0Q*kr*P$w(t*p*q+P/o0[0]3^4W4q7U7g9z<S<`<a!^.b(p)a*X*a.d.e.i/V/[/d/t0l1W1Y3Z4X4]5Y5[6r6u7]7a7i7k9R9V:_<l<mU/a*P.b6xa6x3r6z6{7P8u9v9y:hQ0X*pQ3[.dU4j0X3[8xR8x6zv*R$w(t*p*q+P/`/o0[0]3^4W4i4q7U7g9z<S<`<a!b.c(p)a*X*a.d.e.i/V/[/d/t0l1W1Y3X3Z4X4]5Y5[6r6s6u7]7a7i7k9R9V:_<l<mU/c*R.c6ye6y3r6z6{7P8u8v9v9w9y:hQ0Z*qQ3].eU4l0Z3]8yR8y6{Q*v%VR0_*vQ4v0lR7j4vQ+X%iR0j+XQ5^1]S7x5^9_R9_7yQ,S&ZR1`,SQ5d1bR7{5dQ1n,^S5s1n8YR8Y5uQ0y+lW5P0y5R7p9ZQ5R0|Q7p5QR9Z7qQ+q%}R1P+qQ2Q,pR6T2QYrOXst#dQ&r!ZQ+Z%kQ,j&nQ,l&pQ,m&qQ,o&sQ1{,kS2O,p2QR6S1}Q%mpQ&v!_Q&y!aQ&{!bQ&}!cQ'm!uQ+Y%jQ+f%wQ+x&TQ,`&iQ,w&xW-h'g'o'p'sQ-o'kQ0P*jQ0t+gS1q,a,dQ2Y,vQ2Z,yQ2[,zQ2p-gW2r-j-k-n-pQ4x0uQ5U1SQ5X1WQ5m1hQ5w1sQ6R1|U6b2q2t2wQ6e2uQ7l4yQ7t5WQ7u5YQ7z5cQ8T5nQ8Z5vS8j6c6gQ8l6fQ9[7rQ9d8UQ9i8[Q9p8kQ:Q9]Q:V9eQ:Z9qR:c:WQ%wyQ'`!iQ'k!uU+g%x%y%zQ-O'RU-c'a'b'cS-g'g'qQ/v*eS0u+h+iQ2b-QS2n-d-eQ2u-lQ4a/zQ4y0vQ6^2hQ6a2oQ6f2vR7b4eS$xi<bR*w%WU%Vi%W<bR0^*uQ$wiS(p#v+cQ(t#xS)a$d$eQ*X$yS*a$|*VQ*p%PQ*q%RQ*z%]Q*{%^Q+P%bQ.d;SQ.e;UQ.g;YQ.h;[Q.i;^Q/V)vS/[)|/^Q/`*OQ/b*QQ/d*SQ/o*]S/t*c/fQ0[*sQ0]*th0l+`.^1d3U5j6n8P8p9b9t:U:^Q1W+zQ1Y+}Q3X;fQ3Y;hQ3Z;kQ3^.lS3r;P;QQ4W/pQ4X/qQ4]/sQ4i0WQ4k0YQ4q0dQ5Y1XQ5[1[Q6r;oQ6s;qQ6t;sQ6u;uQ6z;TQ6{;VQ6};ZQ7O;]Q7P;_Q7U4OQ7]4ZQ7a4cQ7g4mQ7i4uQ7k4wQ8u;lQ8v;gQ8w;iQ9R7[Q9V7eQ9v;pQ9w;rQ9x;tQ9y;vQ9z8}Q:_;yQ:h;zQ<S<bQ<`<jQ<a<kQ<l<nR<m<onpOXst!Z#d%k&n&p&q&s,k,p1}2QQ!fPS#fZ#oQ&x!`U'd!o5a7}Q'z#SQ(}#|Q)n$nS,d&g&jQ,i&kQ,v&wQ,{'PQ-_'^Q.s({Q/T)oS0h+V/gQ0o+aQ1y,hQ2l-aQ3S._Q3y/OQ4o0bQ5h1eQ5y1uQ5z1vQ6O1xQ6Q1zQ6V2SQ6m3VQ7S3vQ8R5kQ8_5{Q8`5|Q8b6PQ8r6qQ9c8SR9m8c#YcOPXZst!Z!`!o#d#o#|%k&g&j&k&n&p&q&s&w'P'^({+V+a,h,k,p-a._/g0b1e1u1v1x1z1}2Q2S3V5a5k5{5|6P6q7}8S8cQ#YWQ#eYQ%ouQ%pvS%rw!gS'}#W(QQ(T#ZQ(o#uQ(v#yQ)O$PQ)P$QQ)Q$RQ)R$SQ)S$TQ)T$UQ)U$VQ)V$WQ)W$XQ)X$YQ)Y$ZQ)[$]Q)_$bQ)c$fW)m$n)o/O3vQ+^%qQ+r&OS-R'T2cQ-p'nS-u(O-wQ-z(WQ-|(_Q.k(sQ.q(wQ.u:iQ.w:lQ.x:mQ.y:pQ/Y)zQ0e+RQ2^,|Q2a-PQ2q-iQ2x-}Q3`.oQ3e:qQ3f:rQ3g:sQ3h:tQ3i:uQ3j:vQ3k:wQ3l:xQ3m:yQ3n:zQ3o:{Q3p:|Q3q.vQ3t;RQ3u;`Q3z:}Q4r0gQ4z0wQ6];bQ6c2sQ6h2yQ6v3aQ6w;cQ7Q;eQ7R;mQ7y5_Q8g6ZQ8k6dQ8t;nQ8z;wQ8{;xQ9q8mQ:R9^Q:Y9oQ:k#SR<X<eR#[WR'V!el!tQ!r!v!y!z'['h'i'j-]-m1b5d5fS'R!e-TS-Q'S'ZR2h-ZR(u#xR(x#yQ!fQT-['[-]]!qQ!r'[-]1b5dQ#p]R'e:jY!uQ'[-]1b5dQ'g!rS'q!v!yS's!z5fS-l'h'iQ-n'jR2v-mT#kZ%cS#jZ%cS%im,gU(b#h#i#lS.Q(c(dQ.U(eQ0i+WQ2{.RU2|.S.T.VS6j2}3OR8n6kd#^W#W#Z%f(O(X*V+T-{/fr#gZm#h#i#l%c(c(d(e+W.R.S.T.V2}3O6kS*Y$y*^Q/m*ZQ1w,gQ2_,}Q4Q/iQ6X2VQ7X4RQ8f6YT<P'T+UV#aW%f*VU#`W%f*VS(P#W(XU(U#Z+T/fS-S'T+UT-v(O-{V'Y!e%g*WQ$lfR)t$qT)i$l)jR3x.}T*[$y*^T*d$|*VQ0m+`Q3Q.^Q5g1dQ6o3UQ8Q5jQ8q6nQ9`8PQ9r8pQ:T9bQ:]9tQ:b:UR:e:^nqOXst!Z#d%k&n&p&q&s,k,p1}2QQ&h!VR,`&etmOXst!U!V!Z#d%k&e&n&p&q&s,k,p1}2QR,g&kT%jm,gR1^,PR,_&cQ&R|R+w&SR+m%|T&l!W&oT&m!W&oT2P,p2Q",nodeNames:"⚠ ArithOp ArithOp JSXStartTag LineComment BlockComment Script Hashbang ExportDeclaration export Star as VariableName String Escape from ; default FunctionDeclaration async function VariableDefinition > < TypeParamList TypeDefinition extends ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation InterpolationStart NullType null VoidType void TypeofType typeof MemberExpression . ?. PropertyName [ TemplateString Escape Interpolation super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewExpression new TypeArgList CompareOp < ) ( ArgList UnaryExpression delete LogicOp BitOp YieldExpression yield AwaitExpression await ParenthesizedExpression ClassExpression class ClassBody MethodDeclaration Decorator @ MemberExpression PrivatePropertyName CallExpression declare Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly accessor Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof satisfies in const CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXSelfClosingTag JSXIdentifier JSXBuiltin JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast ArrowFunction TypeParamList SequenceExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature PropertyDefinition CallSignature TypePredicate is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var using TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try CatchClause catch FinallyClause finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement SingleExpression SingleClassItem",maxTerm:372,context:VO,nodeProps:[["isolate",-8,4,5,13,33,35,48,50,52,""],["group",-26,8,16,18,65,201,205,209,210,212,215,218,228,230,236,238,240,242,245,251,257,259,261,263,265,267,268,"Statement",-32,12,13,28,31,32,38,48,51,52,54,59,67,75,79,81,83,84,106,107,116,117,134,137,139,140,141,142,144,145,164,165,167,"Expression",-23,27,29,33,37,39,41,168,170,172,173,175,176,177,179,180,181,183,184,185,195,197,199,200,"Type",-3,87,99,105,"ClassItem"],["openedBy",22,"<",34,"InterpolationStart",53,"[",57,"{",72,"(",157,"JSXStartCloseTag"],["closedBy",23,">",36,"InterpolationEnd",47,"]",58,"}",73,")",162,"JSXEndTag"]],propSources:[NO],skippedNodes:[0,4,5,271],repeatNodeCount:37,tokenData:"$HR(CSR!bOX%ZXY+gYZ-yZ[+g[]%Z]^.c^p%Zpq+gqr/mrs3cst:_tuEruvJSvwLkwx! Yxy!'iyz!(sz{!)}{|!,q|}!.O}!O!,q!O!P!/Y!P!Q!9j!Q!R#:O!R![#<_![!]#I_!]!^#Jk!^!_#Ku!_!`$![!`!a$$v!a!b$*T!b!c$.S!c!}Er!}#O$/^#O#P$0h#P#Q$6P#Q#R$7Z#R#SEr#S#T$8h#T#o$9r#o#p$>S#p#q$>x#q#r$@Y#r#s$Af#s$f%Z$f$g+g$g#BYEr#BY#BZ$Bp#BZ$ISEr$IS$I_$Bp$I_$I|Er$I|$I}$E{$I}$JO$E{$JO$JTEr$JT$JU$Bp$JU$KVEr$KV$KW$Bp$KW&FUEr&FU&FV$Bp&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$Bp?HUOEr(n%d_$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&j&hT$f&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c&j&zP;=`<%l&c'|'U]$f&j(R!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!b(SU(R!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!b(iP;=`<%l'}'|(oP;=`<%l&}'[(y]$f&j(OpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rp)wU(OpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)rp*^P;=`<%l)r'[*dP;=`<%l(r#S*nX(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g#S+^P;=`<%l*g(n+dP;=`<%l%Z(CS+rq$f&j(Op(R!b't(;dOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p$f%Z$f$g+g$g#BY%Z#BY#BZ+g#BZ$IS%Z$IS$I_+g$I_$JT%Z$JT$JU+g$JU$KV%Z$KV$KW+g$KW&FU%Z&FU&FV+g&FV;'S%Z;'S;=`+a<%l?HT%Z?HT?HU+g?HUO%Z(CS.ST(P#S$f&j'u(;dO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c(CS.n_$f&j(Op(R!b'u(;dOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#`/x`$f&j!o$Ip(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S1V`#t$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`2X!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S2d_#t$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/|3l_'}$(n$f&j(R!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k(^4r_$f&j(R!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k&z5vX$f&jOr5qrs6cs!^5q!^!_6y!_#o5q#o#p6y#p;'S5q;'S;=`7h<%lO5q&z6jT$a`$f&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c`6|TOr6yrs7]s;'S6y;'S;=`7b<%lO6y`7bO$a``7eP;=`<%l6y&z7kP;=`<%l5q(^7w]$a`$f&j(R!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!r8uZ(R!bOY8pYZ6yZr8prs9hsw8pwx6yx#O8p#O#P6y#P;'S8p;'S;=`:R<%lO8p!r9oU$a`(R!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!r:UP;=`<%l8p(^:[P;=`<%l4k#%|:hh$f&j(Op(R!bOY%ZYZ&cZq%Zqr<Srs&}st%ZtuCruw%Zwx(rx!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr(r<__VS$f&j(Op(R!bOY<SYZ&cZr<Srs=^sw<Swx@nx!^<S!^!_Bm!_#O<S#O#P>`#P#o<S#o#pBm#p;'S<S;'S;=`Cl<%lO<S(Q=g]VS$f&j(R!bOY=^YZ&cZw=^wx>`x!^=^!^!_?q!_#O=^#O#P>`#P#o=^#o#p?q#p;'S=^;'S;=`@h<%lO=^&n>gXVS$f&jOY>`YZ&cZ!^>`!^!_?S!_#o>`#o#p?S#p;'S>`;'S;=`?k<%lO>`S?XSVSOY?SZ;'S?S;'S;=`?e<%lO?SS?hP;=`<%l?S&n?nP;=`<%l>`!f?xWVS(R!bOY?qZw?qwx?Sx#O?q#O#P?S#P;'S?q;'S;=`@b<%lO?q!f@eP;=`<%l?q(Q@kP;=`<%l=^'`@w]VS$f&j(OpOY@nYZ&cZr@nrs>`s!^@n!^!_Ap!_#O@n#O#P>`#P#o@n#o#pAp#p;'S@n;'S;=`Bg<%lO@ntAwWVS(OpOYApZrAprs?Ss#OAp#O#P?S#P;'SAp;'S;=`Ba<%lOAptBdP;=`<%lAp'`BjP;=`<%l@n#WBvYVS(Op(R!bOYBmZrBmrs?qswBmwxApx#OBm#O#P?S#P;'SBm;'S;=`Cf<%lOBm#WCiP;=`<%lBm(rCoP;=`<%l<S#%|C}i$f&j(g!L^(Op(R!bOY%ZYZ&cZr%Zrs&}st%ZtuCruw%Zwx(rx!Q%Z!Q![Cr![!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr#%|EoP;=`<%lCr(CSFRk$f&j(Op(R!b$Y#t'{&;d([!LYOY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr+dHRk$f&j(Op(R!b$Y#tOY%ZYZ&cZr%Zrs&}st%ZtuGvuw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Gv![!^%Z!^!_*g!_!c%Z!c!}Gv!}#O%Z#O#P&c#P#R%Z#R#SGv#S#T%Z#T#oGv#o#p*g#p$g%Z$g;'SGv;'S;=`Iv<%lOGv+dIyP;=`<%lGv(CSJPP;=`<%lEr%#SJ_`$f&j(Op(R!b#l$IdOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#SKl_$f&j$O$Id(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&COLva(q&;`$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sv%ZvwM{wx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#SNW`$f&j#x$Id(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/|! c_(Q$)`$f&j(OpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b'l!!i_$f&j(OpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b&z!#mX$f&jOw!#hwx6cx!^!#h!^!_!$Y!_#o!#h#o#p!$Y#p;'S!#h;'S;=`!$r<%lO!#h`!$]TOw!$Ywx7]x;'S!$Y;'S;=`!$l<%lO!$Y`!$oP;=`<%l!$Y&z!$uP;=`<%l!#h'l!%R]$a`$f&j(OpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r!Q!&PZ(OpOY!%zYZ!$YZr!%zrs!$Ysw!%zwx!&rx#O!%z#O#P!$Y#P;'S!%z;'S;=`!']<%lO!%z!Q!&yU$a`(OpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)r!Q!'`P;=`<%l!%z'l!'fP;=`<%l!!b(*Q!'t_!k(!b$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z!'l!)O_!jM|$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'+h!*[b$f&j(Op(R!b'|#)d#m$IdOY%ZYZ&cZr%Zrs&}sw%Zwx(rxz%Zz{!+d{!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S!+o`$f&j(Op(R!b#j$IdOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&-O!,|`$f&j(Op(R!bn&%`OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&C[!.Z_!Y&;l$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(CS!/ec$f&j(Op(R!b|'<nOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!0p!P!Q%Z!Q![!3Y![!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z!'d!0ya$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!2O!P!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z!'d!2Z_!XMt$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l!3eg$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!3Y![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S!3Y#S#X%Z#X#Y!4|#Y#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l!5Vg$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx{%Z{|!6n|}%Z}!O!6n!O!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l!6wc$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l!8_c$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(CS!9uf$f&j(Op(R!b#k$IdOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcxz!;Zz{#-}{!P!;Z!P!Q#/d!Q!^!;Z!^!_#(i!_!`#7S!`!a#8i!a!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z(r!;fb$f&j(Op(R!b!USOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z(Q!<w`$f&j(R!b!USOY!<nYZ&cZw!<nwx!=yx!P!<n!P!Q!Eq!Q!^!<n!^!_!Gr!_!}!<n!}#O!KS#O#P!Dy#P#o!<n#o#p!Gr#p;'S!<n;'S;=`!L]<%lO!<n&n!>Q^$f&j!USOY!=yYZ&cZ!P!=y!P!Q!>|!Q!^!=y!^!_!@c!_!}!=y!}#O!CW#O#P!Dy#P#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y&n!?Td$f&j!USO!^&c!_#W&c#W#X!>|#X#Z&c#Z#[!>|#[#]&c#]#^!>|#^#a&c#a#b!>|#b#g&c#g#h!>|#h#i&c#i#j!>|#j#k!>|#k#m&c#m#n!>|#n#o&c#p;'S&c;'S;=`&w<%lO&cS!@hX!USOY!@cZ!P!@c!P!Q!AT!Q!}!@c!}#O!Ar#O#P!Bq#P;'S!@c;'S;=`!CQ<%lO!@cS!AYW!US#W#X!AT#Z#[!AT#]#^!AT#a#b!AT#g#h!AT#i#j!AT#j#k!AT#m#n!ATS!AuVOY!ArZ#O!Ar#O#P!B[#P#Q!@c#Q;'S!Ar;'S;=`!Bk<%lO!ArS!B_SOY!ArZ;'S!Ar;'S;=`!Bk<%lO!ArS!BnP;=`<%l!ArS!BtSOY!@cZ;'S!@c;'S;=`!CQ<%lO!@cS!CTP;=`<%l!@c&n!C][$f&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#O!CW#O#P!DR#P#Q!=y#Q#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW&n!DWX$f&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW&n!DvP;=`<%l!CW&n!EOX$f&jOY!=yYZ&cZ!^!=y!^!_!@c!_#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y&n!EnP;=`<%l!=y(Q!Ezl$f&j(R!b!USOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#W&}#W#X!Eq#X#Z&}#Z#[!Eq#[#]&}#]#^!Eq#^#a&}#a#b!Eq#b#g&}#g#h!Eq#h#i&}#i#j!Eq#j#k!Eq#k#m&}#m#n!Eq#n#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!f!GyZ(R!b!USOY!GrZw!Grwx!@cx!P!Gr!P!Q!Hl!Q!}!Gr!}#O!JU#O#P!Bq#P;'S!Gr;'S;=`!J|<%lO!Gr!f!Hse(R!b!USOY'}Zw'}x#O'}#P#W'}#W#X!Hl#X#Z'}#Z#[!Hl#[#]'}#]#^!Hl#^#a'}#a#b!Hl#b#g'}#g#h!Hl#h#i'}#i#j!Hl#j#k!Hl#k#m'}#m#n!Hl#n;'S'};'S;=`(f<%lO'}!f!JZX(R!bOY!JUZw!JUwx!Arx#O!JU#O#P!B[#P#Q!Gr#Q;'S!JU;'S;=`!Jv<%lO!JU!f!JyP;=`<%l!JU!f!KPP;=`<%l!Gr(Q!KZ^$f&j(R!bOY!KSYZ&cZw!KSwx!CWx!^!KS!^!_!JU!_#O!KS#O#P!DR#P#Q!<n#Q#o!KS#o#p!JU#p;'S!KS;'S;=`!LV<%lO!KS(Q!LYP;=`<%l!KS(Q!L`P;=`<%l!<n'`!Ll`$f&j(Op!USOY!LcYZ&cZr!Lcrs!=ys!P!Lc!P!Q!Mn!Q!^!Lc!^!_# o!_!}!Lc!}#O#%P#O#P!Dy#P#o!Lc#o#p# o#p;'S!Lc;'S;=`#&Y<%lO!Lc'`!Mwl$f&j(Op!USOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#W(r#W#X!Mn#X#Z(r#Z#[!Mn#[#](r#]#^!Mn#^#a(r#a#b!Mn#b#g(r#g#h!Mn#h#i(r#i#j!Mn#j#k!Mn#k#m(r#m#n!Mn#n#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rt# vZ(Op!USOY# oZr# ors!@cs!P# o!P!Q#!i!Q!}# o!}#O#$R#O#P!Bq#P;'S# o;'S;=`#$y<%lO# ot#!pe(Op!USOY)rZr)rs#O)r#P#W)r#W#X#!i#X#Z)r#Z#[#!i#[#])r#]#^#!i#^#a)r#a#b#!i#b#g)r#g#h#!i#h#i)r#i#j#!i#j#k#!i#k#m)r#m#n#!i#n;'S)r;'S;=`*Z<%lO)rt#$WX(OpOY#$RZr#$Rrs!Ars#O#$R#O#P!B[#P#Q# o#Q;'S#$R;'S;=`#$s<%lO#$Rt#$vP;=`<%l#$Rt#$|P;=`<%l# o'`#%W^$f&j(OpOY#%PYZ&cZr#%Prs!CWs!^#%P!^!_#$R!_#O#%P#O#P!DR#P#Q!Lc#Q#o#%P#o#p#$R#p;'S#%P;'S;=`#&S<%lO#%P'`#&VP;=`<%l#%P'`#&]P;=`<%l!Lc(r#&kn$f&j(Op(R!b!USOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#W%Z#W#X#&`#X#Z%Z#Z#[#&`#[#]%Z#]#^#&`#^#a%Z#a#b#&`#b#g%Z#g#h#&`#h#i%Z#i#j#&`#j#k#&`#k#m%Z#m#n#&`#n#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#W#(r](Op(R!b!USOY#(iZr#(irs!Grsw#(iwx# ox!P#(i!P!Q#)k!Q!}#(i!}#O#+`#O#P!Bq#P;'S#(i;'S;=`#,`<%lO#(i#W#)th(Op(R!b!USOY*gZr*grs'}sw*gwx)rx#O*g#P#W*g#W#X#)k#X#Z*g#Z#[#)k#[#]*g#]#^#)k#^#a*g#a#b#)k#b#g*g#g#h#)k#h#i*g#i#j#)k#j#k#)k#k#m*g#m#n#)k#n;'S*g;'S;=`+Z<%lO*g#W#+gZ(Op(R!bOY#+`Zr#+`rs!JUsw#+`wx#$Rx#O#+`#O#P!B[#P#Q#(i#Q;'S#+`;'S;=`#,Y<%lO#+`#W#,]P;=`<%l#+`#W#,cP;=`<%l#(i(r#,o`$f&j(Op(R!bOY#,fYZ&cZr#,frs!KSsw#,fwx#%Px!^#,f!^!_#+`!_#O#,f#O#P!DR#P#Q!;Z#Q#o#,f#o#p#+`#p;'S#,f;'S;=`#-q<%lO#,f(r#-tP;=`<%l#,f(r#-zP;=`<%l!;Z(CS#.[b$f&j(Op(R!b'v(;d!USOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z(CS#/o_$f&j(Op(R!bS(;dOY#/dYZ&cZr#/drs#0nsw#/dwx#4Ox!^#/d!^!_#5}!_#O#/d#O#P#1p#P#o#/d#o#p#5}#p;'S#/d;'S;=`#6|<%lO#/d(Bb#0w]$f&j(R!bS(;dOY#0nYZ&cZw#0nwx#1px!^#0n!^!_#3R!_#O#0n#O#P#1p#P#o#0n#o#p#3R#p;'S#0n;'S;=`#3x<%lO#0n(AO#1wX$f&jS(;dOY#1pYZ&cZ!^#1p!^!_#2d!_#o#1p#o#p#2d#p;'S#1p;'S;=`#2{<%lO#1p(;d#2iSS(;dOY#2dZ;'S#2d;'S;=`#2u<%lO#2d(;d#2xP;=`<%l#2d(AO#3OP;=`<%l#1p(<v#3YW(R!bS(;dOY#3RZw#3Rwx#2dx#O#3R#O#P#2d#P;'S#3R;'S;=`#3r<%lO#3R(<v#3uP;=`<%l#3R(Bb#3{P;=`<%l#0n(Ap#4X]$f&j(OpS(;dOY#4OYZ&cZr#4Ors#1ps!^#4O!^!_#5Q!_#O#4O#O#P#1p#P#o#4O#o#p#5Q#p;'S#4O;'S;=`#5w<%lO#4O(<U#5XW(OpS(;dOY#5QZr#5Qrs#2ds#O#5Q#O#P#2d#P;'S#5Q;'S;=`#5q<%lO#5Q(<U#5tP;=`<%l#5Q(Ap#5zP;=`<%l#4O(=h#6WY(Op(R!bS(;dOY#5}Zr#5}rs#3Rsw#5}wx#5Qx#O#5}#O#P#2d#P;'S#5};'S;=`#6v<%lO#5}(=h#6yP;=`<%l#5}(CS#7PP;=`<%l#/d%#W#7ab$f&j$O$Id(Op(R!b!USOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z+h#8vb$W#t$f&j(Op(R!b!USOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z$/l#:Zp$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#U%Z#U#V#?i#V#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#d#Bq#d#l%Z#l#m#Es#m#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#<jk$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#>j_$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#?rd$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#A]f$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#Bzc$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#Dbe$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#E|g$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z$/l#Gpi$f&j(Op(R!bo$'|OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%Gh#Il_!d$b$f&j#|%<f(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)[#Jv_`l$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(@^#LS^g!*v!h'.r(Op(R!b(uSOY*gZr*grs'}sw*gwx)rx!P*g!P!Q#MO!Q!^*g!^!_#Mt!_!`$ f!`#O*g#P;'S*g;'S;=`+Z<%lO*g(n#MXX$h&j(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g$Kh#M}Z#n$Id(Op(R!bOY*gZr*grs'}sw*gwx)rx!_*g!_!`#Np!`#O*g#P;'S*g;'S;=`+Z<%lO*g$Kh#NyX$O$Id(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g$Kh$ oX#o$Id(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g%Gh$!ga#[%?x$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`!a$#l!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#W$#w_#g$Ih$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%Gh$%VafBf#o$Id$c#|$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`$&[!`!a$'f!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$&g_#o$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$'qa#n$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`!a$(v!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$)R`#n$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'+h$*`c(i$Ip$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P$+k!P!^%Z!^!_*g!_!a%Z!a!b$,u!b#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'+`$+v_}'#p$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$-Q`$f&j#y$Id(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#&^$.__!{!Ln$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(@^$/i_!P(8n$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(n$0mZ$f&jO!^$1`!^!_$1v!_#i$1`#i#j$1{#j#l$1`#l#m$3n#m#o$1`#o#p$1v#p;'S$1`;'S;=`$5y<%lO$1`(n$1gT^#S$f&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c#S$1{O^#S(n$2Q[$f&jO!Q&c!Q![$2v![!^&c!_!c&c!c!i$2v!i#T&c#T#Z$2v#Z#o&c#o#p$5^#p;'S&c;'S;=`&w<%lO&c(n$2{Z$f&jO!Q&c!Q![$3n![!^&c!_!c&c!c!i$3n!i#T&c#T#Z$3n#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$3sZ$f&jO!Q&c!Q![$4f![!^&c!_!c&c!c!i$4f!i#T&c#T#Z$4f#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$4kZ$f&jO!Q&c!Q![$1`![!^&c!_!c&c!c!i$1`!i#T&c#T#Z$1`#Z#o&c#p;'S&c;'S;=`&w<%lO&c#S$5aR!Q![$5j!c!i$5j#T#Z$5j#S$5mS!Q![$5j!c!i$5j#T#Z$5j#q#r$1v(n$5|P;=`<%l$1`!2r$6[_!V!+S$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#S$7f`#v$Id$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&,v$8s_$f&j(Op(R!b(X&%WOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(CS$:Rk$f&j(Op(R!b'{&;d$[#t([!LYOY%ZYZ&cZr%Zrs&}st%Ztu$9ruw%Zwx(rx}%Z}!O$;v!O!Q%Z!Q![$9r![!^%Z!^!_*g!_!c%Z!c!}$9r!}#O%Z#O#P&c#P#R%Z#R#S$9r#S#T%Z#T#o$9r#o#p*g#p$g%Z$g;'S$9r;'S;=`$=|<%lO$9r+d$<Rk$f&j(Op(R!b$[#tOY%ZYZ&cZr%Zrs&}st%Ztu$;vuw%Zwx(rx}%Z}!O$;v!O!Q%Z!Q![$;v![!^%Z!^!_*g!_!c%Z!c!}$;v!}#O%Z#O#P&c#P#R%Z#R#S$;v#S#T%Z#T#o$;v#o#p*g#p$g%Z$g;'S$;v;'S;=`$=v<%lO$;v+d$=yP;=`<%l$;v(CS$>PP;=`<%l$9r!5p$>]X![!3l(Op(R!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g&CO$?Ta(p&;`$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p#q$,u#q;'S%Z;'S;=`+a<%lO%Z%#`$@g_!Z$I`r`$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(r$Aq_!pS$f&j(Op(R!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(CS$CR|$f&j(Op(R!b't(;d$Y#t'{&;d([!LYOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$f%Z$f$g+g$g#BYEr#BY#BZ$Bp#BZ$ISEr$IS$I_$Bp$I_$JTEr$JT$JU$Bp$JU$KVEr$KV$KW$Bp$KW&FUEr&FU&FV$Bp&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$Bp?HUOEr(CS$F^k$f&j(Op(R!b'u(;d$Y#t'{&;d([!LYOY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr",tokenizers:[zO,GO,LO,2,3,4,5,6,7,8,9,10,11,12,13,BO,new SO("$S~RRtu[#O#Pg#S#T#|~_P#o#pb~gOt~~jVO#i!P#i#j!U#j#l!P#l#m!q#m;'S!P;'S;=`#v<%lO!P~!UO!R~~!XS!Q![!e!c!i!e#T#Z!e#o#p#Z~!hR!Q![!q!c!i!q#T#Z!q~!tR!Q![!}!c!i!}#T#Z!}~#QR!Q![!P!c!i!P#T#Z!P~#^R!Q![#g!c!i#g#T#Z#g~#jS!Q![#g!c!i#g#T#Z#g#q#r!P~#yP;=`<%l!P~$RO(Z~~",141,332),new SO("j~RQYZXz{^~^O'x~~aP!P!Qd~iO'y~~",25,315)],topRules:{Script:[0,6],SingleExpression:[1,269],SingleClassItem:[2,270]},dialects:{jsx:0,ts:14826},dynamicPrecedences:{69:1,79:1,81:1,165:1,193:1},specialized:[{term:319,get:t=>UO[t]||-1},{term:334,get:t=>HO[t]||-1},{term:70,get:t=>FO[t]||-1}],tokenPrec:14850}),JO=[hd("function ${name}(${params}) {\n\t${}\n}",{label:"function",detail:"definition",type:"keyword"}),hd("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n\t${}\n}",{label:"for",detail:"loop",type:"keyword"}),hd("for (let ${name} of ${collection}) {\n\t${}\n}",{label:"for",detail:"of loop",type:"keyword"}),hd("do {\n\t${}\n} while (${})",{label:"do",detail:"loop",type:"keyword"}),hd("while (${}) {\n\t${}\n}",{label:"while",detail:"loop",type:"keyword"}),hd("try {\n\t${}\n} catch (${error}) {\n\t${}\n}",{label:"try",detail:"/ catch block",type:"keyword"}),hd("if (${}) {\n\t${}\n}",{label:"if",detail:"block",type:"keyword"}),hd("if (${}) {\n\t${}\n} else {\n\t${}\n}",{label:"if",detail:"/ else block",type:"keyword"}),hd("class ${name} {\n\tconstructor(${params}) {\n\t\t${}\n\t}\n}",{label:"class",detail:"definition",type:"keyword"}),hd('import {${names}} from "${module}"\n${}',{label:"import",detail:"named",type:"keyword"}),hd('import ${name} from "${module}"\n${}',{label:"import",detail:"default",type:"keyword"})],tp=JO.concat([hd("interface ${name} {\n\t${}\n}",{label:"interface",detail:"definition",type:"keyword"}),hd("type ${name} = ${type}",{label:"type",detail:"definition",type:"keyword"}),hd("enum ${name} {\n\t${}\n}",{label:"enum",detail:"definition",type:"keyword"})]),ep=new ya,ip=new Set(["Script","Block","FunctionExpression","FunctionDeclaration","ArrowFunction","MethodDeclaration","ForStatement"]);function np(t){return(e,i)=>{let n=e.node.getChild("VariableDefinition");return n&&i(n,t),!0}}const sp=["FunctionDeclaration"],rp={FunctionDeclaration:np("function"),ClassDeclaration:np("class"),ClassExpression:()=>!0,EnumDeclaration:np("constant"),TypeAliasDeclaration:np("type"),NamespaceDeclaration:np("namespace"),VariableDefinition(t,e){t.matchContext(sp)||e(t,"variable")},TypeDefinition(t,e){e(t,"type")},__proto__:null};function op(t,e){let i=ep.get(e);if(i)return i;let n=[],s=!0;function r(e,i){let s=t.sliceString(e.from,e.to);n.push({label:s,type:i})}return e.cursor(ea.IncludeAnonymous).iterate((e=>{if(s)s=!1;else if(e.name){let t=rp[e.name];if(t&&t(e,r)||ip.has(e.name))return!1}else if(e.to-e.from>8192){for(let i of op(t,e.node))n.push(i);return!1}})),ep.set(e,n),n}const ap=/^[\w$\xa1-\uffff][\w$\d\xa1-\uffff]*$/,lp=["TemplateString","String","RegExp","LineComment","BlockComment","VariableDefinition","TypeDefinition","Label","PropertyDefinition","PropertyName","PrivatePropertyDefinition","PrivatePropertyName",".","?."];function hp(t){let e=sl(t.state).resolveInner(t.pos,-1);if(lp.indexOf(e.name)>-1)return null;let i="VariableName"==e.name||e.to-e.from<20&&ap.test(t.state.sliceDoc(e.from,e.to));if(!i&&!t.explicit)return null;let n=[];for(let s=e;s;s=s.parent)ip.has(s.name)&&(n=n.concat(op(t.state.doc,s)));return{options:n,from:i?e.from:t.pos,validFor:ap}}const cp=nl.define({name:"javascript",parser:KO.configure({props:[yl.add({IfStatement:Cl({except:/^\s*({|else\b)/}),TryStatement:Cl({except:/^\s*({|catch\b|finally\b)/}),LabeledStatement:t=>t.baseIndent,SwitchBody:t=>{let e=t.textAfter,i=/^\s*\}/.test(e),n=/^\s*(case|default)\b/.test(e);return t.baseIndent+(i?0:n?1:2)*t.unit},Block:Pl({closing:"}"}),ArrowFunction:t=>t.baseIndent+t.unit,"TemplateString BlockComment":()=>null,"Statement Property":Cl({except:/^{/}),JSXElement(t){let e=/^\s*<\//.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},JSXEscape(t){let e=/\s*\}/.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},"JSXOpenTag JSXSelfClosingTag":t=>t.column(t.node.from)+t.unit}),Al.add({"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType":function(t){let e=t.firstChild,i=t.lastChild;return e&&e.to<i.from?{from:e.to,to:i.type.isError?t.to:i.from}:null},BlockComment:t=>({from:t.from+2,to:t.to-2})})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"//",block:{open:"/*",close:"*/"}},indentOnInput:/^\s*(?:case |default:|\{|\}|<\/)$/,wordChars:"$"}}),fp={test:t=>/^JSX/.test(t.name),facet:Ja({commentTokens:{block:{open:"{/*",close:"*/}"}}})},up=cp.configure({dialect:"ts"},"typescript"),dp=cp.configure({dialect:"jsx",props:[tl.add((t=>t.isTop?[fp]:void 0))]}),Op=cp.configure({dialect:"jsx ts",props:[tl.add((t=>t.isTop?[fp]:void 0))]},"typescript");let pp=t=>({label:t,type:"keyword"});const gp="break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield".split(" ").map(pp),mp=gp.concat(["declare","implements","private","protected","public"].map(pp));function wp(t={}){let e=t.jsx?t.typescript?Op:dp:t.typescript?up:cp,i=t.typescript?tp.concat(mp):JO.concat(gp);return new Ol(e,[cp.data.of({autocomplete:(n=lp,s=fu(i),t=>{for(let e=sl(t.state).resolveInner(t.pos,-1);e;e=e.parent){if(n.indexOf(e.name)>-1)return null;if(e.type.isTop)break}return s(t)})}),cp.data.of({autocomplete:hp}),t.jsx?yp:[]]);var n,s}function vp(t,e,i=t.length){for(let n=null==e?void 0:e.firstChild;n;n=n.nextSibling)if("JSXIdentifier"==n.name||"JSXBuiltin"==n.name||"JSXNamespacedName"==n.name||"JSXMemberExpression"==n.name)return t.sliceString(n.from,Math.min(n.to,i));return""}const bp="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),yp=tr.inputHandler.of(((t,e,i,n,s)=>{if((bp?t.composing:t.compositionStarted)||t.state.readOnly||e!=i||">"!=n&&"/"!=n||!cp.isActiveAt(t.state,e,-1))return!1;let r=s(),{state:o}=r,a=o.changeByRange((t=>{var e;let i,{head:s}=t,r=sl(o).resolveInner(s-1,-1);if("JSXStartTag"==r.name&&(r=r.parent),o.doc.sliceString(s-1,s)!=n||"JSXAttributeValue"==r.name&&r.to>s);else{if(">"==n&&"JSXFragmentTag"==r.name)return{range:t,changes:{from:s,insert:"</>"}};if("/"==n&&"JSXStartCloseTag"==r.name){let t=r.parent,n=t.parent;if(n&&t.from==s-2&&((i=vp(o.doc,n.firstChild,s))||"JSXFragmentTag"==(null===(e=n.firstChild)||void 0===e?void 0:e.name))){let t=`${i}>`;return{range:V.cursor(s+t.length,-1),changes:{from:s,insert:t}}}}else if(">"==n){let e=function(t){for(;;){if("JSXOpenTag"==t.name||"JSXSelfClosingTag"==t.name||"JSXFragmentTag"==t.name)return t;if("JSXEscape"==t.name||!t.parent)return null;t=t.parent}}(r);if(e&&"JSXOpenTag"==e.name&&!/^\/?>|^<\//.test(o.doc.sliceString(s,s+2))&&(i=vp(o.doc,e,s)))return{range:t,changes:{from:s,insert:`</${i}>`}}}}return{range:t}}));return!a.changes.empty&&(t.dispatch([r,o.update(a,{userEvent:"input.complete",scrollIntoView:!0})]),!0)})),Qp="#e06c75",kp="#abb2bf",Sp="#7d8799",xp="#d19a66",$p="#2c313a",Pp="#282c34",Zp="#353a42",Cp="#528bff",Tp=[tr.theme({"&":{color:kp,backgroundColor:Pp},".cm-content":{caretColor:Cp},".cm-cursor, .cm-dropCursor":{borderLeftColor:Cp},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:"#3E4451"},".cm-panels":{backgroundColor:"#21252b",color:kp},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:"1px solid #457dff"},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:"#6199ff2f"},".cm-activeLine":{backgroundColor:"#6699ff0b"},".cm-selectionMatch":{backgroundColor:"#aafe661a"},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bad0f847"},".cm-gutters":{backgroundColor:Pp,color:Sp,border:"none"},".cm-activeLineGutter":{backgroundColor:$p},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:Zp},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Zp,borderBottomColor:Zp},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:$p,color:kp}}},{dark:!0}),sh(th.define([{tag:Ha.keyword,color:"#c678dd"},{tag:[Ha.name,Ha.deleted,Ha.character,Ha.propertyName,Ha.macroName],color:Qp},{tag:[Ha.function(Ha.variableName),Ha.labelName],color:"#61afef"},{tag:[Ha.color,Ha.constant(Ha.name),Ha.standard(Ha.name)],color:xp},{tag:[Ha.definition(Ha.name),Ha.separator],color:kp},{tag:[Ha.typeName,Ha.className,Ha.number,Ha.changed,Ha.annotation,Ha.modifier,Ha.self,Ha.namespace],color:"#e5c07b"},{tag:[Ha.operator,Ha.operatorKeyword,Ha.url,Ha.escape,Ha.regexp,Ha.link,Ha.special(Ha.string)],color:"#56b6c2"},{tag:[Ha.meta,Ha.comment],color:Sp},{tag:Ha.strong,fontWeight:"bold"},{tag:Ha.emphasis,fontStyle:"italic"},{tag:Ha.strikethrough,textDecoration:"line-through"},{tag:Ha.link,color:Sp,textDecoration:"underline"},{tag:Ha.heading,fontWeight:"bold",color:Qp},{tag:[Ha.atom,Ha.bool,Ha.special(Ha.variableName)],color:xp},{tag:[Ha.processingInstruction,Ha.string,Ha.inserted],color:"#98c379"},{tag:Ha.invalid,color:"#ffffff"}]))];export{dO as T,wp as j,Tp as o};
