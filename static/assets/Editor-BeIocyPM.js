import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{_ as t}from"./style.css_vue_type_style_index_0_src_true_lang-DFrnfRdK.js";import{d as s,l as a,r,H as o,a as l,o as i,i as p,w as _,e as n}from"./index-C6fb_XFi.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";const u=s({__name:"Editor",setup(s){const{t:u}=a(),m=e=>{},d=r(),c=r("");return o((async()=>{var e;await(null==(e=l(d))?void 0:e.getEditorRef())})),setTimeout((()=>{c.value="<p>hello <strong>world</strong></p>"}),3e3),(s,a)=>(i(),p(l(e),{title:l(u)("richText.richText"),message:l(u)("richText.richTextDes")},{default:_((()=>[n(l(t),{modelValue:c.value,"onUpdate:modelValue":a[0]||(a[0]=e=>c.value=e),ref_key:"editorRef",ref:d,onChange:m},null,8,["modelValue"])])),_:1},8,["title","message"]))}});export{u as default};
