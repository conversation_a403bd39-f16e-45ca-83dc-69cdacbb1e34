import{D as e}from"./Descriptions-CGdh5AeD.js";import{d as s,l,s as a,e as o,A as t,y as i,o as r,c as m,a as p,w as d,f as n,z as u,t as c,F as j,_ as f}from"./index-3XfDPlIS.js";import{u as b,F as x}from"./useForm-CxJHOWP1.js";import{a as _}from"./el-form-BY8piFS2.js";import{u as h}from"./useValidator-ByHrE6OC.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./el-col-CN1tVfqh.js";import"./index-6fa0VYPg.js";import"./el-tag-DcMbxLLg.js";import"./el-checkbox-DjLAvZXr.js";import"./index-tjM0-mlU.js";import"./el-radio-group-evFfsZkP.js";/* empty css                          */import"./el-input-number-CfcpPMpr.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-virtual-list-Drl4IGmp.js";import"./raf-BoCEWvzN.js";import"./el-select-v2-CJw7ZO42.js";import"./el-switch-C-DLgt5X.js";import"./el-autocomplete-DpYoUHkX.js";import"./el-divider-D9UCOo44.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./el-upload-D4EYBM-E.js";import"./el-progress-DtkkA0nz.js";import"./InputPassword-CsftE_fC.js";import"./style.css_vue_type_style_index_0_src_true_lang-C2CruVLZ.js";import"./JsonEditor.vue_vue_type_script_setup_true_lang-C3k54pLm.js";import"./IconPicker-CGJrUvM2.js";import"./el-tab-pane-xcqYouKU.js";import"./el-pagination-DwzzZyu4.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./tsxHelper-C7SpLWNA.js";/* empty css                        */import"./castArray-uOT054sj.js";const D={class:"text-center mt-10px"},k=f(s({__name:"Descriptions",setup(s){const{required:f}=h(),{t:k}=l(),g=a({username:"chenkl",nickName:"梦似花落。",age:26,phone:"13655971xxxx",email:"<EMAIL>",addr:"这是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的地址",sex:"男",certy:"3505831994xxxxxxxx"}),V=a([{field:"username",label:k("descriptionsDemo.username")},{field:"nickName",label:k("descriptionsDemo.nickName")},{field:"phone",label:k("descriptionsDemo.phone")},{field:"email",label:k("descriptionsDemo.email")},{field:"addr",label:k("descriptionsDemo.addr"),span:24}]),N=a([{field:"username",label:k("descriptionsDemo.username"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"username"},{default:()=>[o(t,{modelValue:v.username,"onUpdate:modelValue":e=>v.username=e},null)]})}},{field:"nickName",label:k("descriptionsDemo.nickName"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"nickName"},{default:()=>[o(t,{modelValue:v.nickName,"onUpdate:modelValue":e=>v.nickName=e},null)]})}},{field:"phone",label:k("descriptionsDemo.phone"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"phone"},{default:()=>[o(t,{modelValue:v.phone,"onUpdate:modelValue":e=>v.phone=e},null)]})}},{field:"email",label:k("descriptionsDemo.email"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"email"},{default:()=>[o(t,{modelValue:v.email,"onUpdate:modelValue":e=>v.email=e},null)]})}},{field:"addr",label:k("descriptionsDemo.addr"),slots:{label:e=>o("span",{class:"is-required--item"},[e.label]),default:()=>o(_,{prop:"addr"},{default:()=>[o(t,{modelValue:v.addr,"onUpdate:modelValue":e=>v.addr=e},null)]})},span:24}]),v=a({username:"",nickName:"",phone:"",email:"",addr:""}),y=a({username:[f()],nickName:[f()],phone:[f()],email:[f()],addr:[f()]}),{formRegister:q,formMethods:U}=b(),{getElFormExpose:w}=U,F=async()=>{const e=await w();null==e||e.validate((e=>{}))};return(s,l)=>{const a=i("BaseButton");return r(),m(j,null,[o(p(e),{title:p(k)("descriptionsDemo.descriptions"),message:p(k)("descriptionsDemo.descriptionsDes"),data:g,schema:V},null,8,["title","message","data","schema"]),o(p(x),{"is-custom":"",model:v,rules:y,onRegister:p(q)},{default:d((()=>[o(p(e),{title:p(k)("descriptionsDemo.form"),data:g,schema:N,class:"mt-20px"},null,8,["title","data","schema"]),n("div",D,[o(a,{onClick:F},{default:d((()=>[u(c(p(k)("formDemo.formValidation")),1)])),_:1})])])),_:1},8,["model","rules","onRegister"])],64)}}}),[["__scopeId","data-v-25b10d4d"]]);export{k as default};
