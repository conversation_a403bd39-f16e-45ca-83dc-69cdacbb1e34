import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as t,l as a,e as i,r as l,o,i as r,w as s,a as m}from"./index-3XfDPlIS.js";import{_ as p}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{a as n}from"./index-DkbkkiFT.js";import{E as d}from"./el-tag-DcMbxLLg.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./el-table-column-B5hg3WH6.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./index-Dz8ZrwBc.js";const j=t({__name:"TableImagePreview",setup(t){const{t:j}=a(),u=[{field:"title",label:j("tableDemo.title")},{field:"image_uri",label:j("tableDemo.preview")},{field:"author",label:j("tableDemo.author")},{field:"display_time",label:j("tableDemo.displayTime")},{field:"importance",label:j("tableDemo.importance"),formatter:(e,t,a)=>i(d,{type:1===a?"success":2===a?"warning":"danger"},{default:()=>[j(1===a?"tableDemo.important":2===a?"tableDemo.good":"tableDemo.commonly")]})},{field:"pageviews",label:j("tableDemo.pageviews")}],c=l(!0);let b=l([]);return(async e=>{const t=await n(e||{pageIndex:1,pageSize:10}).catch((()=>{})).finally((()=>{c.value=!1}));t&&(b.value=t.data.list)})(),(t,a)=>(o(),r(m(e),{title:m(j)("router.PicturePreview")},{default:s((()=>[i(m(p),{columns:u,data:m(b),loading:c.value,preview:["image_uri"]},null,8,["data","loading"])])),_:1},8,["title"]))}});export{j as default};
