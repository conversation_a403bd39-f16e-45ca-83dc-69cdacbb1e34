import{d as l,a6 as a,aR as t,o as s,c as e,f as o,a as r,a9 as i,Y as n,aA as c,a7 as f,b6 as p,a8 as d,e as m,n as g,aH as y,t as u,j as k,ag as v}from"./index-C6fb_XFi.js";const $={viewBox:"0 0 79 86",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},h=["id"],x=["stop-color"],C=["stop-color"],w=["id"],B=["stop-color"],N=["stop-color"],V=["id"],R={id:"Illustrations",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},G={id:"B-type",transform:"translate(-1268.000000, -535.000000)"},b={id:"Group-2",transform:"translate(1268.000000, 535.000000)"},E=["fill"],S=["fill"],_={id:"Group-Copy",transform:"translate(34.500000, 31.500000) scale(-1, 1) rotate(-25.000000) translate(-34.500000, -31.500000) translate(7.000000, 10.000000)"},j=["fill"],z=["fill"],I=["fill"],M=["fill"],A=["fill"],H={id:"Rectangle-Copy-17",transform:"translate(53.000000, 45.000000)"},O=["fill","xlink:href"],T=["fill","mask"],Y=["fill"],Z=l({name:"ImgEmpty"});var q=i(l({...Z,setup(l){const i=a("empty"),n=t();return(l,a)=>(s(),e("svg",$,[o("defs",null,[o("linearGradient",{id:`linearGradient-1-${r(n)}`,x1:"38.8503086%",y1:"0%",x2:"61.1496914%",y2:"100%"},[o("stop",{"stop-color":`var(${r(i).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,x),o("stop",{"stop-color":`var(${r(i).cssVarBlockName("fill-color-4")})`,offset:"100%"},null,8,C)],8,h),o("linearGradient",{id:`linearGradient-2-${r(n)}`,x1:"0%",y1:"9.5%",x2:"100%",y2:"90.5%"},[o("stop",{"stop-color":`var(${r(i).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,B),o("stop",{"stop-color":`var(${r(i).cssVarBlockName("fill-color-6")})`,offset:"100%"},null,8,N)],8,w),o("rect",{id:`path-3-${r(n)}`,x:"0",y:"0",width:"17",height:"36"},null,8,V)]),o("g",R,[o("g",G,[o("g",b,[o("path",{id:"Oval-Copy-2",d:"M39.5,86 C61.3152476,86 79,83.9106622 79,81.3333333 C79,78.7560045 57.3152476,78 35.5,78 C13.6847524,78 0,78.7560045 0,81.3333333 C0,83.9106622 17.6847524,86 39.5,86 Z",fill:`var(${r(i).cssVarBlockName("fill-color-3")})`},null,8,E),o("polygon",{id:"Rectangle-Copy-14",fill:`var(${r(i).cssVarBlockName("fill-color-7")})`,transform:"translate(27.500000, 51.500000) scale(1, -1) translate(-27.500000, -51.500000) ",points:"13 58 53 58 42 45 2 45"},null,8,S),o("g",_,[o("polygon",{id:"Rectangle-Copy-10",fill:`var(${r(i).cssVarBlockName("fill-color-7")})`,transform:"translate(11.500000, 5.000000) scale(1, -1) translate(-11.500000, -5.000000) ",points:"2.84078316e-14 3 18 3 23 7 5 7"},null,8,j),o("polygon",{id:"Rectangle-Copy-11",fill:`var(${r(i).cssVarBlockName("fill-color-5")})`,points:"-3.69149156e-15 7 38 7 38 43 -3.69149156e-15 43"},null,8,z),o("rect",{id:"Rectangle-Copy-12",fill:`url(#linearGradient-1-${r(n)})`,transform:"translate(46.500000, 25.000000) scale(-1, 1) translate(-46.500000, -25.000000) ",x:"38",y:"7",width:"17",height:"36"},null,8,I),o("polygon",{id:"Rectangle-Copy-13",fill:`var(${r(i).cssVarBlockName("fill-color-2")})`,transform:"translate(39.500000, 3.500000) scale(-1, 1) translate(-39.500000, -3.500000) ",points:"24 7 41 7 55 -3.63806207e-12 38 -3.63806207e-12"},null,8,M)]),o("rect",{id:"Rectangle-Copy-15",fill:`url(#linearGradient-2-${r(n)})`,x:"13",y:"45",width:"40",height:"36"},null,8,A),o("g",H,[o("use",{id:"Mask",fill:`var(${r(i).cssVarBlockName("fill-color-8")})`,transform:"translate(8.500000, 18.000000) scale(-1, 1) translate(-8.500000, -18.000000) ","xlink:href":`#path-3-${r(n)}`},null,8,O),o("polygon",{id:"Rectangle-Copy",fill:`var(${r(i).cssVarBlockName("fill-color-9")})`,mask:`url(#mask-4-${r(n)})`,transform:"translate(12.000000, 9.000000) scale(-1, 1) translate(-12.000000, -9.000000) ",points:"7 0 24 0 20 18 7 16.5"},null,8,T)]),o("polygon",{id:"Rectangle-Copy-18",fill:`var(${r(i).cssVarBlockName("fill-color-2")})`,transform:"translate(66.000000, 51.500000) scale(-1, 1) translate(-66.000000, -51.500000) ",points:"62 45 79 45 70 58 53 58"},null,8,Y)])])])]))}}),[["__file","img-empty.vue"]]);const D=n({image:{type:String,default:""},imageSize:Number,description:{type:String,default:""}}),F=["src"],J={key:1},K=l({name:"ElEmpty"});const L=v(i(l({...K,props:D,setup(l){const t=l,{t:i}=c(),n=a("empty"),v=f((()=>t.description||i("el.table.emptyText"))),$=f((()=>({width:p(t.imageSize)})));return(l,a)=>(s(),e("div",{class:g(r(n).b())},[o("div",{class:g(r(n).e("image")),style:y(r($))},[l.image?(s(),e("img",{key:0,src:l.image,ondragstart:"return false"},null,8,F)):d(l.$slots,"image",{key:1},(()=>[m(q)]))],6),o("div",{class:g(r(n).e("description"))},[l.$slots.description?d(l.$slots,"description",{key:0}):(s(),e("p",J,u(r(v)),1))],2),l.$slots.default?(s(),e("div",{key:0,class:g(r(n).e("bottom"))},[d(l.$slots,"default")],2)):k("v-if",!0)],2))}}),[["__file","empty.vue"]]));export{L as E};
