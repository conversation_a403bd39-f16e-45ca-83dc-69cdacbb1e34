import{_ as e}from"./Highlight.vue_vue_type_script_lang-zuUE9qRJ.js";import{d as s,aq as r,y as t,o,c as l,n as a,a as i,e as c,f as p,t as n,j as d,F as m,R as f,w as x,z as y,k as _}from"./index-3XfDPlIS.js";const g=s({__name:"Infotip",props:{title:r.string.def(""),schema:{type:Array,required:!0,default:()=>[]},showIndex:r.bool.def(!0),highlightColor:r.string.def("var(--el-color-primary)")},emits:["click"],setup(s,{emit:r}){const{getPrefixCls:g}=_(),h=g("infotip"),b=r,k=e=>{b("click",e)};return(r,_)=>{const g=t("Icon");return o(),l("div",{class:a([i(h),"p-20px mb-20px border-1px border-solid border-[var(--el-color-primary)] bg-[var(--el-color-primary-light-9)]"])},[s.title?(o(),l("div",{key:0,class:a([`${i(h)}__header`,"flex items-center"])},[c(g,{icon:"bi:exclamation-circle-fill",size:22,color:"var(--el-color-primary)"}),p("span",{class:a([`${i(h)}__title`,"pl-5px text-16px font-bold"])},n(s.title),3)],2)):d("",!0),p("div",{class:a(`${i(h)}__content`)},[(o(!0),l(m,null,f(s.schema,((r,t)=>(o(),l("p",{key:t,class:"text-14px mt-15px"},[c(i(e),{keys:"string"==typeof r?[]:r.keys,color:s.highlightColor,onClick:k},{default:x((()=>[y(n(s.showIndex?`${t+1}、`:"")+n("string"==typeof r?r:r.label),1)])),_:2},1032,["keys","color"])])))),128))],2)],2)}}});export{g as _};
