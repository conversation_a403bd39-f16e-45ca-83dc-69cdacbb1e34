import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as s,l as o,o as t,i,w as n,e as p,a}from"./index-C6fb_XFi.js";import{_ as m}from"./Infotip.vue_vue_type_script_setup_true_lang-59YrZr7S.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./Highlight.vue_vue_type_script_lang-9Vlpm4kA.js";const c=s({__name:"Infotip",setup(s){const{t:c}=o(),r=e=>{e===c("iconDemo.accessAddress")&&window.open("https://iconify.design/")};return(s,o)=>(t(),i(a(e),{title:a(c)("infotipDemo.infotip"),message:a(c)("infotipDemo.infotipDes")},{default:n((()=>[p(a(m),{"show-index":!1,title:`${a(c)("iconDemo.recommendedUse")}${a(c)("iconDemo.iconify")}`,schema:[{label:a(c)("iconDemo.recommendeDes"),keys:["Iconify"]},{label:a(c)("iconDemo.accessAddress"),keys:[a(c)("iconDemo.accessAddress")]}],onClick:r},null,8,["title","schema"])])),_:1},8,["title","message"]))}});export{c as default};
