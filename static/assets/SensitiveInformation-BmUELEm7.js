import{d as e,r as t,s as l,e as a,L as i,E as s,v as o,A as n,B as r,G as u,F as d,H as p,o as m,c,a as f,w as v,I as g,f as h,t as y,z as j,R as x,J as b,l as _,Y as w,_ as S}from"./index-3XfDPlIS.js";import{u as k}from"./useTable-BezX3TfM.js";import{E as C}from"./el-card-CuEws33_.js";import{E as V}from"./el-pagination-DwzzZyu4.js";import{E}from"./el-tag-DcMbxLLg.js";import{E as A,a as z}from"./el-select-DH55-Cab.js";import"./el-popper-DVoWBu_3.js";import{E as I,a as L}from"./el-col-CN1tVfqh.js";import{E as H}from"./el-drawer-COw0uCzP.js";import{E as P,a as R}from"./el-table-column-B5hg3WH6.js";import"./el-checkbox-DjLAvZXr.js";import"./el-tooltip-l0sNRNKZ.js";import{_ as U}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{_ as O}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as T}from"./useCrudSchemas-6tFKup3N.js";import{u as W,p as N,a as D,d as F,q as K,r as $,s as G}from"./index-BAb9yQka.js";import B from"./Csearch-CpC9XwHn.js";import"./index-tjM0-mlU.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./refs-CSSW5x_d.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./index-DkclijAA.js";import"./tree-BfZhwLPs.js";import"./index-Dz8ZrwBc.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import"./el-text-CLWE0mUm.js";import"./el-divider-D9UCOo44.js";import"./el-autocomplete-DpYoUHkX.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-switch-C-DLgt5X.js";import"./useIcon-k-uSyz6l.js";import"./exportData.vue_vue_type_script_setup_true_lang--cnrLcU_.js";import"./el-tab-pane-xcqYouKU.js";import"./el-form-BY8piFS2.js";import"./el-radio-group-evFfsZkP.js";import"./el-space-BFgHxiAK.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import"./el-virtual-list-Drl4IGmp.js";import"./el-select-v2-CJw7ZO42.js";import"./index-lpN3i-fa.js";import"./index-BuRY9bVn.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-Cyn7ZzlS.js";import"./index-D4TGn7aE.js";const J={style:{whiteSpace:"pre-line"}},M=["onClick"],q=["onClick"];function Y(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!b(e)}const Q=S(e({__name:"SensitiveInformation",props:{projectList:{}},setup(e){const{t:b}=_(),S=[{keyword:"url",example:'url="http://example.com"',explain:b("searchHelp.url")},{keyword:"sname",example:'sname="twilio_account_sid"',explain:b("searchHelp.sname")},{keyword:"info",example:'info="api-key-example"',explain:b("searchHelp.sinfo")},{keyword:"project",example:'project="Hackerone"',explain:b("searchHelp.project")},{keyword:"md5",example:'md5=="1d49e5e190f7a38ab498e28e6578f64f"',explain:b("searchHelp.sensMd5")},{keyword:"level",example:'level=="rad"',explain:b("searchHelp.sensLevel")}],Q=t(""),Z=e=>{Q.value=e,Ee()},X=l({}),ee=l([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:b("tableDemo.index"),type:"index",minWidth:55},{field:"url",label:"URL",minWidth:200},{field:"name",label:b("sensitiveInformation.sensitiveName"),minWidth:150},{field:"color",label:"Level",minWidth:50,columnKey:"color",formatter:(e,t,l)=>{if(l)return a(i,{icon:"clarity:circle-solid",color:l,style:"transform: translateY(-35%)"},null)},filters:[{text:"Red",value:"red"},{text:"Green",value:"green"},{text:"Cyan",value:"cyan"},{text:"Yellow",value:"yellow"},{text:"Orange",value:"orange"},{text:"Gray",value:"gray"},{text:"Pink",value:"pink"},{text:"Null",value:"null"}]},{field:"match",label:"Info",minWidth:150,formatter:(e,t,l)=>{if(!l)return;const i=l.map(((e,t)=>a("div",{key:t},[e])));return a(s,{height:"100px"},{default:()=>[a("div",{class:"scrollbar-demo-item"},[i])]})}},{field:"status",label:b("common.state"),minWidth:100,columnKey:"status",formatter:(e,t,l)=>{let i;if(e.id.includes("//"))return;if(e.id.includes("APP"))return;null==e.status&&(e.status=1);const s=[{value:1,label:b("common.unprocessed")},{value:2,label:b("common.processing")},{value:3,label:b("common.ignored")},{value:4,label:b("common.suspected")},{value:5,label:b("common.confirmed")}];return a(z,{modelValue:e.status,"onUpdate:modelValue":async t=>{try{e.status=t,W(e.id,"SensitiveResult",t)}catch(l){}}},Y(i=s.map((e=>a(A,{key:e.value,label:e.label,value:e.value},null))))?i:{default:()=>[i]})},filters:[{text:b("common.unprocessed"),value:1},{text:b("common.processing"),value:2},{text:b("common.ignored"),value:3},{text:b("common.suspected"),value:4},{text:b("common.confirmed"),value:5}]},{field:"tags",label:"TAG",fit:"true",formatter:(e,l,a)=>{if(e.id.includes("//"))return;if(e.id.includes("APP"))return;null==a&&(a=[]),X[e.id]||(X[e.id]={inputVisible:!1,inputValue:"",inputRef:t(null)});const i=X[e.id],s=async()=>{i.inputValue&&(a.push(i.inputValue),D(e.id,te,i.inputValue)),i.inputVisible=!1,i.inputValue=""};return o(L,{},(()=>[...a.map((t=>o(I,{span:24,key:t},(()=>[o("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||ze("tags",t)})(e,t)},[o(E,{closable:!0,onClose:()=>(async t=>{const l=a.indexOf(t);l>-1&&a.splice(l,1),F(e.id,te,t)})(t)},(()=>t))])])))),o(I,{span:24},i.inputVisible?()=>o(n,{ref:i.inputRef,modelValue:i.inputValue,"onUpdate:modelValue":e=>i.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&s()},onBlur:s}):()=>o(r,{class:"button-new-tag",size:"small",onClick:()=>(i.inputVisible=!0,void w((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:b("asset.time"),minWidth:200},{field:"action",label:b("tableDemo.action"),formatter:(e,t,l)=>{if(e.body_id){let t;return a(d,null,[a(u,{type:"primary",onClick:()=>be(e.body_id)},Y(t=b("asset.detail"))?t:{default:()=>[t]})])}},minWidth:100}]);let te="SensitiveResult";ee.forEach((e=>{e.hidden=e.hidden??!1}));let le=t(!1);const ae=({field:e,hidden:t})=>{const l=ee.findIndex((t=>t.field===e));-1!==l&&(ee[l].hidden=t),(()=>{const e=ee.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=le.value,localStorage.setItem(`columnConfig_${te}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${te}`)||"{}");ee.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),le.value=e.statisticsHidden})();const ie=l({}),{allSchemas:se}=T(ee),{tableRegister:oe,tableState:ne,tableMethods:re}=k({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=ne,l=await K(Q.value,e.value,t.value,ie);return{list:l.data.list,total:l.data.total}},immediate:!1}),{loading:ue,dataList:de,total:pe,currentPage:me,pageSize:ce}=ne,{getList:fe,getElTableExpose:ve}=re;function ge(){return{background:"var(--el-fill-color-light)"}}p((()=>{ye(),window.addEventListener("resize",ye)}));const he=t(0),ye=()=>{const e=window.innerHeight||document.documentElement.clientHeight;he.value=.7*e},je=t(!1),xe=t(""),be=async e=>{e.replace("md5==","");const t=await N(e);xe.value=t.data.body,je.value=!0},_e=async e=>{Object.assign(ie,e),Ee()},we=(e,t)=>{Object.assign(ie,t),Q.value=e,Ee()},Se=t(!1),ke=()=>{Se.value=!0},Ce=t([]),Ve=async()=>{try{const e=await G(Q.value,ie);e&&e.data&&Array.isArray(e.data.list)&&(Ce.value=e.data.list.map((e=>({name:e.name,color:e.color,count:e.count}))))}catch(e){}},Ee=async()=>{try{await Promise.all([fe(),Ve()])}catch(e){}},Ae=t([]),ze=(e,t)=>{const l=`${e}=${t}`;Ae.value=[...Ae.value,l]},Ie=e=>{if(Ae.value){const[t,l]=e.split("=");t in ie&&Array.isArray(ie[t])&&(ie[t]=ie[t].filter((e=>e!==l)),0===ie[t].length&&delete ie[t]),Ae.value=Ae.value.filter((t=>t!==e))}},Le=()=>ie,He=t(!1),Pe=t([]),Re=t("");return(e,t)=>(m(),c(d,null,[a(B,{getList:f(fe),handleSearch:Z,searchKeywordsData:S,index:f(te),getElTableExpose:f(ve),handleFilterSearch:we,projectList:e.$props.projectList,openAggregation:ke,dynamicTags:Ae.value,handleClose:Ie,crudSchemas:ee,onUpdateColumnVisibility:ae,searchResultCount:f(pe),getFilter:Le},null,8,["getList","index","getElTableExpose","projectList","dynamicTags","crudSchemas","searchResultCount"]),a(f(L),null,{default:v((()=>[a(f(I),null,{default:v((()=>[a(f(C),null,{default:v((()=>[a(f(O),{pageSize:f(ce),"onUpdate:pageSize":t[0]||(t[0]=e=>g(ce)?ce.value=e:null),currentPage:f(me),"onUpdate:currentPage":t[1]||(t[1]=e=>g(me)?me.value=e:null),columns:f(se).tableColumns,data:f(de),stripe:"",rowKey:"id",border:!0,"max-height":he.value,loading:f(ue),resizable:!0,onRegister:f(oe),onFilterChange:_e,headerCellStyle:ge,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!0,showAfter:0,popperOptions:{},popperClass:"test",placement:"top",hideAfter:0,disabled:!1},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),a(f(I),{":span":24},{default:v((()=>[a(f(C),null,{default:v((()=>[a(f(V),{pageSize:f(ce),"onUpdate:pageSize":t[2]||(t[2]=e=>g(ce)?ce.value=e:null),currentPage:f(me),"onUpdate:currentPage":t[3]||(t[3]=e=>g(me)?me.value=e:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:f(pe)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1}),a(f(U),{modelValue:je.value,"onUpdate:modelValue":t[4]||(t[4]=e=>je.value=e),title:f(b)("asset.detail"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},width:"70%","max-height":he.value},{default:v((()=>[a(f(s),{"max-height":he.value},{default:v((()=>[h("div",J,y(xe.value),1)])),_:1},8,["max-height"])])),_:1},8,["modelValue","title","max-height"]),a(f(H),{modelValue:Se.value,"onUpdate:modelValue":t[5]||(t[5]=e=>Se.value=e),title:f(b)("sensitiveInformation.sensAggre"),direction:"rtl",size:"30%"},{default:v((()=>[a(f(P),{data:Ce.value},{default:v((()=>[a(f(R),{prop:"name",label:f(b)("sensitiveInformation.sensitiveName"),width:"180"},{default:v((e=>[h("div",{style:{display:"flex","align-items":"center"},onClick:t=>ze("sname",e.row.name)},[a(f(E),null,{default:v((()=>[j(y(e.row.name),1)])),_:2},1024)],8,M)])),_:1},8,["label"]),a(f(R),{prop:"color",label:"color",width:"100"},{default:v((e=>[a(f(E),{color:e.row.color,round:"",effect:"plain",size:"small",style:{width:"20px"}},null,8,["color"])])),_:1}),a(f(R),{prop:"count",label:f(b)("common.quantity"),width:"130"},null,8,["label"]),a(f(R),{label:f(b)("tableDemo.operate"),width:"180"},{default:v((e=>[h("div",{style:{display:"flex","align-items":"center"},onClick:t=>(async e=>{Re.value=e;const t=await $(e,Q.value,ie);Pe.value=t.data.list,He.value=!0})(e.row.name)},[a(f(r),{type:"success"},{default:v((()=>[j("info")])),_:1})],8,q)])),_:1},8,["label"])])),_:1},8,["data"])])),_:1},8,["modelValue","title"]),a(f(U),{modelValue:He.value,"onUpdate:modelValue":t[6]||(t[6]=e=>He.value=e),title:Re.value},{default:v((()=>[(m(!0),c(d,null,x(Pe.value,((e,t)=>(m(),c("div",{key:t},[h("p",null,y(e),1)])))),128))])),_:1},8,["modelValue","title"])],64))}}),[["__scopeId","data-v-834293cc"]]);export{Q as default};
