import{cN as e,Y as a,Z as s,$ as n,a2 as t,d as l,br as o,ds as i,dt as c,aA as u,a6 as r,b$ as d,r as f,du as v,b5 as m,a7 as p,O as b,ai as g,H as k,o as x,i as w,e as y,w as I,f as _,n as C,a as h,aH as z,ae as N,j as O,C as T,bd as A,c as R,F as L,bj as Y,ac as E,dv as S,cu as X,aI as $,dw as F,dx as j,R as B,Q as D,af as H,a8 as M,h as W,c1 as P,a9 as V,bt as Z,aK as G,dy as K,ag as Q}from"./index-C6fb_XFi.js";import{d as q}from"./debounce-BwgdhaaK.js";function J(a,s,n){var t=!0,l=!0;if("function"!=typeof a)throw new TypeError("Expected a function");return e(n)&&(t="leading"in n?!!n.leading:t,l="trailing"in n?!!n.trailing:l),q(a,s,{leading:t,maxWait:s,trailing:l})}const U=a({urlList:{type:s(Array),default:()=>n([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:Boolean,teleported:Boolean,closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},crossorigin:{type:s(String)}}),ee={close:()=>!0,switch:e=>t(e),rotate:e=>t(e)},ae=["src","crossorigin"],se=l({name:"ElImageViewer"});const ne=Q(V(l({...se,props:U,emits:ee,setup(e,{expose:a,emit:s}){var n;const t=e,l={CONTAIN:{name:"contain",icon:o(i)},ORIGINAL:{name:"original",icon:o(c)}},{t:V}=u(),Q=r("image-viewer"),{nextZIndex:q}=d(),U=f(),ee=f([]),se=v(),ne=f(!0),te=f(t.initialIndex),le=m(l.CONTAIN),oe=f({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),ie=f(null!=(n=t.zIndex)?n:q()),ce=p((()=>{const{urlList:e}=t;return e.length<=1})),ue=p((()=>0===te.value)),re=p((()=>te.value===t.urlList.length-1)),de=p((()=>t.urlList[te.value])),fe=p((()=>[Q.e("btn"),Q.e("prev"),Q.is("disabled",!t.infinite&&ue.value)])),ve=p((()=>[Q.e("btn"),Q.e("next"),Q.is("disabled",!t.infinite&&re.value)])),me=p((()=>{const{scale:e,deg:a,offsetX:s,offsetY:n,enableTransition:t}=oe.value;let o=s/e,i=n/e;switch(a%360){case 90:case-270:[o,i]=[i,-o];break;case 180:case-180:[o,i]=[-o,-i];break;case 270:case-90:[o,i]=[-i,o]}const c={transform:`scale(${e}) rotate(${a}deg) translate(${o}px, ${i}px)`,transition:t?"transform .3s":""};return le.value.name===l.CONTAIN.name&&(c.maxWidth=c.maxHeight="100%"),c}));function pe(){se.stop(),s("close")}function be(){ne.value=!1}function ge(e){ne.value=!1,e.target.alt=V("el.image.error")}function ke(e){if(ne.value||0!==e.button||!U.value)return;oe.value.enableTransition=!1;const{offsetX:a,offsetY:s}=oe.value,n=e.pageX,t=e.pageY,l=J((e=>{oe.value={...oe.value,offsetX:a+e.pageX-n,offsetY:s+e.pageY-t}})),o=Z(document,"mousemove",l);Z(document,"mouseup",(()=>{o()})),e.preventDefault()}function xe(){oe.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function we(){if(ne.value)return;const e=K(l),a=Object.values(l),s=le.value.name,n=(a.findIndex((e=>e.name===s))+1)%e.length;le.value=l[e[n]],xe()}function ye(e){const a=t.urlList.length;te.value=(e+a)%a}function Ie(){ue.value&&!t.infinite||ye(te.value-1)}function _e(){re.value&&!t.infinite||ye(te.value+1)}function Ce(e,a={}){if(ne.value)return;const{minScale:n,maxScale:l}=t,{zoomRate:o,rotateDeg:i,enableTransition:c}={zoomRate:t.zoomRate,rotateDeg:90,enableTransition:!0,...a};switch(e){case"zoomOut":oe.value.scale>n&&(oe.value.scale=Number.parseFloat((oe.value.scale/o).toFixed(3)));break;case"zoomIn":oe.value.scale<l&&(oe.value.scale=Number.parseFloat((oe.value.scale*o).toFixed(3)));break;case"clockwise":oe.value.deg+=i,s("rotate",oe.value.deg);break;case"anticlockwise":oe.value.deg-=i,s("rotate",oe.value.deg)}oe.value.enableTransition=c}return b(de,(()=>{g((()=>{const e=ee.value[0];(null==e?void 0:e.complete)||(ne.value=!0)}))})),b(te,(e=>{xe(),s("switch",e)})),k((()=>{var e,a;!function(){const e=J((e=>{switch(e.code){case G.esc:t.closeOnPressEscape&&pe();break;case G.space:we();break;case G.left:Ie();break;case G.up:Ce("zoomIn");break;case G.right:_e();break;case G.down:Ce("zoomOut")}})),a=J((e=>{Ce((e.deltaY||e.deltaX)<0?"zoomIn":"zoomOut",{zoomRate:t.zoomRate,enableTransition:!1})}));se.run((()=>{Z(document,"keydown",e),Z(document,"wheel",a)}))}(),null==(a=null==(e=U.value)?void 0:e.focus)||a.call(e)})),a({setActiveItem:ye}),(e,a)=>(x(),w(P,{to:"body",disabled:!e.teleported},[y(W,{name:"viewer-fade",appear:""},{default:I((()=>[_("div",{ref_key:"wrapper",ref:U,tabindex:-1,class:C(h(Q).e("wrapper")),style:z({zIndex:ie.value})},[_("div",{class:C(h(Q).e("mask")),onClick:a[0]||(a[0]=N((a=>e.hideOnClickModal&&pe()),["self"]))},null,2),O(" CLOSE "),_("span",{class:C([h(Q).e("btn"),h(Q).e("close")]),onClick:pe},[y(h(T),null,{default:I((()=>[y(h(A))])),_:1})],2),O(" ARROW "),h(ce)?O("v-if",!0):(x(),R(L,{key:0},[_("span",{class:C(h(fe)),onClick:Ie},[y(h(T),null,{default:I((()=>[y(h(Y))])),_:1})],2),_("span",{class:C(h(ve)),onClick:_e},[y(h(T),null,{default:I((()=>[y(h(E))])),_:1})],2)],64)),O(" ACTIONS "),_("div",{class:C([h(Q).e("btn"),h(Q).e("actions")])},[_("div",{class:C(h(Q).e("actions__inner"))},[y(h(T),{onClick:a[1]||(a[1]=e=>Ce("zoomOut"))},{default:I((()=>[y(h(S))])),_:1}),y(h(T),{onClick:a[2]||(a[2]=e=>Ce("zoomIn"))},{default:I((()=>[y(h(X))])),_:1}),_("i",{class:C(h(Q).e("actions__divider"))},null,2),y(h(T),{onClick:we},{default:I((()=>[(x(),w($(h(le).icon)))])),_:1}),_("i",{class:C(h(Q).e("actions__divider"))},null,2),y(h(T),{onClick:a[3]||(a[3]=e=>Ce("anticlockwise"))},{default:I((()=>[y(h(F))])),_:1}),y(h(T),{onClick:a[4]||(a[4]=e=>Ce("clockwise"))},{default:I((()=>[y(h(j))])),_:1})],2)],2),O(" CANVAS "),_("div",{class:C(h(Q).e("canvas"))},[(x(!0),R(L,null,B(e.urlList,((a,s)=>D((x(),R("img",{ref_for:!0,ref:e=>ee.value[s]=e,key:a,src:a,style:z(h(me)),class:C(h(Q).e("img")),crossorigin:e.crossorigin,onLoad:be,onError:ge,onMousedown:ke},null,46,ae)),[[H,s===te.value]]))),128))],2),M(e.$slots,"default")],6)])),_:3})],8,["disabled"]))}}),[["__file","image-viewer.vue"]]));export{ne as E};
