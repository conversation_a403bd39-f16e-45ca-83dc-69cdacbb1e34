import{r as a}from"./index-CnCQNuY4.js";const t=(t,p,e,d)=>a.post({url:"/api/poc/data",data:{search:t,pageIndex:p,pageSize:e,filter:d}}),p=()=>a.get({url:"/api/poc/data/all"}),e=t=>a.post({url:"/api/poc/content",data:{id:t}}),d=t=>a.post({url:"/api/poc/detail",data:{id:t}}),o=(t,p,e,d)=>a.post({url:"/api/poc/add",data:{name:t,content:p,level:e,tags:d}}),s=(t,p,e,d,o)=>a.post({url:"/api/poc/update",data:{id:t,name:p,content:e,level:d,tags:o}}),i=t=>a.post({url:"/api/poc/delete",data:{ids:t}});export{t as a,d as b,o as c,i as d,p as e,e as g,s as u};
