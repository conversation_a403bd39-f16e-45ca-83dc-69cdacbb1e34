import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,s as a,v as o,e as l,G as i,F as n,r as s,O as r,H as m,o as d,c as u,w as p,a as c,f,z as g,t as v,I as y,E as j,j as _,J as x,l as b}from"./index-DfJTpRkj.js";import{a as h,E as w}from"./el-col-B4Ik8fnS.js";import{E as k}from"./el-tag-CbhrEnto.js";import"./el-tooltip-l0sNRNKZ.js";import{E as S}from"./el-popper-D2BmgSQA.js";import{_ as C}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as N}from"./useTable-CtyddZqf.js";import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{_ as H}from"./Configuration.vue_vue_type_script_setup_true_lang-CVD_V4a-.js";import{_ as E}from"./plugin.vue_vue_type_script_setup_true_lang-CTi2M577.js";import{r as F,b as T,g as W,d as U}from"./index-KH6atv8j.js";import"./el-card-DyZz6u6e.js";import"./el-table-column-7FjdLFwR.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./el-form-DsaI0u2w.js";import"./el-switch-C5ZBDFmL.js";import"./el-divider-0NmzbuNU.js";import"./el-text-vKNLRkxx.js";import"./index-B-gHSwWD.js";import"./useIcon-CNpM61rT.js";import"./index-Dt8htmKv.js";import"./index-D1ADinPR.js";const V={class:"mb-10px"},I={style:{position:"relative",top:"12px"}},A={key:0};function M(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!x(e)}const R=t({__name:"Node",setup(t){const{t:x}=b(),R=a([{field:"selection",type:"selection",width:"55"},{field:"name",label:x("node.nodeName"),minWidth:20},{field:"maxTaskNum",label:x("configuration.maxTaskNum"),minWidth:10,formatter:(e,t,a)=>o(k,{type:"info"},(()=>a))},{field:"running",label:x("node.taskCount"),minWidth:10,formatter:(e,t,a)=>o(k,{round:!0,effect:"plain",hit:!0},(()=>a))},{field:"finished",label:x("node.finished"),minWidth:10,formatter:(e,t,a)=>o(k,{round:!0,effect:"plain",hit:!0},(()=>a))},{field:"cpuNum",label:x("node.nodeUsageCpu"),minWidth:20,formatter:(e,t,a)=>{let l=parseFloat(a);return l=parseFloat(l.toFixed(2)),o(k,{round:!0,effect:"plain",hit:!0,type:l<50?"":l<80?"warning":"danger"},(()=>l+"%"))}},{field:"memNum",label:x("node.nodeUsageMemory"),minWidth:20,formatter:(e,t,a)=>{let l=parseFloat(a);return l=parseFloat(l.toFixed(2)),o(k,{round:!0,effect:"plain",hit:!0,type:l<50?"":l<80?"warning":"danger"},(()=>l+"%"))}},{field:"state",label:x("node.nodeStatus"),minWidth:20,formatter:(e,t,a)=>o(k,{type:"1"===a?"success":"2"===a?"warning":"danger",effect:"light",hit:!0},(()=>x("1"===a?"node.statusRun":"2"===a?"node.statusStop":"node.statusError")))},{field:"updateTime",label:x("node.updateTime"),minWidth:20},{field:"action",label:x("tableDemo.action"),minWidth:30,formatter:(e,t,a)=>{let o,s,r,m;return l(n,null,[l(i,{type:"warning",size:"small",onClick:()=>ce(e.name)},M(o=x("node.plugin"))?o:{default:()=>[o]}),l(i,{type:"success",size:"small",onClick:()=>re(e)},M(s=x("node.log"))?s:{default:()=>[s]}),l(i,{type:"primary",size:"small",onClick:()=>Z(e)},M(r=x("common.config"))?r:{default:()=>[r]}),l(S,{content:x("node.restartMsg")},{default:()=>[l(i,{type:"danger",size:"small",onClick:()=>ee(e.name)},M(m=x("node.restart"))?m:{default:()=>[m]})]})])}}]),{tableRegister:L,tableState:D,tableMethods:O}=N({fetchDataApi:async()=>({list:(await W()).data.list})}),{loading:P,dataList:G,currentPage:B,pageSize:J}=D,{getList:$,getElTableExpose:K}=O;function q(){return{background:"var(--el-fill-color-light)"}}const Q=s(!1),X=()=>{Q.value=!1},Y=a({name:"",maxTaskNum:"",state:"",ModulesConfig:""}),Z=async e=>{Y.name=e.name,Y.maxTaskNum=e.maxTaskNum,Y.ModulesConfig=e.modulesConfig,Y.state=e.state,Q.value=!0},ee=async e=>{await F(e)},te=s(!1),ae=s([]),oe=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{const e=await K(),t=(null==e?void 0:e.getSelectionRows())||[];ae.value=t.map((e=>e.name)),te.value=!0;try{await U(ae.value),te.value=!1,$()}catch(a){te.value=!1,$()}})()},le=s(!1),ie=()=>{le.value=!1},ne=s(""),se=s(),re=async e=>{const t=await T(e.name);ne.value=t.logs,le.value=!0;const a="https:"===window.location.protocol?"wss://":"ws://",o=window.location.host,l=new WebSocket(a+o);l.onopen=()=>{setInterval((()=>{const t={node_name:e.name};l.send(JSON.stringify(t))}),3e3)},l.onmessage=e=>{ne.value+=e.data,se.value.setScrollTop(5e3)};const i=r(le,(e=>{e||(l.close(),i())}))};m((()=>{de(),window.addEventListener("resize",de)}));const me=s(0),de=()=>{const e=window.innerHeight||document.documentElement.clientHeight;me.value=.7*e},ue=s(""),pe=s(!1),ce=async e=>{ue.value=e,pe.value=!0},fe=()=>{pe.value=!1};return(t,a)=>(d(),u(n,null,[l(c(e),null,{default:p((()=>[l(c(h),null,{default:p((()=>[l(c(w),{style:{position:"relative",top:"16px"}},{default:p((()=>[f("div",V,[l(c(i),{type:"danger",loading:te.value,onClick:oe},{default:p((()=>[g(v(c(x)("common.delete")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),f("div",I,[l(c(C),{pageSize:c(J),"onUpdate:pageSize":a[0]||(a[0]=e=>y(J)?J.value=e:null),currentPage:c(B),"onUpdate:currentPage":a[1]||(a[1]=e=>y(B)?B.value=e:null),columns:R,data:c(G),stripe:"",border:!0,loading:c(P),resizable:!0,onRegister:c(L),headerCellStyle:q,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","onRegister"])])])),_:1}),l(c(z),{modelValue:Q.value,"onUpdate:modelValue":a[2]||(a[2]=e=>Q.value=e),title:t.$t("common.config"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:me.value},{default:p((()=>[l(H,{closeDialog:X,nodeConfForm:Y,getList:c($)},null,8,["nodeConfForm","getList"])])),_:1},8,["modelValue","title","maxHeight"]),l(c(z),{modelValue:le.value,"onUpdate:modelValue":a[3]||(a[3]=e=>le.value=e),title:c(x)("node.log"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:me.value},{footer:p((()=>[l(c(i),{onClick:ie},{default:p((()=>[g(v(c(x)("common.off")),1)])),_:1})])),default:p((()=>[l(c(j),{ref_key:"scrollbarRef",ref:se},{default:p((()=>[ne.value?(d(),u("pre",A,v(ne.value),1)):_("",!0)])),_:1},512)])),_:1},8,["modelValue","title","maxHeight"]),l(c(z),{modelValue:pe.value,"onUpdate:modelValue":a[4]||(a[4]=e=>pe.value=e),title:c(x)("node.plugin"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:me.value},{default:p((()=>[l(E,{closeDialog:fe,name:ue.value},null,8,["name"])])),_:1},8,["modelValue","title","maxHeight"])],64))}});export{R as default};
