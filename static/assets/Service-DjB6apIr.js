import{d as e,dC as t,H as l,r as a,s,e as i,z as o,F as n,A as r,o as m,i as d,w as p,a as c,B as u,j as h,J as v,l as f,K as g,M as j,_ as y}from"./index-C6fb_XFi.js";import{u as b}from"./useTable-CijeIiBB.js";import{E as x}from"./el-card-B37ahJ8o.js";import{E as _,a as w}from"./el-col-Dl4_4Pn5.js";import{E as S}from"./el-text-BnUG9HvL.js";import{_ as C}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as E}from"./useCrudSchemas-CEXr0LRM.js";import{f as W}from"./index-DKxEKp57.js";import{t as z}from"./index-BBupWySc.js";import"./el-table-column-C9CkC7I1.js";import"./el-popper-CeVwVUf9.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-C_oEQYGz.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./tree-BfZhwLPs.js";import"./index-CnCQNuY4.js";function V(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!v(e)}const k=y(e({__name:"Service",setup(e){const{t:v}=f(),{query:y}=t();l((()=>{I(),window.addEventListener("resize",I)}));const k=a(0),I=()=>{const e=window.innerHeight||document.documentElement.clientHeight;k.value=.8*e};a("");const U=s({});U.project=[y.id];const A=async e=>{Object.assign(U,e),L()},H=s([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:v("tableDemo.index"),type:"index",minWidth:"30"},{field:"service",label:v("asset.service"),minWidth:"100",formatter:(e,t,l)=>e.count?i(n,null,[i(S,null,V(l)?l:{default:()=>[l]}),i(S,{type:"info"},{default:()=>[o("("),e.count,o(")")]})]):i(S,null,V(l)?l:{default:()=>[l]}),slots:{header:()=>i("div",null,[i("span",null,[v("asset.service")]),i(r,{modelValue:q.value,"onUpdate:modelValue":e=>q.value=e,placeholder:"Search",style:"width: 80px; margin-left: 10px;",size:"small",onChange:()=>Y("service_service")},null)])}},{field:"host",label:v("asset.domain"),minWidth:"200",slots:{header:()=>i("div",null,[i("span",null,[v("asset.domain")]),i(r,{modelValue:J.value,"onUpdate:modelValue":e=>J.value=e,placeholder:"Search",style:"width: 80px; margin-left: 10px;",size:"small",onChange:()=>Y("service_domain")},null)])}},{field:"ip",label:"IP",minWidth:"250",slots:{header:()=>i("div",null,[i("span",null,[o("IP")]),i(r,{modelValue:P.value,"onUpdate:modelValue":e=>P.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>Y("service_ip")},null)])}},{field:"port",label:v("asset.port"),minWidth:"250",slots:{header:()=>i("div",null,[i("span",null,[v("asset.port")]),i(r,{modelValue:N.value,"onUpdate:modelValue":e=>N.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>Y("service_port")},null)])}},{field:"time",label:v("asset.time"),minWidth:"200"}]),{allSchemas:R}=E(H),{tableRegister:D,tableState:F,tableMethods:O}=b({fetchDataApi:async()=>({list:(await W("",U,X)).data.list}),immediate:!0}),{loading:T,dataList:B}=F,{getList:L,getElTableExpose:K}=O;function M(){return{background:"var(--el-fill-color-light)"}}const N=a(""),J=a(""),P=a(""),q=a(""),X=s({}),Y=async e=>{let t="";"service_port"==e&&(t=N.value),"service_domain"==e&&(t=J.value),"service_ip"==e&&(t=P.value),"service_service"==e&&(t=q.value),X[e]=t,L()},G=a([]),Q=async()=>{g.confirm("Whether to delete?","Warning",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{const e=await K(),t=(null==e?void 0:e.getSelectionRows())||[];G.value=t.map((e=>e.id)),await z(G.value,"asset"),L()})).catch((()=>{j({type:"info",message:"Delete canceled"})}))};let Z=a(!1);const $=async()=>{const e=await K(),t=(null==e?void 0:e.getSelectionRows())||[];G.value=t.map((e=>e.id)),0!=G.value.length?Z.value=!0:Z.value=!1};return(e,t)=>(m(),d(c(w),null,{default:p((()=>[i(c(_),null,{default:p((()=>[i(c(x),{style:{height:"min-content"}},{default:p((()=>[c(Z)?(m(),d(c(u),{key:0,onClick:Q,type:"danger",size:"small"},{default:p((()=>[o("Dlete")])),_:1})):h("",!0),i(c(C),{columns:c(R).tableColumns,data:c(B),"max-height":k.value,border:!0,loading:c(T),onSelectionChange:$,rowKey:"id",resizable:!0,onRegister:c(D),onFilterChange:A,headerCellStyle:M,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["columns","data","max-height","loading","onRegister"])])),_:1})])),_:1})])),_:1}))}}),[["__scopeId","data-v-f2d00836"]]);export{k as default};
