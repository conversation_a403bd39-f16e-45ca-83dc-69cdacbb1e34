import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,r as a,s as l,e as o,S as i,G as s,F as n,N as r,o as p,c,w as u,a as m,z as d,t as g,A as f,B as v,f as y,I as j,J as _,l as h,M as x}from"./index-DfJTpRkj.js";import{E as b,a as w}from"./el-col-B4Ik8fnS.js";import{E as k}from"./el-text-vKNLRkxx.js";import{E as S}from"./el-upload-BW2TOFr8.js";import"./el-progress-Wzr98AjO.js";import"./el-tooltip-l0sNRNKZ.js";import{E as C}from"./el-popper-D2BmgSQA.js";import{E as z}from"./el-tag-CbhrEnto.js";import{_ as A}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as E}from"./useTable-CtyddZqf.js";import{u as F}from"./useIcon-CNpM61rT.js";import{g as U,d as I,a as T}from"./index-CCMFk4pF.js";import{_ as P}from"./Detail.vue_vue_type_script_setup_true_lang-Cp4j-41e.js";import{_ as L}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import"./el-card-DyZz6u6e.js";import"./index-DE7jtbbk.js";import"./el-table-column-7FjdLFwR.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";import"./el-form-DsaI0u2w.js";import"./el-divider-0NmzbuNU.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./index-B-gHSwWD.js";const N={class:"mb-10px"},V={class:"mb-10px"};function W(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!_(e)}const D=t({__name:"Poc",setup(t){const _=F({icon:"iconoir:search"}),{t:D}=h(),M=a(!1),R=a(""),H=()=>{Y()};l({});const O=l([{field:"selection",type:"selection",width:"55"},{field:"name",label:D("poc.pocName"),minWidth:70},{field:"level",label:D("poc.level"),minWidth:50,columnKey:"level",formatter:(e,t,a)=>{if(null==a)return o("div",null,null);let l="",s="";return"critical"===a?(l="red",s=D("poc.critical")):"high"===a?(l="orange",s=D("poc.high")):"medium"===a?(l="yellow",s=D("poc.medium")):"low"===a?(l="blue",s=D("poc.low")):"info"===a?(l="green",s=D("poc.info")):"unknown"===a&&(l="gray",s=D("poc.unknown")),o(w,{gutter:20,style:"width: 80%"},{default:()=>[o(b,{span:1},{default:()=>[o(i,{icon:"clarity:circle-solid",color:l,size:10},null)]}),o(b,{span:5},{default:()=>[o(k,{type:"info"},W(s)?s:{default:()=>[s]})]})]})},filters:[{text:D("poc.critical"),value:"critical"},{text:D("poc.high"),value:"high"},{text:D("poc.medium"),value:"medium"},{text:D("poc.low"),value:"low"},{text:D("poc.info"),value:"info"},{text:D("poc.unknown"),value:"unknown"}]},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,a)=>{if(0!=a.length){let e;return o(w,{style:{flexWrap:"wrap"}},W(e=a.map((e=>o(b,{span:24,key:e},{default:()=>[o("div",{onClick:()=>te("app",e),style:"display: inline-block; cursor: pointer"},[o(z,{type:"success"},W(e)?e:{default:()=>[e]})])]}))))?e:{default:()=>[e]})}}},{field:"time",label:D("node.createTime"),minWidth:50},{field:"action",label:D("tableDemo.action"),minWidth:30,formatter:(e,t,a)=>{let l,i;return o(n,null,[o(s,{type:"primary",onClick:()=>oe(e)},W(l=D("common.edit"))?l:{default:()=>[l]}),o(s,{type:"danger",onClick:()=>ne(e)},W(i=D("common.delete"))?i:{default:()=>[i]})])}}]),{tableRegister:$,tableState:B,tableMethods:G}=E({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=B,a=await T(R.value,e.value,t.value,ve);return{list:a.data.list,total:a.data.total}}}),{loading:J,dataList:K,total:q,currentPage:Q,pageSize:X}=B,{getList:Y,getElTableExpose:Z}=G;function ee(){return{background:"var(--el-fill-color-light)"}}const te=(e,t)=>{};let ae=l({id:"",name:"",level:"",content:"",tags:[]});const le=async()=>{ae.id="",ae.name="",ae.level="",ae.content="",ae.tags=[],M.value=!0},oe=async e=>{ae.id=e.id,ae.name=e.name,ae.level=e.level,ae.tags=e.tags;const t=await U(ae.id);ae.content=t.data.content,M.value=!0},ie=()=>{M.value=!1},se=a(!1),ne=async e=>{se.value=!0;try{await I([e.id]);se.value=!1,Y()}catch(t){se.value=!1,Y()}},re=a([]),pe=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{const e=await Z(),t=(null==e?void 0:e.getSelectionRows())||[];re.value=t.map((e=>e.id)),se.value=!0;try{await I(re.value),se.value=!1,Y()}catch(a){se.value=!1,Y()}})()},ce=r(),ue=a({Authorization:`${ce.getToken}`}),me=a(),de=e=>{me.value.clearFiles();const t=e[0];me.value.handleStart(t)},ge=e=>{var t;200===e.code?x.success("Upload succes"):x.error(e.message),505==e.code&&localStorage.removeItem("plugin_key"),Y(),null==(t=me.value)||t.clearFiles()},fe=(e,t)=>{t.length>0&&me.value.submit()},ve=l({}),ye=async e=>{Object.assign(ve,e),Y()};return(t,a)=>(p(),c(n,null,[o(m(e),null,{default:u((()=>[o(m(w),{gutter:20,style:{"margin-bottom":"15px"}},{default:u((()=>[o(m(b),{span:1.5},{default:u((()=>[o(m(k),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:u((()=>[d(g(m(D)("poc.pocName"))+":",1)])),_:1})])),_:1}),o(m(b),{span:5},{default:u((()=>[o(m(f),{modelValue:R.value,"onUpdate:modelValue":a[0]||(a[0]=e=>R.value=e),placeholder:m(D)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(m(b),{span:5,style:{position:"relative",left:"16px"}},{default:u((()=>[o(m(v),{type:"primary",icon:m(_),style:{height:"100%"},onClick:H},{default:u((()=>[d("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(m(w),{gutter:60},{default:u((()=>[o(m(b),{span:1},{default:u((()=>[y("div",N,[o(m(v),{type:"primary",onClick:le},{default:u((()=>[d(g(m(D)("common.new")),1)])),_:1})])])),_:1}),o(m(b),{span:1},{default:u((()=>[y("div",V,[o(m(s),{type:"danger",loading:se.value,onClick:pe},{default:u((()=>[d(g(m(D)("common.delete")),1)])),_:1},8,["loading"])])])),_:1}),o(m(b),{span:3},{default:u((()=>[o(m(C),{content:m(D)("common.uploadMsg"),placement:"top"},{default:u((()=>[o(m(S),{ref_key:"upload",ref:me,class:"flex items-center",action:"/api/poc/data/import",headers:ue.value,"on-success":ge,limit:1,"on-exceed":de,"auto-upload":!1,onChange:fe},{trigger:u((()=>[o(m(s),null,{icon:u((()=>[o(m(i),{icon:"iconoir:upload"})])),default:u((()=>[d(" "+g(m(D)("plugin.import")),1)])),_:1})])),_:1},8,["headers"])])),_:1},8,["content"])])),_:1})])),_:1}),o(m(A),{pageSize:m(X),"onUpdate:pageSize":a[1]||(a[1]=e=>j(X)?X.value=e:null),currentPage:m(Q),"onUpdate:currentPage":a[2]||(a[2]=e=>j(Q)?Q.value=e:null),onFilterChange:ye,columns:O,data:m(K),stripe:"",border:!0,loading:m(J),resizable:!0,pagination:{total:m(q),pageSizes:[10,20,50,100,200,500,1e3]},onRegister:m($),headerCellStyle:ee,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","pagination","onRegister"])])),_:1}),o(m(L),{modelValue:M.value,"onUpdate:modelValue":a[3]||(a[3]=e=>M.value=e),title:m(ae).id?t.$t("common.edit"):t.$t("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:800},{default:u((()=>[o(P,{closeDialog:ie,pocForm:m(ae),getList:m(Y)},null,8,["pocForm","getList"])])),_:1},8,["modelValue","title"])],64))}});export{D as default};
