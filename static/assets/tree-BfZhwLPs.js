const n={id:"id",children:"children",pid:"pid"},r=r=>Object.assign({},n,r),e=(n,e={})=>{e=r(e);const{children:t}=e,i=[...n];for(let r=0;r<i.length;r++)i[r][t]&&i.splice(r+1,0,...i[r][t]);return i},t=(n,e,t={})=>{t=r(t);const i=[],c=[...n],s=new Set,{children:l}=t;for(;c.length;){const n=c[0];if(s.has(n))i.pop(),c.shift();else if(s.add(n),n[l]&&c.unshift(...n[l]),i.push(n),e(n))return i}return null},i=(n,e,t={})=>{const i=(t=r(t)).children;return function n(r){return r.map((n=>({...n}))).filter((r=>(r[i]=r[i]&&n(r[i]),e(r)||r[i]&&r[i].length)))}(n)},c=(n,r)=>n.map((n=>s(n,r))),s=(n,{children:r="children",conversion:e})=>{const t=Array.isArray(n[r])&&n[r].length>0,i=e(n)||{};return t?{...i,[r]:n[r].map((n=>s(n,{children:r,conversion:e})))}:{...i}},l=(n,r,e={})=>{n.forEach((n=>{const t=r(n,e)||n;n.children&&l(n.children,r,t)}))};export{t as a,e as b,l as e,i as f,c as t};
