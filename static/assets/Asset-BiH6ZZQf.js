import{d as e,s as t,o as r,i,w as o,e as s,a as l,l as p}from"./index-3XfDPlIS.js";import{E as a,a as m}from"./el-tab-pane-xcqYouKU.js";import j from"./AssetInfo2-fvzrmtep.js";import n from"./Subdomain-BInZ95Nw.js";import u from"./URL-B3s-4hiq.js";import c from"./Crawler-CH8EHTrd.js";import _ from"./SensitiveInformation-BmUELEm7.js";import d from"./DirScan-KbiDLALu.js";import b from"./PageMonitoring-BZHPMr7u.js";import f from"./vul--41kN7O3.js";import L from"./SubdomainTakeover-B5Sr2bgC.js";import{g as v}from"./index-D4TGn7aE.js";import g from"./RootDomain-BM1WYXxz.js";import x from"./APP-PXjf4ivI.js";import y from"./MP-CQEdhBGO.js";import"./strings-Dm4Pnsdt.js";import"./useTable-BezX3TfM.js";import"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import"./refs-CSSW5x_d.js";import"./el-popper-DVoWBu_3.js";import"./el-col-CN1tVfqh.js";import"./el-card-CuEws33_.js";import"./el-tag-DcMbxLLg.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./index-tjM0-mlU.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-link-Dzmhaz1a.js";import"./el-text-CLWE0mUm.js";import"./Table.vue_vue_type_script_lang-B7lrRql4.js";import"./el-table-column-B5hg3WH6.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./index-DkclijAA.js";import"./useCrudSchemas-6tFKup3N.js";import"./tree-BfZhwLPs.js";import"./index-BAb9yQka.js";import"./index-Dz8ZrwBc.js";import"./Csearch-CpC9XwHn.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import"./el-divider-D9UCOo44.js";import"./el-autocomplete-DpYoUHkX.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-switch-C-DLgt5X.js";import"./useIcon-k-uSyz6l.js";import"./exportData.vue_vue_type_script_setup_true_lang--cnrLcU_.js";import"./el-form-BY8piFS2.js";import"./el-radio-group-evFfsZkP.js";import"./el-space-BFgHxiAK.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import"./el-virtual-list-Drl4IGmp.js";import"./el-select-v2-CJw7ZO42.js";import"./index-lpN3i-fa.js";import"./index-BuRY9bVn.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-Cyn7ZzlS.js";import"./index-Cgkdj9wb.js";import"./AssetDetail2.vue_vue_type_script_setup_true_lang-B4e4q6U8.js";import"./index-C7jW5IRr.js";import"./el-drawer-COw0uCzP.js";import"./MonacoDiffEditor-CT_9o8pr.js";import"./el-descriptions-item-DHQ8ug0J.js";import"./Detail.vue_vue_type_script_setup_true_lang-Dj-GId_d.js";const N=e({__name:"Asset",setup(e){const{t:N}=p(),w=t([]);return(async()=>{(await v()).data.list.forEach((e=>{w.push(e)}))})(),(e,t)=>(r(),i(l(m),{type:"border-card"},{default:o((()=>[s(l(a),{label:l(N)("asset.assetName")},{default:o((()=>[s(j,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("rootDomain.rootDomainName")},{default:o((()=>[s(g,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("subdomain.subdomainName")},{default:o((()=>[s(n,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("task.subdomainTakeover")},{default:o((()=>[s(L,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("app.appName")},{default:o((()=>[s(x,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("miniProgram.miniProgramName")},{default:o((()=>[s(y,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("URL.URLName")},{default:o((()=>[s(u,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("crawler.crawlerName")},{default:o((()=>[s(c,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("sensitiveInformation.sensitiveInformationName")},{default:o((()=>[s(_,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("dirScan.dirScanName")},{default:o((()=>[s(d,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("vulnerability.vulnerabilityName")},{default:o((()=>[s(f,{projectList:w},null,8,["projectList"])])),_:1},8,["label"]),s(l(a),{label:l(N)("PageMonitoring.pageMonitoringName")},{default:o((()=>[s(b,{projectList:w},null,8,["projectList"])])),_:1},8,["label"])])),_:1}))}});export{N as default};
