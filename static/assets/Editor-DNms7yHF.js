import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{_ as t}from"./style.css_vue_type_style_index_0_src_true_lang-C2CruVLZ.js";import{d as s,l as a,r,H as o,a as l,o as i,i as p,w as _,e as u}from"./index-3XfDPlIS.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";const n=s({__name:"Editor",setup(s){const{t:n}=a(),m=e=>{},d=r(),c=r("");return o((async()=>{var e;await(null==(e=l(d))?void 0:e.getEditorRef())})),setTimeout((()=>{c.value="<p>hello <strong>world</strong></p>"}),3e3),(s,a)=>(i(),p(l(e),{title:l(n)("richText.richText"),message:l(n)("richText.richTextDes")},{default:_((()=>[u(l(t),{modelValue:c.value,"onUpdate:modelValue":a[0]||(a[0]=e=>c.value=e),ref_key:"editorRef",ref:d,onChange:m},null,8,["modelValue"])])),_:1},8,["title","message"]))}});export{n as default};
