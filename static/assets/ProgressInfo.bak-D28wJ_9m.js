import{d as e,a6 as t,aQ as o,r as l,a2 as r,a7 as a,a as n,H as s,aX as i,ai as c,dM as d,aI as u,v as h,a3 as m,b8 as w,aN as f,bq as p,O as g,b5 as y,a4 as x,aS as R,b6 as S,J as b,b1 as v,x as T,bh as H,Z as C,$ as I,Y as E,a8 as M,e as O,aJ as W,aa as K,dN as A,dO as D,C as L,ac as z,bx as k,a5 as F,ag as j,l as P,o as V,i as G}from"./index-C6fb_XFi.js";import{E as N}from"./el-empty-jJjDxScx.js";import{j as $,I as B,S as q,F as U,u as X,B as Y,d as J,A as _,R as Q,g as Z,a as ee,b as te,k as oe,c as le,e as re,C as ae,E as ne,f as se,h as ie,D as ce,v as de,l as ue}from"./el-virtual-list-D7NvYvyu.js";import{d as he}from"./debounce-BwgdhaaK.js";import{c as me,r as we}from"./raf-DGOAeO92.js";import{c as fe}from"./castArray-DRqY4cIf.js";const pe=({name:f,clearCache:p,getColumnPosition:g,getColumnStartIndexForOffset:y,getColumnStopIndexForStartIndex:x,getEstimatedTotalHeight:R,getEstimatedTotalWidth:S,getColumnOffset:b,getRowOffset:v,getRowPosition:T,getRowStartIndexForOffset:H,getRowStopIndexForStartIndex:C,initCache:I,injectToInstance:E,validateProps:M})=>e({name:null!=f?f:"ElVirtualList",props:$,emits:[B,q],setup(e,{emit:f,expose:O,slots:W}){const K=t("vl");M(e);const A=o(),D=l(I(e,A));null==E||E(A,D);const L=l(),z=l(),k=l(),F=l(null),j=l({isScrolling:!1,scrollLeft:r(e.initScrollLeft)?e.initScrollLeft:0,scrollTop:r(e.initScrollTop)?e.initScrollTop:0,updateRequested:!1,xAxisScrollDir:U,yAxisScrollDir:U}),P=X(),V=a((()=>Number.parseInt(`${e.height}`,10))),G=a((()=>Number.parseInt(`${e.width}`,10))),N=a((()=>{const{totalColumn:t,totalRow:o,columnCache:l}=e,{isScrolling:r,xAxisScrollDir:a,scrollLeft:s}=n(j);if(0===t||0===o)return[0,0,0,0];const i=y(e,s,n(D)),c=x(e,i,s,n(D)),d=r&&a!==Y?1:Math.max(1,l),u=r&&a!==U?1:Math.max(1,l);return[Math.max(0,i-d),Math.max(0,Math.min(t-1,c+u)),i,c]})),$=a((()=>{const{totalColumn:t,totalRow:o,rowCache:l}=e,{isScrolling:r,yAxisScrollDir:a,scrollTop:s}=n(j);if(0===t||0===o)return[0,0,0,0];const i=H(e,s,n(D)),c=C(e,i,s,n(D)),d=r&&a!==Y?1:Math.max(1,l),u=r&&a!==U?1:Math.max(1,l);return[Math.max(0,i-d),Math.max(0,Math.min(o-1,c+u)),i,c]})),ae=a((()=>R(e,n(D)))),ne=a((()=>S(e,n(D)))),se=a((()=>{var t;return[{position:"relative",overflow:"hidden",WebkitOverflowScrolling:"touch",willChange:"transform"},{direction:e.direction,height:r(e.height)?`${e.height}px`:e.height,width:r(e.width)?`${e.width}px`:e.width},null!=(t=e.style)?t:{}]})),ie=a((()=>{const e=`${n(ne)}px`;return{height:`${n(ae)}px`,pointerEvents:n(j).isScrolling?"none":void 0,width:e}})),ce=()=>{const{totalColumn:t,totalRow:o}=e;if(t>0&&o>0){const[e,t,o,l]=n(N),[r,a,s,i]=n($);f(B,{columnCacheStart:e,columnCacheEnd:t,rowCacheStart:r,rowCacheEnd:a,columnVisibleStart:o,columnVisibleEnd:l,rowVisibleStart:s,rowVisibleEnd:i})}const{scrollLeft:l,scrollTop:r,updateRequested:a,xAxisScrollDir:s,yAxisScrollDir:i}=n(j);f(q,{xAxisScrollDir:s,scrollLeft:l,yAxisScrollDir:i,scrollTop:r,updateRequested:a})},de=t=>{const{clientHeight:o,clientWidth:l,scrollHeight:r,scrollLeft:a,scrollTop:s,scrollWidth:i}=t.currentTarget,d=n(j);if(d.scrollTop===s&&d.scrollLeft===a)return;let u=a;if(oe(e.direction))switch(Z()){case te:u=-a;break;case re:u=i-l-a}j.value={...d,isScrolling:!0,scrollLeft:u,scrollTop:Math.max(0,Math.min(s,r-o)),updateRequested:!0,xAxisScrollDir:J(d.scrollLeft,u),yAxisScrollDir:J(d.scrollTop,s)},c((()=>ye())),xe(),ce()},ue=(e,t)=>{const o=n(V),l=(ae.value-o)/t*e;pe({scrollTop:Math.min(ae.value-o,l)})},he=(e,t)=>{const o=n(G),l=(ne.value-o)/t*e;pe({scrollLeft:Math.min(ne.value-o,l)})},{onWheel:fe}=(({atXEndEdge:e,atXStartEdge:t,atYEndEdge:o,atYStartEdge:l},r)=>{let a=null,n=0,s=0;const i=(r,a)=>{const n=r<=0&&t.value||r>=0&&e.value,s=a<=0&&l.value||a>=0&&o.value;return n&&s};return{hasReachedEdge:i,onWheel:e=>{me(a);let t=e.deltaX,o=e.deltaY;Math.abs(t)>Math.abs(o)?o=0:t=0,e.shiftKey&&0!==o&&(t=o,o=0),i(n,s)&&i(n+t,s+o)||(n+=t,s+=o,e.preventDefault(),a=we((()=>{r(n,s),n=0,s=0})))}}})({atXStartEdge:a((()=>j.value.scrollLeft<=0)),atXEndEdge:a((()=>j.value.scrollLeft>=ne.value-n(G))),atYStartEdge:a((()=>j.value.scrollTop<=0)),atYEndEdge:a((()=>j.value.scrollTop>=ae.value-n(V)))},((e,t)=>{var o,l,r,a;null==(l=null==(o=z.value)?void 0:o.onMouseUp)||l.call(o),null==(a=null==(r=k.value)?void 0:r.onMouseUp)||a.call(r);const s=n(G),i=n(V);pe({scrollLeft:Math.min(j.value.scrollLeft+e,ne.value-s),scrollTop:Math.min(j.value.scrollTop+t,ae.value-i)})})),pe=({scrollLeft:e=j.value.scrollLeft,scrollTop:t=j.value.scrollTop})=>{e=Math.max(e,0),t=Math.max(t,0);const o=n(j);t===o.scrollTop&&e===o.scrollLeft||(j.value={...o,xAxisScrollDir:J(o.scrollLeft,e),yAxisScrollDir:J(o.scrollTop,t),scrollLeft:e,scrollTop:t,updateRequested:!0},c((()=>ye())),xe(),ce())},ge=(t,o)=>{const{columnWidth:l,direction:r,rowHeight:a}=e,s=P.value(p&&l,p&&a,p&&r),i=`${t},${o}`;if(w(s,i))return s[i];{const[,l]=g(e,o,n(D)),a=n(D),c=oe(r),[d,u]=T(e,t,a),[h]=g(e,o,a);return s[i]={position:"absolute",left:c?void 0:`${l}px`,right:c?`${l}px`:void 0,top:`${u}px`,height:`${d}px`,width:`${h}px`},s[i]}},ye=()=>{j.value.isScrolling=!1,c((()=>{P.value(-1,null,null)}))};s((()=>{if(!i)return;const{initScrollLeft:t,initScrollTop:o}=e,l=n(L);l&&(r(t)&&(l.scrollLeft=t),r(o)&&(l.scrollTop=o)),ce()}));const xe=()=>{const{direction:t}=e,{scrollLeft:o,scrollTop:l,updateRequested:r}=n(j),a=n(L);if(r&&a){if(t===Q)switch(Z()){case te:a.scrollLeft=-o;break;case ee:a.scrollLeft=o;break;default:{const{clientWidth:e,scrollWidth:t}=a;a.scrollLeft=t-e-o;break}}else a.scrollLeft=Math.max(0,o);a.scrollTop=Math.max(0,l)}},{resetAfterColumnIndex:Re,resetAfterRowIndex:Se,resetAfter:be}=A.proxy;O({windowRef:L,innerRef:F,getItemStyleCache:P,scrollTo:pe,scrollToItem:(t=0,o=0,l=_)=>{const r=n(j);o=Math.max(0,Math.min(o,e.totalColumn-1)),t=Math.max(0,Math.min(t,e.totalRow-1));const a=d(K.namespace.value),s=n(D),i=R(e,s),c=S(e,s);pe({scrollLeft:b(e,o,l,r.scrollLeft,s,c>e.width?a:0),scrollTop:v(e,t,l,r.scrollTop,s,i>e.height?a:0)})},states:j,resetAfterColumnIndex:Re,resetAfterRowIndex:Se,resetAfter:be});const ve=()=>{const t=u(e.innerElement),o=(()=>{var t;const[o,l]=n(N),[r,a]=n($),{data:s,totalColumn:i,totalRow:c,useIsScrolling:d,itemKey:u}=e,h=[];if(c>0&&i>0)for(let e=r;e<=a;e++)for(let r=o;r<=l;r++)h.push(null==(t=W.default)?void 0:t.call(W,{columnIndex:r,data:s,key:u({columnIndex:r,data:s,rowIndex:e}),isScrolling:d?n(j).isScrolling:void 0,style:ge(e,r),rowIndex:e}));return h})();return[h(t,{style:n(ie),ref:F},m(t)?o:{default:()=>o})]};return()=>{const t=u(e.containerElement),{horizontalScrollbar:o,verticalScrollbar:l}=(()=>{const{scrollbarAlwaysOn:t,scrollbarStartGap:o,scrollbarEndGap:l,totalColumn:r,totalRow:a}=e,s=n(G),i=n(V),c=n(ne),d=n(ae),{scrollLeft:u,scrollTop:m}=n(j);return{horizontalScrollbar:h(le,{ref:z,alwaysOn:t,startGap:o,endGap:l,class:K.e("horizontal"),clientSize:s,layout:"horizontal",onScroll:he,ratio:100*s/c,scrollFrom:u/(c-s),total:a,visible:!0}),verticalScrollbar:h(le,{ref:k,alwaysOn:t,startGap:o,endGap:l,class:K.e("vertical"),clientSize:i,layout:"vertical",onScroll:ue,ratio:100*i/d,scrollFrom:m/(d-i),total:r,visible:!0})}})(),r=ve();return h("div",{key:0,class:K.e("wrapper"),role:e.role},[h(t,{class:e.className,style:n(se),onScroll:de,onWheel:fe,ref:L},m(t)?r:{default:()=>r}),o,l])}}}),ge=pe({name:"ElFixedSizeGrid",getColumnPosition:({columnWidth:e},t)=>[e,t*e],getRowPosition:({rowHeight:e},t)=>[e,t*e],getEstimatedTotalHeight:({totalRow:e,rowHeight:t})=>t*e,getEstimatedTotalWidth:({totalColumn:e,columnWidth:t})=>t*e,getColumnOffset:({totalColumn:e,columnWidth:t,width:o},l,r,a,n,s)=>{o=Number(o);const i=Math.max(0,e*t-o),c=Math.min(i,l*t),d=Math.max(0,l*t-o+s+t);switch("smart"===r&&(r=a>=d-o&&a<=c+o?_:ae),r){case se:return c;case ne:return d;case ae:{const e=Math.round(d+(c-d)/2);return e<Math.ceil(o/2)?0:e>i+Math.floor(o/2)?i:e}default:return a>=d&&a<=c?a:d>c||a<d?d:c}},getRowOffset:({rowHeight:e,height:t,totalRow:o},l,r,a,n,s)=>{t=Number(t);const i=Math.max(0,o*e-t),c=Math.min(i,l*e),d=Math.max(0,l*e-t+s+e);switch(r===ie&&(r=a>=d-t&&a<=c+t?_:ae),r){case se:return c;case ne:return d;case ae:{const e=Math.round(d+(c-d)/2);return e<Math.ceil(t/2)?0:e>i+Math.floor(t/2)?i:e}default:return a>=d&&a<=c?a:d>c||a<d?d:c}},getColumnStartIndexForOffset:({columnWidth:e,totalColumn:t},o)=>Math.max(0,Math.min(t-1,Math.floor(o/e))),getColumnStopIndexForStartIndex:({columnWidth:e,totalColumn:t,width:o},l,r)=>{const a=l*e,n=Math.ceil((o+r-a)/e);return Math.max(0,Math.min(t-1,l+n-1))},getRowStartIndexForOffset:({rowHeight:e,totalRow:t},o)=>Math.max(0,Math.min(t-1,Math.floor(o/e))),getRowStopIndexForStartIndex:({rowHeight:e,totalRow:t,height:o},l,r)=>{const a=l*e,n=Math.ceil((o+r-a)/e);return Math.max(0,Math.min(t-1,l+n-1))},initCache:()=>{},clearCache:!0,validateProps:({columnWidth:e,rowHeight:t})=>{}}),{max:ye,min:xe,floor:Re}=Math,Se={column:"columnWidth",row:"rowHeight"},be={column:"lastVisitedColumnIndex",row:"lastVisitedRowIndex"},ve=(e,t,o,l)=>{const[r,a,n]=[o[l],e[Se[l]],o[be[l]]];if(t>n){let e=0;if(n>=0){const t=r[n];e=t.offset+t.size}for(let o=n+1;o<=t;o++){const t=a(o);r[o]={offset:e,size:t},e+=t}o[be[l]]=t}return r[t]},Te=(e,t,o,l,r,a)=>{for(;o<=l;){const n=o+Re((l-o)/2),s=ve(e,n,t,a).offset;if(s===r)return n;s<r?o=n+1:l=n-1}return ye(0,o-1)},He=(e,t,o,l)=>{const[r,a]=[t[l],t[be[l]]];return(a>0?r[a].offset:0)>=o?Te(e,t,0,a,o,l):((e,t,o,l,r)=>{const a="column"===r?e.totalColumn:e.totalRow;let n=1;for(;o<a&&ve(e,o,t,r).offset<l;)o+=n,n*=2;return Te(e,t,Re(o/2),xe(o,a-1),l,r)})(e,t,ye(0,a),o,l)},Ce=({totalRow:e},{estimatedRowHeight:t,lastVisitedRowIndex:o,row:l})=>{let r=0;if(o>=e&&(o=e-1),o>=0){const e=l[o];r=e.offset+e.size}return r+(e-o-1)*t},Ie=({totalColumn:e},{column:t,estimatedColumnWidth:o,lastVisitedColumnIndex:l})=>{let r=0;if(l>e&&(l=e-1),l>=0){const e=t[l];r=e.offset+e.size}return r+(e-l-1)*o},Ee={column:Ie,row:Ce},Me=(e,t,o,l,r,a,n)=>{const[s,i]=["row"===a?e.height:e.width,Ee[a]],c=ve(e,t,r,a),d=i(e,r),u=ye(0,xe(d-s,c.offset)),h=ye(0,c.offset-s+n+c.size);switch(o===ie&&(o=l>=h-s&&l<=u+s?_:ae),o){case se:return u;case ne:return h;case ae:return Math.round(h+(u-h)/2);default:return l>=h&&l<=u?l:h>u||l<h?h:u}},Oe=pe({name:"ElDynamicSizeGrid",getColumnPosition:(e,t,o)=>{const l=ve(e,t,o,"column");return[l.size,l.offset]},getRowPosition:(e,t,o)=>{const l=ve(e,t,o,"row");return[l.size,l.offset]},getColumnOffset:(e,t,o,l,r,a)=>Me(e,t,o,l,r,"column",a),getRowOffset:(e,t,o,l,r,a)=>Me(e,t,o,l,r,"row",a),getColumnStartIndexForOffset:(e,t,o)=>He(e,o,t,"column"),getColumnStopIndexForStartIndex:(e,t,o,l)=>{const r=ve(e,t,l,"column"),a=o+e.width;let n=r.offset+r.size,s=t;for(;s<e.totalColumn-1&&n<a;)s++,n+=ve(e,t,l,"column").size;return s},getEstimatedTotalHeight:Ce,getEstimatedTotalWidth:Ie,getRowStartIndexForOffset:(e,t,o)=>He(e,o,t,"row"),getRowStopIndexForStartIndex:(e,t,o,l)=>{const{totalRow:r,height:a}=e,n=ve(e,t,l,"row"),s=o+a;let i=n.size+n.offset,c=t;for(;c<r-1&&i<s;)c++,i+=ve(e,c,l,"row").size;return c},injectToInstance:(e,t)=>{const o=({columnIndex:o,rowIndex:l},a)=>{var n,s;a=!!f(a)||a,r(o)&&(t.value.lastVisitedColumnIndex=Math.min(t.value.lastVisitedColumnIndex,o-1)),r(l)&&(t.value.lastVisitedRowIndex=Math.min(t.value.lastVisitedRowIndex,l-1)),null==(n=e.exposed)||n.getItemStyleCache.value(-1,null,null),a&&(null==(s=e.proxy)||s.$forceUpdate())};Object.assign(e.proxy,{resetAfterColumnIndex:(e,t)=>{o({columnIndex:e},t)},resetAfterRowIndex:(e,t)=>{o({rowIndex:e},t)},resetAfter:o})},initCache:({estimatedColumnWidth:e=ce,estimatedRowHeight:t=ce})=>({column:{},estimatedColumnWidth:e,estimatedRowHeight:t,lastVisitedColumnIndex:-1,lastVisitedRowIndex:-1,row:{}}),clearCache:!1,validateProps:({columnWidth:e,rowHeight:t})=>{}});var We=(e=>(e.ASC="asc",e.DESC="desc",e))(We||{}),Ke=(e=>(e.CENTER="center",e.RIGHT="right",e))(Ke||{}),Ae=(e=>(e.LEFT="left",e.RIGHT="right",e))(Ae||{});const De={asc:"desc",desc:"asc"},Le=Symbol("placeholder"),ze=(e,t,o)=>{var l;const r={flexGrow:0,flexShrink:0,...o?{}:{flexGrow:e.flexGrow||0,flexShrink:e.flexShrink||1}};o||(r.flexShrink=1);const a={...null!=(l=e.style)?l:{},...r,flexBasis:"auto",width:e.width};return t||(e.maxWidth&&(a.maxWidth=e.maxWidth),e.minWidth&&(a.minWidth=e.minWidth)),a};const ke=(e,{mainTableRef:t,leftTableRef:s,rightTableRef:i})=>{const c=o(),{emit:d}=c,u=y(!1),h=y(null),m=l(e.defaultExpandedRowKeys||[]),w=l(-1),f=y(null),p=l({}),g=l({}),x=y({}),R=y({}),S=y({}),b=a((()=>r(e.estimatedRowHeight)));const v=he((()=>{var e,o,l,r;u.value=!0,p.value={...n(p),...n(g)},T(n(f),!1),g.value={},f.value=null,null==(e=t.value)||e.forceUpdate(),null==(o=s.value)||o.forceUpdate(),null==(l=i.value)||l.forceUpdate(),null==(r=c.proxy)||r.$forceUpdate(),u.value=!1}),0);function T(e,o=!1){n(b)&&[t,s,i].forEach((t=>{const l=n(t);l&&l.resetAfterRowIndex(e,o)}))}return{hoveringRowKey:h,expandedRowKeys:m,lastRenderedRowIndex:w,isDynamic:b,isResetting:u,rowHeights:p,resetAfterIndex:T,onRowExpanded:function({expanded:t,rowData:o,rowIndex:l,rowKey:r}){var a,s;const i=[...n(m)],c=i.indexOf(r);t?-1===c&&i.push(r):c>-1&&i.splice(c,1),m.value=i,d("update:expandedRowKeys",i),null==(a=e.onRowExpand)||a.call(e,{expanded:t,rowData:o,rowIndex:l,rowKey:r}),null==(s=e.onExpandedRowsChange)||s.call(e,i)},onRowHovered:function({hovered:e,rowKey:t}){h.value=e?t:null},onRowsRendered:function(t){var o;null==(o=e.onRowsRendered)||o.call(e,t),t.rowCacheEnd>n(w)&&(w.value=t.rowCacheEnd)},onRowHeightChange:function({rowKey:e,height:t,rowIndex:o},l){l?l===Ae.RIGHT?S.value[e]=t:x.value[e]=t:R.value[e]=t;const r=Math.max(...[x,S,R].map((t=>t.value[e]||0)));n(p)[e]!==r&&(!function(e,t,o){const l=n(f);(null===l||l>o)&&(f.value=o),g.value[e]=t}(e,r,o),v())}}},Fe=(e,t)=>e+t,je=e=>x(e)?e.reduce(Fe,0):e,Pe=(e,t,o={})=>R(e)?e(t):null!=e?e:o,Ve=e=>(["width","maxWidth","minWidth","height"].forEach((t=>{e[t]=S(e[t])})),e),Ge=e=>b(e)?t=>h(e,t):e;function Ne(e){const t=l(),o=l(),s=l(),{columns:i,columnsStyles:c,columnsTotalWidth:d,fixedColumnsOnLeft:u,fixedColumnsOnRight:h,hasFixedColumns:m,mainColumns:w,onColumnSorted:f}=function(e,t,o){const l=a((()=>n(t).filter((e=>!e.hidden)))),r=a((()=>n(l).filter((e=>"left"===e.fixed||!0===e.fixed)))),s=a((()=>n(l).filter((e=>"right"===e.fixed)))),i=a((()=>n(l).filter((e=>!e.fixed)))),c=a((()=>{const e=[];return n(r).forEach((t=>{e.push({...t,placeholderSign:Le})})),n(i).forEach((t=>{e.push(t)})),n(s).forEach((t=>{e.push({...t,placeholderSign:Le})})),e})),d=a((()=>n(r).length||n(s).length)),u=a((()=>n(t).reduce(((t,l)=>(t[l.key]=ze(l,n(o),e.fixed),t)),{}))),h=a((()=>n(l).reduce(((e,t)=>e+t.width),0))),m=e=>n(t).find((t=>t.key===e));return{columns:t,columnsStyles:u,columnsTotalWidth:h,fixedColumnsOnLeft:r,fixedColumnsOnRight:s,hasFixedColumns:d,mainColumns:c,normalColumns:i,visibleColumns:l,getColumn:m,getColumnStyle:e=>n(u)[e],updateColumnWidth:(e,t)=>{e.width=t},onColumnSorted:function(t){var o;const{key:l}=t.currentTarget.dataset;if(!l)return;const{sortState:r,sortBy:a}=e;let n=We.ASC;n=p(r)?De[r[l]]:De[a.order],null==(o=e.onColumnSort)||o.call(e,{column:m(l),key:l,order:n})}}}(e,H(e,"columns"),H(e,"fixed")),{scrollTo:R,scrollToLeft:b,scrollToTop:v,scrollToRow:T,onScroll:C,onVerticalScroll:I,scrollPos:E}=((e,{mainTableRef:t,leftTableRef:o,rightTableRef:r,onMaybeEndReached:a})=>{const s=l({scrollLeft:0,scrollTop:0});function i(e){var l,a,n;const{scrollTop:s}=e;null==(l=t.value)||l.scrollTo(e),null==(a=o.value)||a.scrollToTop(s),null==(n=r.value)||n.scrollToTop(s)}function c(e){s.value=e,i(e)}function d(e){s.value.scrollTop=e,i(n(s))}return g((()=>n(s).scrollTop),((e,t)=>{e>t&&a()})),{scrollPos:s,scrollTo:c,scrollToLeft:function(e){var o,l;s.value.scrollLeft=e,null==(l=null==(o=t.value)?void 0:o.scrollTo)||l.call(o,n(s))},scrollToTop:d,scrollToRow:function(e,o="auto"){var l;null==(l=t.value)||l.scrollToRow(e,o)},onScroll:function(t){var o;c(t),null==(o=e.onScroll)||o.call(e,t)},onVerticalScroll:function({scrollTop:e}){const{scrollTop:t}=n(s);e!==t&&d(e)}}})(e,{mainTableRef:t,leftTableRef:o,rightTableRef:s,onMaybeEndReached:function(){const{onEndReached:t}=e;if(!t)return;const{scrollTop:o}=n(E),l=n(X),r=n(Y),a=l-(o+r)+e.hScrollbarSize;n(W)>=0&&l===o+n($)-n(Z)&&t(a)}}),{expandedRowKeys:M,hoveringRowKey:O,lastRenderedRowIndex:W,isDynamic:K,isResetting:A,rowHeights:D,resetAfterIndex:L,onRowExpanded:z,onRowHeightChange:k,onRowHovered:F,onRowsRendered:j}=ke(e,{mainTableRef:t,leftTableRef:o,rightTableRef:s}),{data:P,depthMap:V}=((e,{expandedRowKeys:t,lastRenderedRowIndex:o,resetAfterIndex:r})=>{const s=l({}),i=a((()=>{const o={},{data:l,rowKey:r}=e,a=n(t);if(!a||!a.length)return l;const i=[],c=new Set;a.forEach((e=>c.add(e)));let d=l.slice();for(d.forEach((e=>o[e[r]]=0));d.length>0;){const e=d.shift();i.push(e),c.has(e[r])&&Array.isArray(e.children)&&e.children.length>0&&(d=[...e.children,...d],e.children.forEach((t=>o[t[r]]=o[e[r]]+1)))}return s.value=o,i})),c=a((()=>{const{data:t,expandColumnKey:o}=e;return o?n(i):t}));return g(c,((e,t)=>{e!==t&&(o.value=-1,r(0,!0))})),{data:c,depthMap:s}})(e,{expandedRowKeys:M,lastRenderedRowIndex:W,resetAfterIndex:L}),{bodyWidth:G,fixedTableHeight:N,mainTableHeight:$,leftTableWidth:B,rightTableWidth:q,headerWidth:U,rowsHeight:X,windowHeight:Y,footerHeight:J,emptyStyle:_,rootStyle:Q,headerHeight:Z}=((e,{columnsTotalWidth:t,data:o,fixedColumnsOnLeft:l,fixedColumnsOnRight:s})=>{const i=a((()=>{const{fixed:o,width:l,vScrollbarSize:r}=e,a=l-r;return o?Math.max(Math.round(n(t)),a):a})),c=a((()=>n(i)+e.vScrollbarSize)),d=a((()=>{const{height:t=0,maxHeight:o=0,footerHeight:l,hScrollbarSize:r}=e;if(o>0){const e=n(g),t=n(u),a=n(p)+e+t+r;return Math.min(a,o-l)}return t-l})),u=a((()=>{const{rowHeight:t,estimatedRowHeight:l}=e,a=n(o);return r(l)?a.length*l:a.length*t})),h=a((()=>{const{maxHeight:t}=e,o=n(d);if(r(t)&&t>0)return o;const l=n(u)+n(p)+n(g);return Math.min(o,l)})),m=e=>e.width,w=a((()=>je(n(l).map(m)))),f=a((()=>je(n(s).map(m)))),p=a((()=>je(e.headerHeight))),g=a((()=>{var t;return((null==(t=e.fixedData)?void 0:t.length)||0)*e.rowHeight})),y=a((()=>n(d)-n(p)-n(g))),x=a((()=>{const{style:t={},height:o,width:l}=e;return Ve({...t,height:o,width:l})})),R=a((()=>Ve({height:e.footerHeight}))),b=a((()=>({top:S(n(p)),bottom:S(e.footerHeight),width:S(e.width)})));return{bodyWidth:i,fixedTableHeight:h,mainTableHeight:d,leftTableWidth:w,rightTableWidth:f,headerWidth:c,rowsHeight:u,windowHeight:y,footerHeight:R,emptyStyle:b,rootStyle:x,headerHeight:p}})(e,{columnsTotalWidth:d,data:P,fixedColumnsOnLeft:u,fixedColumnsOnRight:h}),ee=y(!1),te=l(),oe=a((()=>{const t=0===n(P).length;return x(e.fixedData)?0===e.fixedData.length&&t:t}));return g((()=>e.expandedRowKeys),(e=>M.value=e),{deep:!0}),{columns:i,containerRef:te,mainTableRef:t,leftTableRef:o,rightTableRef:s,isDynamic:K,isResetting:A,isScrolling:ee,hoveringRowKey:O,hasFixedColumns:m,columnsStyles:c,columnsTotalWidth:d,data:P,expandedRowKeys:M,depthMap:V,fixedColumnsOnLeft:u,fixedColumnsOnRight:h,mainColumns:w,bodyWidth:G,emptyStyle:_,rootStyle:Q,headerWidth:U,footerHeight:J,mainTableHeight:$,fixedTableHeight:N,leftTableWidth:B,rightTableWidth:q,showEmpty:oe,getRowHeight:function(t){const{estimatedRowHeight:o,rowHeight:l,rowKey:r}=e;return o?n(D)[n(P)[t][r]]||o:l},onColumnSorted:f,onRowHovered:F,onRowExpanded:z,onRowsRendered:j,onRowHeightChange:k,scrollTo:R,scrollToLeft:b,scrollToTop:v,scrollToRow:T,onScroll:C,onVerticalScroll:I}}const $e=Symbol("tableV2"),Be=String,qe={type:C(Array),required:!0},Ue={type:C(Array)},Xe={...Ue,required:!0},Ye=String,Je={type:C(Array),default:()=>I([])},_e={type:Number,required:!0},Qe={type:C([String,Number,Symbol]),default:"id"},Ze={type:C(Object)},et=E({class:String,columns:qe,columnsStyles:{type:C(Object),required:!0},depth:Number,expandColumnKey:Ye,estimatedRowHeight:{...$.estimatedRowHeight,default:void 0},isScrolling:Boolean,onRowExpand:{type:C(Function)},onRowHover:{type:C(Function)},onRowHeightChange:{type:C(Function)},rowData:{type:C(Object),required:!0},rowEventHandlers:{type:C(Object)},rowIndex:{type:Number,required:!0},rowKey:Qe,style:{type:C(Object)}}),tt={type:Number,required:!0},ot=E({class:String,columns:qe,fixedHeaderData:{type:C(Array)},headerData:{type:C(Array),required:!0},headerHeight:{type:C([Number,Array]),default:50},rowWidth:tt,rowHeight:{type:Number,default:50},height:tt,width:tt}),lt=E({columns:qe,data:Xe,fixedData:Ue,estimatedRowHeight:et.estimatedRowHeight,width:_e,height:_e,headerWidth:_e,headerHeight:ot.headerHeight,bodyWidth:_e,rowHeight:_e,cache:de.cache,useIsScrolling:Boolean,scrollbarAlwaysOn:$.scrollbarAlwaysOn,scrollbarStartGap:$.scrollbarStartGap,scrollbarEndGap:$.scrollbarEndGap,class:Be,style:Ze,containerStyle:Ze,getRowHeight:{type:C(Function),required:!0},rowKey:et.rowKey,onRowsRendered:{type:C(Function)},onScroll:{type:C(Function)}}),rt=E({cache:lt.cache,estimatedRowHeight:et.estimatedRowHeight,rowKey:Qe,headerClass:{type:C([String,Function])},headerProps:{type:C([Object,Function])},headerCellProps:{type:C([Object,Function])},headerHeight:ot.headerHeight,footerHeight:{type:Number,default:0},rowClass:{type:C([String,Function])},rowProps:{type:C([Object,Function])},rowHeight:{type:Number,default:50},cellProps:{type:C([Object,Function])},columns:qe,data:Xe,dataGetter:{type:C(Function)},fixedData:Ue,expandColumnKey:et.expandColumnKey,expandedRowKeys:Je,defaultExpandedRowKeys:Je,class:Be,fixed:Boolean,style:{type:C(Object)},width:_e,height:_e,maxHeight:Number,useIsScrolling:Boolean,indentSize:{type:Number,default:12},iconSize:{type:Number,default:12},hScrollbarSize:$.hScrollbarSize,vScrollbarSize:$.vScrollbarSize,scrollbarAlwaysOn:ue.alwaysOn,sortBy:{type:C(Object),default:()=>({})},sortState:{type:C(Object),default:void 0},onColumnSort:{type:C(Function)},onExpandedRowsChange:{type:C(Function)},onEndReached:{type:C(Function)},onRowExpand:et.onRowExpand,onScroll:lt.onScroll,onRowsRendered:lt.onRowsRendered,rowEventHandlers:et.rowEventHandlers}),at=(e,{slots:t})=>{var o;const{cellData:l,style:r}=e,a=(null==(o=null==l?void 0:l.toString)?void 0:o.call(l))||"",n=M(t,"default",e,(()=>[a]));return O("div",{class:e.class,title:a,style:r},[n])};at.displayName="ElTableV2Cell",at.inheritAttrs=!1;const nt=(e,{slots:t})=>M(t,"default",e,(()=>{var t,o;return[O("div",{class:e.class,title:null==(t=e.column)?void 0:t.title},[null==(o=e.column)?void 0:o.title])]}));nt.displayName="ElTableV2HeaderCell",nt.inheritAttrs=!1;const st=E({class:String,columns:qe,columnsStyles:{type:C(Object),required:!0},headerIndex:Number,style:{type:C(Object)}}),it=e({name:"ElTableV2HeaderRow",props:st,setup:(e,{slots:t})=>()=>{const{columns:o,columnsStyles:l,headerIndex:r,style:a}=e;let n=o.map(((e,a)=>t.cell({columns:o,column:e,columnIndex:a,headerIndex:r,style:l[e.key]})));return t.header&&(n=t.header({cells:n.map((e=>x(e)&&1===e.length?e[0]:e)),columns:o,headerIndex:r})),O("div",{class:e.class,style:a,role:"row"},[n])}}),ct=e({name:"ElTableV2Header",props:ot,setup(e,{slots:o,expose:r}){const s=t("table-v2"),i=l(),d=a((()=>Ve({width:e.width,height:e.height}))),u=a((()=>Ve({width:e.rowWidth,height:e.height}))),h=a((()=>fe(n(e.headerHeight)))),m=()=>{const t=s.e("fixed-header-row"),{columns:l,fixedHeaderData:r,rowHeight:a}=e;return null==r?void 0:r.map(((e,r)=>{var n;const s=Ve({height:a,width:"100%"});return null==(n=o.fixed)?void 0:n.call(o,{class:t,columns:l,rowData:e,rowIndex:-(r+1),style:s})}))},w=()=>{const t=s.e("dynamic-header-row"),{columns:l}=e;return n(h).map(((e,r)=>{var a;const n=Ve({width:"100%",height:e});return null==(a=o.dynamic)?void 0:a.call(o,{class:t,columns:l,headerIndex:r,style:n})}))};return r({scrollToLeft:e=>{const t=n(i);c((()=>{(null==t?void 0:t.scroll)&&t.scroll({left:e})}))}}),()=>{if(!(e.height<=0))return O("div",{ref:i,class:e.class,style:n(d),role:"rowgroup"},[O("div",{style:n(u),class:s.e("header")},[w(),m()])])}}}),dt=e=>{const{isScrolling:t}=K($e),o=l(!1),i=l(),d=a((()=>r(e.estimatedRowHeight)&&e.rowIndex>=0)),u=a((()=>{const{rowData:t,rowIndex:o,rowKey:l,onRowHover:r}=e,a=e.rowEventHandlers||{},n={};return Object.entries(a).forEach((([e,r])=>{R(r)&&(n[e]=e=>{r({event:e,rowData:t,rowIndex:o,rowKey:l})})})),r&&[{name:"onMouseleave",hovered:!1},{name:"onMouseenter",hovered:!0}].forEach((({name:e,hovered:a})=>{const s=n[e];n[e]=e=>{r({event:e,hovered:a,rowData:t,rowIndex:o,rowKey:l}),null==s||s(e)}})),n}));return s((()=>{n(d)&&((t=!1)=>{const l=n(i);if(!l)return;const{columns:r,onRowHeightChange:a,rowKey:s,rowIndex:d,style:u}=e,{height:h}=l.getBoundingClientRect();o.value=!0,c((()=>{if(t||h!==Number.parseInt(u.height)){const e=r[0],t=(null==e?void 0:e.placeholderSign)===Le;null==a||a({rowKey:s,height:h,rowIndex:d},e&&!t&&e.fixed)}}))})(!0)})),{isScrolling:t,measurable:d,measured:o,rowRef:i,eventHandlers:u,onExpand:t=>{const{onRowExpand:o,rowData:l,rowIndex:r,rowKey:a}=e;null==o||o({expanded:t,rowData:l,rowIndex:r,rowKey:a})}}},ut=e({name:"ElTableV2TableRow",props:et,setup(e,{expose:t,slots:o,attrs:l}){const{eventHandlers:r,isScrolling:a,measurable:s,measured:i,rowRef:c,onExpand:d}=dt(e);return t({onExpand:d}),()=>{const{columns:t,columnsStyles:u,expandColumnKey:h,depth:m,rowData:w,rowIndex:f,style:p}=e;let g=t.map(((e,l)=>{const r=x(w.children)&&w.children.length>0&&e.key===h;return o.cell({column:e,columns:t,columnIndex:l,depth:m,style:u[e.key],rowData:w,rowIndex:f,isScrolling:n(a),expandIconProps:r?{rowData:w,rowIndex:f,onExpand:d}:void 0})}));if(o.row&&(g=o.row({cells:g.map((e=>x(e)&&1===e.length?e[0]:e)),style:p,columns:t,depth:m,rowData:w,rowIndex:f,isScrolling:n(a)})),n(s)){const{height:t,...o}=p||{},a=n(i);return O("div",W({ref:c,class:e.class,style:a?p:o,role:"row"},l,n(r)),[g])}return O("div",W(l,{ref:c,class:e.class,style:p,role:"row"},n(r)),[g])}}}),ht=e=>{const{sortOrder:t}=e;return O(L,{size:14,class:e.class},{default:()=>[t===We.ASC?O(A,null,null):O(D,null,null)]})},mt=e=>{const{expanded:t,expandable:o,onExpand:l,style:r,size:a}=e,n={onClick:o?()=>l(!t):void 0,class:e.class};return O(L,W(n,{size:a,style:r}),{default:()=>[O(z,null,null)]})},wt=e({name:"ElTableV2Grid",props:lt,setup(e,{slots:t,expose:o}){const{ns:s}=K($e),{bodyRef:i,fixedRowHeight:c,gridHeight:d,hasHeader:u,headerRef:h,headerHeight:m,totalHeight:w,forceUpdate:f,itemKey:g,onItemRendered:y,resetAfterRowIndex:x,scrollTo:R,scrollToTop:S,scrollToRow:b}=(e=>{const t=l(),o=l(),r=a((()=>{const{data:t,rowHeight:o,estimatedRowHeight:l}=e;if(!l)return t.length*o})),s=a((()=>{const{fixedData:t,rowHeight:o}=e;return((null==t?void 0:t.length)||0)*o})),i=a((()=>je(e.headerHeight))),c=a((()=>{const{height:t}=e;return Math.max(0,t-n(i)-n(s))})),d=a((()=>n(i)+n(s)>0));return{bodyRef:o,forceUpdate:function(){var e,l;null==(e=n(o))||e.$forceUpdate(),null==(l=n(t))||l.$forceUpdate()},fixedRowHeight:s,gridHeight:c,hasHeader:d,headerHeight:i,headerRef:t,totalHeight:r,itemKey:({data:t,rowIndex:o})=>t[o][e.rowKey],onItemRendered:function({rowCacheStart:t,rowCacheEnd:o,rowVisibleStart:l,rowVisibleEnd:r}){var a;null==(a=e.onRowsRendered)||a.call(e,{rowCacheStart:t,rowCacheEnd:o,rowVisibleStart:l,rowVisibleEnd:r})},resetAfterRowIndex:function(e,t){var l;null==(l=o.value)||l.resetAfterRowIndex(e,t)},scrollTo:function(e,l){const r=n(t),a=n(o);r&&a&&(p(e)?(r.scrollToLeft(e.scrollLeft),a.scrollTo(e)):(r.scrollToLeft(e),a.scrollTo({scrollLeft:e,scrollTop:l})))},scrollToTop:function(e){var t;null==(t=n(o))||t.scrollTo({scrollTop:e})},scrollToRow:function(e,t){var l;null==(l=n(o))||l.scrollToItem(e,1,t)}}})(e);o({forceUpdate:f,totalHeight:w,scrollTo:R,scrollToTop:S,scrollToRow:b,resetAfterRowIndex:x});const v=()=>e.bodyWidth;return()=>{const{cache:o,columns:l,data:a,fixedData:w,useIsScrolling:f,scrollbarAlwaysOn:p,scrollbarEndGap:x,scrollbarStartGap:R,style:S,rowHeight:b,bodyWidth:T,estimatedRowHeight:H,headerWidth:C,height:I,width:E,getRowHeight:M,onScroll:W}=e,K=r(H),A=K?Oe:ge,D=n(m);return O("div",{role:"table",class:[s.e("table"),e.class],style:S},[O(A,{ref:i,data:a,useIsScrolling:f,itemKey:g,columnCache:0,columnWidth:K?v:T,totalColumn:1,totalRow:a.length,rowCache:o,rowHeight:K?M:b,width:E,height:n(d),class:s.e("body"),role:"rowgroup",scrollbarStartGap:R,scrollbarEndGap:x,scrollbarAlwaysOn:p,onScroll:W,onItemRendered:y,perfMode:!1},{default:e=>{var o;const r=a[e.rowIndex];return null==(o=t.row)?void 0:o.call(t,{...e,columns:l,rowData:r})}}),n(u)&&O(ct,{ref:h,class:s.e("header-wrapper"),columns:l,headerData:a,headerHeight:e.headerHeight,fixedHeaderData:w,rowWidth:C,rowHeight:b,width:E,height:Math.min(D+n(c),I)},{dynamic:t.header,fixed:t.row})])}}});const ft=(e,{slots:t})=>{const{mainTableRef:o,...l}=e;return O(wt,W({ref:o},l),"function"==typeof(r=t)||"[object Object]"===Object.prototype.toString.call(r)&&!b(r)?t:{default:()=>[t]});var r};const pt=(e,{slots:t})=>{if(!e.columns.length)return;const{leftTableRef:o,...l}=e;return O(wt,W({ref:o},l),"function"==typeof(r=t)||"[object Object]"===Object.prototype.toString.call(r)&&!b(r)?t:{default:()=>[t]});var r};const gt=(e,{slots:t})=>{if(!e.columns.length)return;const{rightTableRef:o,...l}=e;return O(wt,W({ref:o},l),"function"==typeof(r=t)||"[object Object]"===Object.prototype.toString.call(r)&&!b(r)?t:{default:()=>[t]});var r};const yt=(e,{slots:t})=>{const{columns:o,columnsStyles:l,depthMap:r,expandColumnKey:a,expandedRowKeys:n,estimatedRowHeight:s,hasFixedColumns:i,hoveringRowKey:c,rowData:d,rowIndex:u,style:h,isScrolling:m,rowProps:w,rowClass:f,rowKey:p,rowEventHandlers:g,ns:y,onRowHovered:x,onRowExpanded:R}=e,S=Pe(f,{columns:o,rowData:d,rowIndex:u},""),v=Pe(w,{columns:o,rowData:d,rowIndex:u}),T=d[p],H=r[T]||0,C=Boolean(a),I=u<0,E={...v,columns:o,columnsStyles:l,class:[y.e("row"),S,{[y.e(`row-depth-${H}`)]:C&&u>=0,[y.is("expanded")]:C&&n.includes(T),[y.is("hovered")]:!m&&T===c,[y.is("fixed")]:!H&&I,[y.is("customized")]:Boolean(t.row)}],depth:H,expandColumnKey:a,estimatedRowHeight:I?void 0:s,isScrolling:m,rowIndex:u,rowData:d,rowKey:T,rowEventHandlers:g,style:h};return O(ut,W(E,{onRowHover:i?x:void 0,onRowExpand:R}),"function"==typeof(M=t)||"[object Object]"===Object.prototype.toString.call(M)&&!b(M)?t:{default:()=>[t]});var M},xt=({columns:e,column:t,columnIndex:o,depth:l,expandIconProps:r,isScrolling:a,rowData:n,rowIndex:s,style:i,expandedRowKeys:c,ns:d,cellProps:u,expandColumnKey:h,indentSize:m,iconSize:w,rowKey:f},{slots:g})=>{const y=Ve(i);if(t.placeholderSign===Le)return O("div",{class:d.em("row-cell","placeholder"),style:y},null);const{cellRenderer:x,dataKey:S,dataGetter:b}=t,v=R(b)?b({columns:e,column:t,columnIndex:o,rowData:n,rowIndex:s}):k(n,null!=S?S:""),T=Pe(u,{cellData:v,columns:e,column:t,columnIndex:o,rowIndex:s,rowData:n}),H={class:d.e("cell-text"),columns:e,column:t,columnIndex:o,cellData:v,isScrolling:a,rowData:n,rowIndex:s},C=Ge(x),I=C?C(H):M(g,"default",H,(()=>[O(at,H,null)])),E=[d.e("row-cell"),t.class,t.align===Ke.CENTER&&d.is("align-center"),t.align===Ke.RIGHT&&d.is("align-right")],K=s>=0&&h&&t.key===h,A=s>=0&&c.includes(n[f]);let D;const L=`margin-inline-start: ${l*m}px;`;return K&&(D=p(r)?O(mt,W(r,{class:[d.e("expand-icon"),d.is("expanded",A)],size:w,expanded:A,style:L,expandable:!0}),null):O("div",{style:[L,`width: ${w}px; height: ${w}px;`].join(" ")},null)),O("div",W({class:E,style:y},T,{role:"cell"}),[D,I])};xt.inheritAttrs=!1;const Rt=({columns:e,columnsStyles:t,headerIndex:o,style:l,headerClass:r,headerProps:a,ns:n},{slots:s})=>{const i={columns:e,headerIndex:o},c=[n.e("header-row"),Pe(r,i,""),{[n.is("customized")]:Boolean(s.header)}],d={...Pe(a,i),columnsStyles:t,class:c,columns:e,headerIndex:o,style:l};return O(it,d,"function"==typeof(u=s)||"[object Object]"===Object.prototype.toString.call(u)&&!b(u)?s:{default:()=>[s]});var u},St=(e,{slots:t})=>{const{column:o,ns:l,style:r,onColumnSorted:a}=e,n=Ve(r);if(o.placeholderSign===Le)return O("div",{class:l.em("header-row-cell","placeholder"),style:n},null);const{headerCellRenderer:s,headerClass:i,sortable:c}=o,d={...e,class:l.e("header-cell-text")},u=Ge(s),h=u?u(d):M(t,"default",d,(()=>[O(nt,d,null)])),{sortBy:m,sortState:w,headerCellProps:f}=e;let p,g;if(w){const e=w[o.key];p=Boolean(De[e]),g=p?e:We.ASC}else p=o.key===m.key,g=p?m.order:We.ASC;const y=[l.e("header-cell"),Pe(i,e,""),o.align===Ke.CENTER&&l.is("align-center"),o.align===Ke.RIGHT&&l.is("align-right"),c&&l.is("sortable")],x={...Pe(f,e),onClick:o.sortable?a:void 0,class:y,style:n,"data-key":o.key};return O("div",W(x,{role:"columnheader"}),[h,c&&O(ht,{class:[l.e("sort-icon"),p&&l.is("sorting")],sortOrder:g},null)])},bt=(e,{slots:t})=>{var o;return O("div",{class:e.class,style:e.style},[null==(o=t.default)?void 0:o.call(t)])};bt.displayName="ElTableV2Footer";const vt=(e,{slots:t})=>{const o=M(t,"default",{},(()=>[O(N,null,null)]));return O("div",{class:e.class,style:e.style},[o])};vt.displayName="ElTableV2Empty";const Tt=(e,{slots:t})=>{var o;return O("div",{class:e.class,style:e.style},[null==(o=t.default)?void 0:o.call(t)])};function Ht(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!b(e)}Tt.displayName="ElTableV2Overlay";const Ct=e({name:"ElTableV2",props:rt,setup(e,{slots:o,expose:l}){const r=t("table-v2"),{columnsStyles:a,fixedColumnsOnLeft:s,fixedColumnsOnRight:i,mainColumns:c,mainTableHeight:d,fixedTableHeight:u,leftTableWidth:h,rightTableWidth:m,data:w,depthMap:f,expandedRowKeys:p,hasFixedColumns:g,hoveringRowKey:y,mainTableRef:x,leftTableRef:R,rightTableRef:S,isDynamic:b,isResetting:v,isScrolling:T,bodyWidth:H,emptyStyle:C,rootStyle:I,headerWidth:E,footerHeight:M,showEmpty:K,scrollTo:A,scrollToLeft:D,scrollToTop:L,scrollToRow:z,getRowHeight:k,onColumnSorted:j,onRowHeightChange:P,onRowHovered:V,onRowExpanded:G,onRowsRendered:N,onScroll:$,onVerticalScroll:B}=Ne(e);return l({scrollTo:A,scrollToLeft:D,scrollToTop:L,scrollToRow:z}),F($e,{ns:r,isResetting:v,hoveringRowKey:y,isScrolling:T}),()=>{const{cache:t,cellProps:l,estimatedRowHeight:v,expandColumnKey:T,fixedData:A,headerHeight:D,headerClass:L,headerProps:z,headerCellProps:F,sortBy:q,sortState:U,rowHeight:X,rowClass:Y,rowEventHandlers:J,rowKey:_,rowProps:Q,scrollbarAlwaysOn:Z,indentSize:ee,iconSize:te,useIsScrolling:oe,vScrollbarSize:le,width:re}=e,ae=n(w),ne={cache:t,class:r.e("main"),columns:n(c),data:ae,fixedData:A,estimatedRowHeight:v,bodyWidth:n(H)+le,headerHeight:D,headerWidth:n(E),height:n(d),mainTableRef:x,rowKey:_,rowHeight:X,scrollbarAlwaysOn:Z,scrollbarStartGap:2,scrollbarEndGap:le,useIsScrolling:oe,width:re,getRowHeight:k,onRowsRendered:N,onScroll:$},se=n(h),ie=n(u),ce={cache:t,class:r.e("left"),columns:n(s),data:ae,estimatedRowHeight:v,leftTableRef:R,rowHeight:X,bodyWidth:se,headerWidth:se,headerHeight:D,height:ie,rowKey:_,scrollbarAlwaysOn:Z,scrollbarStartGap:2,scrollbarEndGap:le,useIsScrolling:oe,width:se,getRowHeight:k,onScroll:B},de=n(m)+le,ue={cache:t,class:r.e("right"),columns:n(i),data:ae,estimatedRowHeight:v,rightTableRef:S,rowHeight:X,bodyWidth:de,headerWidth:de,headerHeight:D,height:ie,rowKey:_,scrollbarAlwaysOn:Z,scrollbarStartGap:2,scrollbarEndGap:le,width:de,style:`--${n(r.namespace)}-table-scrollbar-size: ${le}px`,useIsScrolling:oe,getRowHeight:k,onScroll:B},he=n(a),me={ns:r,depthMap:n(f),columnsStyles:he,expandColumnKey:T,expandedRowKeys:n(p),estimatedRowHeight:v,hasFixedColumns:n(g),hoveringRowKey:n(y),rowProps:Q,rowClass:Y,rowKey:_,rowEventHandlers:J,onRowHovered:V,onRowExpanded:G,onRowHeightChange:P},we={cellProps:l,expandColumnKey:T,indentSize:ee,iconSize:te,rowKey:_,expandedRowKeys:n(p),ns:r},fe={ns:r,headerClass:L,headerProps:z,columnsStyles:he},pe={ns:r,sortBy:q,sortState:U,headerCellProps:F,onColumnSorted:j},ge={row:e=>O(yt,W(e,me),{row:o.row,cell:e=>{let t;return o.cell?O(xt,W(e,we,{style:he[e.column.key]}),Ht(t=o.cell(e))?t:{default:()=>[t]}):O(xt,W(e,we,{style:he[e.column.key]}),null)}}),header:e=>O(Rt,W(e,fe),{header:o.header,cell:e=>{let t;return o["header-cell"]?O(St,W(e,pe,{style:he[e.column.key]}),Ht(t=o["header-cell"](e))?t:{default:()=>[t]}):O(St,W(e,pe,{style:he[e.column.key]}),null)}})},ye=[e.class,r.b(),r.e("root"),{[r.is("dynamic")]:n(b)}],xe={class:r.e("footer"),style:n(M)};return O("div",{class:ye,style:n(I)},[O(ft,ne,Ht(ge)?ge:{default:()=>[ge]}),O(pt,ce,Ht(ge)?ge:{default:()=>[ge]}),O(gt,ue,Ht(ge)?ge:{default:()=>[ge]}),o.footer&&O(bt,xe,{default:o.footer}),n(K)&&O(vt,{class:r.e("empty"),style:n(C)},{default:o.empty}),o.overlay&&O(Tt,{class:r.e("overlay")},{default:o.overlay})])}}}),It=E({disableWidth:Boolean,disableHeight:Boolean,onResize:{type:C(Function)}}),Et=e({name:"ElAutoResizer",props:It,setup(e,{slots:o}){const r=t("auto-resizer"),{height:a,width:n,sizer:i}=(e=>{const t=l(),o=l(0),r=l(0);let a;return s((()=>{a=v(t,(([e])=>{const{width:t,height:l}=e.contentRect,{paddingLeft:a,paddingRight:n,paddingTop:s,paddingBottom:i}=getComputedStyle(e.target),c=Number.parseInt(a)||0,d=Number.parseInt(n)||0,u=Number.parseInt(s)||0,h=Number.parseInt(i)||0;o.value=t-c-d,r.value=l-u-h})).stop})),T((()=>{null==a||a()})),g([o,r],(([t,o])=>{var l;null==(l=e.onResize)||l.call(e,{width:t,height:o})})),{sizer:t,width:o,height:r}})(e),c={width:"100%",height:"100%"};return()=>{var e;return O("div",{ref:i,class:r.b(),style:c},[null==(e=o.default)?void 0:e.call(o,{height:a.value,width:n.value})])}}}),Mt=j(Ct);j(Et);const Ot=e({__name:"ProgressInfo.bak",props:{closeDialog:{type:Function},getProgressInfoID:{},getProgressInfotype:{},getProgressInforunnerid:{}},setup(e){const{t:t}=P();let o=0;const r=[{key:"target",title:t("scanTemplate.TargetHandler"),dataKey:"target",width:150},{key:"TargetHandler",title:t("scanTemplate.TargetHandler"),dataKey:"TargetHandler",width:100},{key:"SubdomainScan",title:t("scanTemplate.SubdomainScan"),dataKey:"name",width:100,align:"center"},{key:"SubdomainSecurity",title:t("scanTemplate.SubdomainSecurity"),width:100},{key:"PortScanPreparation",title:t("scanTemplate.PortScanPreparation"),width:100},{key:"PortScan",title:t("scanTemplate.PortScan"),width:100},{key:"PortFingerprint",title:t("scanTemplate.PortFingerprint"),width:100},{key:"AssetMapping",title:t("scanTemplate.AssetMapping"),width:100},{key:"AssetHandle",title:t("scanTemplate.AssetHandle"),width:100},{key:"URLScan",title:t("scanTemplate.URLScan"),width:100},{key:"WebCrawler",title:t("scanTemplate.WebCrawler"),width:100},{key:"URLSecurity",title:t("scanTemplate.URLSecurity"),width:100},{key:"DirScan",title:t("scanTemplate.DirScan"),width:100},{key:"VulnerabilityScan",title:t("scanTemplate.VulnerabilityScan"),width:100},{key:"All",title:"All",width:100}];r[0].fixed=!0;const a=l(Array.from({length:200}).map((()=>({id:"random-id-"+ ++o,name:"Tom",date:"2020-10-1"}))));return(e,t)=>(V(),G(n(Mt),{columns:r,data:a.value,width:900,height:700,fixed:""},null,8,["data"]))}});export{Ot as default};
