import{b as n}from"./index-BWEJ0epC.js";import{t}from"./debounce-BwgdhaaK.js";import{r,b3 as a,aS as e}from"./index-C6fb_XFi.js";var o=1/0,i=17976931348623157e292;function u(n){var r=function(n){return n?(n=t(n))===o||n===-o?(n<0?-1:1)*i:n==n?n:0:0===n?n:0}(n),a=r%1;return r==r?a?r-a:r:0}function s(n,t,r,a){for(var e=n.length,o=r+(a?1:-1);a?o--:++o<e;)if(t(n[o],o,n))return o;return-1}var l=Math.max,f=Math.min;function v(t,r,a){var e=null==t?0:t.length;if(!e)return-1;var o=e-1;return void 0!==a&&(o=u(a),o=a<0?l(e+o,0):f(o,e-1)),s(t,n(r),o,!0)}function m(n){const t=r(!1);return{handleCompositionStart:()=>{t.value=!0},handleCompositionUpdate:n=>{const r=n.target.value,e=r[r.length-1]||"";t.value=!a(e)},handleCompositionEnd:r=>{t.value&&(t.value=!1,e(n)&&n(r))}}}export{s as b,v as f,m as u};
