import{_ as e}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{Z as a,ca as t,a3 as l,bm as s,$ as o,d as r,a7 as i,r as n,a8 as u,a4 as c,b6 as d,O as p,o as m,c as g,aH as v,a as y,i as f,w as _,aI as j,C as b,a9 as h,n as x,aa as S,ah as w,u as k,z,t as V,j as D,e as A,f as E,F as P,l as C,K as R,v as L}from"./index-3XfDPlIS.js";import{b as U,E as N,a as F}from"./el-dropdown-item-BMccEdtX.js";import"./el-popper-DVoWBu_3.js";import{E as T}from"./el-pagination-DwzzZyu4.js";import"./el-tag-DcMbxLLg.js";import"./el-select-DH55-Cab.js";import{E as $,a as B}from"./el-checkbox-DjLAvZXr.js";import{E as I}from"./el-switch-C-DLgt5X.js";/* empty css                          */import{_ as O}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{d as q}from"./index-D4TGn7aE.js";import{_ as J}from"./AddProject.vue_vue_type_script_setup_true_lang-CRi0NtY7.js";import{u as H}from"./useIcon-k-uSyz6l.js";const K=a({size:{type:[Number,String],values:t,default:"",validator:e=>l(e)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:s},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:o(String),default:"cover"}}),Z={error:e=>e instanceof Event},G=["src","alt","srcset"],M=r({name:"ElAvatar"});const Q=w(S(r({...M,props:K,emits:Z,setup(e,{emit:a}){const t=e,s=i("avatar"),o=n(!1),r=u((()=>{const{size:e,icon:a,shape:l}=t,o=[s.b()];return c(e)&&o.push(s.m(e)),a&&o.push(s.m("icon")),l&&o.push(s.m(l)),o})),S=u((()=>{const{size:e}=t;return l(e)?s.cssVarBlock({size:d(e)||""}):void 0})),w=u((()=>({objectFit:t.fit})));function k(e){o.value=!0,a("error",e)}return p((()=>t.src),(()=>o.value=!1)),(e,a)=>(m(),g("span",{class:x(y(r)),style:v(y(S))},[!e.src&&!e.srcSet||o.value?e.icon?(m(),f(y(b),{key:1},{default:_((()=>[(m(),f(j(e.icon)))])),_:1})):h(e.$slots,"default",{key:2}):(m(),g("img",{key:0,src:e.src,alt:e.alt,srcset:e.srcSet,style:v(y(w)),onError:k},null,44,G))],6))}}),[["__file","avatar.vue"]])),W={class:"flex cursor-pointer"},X={class:"pr-16px"},Y={class:"name"},ee={class:"assets-info"},ae=r({__name:"ProjectList",props:{tableDataList:{type:Array,default:()=>[]},getProjectTag:{type:Function,required:!0},total:{type:Number,default:0},multipleSelection:{type:Boolean},selectedRows:{type:Array,default:()=>[]}},emits:["update:selectedRows"],setup(a,{emit:t}){const{t:l}=C(),{push:s}=k(),o=a,r=n(!1);let i="";const u=n(!1),c=()=>{u.value=!1},d=H({icon:"uil:edit"}),v=H({icon:"material-symbols:delete-outline"}),j=H({icon:"carbon:data-vis-1"}),b=e=>{"edit"==e.type?(async e=>{i=e,u.value=!0})(e.id):"del"==e.type?(e=>{const a=n(!1);R({title:"Delete",draggable:!0,message:()=>L("div",{style:{display:"flex",alignItems:"center"}},[L("p",{style:{margin:"0 10px 0 0"}},l("task.delAsset")),L(I,{modelValue:a.value,"onUpdate:modelValue":e=>{a.value=e}})])}).then((async()=>{await q([e],a.value)}))})(e.id):le(e.id)},h=()=>{o.getProjectTag(x.value,S.value)},x=n(1),S=n(50),w=n(!1),K=n(!1),Z=n(!1),G=t,M=n([...o.selectedRows]);p(M,(e=>{JSON.stringify(e)!==JSON.stringify(o.selectedRows)&&G("update:selectedRows",e),ae.value=e.length===o.tableDataList.length})),p((()=>o.selectedRows),(e=>{M.value=[...e]})),p((()=>o.tableDataList),(e=>{ae.value=M.value.length===e.length}));const ae=n(!1),te=()=>{ae.value?M.value=o.tableDataList.map((e=>e.id)):M.value=[]},le=e=>{s(`/project-management/project-detail?id=${e}`)};return(t,s)=>(m(),g(P,null,[a.multipleSelection?(m(),f(y($),{key:0,modelValue:ae.value,"onUpdate:modelValue":s[0]||(s[0]=e=>ae.value=e),onChange:te},{default:_((()=>[z(V(y(l)("common.selectAll")),1)])),_:1},8,["modelValue"])):D("",!0),A(y(B),{modelValue:M.value,"onUpdate:modelValue":s[1]||(s[1]=e=>M.value=e)},{default:_((()=>[A(y(e),{columns:[],data:a.tableDataList,loading:r.value,"custom-content":"","card-wrap-style":{width:"210px",marginBottom:"20px",marginRight:"20px"}},{content:_((e=>[A(y(U),{trigger:"contextmenu",onCommand:b},{dropdown:_((()=>[A(y(N),null,{default:_((()=>[A(y(F),{icon:y(d),command:{type:"edit",id:e.id}},{default:_((()=>[z(V(y(l)("common.edit")),1)])),_:2},1032,["icon","command"]),A(y(F),{icon:y(v),command:{type:"del",id:e.id}},{default:_((()=>[z(V(y(l)("common.delete")),1)])),_:2},1032,["icon","command"]),A(y(F),{icon:y(j),command:{type:"aggregation",id:e.id}},{default:_((()=>[z(V(y(l)("project.aggregation")),1)])),_:2},1032,["icon","command"])])),_:2},1024)])),default:_((()=>[E("div",W,[a.multipleSelection?(m(),f(y($),{key:0,value:e.id,class:"pr-16px"},null,8,["value"])):D("",!0),E("div",X,[""!=e.logo?(m(),f(y(Q),{key:0,src:e.logo,class:"avatar",fit:"cover"},null,8,["src"])):(m(),f(y(Q),{key:1,class:"avatar avatar-placeholder"},{default:_((()=>[z(V(e.name.charAt(0)),1)])),_:2},1024))]),E("div",null,[E("div",Y,V(e.name),1),E("div",ee,V(y(l)("project.totalAssets"))+" : "+V(e.AssetCount),1)])])])),_:2},1024)])),_:1},8,["data","loading"])])),_:1},8,["modelValue"]),A(y(T),{"current-page":x.value,"onUpdate:currentPage":s[2]||(s[2]=e=>x.value=e),"page-size":S.value,"onUpdate:pageSize":s[3]||(s[3]=e=>S.value=e),"page-sizes":[50,70,100,200,400],small:w.value,disabled:Z.value,background:K.value,layout:"total, sizes, prev, pager, next, jumper",total:a.total,onSizeChange:h,onCurrentChange:h},null,8,["current-page","page-size","small","disabled","background","total"]),A(y(O),{modelValue:u.value,"onUpdate:modelValue":s[4]||(s[4]=e=>u.value=e),title:y(l)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:_((()=>[A(J,{closeDialog:c,projectid:y(i),getProjectData:t.$props.getProjectTag,schedule:!1},null,8,["projectid","getProjectData"])])),_:1},8,["modelValue","title"])],64))}});export{ae as _};
