import{Y as e,Z as t,d as s,a6 as a,a7 as i,o as r,c as o,n as l,a as n,a8 as d,j as c,aH as v,a9 as u,ag as f}from"./index-C6fb_XFi.js";const p=e({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:t(String),default:"solid"}}),y=s({name:"ElDivider"});const g=f(u(s({...y,props:p,setup(e){const t=e,s=a("divider"),u=i((()=>s.cssVar({"border-style":t.borderStyle})));return(e,t)=>(r(),o("div",{class:l([n(s).b(),n(s).m(e.direction)]),style:v(n(u)),role:"separator"},[e.$slots.default&&"vertical"!==e.direction?(r(),o("div",{key:0,class:l([n(s).e("text"),n(s).is(e.contentPosition)])},[d(e.$slots,"default")],2)):c("v-if",!0)],6))}}),[["__file","divider.vue"]]));export{g as E};
