import{d as e,H as t,r as a,s as i,e as l,E as s,v as o,A as r,B as n,o as p,c as u,a as d,w as m,I as c,F as g,l as j,ai as f,_ as h}from"./index-C6fb_XFi.js";import{u as b}from"./useTable-CijeIiBB.js";import{E as v}from"./el-card-B37ahJ8o.js";import{E as y}from"./el-pagination-FWx5cl5J.js";import{E as _}from"./el-tag-C_oEQYGz.js";import"./el-select-vbM8Rxr1.js";import"./el-popper-CeVwVUf9.js";import{E as x,a as S}from"./el-col-Dl4_4Pn5.js";import{_ as w}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as k}from"./useCrudSchemas-CEXr0LRM.js";import{a as E,d as C,r as V}from"./index-BBupWySc.js";import T from"./Csearch-B51tl_vU.js";import"./index-BWEJ0epC.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./tree-BfZhwLPs.js";import"./index-CnCQNuY4.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import"./el-text-BnUG9HvL.js";import"./el-divider-Bw95UAdD.js";import"./el-tree-select-D8i5b8X5.js";import"./index-w1A_Rrgh.js";/* empty css                */import"./el-switch-Bh7JeorW.js";import"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import"./useIcon-BxqaCND-.js";import"./exportData.vue_vue_type_script_setup_true_lang-C3cw41xZ.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-space-CdSK6Ce1.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const z=h(e({__name:"SubdomainTakeover",props:{projectList:{}},setup(e){const{t:h}=j(),z=[{keyword:"domain",example:'domain="example.com"',explain:h("searchHelp.domain")},{keyword:"type",example:'type="github"',explain:h("searchHelp.subdomainType")},{keyword:"value",example:'value="exapmle.github.com"',explain:h("searchHelp.subdoaminValue")},{keyword:"response",example:'response="404 Not Found"',explain:h("searchHelp.body")},{keyword:"project",example:'project="Hackerone"',explain:h("searchHelp.project")}];t((()=>{H(),window.addEventListener("resize",H)}));const A=a(0),H=()=>{const e=window.innerHeight||document.documentElement.clientHeight;A.value=.7*e},L=a(""),I=e=>{L.value=e,q()},R=i({}),U=i([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:h("tableDemo.index"),type:"index",minWidth:"30"},{field:"host",label:"Domain",minWidth:"300"},{field:"value",label:h("subdomain.recordValue"),minWidth:"400"},{field:"type",label:"Type",minWidth:"200"},{field:"response",label:"Response",minWidth:"300",formatter:(e,t,a)=>l(s,{"max-height":"100"},{default:()=>[l("div",{style:"whiteSpace: 'pre-line'"},[a])]})},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,i)=>{null==i&&(i=[]),R[e.id]||(R[e.id]={inputVisible:!1,inputValue:"",inputRef:a(null)});const l=R[e.id],s=async()=>{l.inputValue&&(i.push(l.inputValue),E(e.id,W,l.inputValue)),l.inputVisible=!1,l.inputValue=""};return o(S,{},(()=>[...i.map((t=>o(x,{span:24,key:t},(()=>[o("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||te("tags",t)})(e,t)},[o(_,{closable:!0,onClose:()=>(async t=>{const a=i.indexOf(t);a>-1&&i.splice(a,1),C(e.id,W,t)})(t)},(()=>t))])])))),o(x,{span:24},l.inputVisible?()=>o(r,{ref:l.inputRef,modelValue:l.inputValue,"onUpdate:modelValue":e=>l.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&s()},onBlur:s}):()=>o(n,{class:"button-new-tag",size:"small",onClick:()=>(l.inputVisible=!0,void f((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"}]);let W="SubdoaminTakerResult";U.forEach((e=>{e.hidden=e.hidden??!1}));let D=a(!1);const N=({field:e,hidden:t})=>{const a=U.findIndex((t=>t.field===e));-1!==a&&(U[a].hidden=t),(()=>{const e=U.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=D.value,localStorage.setItem(`columnConfig_${W}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${W}`)||"{}");U.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),D.value=e.statisticsHidden})();const{allSchemas:P}=k(U),{tableRegister:F,tableState:O,tableMethods:$}=b({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=O,a=await V(L.value,e.value,t.value,Q);return{list:a.data.list,total:a.data.total}},immediate:!1}),{loading:B,dataList:J,total:K,currentPage:M,pageSize:G}=O;G.value=20;const{getList:q,getElTableExpose:X}=$;function Y(){return{background:"var(--el-fill-color-light)"}}const Q=i({}),Z=(e,t)=>{Object.assign(Q,t),L.value=e,q()},ee=a([]),te=(e,t)=>{const a=`${e}=${t}`;ee.value=[...ee.value,a]},ae=e=>{if(ee.value){const[t,a]=e.split("=");t in Q&&Array.isArray(Q[t])&&(Q[t]=Q[t].filter((e=>e!==a)),0===Q[t].length&&delete Q[t]),ee.value=ee.value.filter((t=>t!==e))}},ie=()=>Q;return(e,t)=>(p(),u(g,null,[l(T,{getList:d(q),handleSearch:I,searchKeywordsData:z,index:"SubdoaminTakerResult",getElTableExpose:d(X),projectList:e.$props.projectList,handleFilterSearch:Z,crudSchemas:U,dynamicTags:ee.value,handleClose:ae,onUpdateColumnVisibility:N,searchResultCount:d(K),getFilter:ie},null,8,["getList","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),l(d(S),null,{default:m((()=>[l(d(x),null,{default:m((()=>[l(d(v),{style:{height:"min-content"}},{default:m((()=>[l(d(w),{pageSize:d(G),"onUpdate:pageSize":t[0]||(t[0]=e=>c(G)?G.value=e:null),currentPage:d(M),"onUpdate:currentPage":t[1]||(t[1]=e=>c(M)?M.value=e:null),columns:d(P).tableColumns,data:d(J),stripe:"","max-height":A.value,border:!0,loading:d(B),resizable:!0,onRegister:d(F),headerCellStyle:Y,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),l(d(x),{":span":24},{default:m((()=>[l(d(v),null,{default:m((()=>[l(d(y),{pageSize:d(G),"onUpdate:pageSize":t[2]||(t[2]=e=>c(G)?G.value=e:null),currentPage:d(M),"onUpdate:currentPage":t[3]||(t[3]=e=>c(M)?M.value=e:null),"page-sizes":[20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:d(K)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-f8bf9d5e"]]);export{z as default};
