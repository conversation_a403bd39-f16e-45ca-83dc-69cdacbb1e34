import{r as a}from"./index-D1ADinPR.js";const e=(e,t,s)=>a.post({url:"/api/task/data",data:{search:e,pageIndex:t,pageSize:s}}),t=e=>a.post({url:"/api/task/stop",data:{ids:e}}),s=e=>a.post({url:"/api/task/start",data:{ids:e}}),d=(e,t,s,d,p,r,l,i,o,u,c,g,n,k,h,m,y,j,T,I,S)=>a.post({url:"/api/task/add",data:{name:e,target:t,ignore:s,node:d,allNode:p,duplicates:r,scheduledTasks:l,hour:i,template:o,targetTp:u,search:c,filter:g,targetNumber:n,targetIds:k,project:h,targetSource:m,day:y,minute:j,week:T,bindProject:I,cycleType:S}}),p=(e,t,s,d,p,r,l,i,o,u,c,g,n,k,h,m,y,j,T,I,S)=>a.post({url:"/api/task/scheduled/add",data:{name:e,target:t,ignore:s,node:d,allNode:p,duplicates:r,scheduledTasks:l,hour:i,template:o,targetTp:u,search:c,filter:g,targetNumber:n,targetIds:k,project:h,targetSource:m,day:y,minute:j,week:T,bindProject:I,cycleType:S}}),r=(e,t,s,d,p,r,l,i,o,u,c,g,n,k,h,m,y,j,T,I,S,b)=>a.post({url:"/api/task/scheduled/update",data:{id:e,name:t,target:s,ignore:d,node:p,allNode:r,duplicates:l,scheduledTasks:i,hour:o,template:u,targetTp:c,search:g,filter:n,targetNumber:k,targetIds:h,project:m,targetSource:y,day:j,minute:T,week:I,bindProject:S,cycleType:b}}),l=e=>a.post({url:"/api/task/detail",data:{id:e}}),i=e=>a.post({url:"/api/task/scheduled/detail",data:{id:e}}),o=(e,t)=>a.post({url:"/api/task/delete",data:{ids:e,delA:t}}),u=e=>a.post({url:"/api/task/retest",data:{id:e}}),c=(e,t,s)=>a.post({url:"/api/task/progress/info",data:{id:e,pageIndex:t,pageSize:s}}),g=(e,t,s)=>a.post({url:"/api/task/scheduled/data",data:{search:e,pageIndex:t,pageSize:s}}),n=e=>a.post({url:"/api/task/scheduled/delete",data:{ids:e}}),k=(e,t,s)=>a.post({url:"/api/task/scheduled/pagemonit/data",data:{search:e,pageIndex:t,pageSize:s}}),h=e=>a.post({url:"/api/task/scheduled/pagemonit/delete",data:{ids:e}}),m=(e,t,s,d)=>a.post({url:"/api/task/scheduled/pagemonit/update",data:{hour:e,node:t,allNode:s,state:d}}),y=e=>a.post({url:"/api/task/scheduled/pagemonit/add",data:{url:e}}),j=(e,t,s)=>a.post({url:"/api/task/template/list",data:{search:e,pageIndex:t,pageSize:s}}),T=e=>a.post({url:"/api/task/template/detail",data:{id:e}}),I=(e,t)=>a.post({url:"/api/task/template/save",data:{result:e,id:t}}),S=e=>a.post({url:"/api/task/template/delete",data:{ids:e}}),b=(e,t,s,d,p)=>a.post({url:"/api/task/sync",data:{ids:e,option:t,project:s,tag:d,name:p}});export{s as a,b,n as c,o as d,g as e,S as f,e as g,j as h,r as i,p as j,d as k,l,i as m,I as n,T as o,c as p,h as q,u as r,t as s,k as t,m as u,y as v};
