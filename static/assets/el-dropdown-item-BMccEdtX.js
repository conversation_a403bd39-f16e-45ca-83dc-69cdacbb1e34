import{Z as e,$ as o,aK as n,aa as t,d as l,r,ab as a,a8 as i,a6 as s,bN as d,bh as u,a as c,O as p,bt as f,a9 as v,y as m,o as g,i as b,w,e as h,bO as y,bP as I,aR as E,Y as F,B as C,E as R,C as _,D as k,aQ as T,a7 as x,aA as S,b6 as B,x as D,aF as $,c as P,bQ as K,aJ as M,n as L,j as G,f as O,aI as z,af as A,F as H,bR as j,aH as N,ah as J,ai as U}from"./index-3XfDPlIS.js";import{c as Y,E as Q,O as W,w as V}from"./el-popper-DVoWBu_3.js";import{c as Z,a as q,d as X,b as ee,C as oe,e as ne,f as te,g as le,h as re,F as ae,L as ie}from"./el-pagination-DwzzZyu4.js";import{c as se}from"./castArray-uOT054sj.js";import{c as de}from"./refs-CSSW5x_d.js";const ue=e({style:{type:o([String,Array,Object])},currentTabId:{type:o(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:o(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:ce,ElCollectionItem:pe,COLLECTION_INJECTION_KEY:fe,COLLECTION_ITEM_INJECTION_KEY:ve}=Z("RovingFocusGroup"),me=Symbol("elRovingFocusGroup"),ge=Symbol("elRovingFocusGroupItem"),be={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},we=(e,o,t)=>{const l=((e,o)=>{if("rtl"!==o)return e;switch(e){case n.right:return n.left;case n.left:return n.right;default:return e}})(e.key,t);if(!("vertical"===o&&[n.left,n.right].includes(l)||"horizontal"===o&&[n.up,n.down].includes(l)))return be[l]},he=e=>{const{activeElement:o}=document;for(const n of e){if(n===o)return;if(n.focus(),o!==document.activeElement)return}},ye="currentTabIdChange",Ie="rovingFocusGroup.entryFocus",Ee={bubbles:!1,cancelable:!0},Fe=l({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:ue,emits:[ye,"entryFocus"],setup(e,{emit:o}){var n;const t=r(null!=(n=e.currentTabId||e.defaultCurrentTabId)?n:null),l=r(!1),v=r(!1),m=r(null),{getItems:g}=a(fe,void 0),b=i((()=>[{outline:"none"},e.style])),w=Y((o=>{var n;null==(n=e.onMousedown)||n.call(e,o)}),(()=>{v.value=!0})),h=Y((o=>{var n;null==(n=e.onFocus)||n.call(e,o)}),(e=>{const o=!c(v),{target:n,currentTarget:r}=e;if(n===r&&o&&!c(l)){const e=new Event(Ie,Ee);if(null==r||r.dispatchEvent(e),!e.defaultPrevented){const e=g().filter((e=>e.focusable)),o=[e.find((e=>e.active)),e.find((e=>e.id===c(t))),...e].filter(Boolean).map((e=>e.ref));he(o)}}v.value=!1})),y=Y((o=>{var n;null==(n=e.onBlur)||n.call(e,o)}),(()=>{l.value=!1}));s(me,{currentTabbedId:d(t),loop:u(e,"loop"),tabIndex:i((()=>c(l)?-1:0)),rovingFocusGroupRef:m,rovingFocusGroupRootStyle:b,orientation:u(e,"orientation"),dir:u(e,"dir"),onItemFocus:e=>{o(ye,e)},onItemShiftTab:()=>{l.value=!0},onBlur:y,onFocus:h,onMousedown:w}),p((()=>e.currentTabId),(e=>{t.value=null!=e?e:null})),f(m,Ie,((...e)=>{o("entryFocus",...e)}))}});var Ce=t(l({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:ce,ElRovingFocusGroupImpl:t(Fe,[["render",function(e,o,n,t,l,r){return v(e.$slots,"default")}],["__file","roving-focus-group-impl.vue"]])}}),[["render",function(e,o,n,t,l,r){const a=m("el-roving-focus-group-impl"),i=m("el-focus-group-collection");return g(),b(i,null,{default:w((()=>[h(a,y(I(e.$attrs)),{default:w((()=>[v(e.$slots,"default")])),_:3},16)])),_:3})}],["__file","roving-focus-group.vue"]]);var Re=t(l({components:{ElRovingFocusCollectionItem:pe},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:o}){const{currentTabbedId:t,loop:l,onItemFocus:d,onItemShiftTab:u}=a(me,void 0),{getItems:p}=a(fe,void 0),f=E(),v=r(null),m=Y((e=>{o("mousedown",e)}),(o=>{e.focusable?d(c(f)):o.preventDefault()})),g=Y((e=>{o("focus",e)}),(()=>{d(c(f))})),b=Y((e=>{o("keydown",e)}),(e=>{const{key:o,shiftKey:t,target:r,currentTarget:a}=e;if(o===n.tab&&t)return void u();if(r!==a)return;const i=we(e);if(i){e.preventDefault();let o=p().filter((e=>e.focusable)).map((e=>e.ref));switch(i){case"last":o.reverse();break;case"prev":case"next":{"prev"===i&&o.reverse();const e=o.indexOf(a);o=l.value?(d=e+1,(s=o).map(((e,o)=>s[(o+d)%s.length]))):o.slice(e+1);break}}F((()=>{he(o)}))}var s,d})),w=i((()=>t.value===c(f)));return s(ge,{rovingFocusGroupItemRef:v,tabIndex:i((()=>c(w)?0:-1)),handleMousedown:m,handleFocus:g,handleKeydown:b}),{id:f,handleKeydown:b,handleFocus:g,handleMousedown:m}}}),[["render",function(e,o,n,t,l,r){const a=m("el-roving-focus-collection-item");return g(),b(a,{id:e.id,focusable:e.focusable,active:e.active},{default:w((()=>[v(e.$slots,"default")])),_:3},8,["id","focusable","active"])}],["__file","roving-focus-item.vue"]]);const _e=Symbol("elDropdown"),{ButtonGroup:ke}=C;var Te=t(l({name:"ElDropdown",components:{ElButton:C,ElButtonGroup:ke,ElScrollbar:R,ElDropdownCollection:q,ElTooltip:Q,ElRovingFocusGroup:Ce,ElOnlyChild:W,ElIcon:_,ArrowDown:k},props:X,emits:["visible-change","click","command"],setup(e,{emit:o}){const t=T(),l=x("dropdown"),{t:a}=S(),d=r(),f=r(),v=r(null),m=r(null),g=r(null),b=r(null),w=r(!1),h=[n.enter,n.space,n.down],y=i((()=>({maxHeight:B(e.maxHeight)}))),I=i((()=>[l.m(k.value)])),F=i((()=>se(e.trigger))),C=E().value,R=i((()=>e.id||C));function _(){var e;null==(e=v.value)||e.onClose()}p([d,F],(([e,o],[n])=>{var t,l,r;(null==(t=null==n?void 0:n.$el)?void 0:t.removeEventListener)&&n.$el.removeEventListener("pointerenter",P),(null==(l=null==e?void 0:e.$el)?void 0:l.removeEventListener)&&e.$el.removeEventListener("pointerenter",P),(null==(r=null==e?void 0:e.$el)?void 0:r.addEventListener)&&o.includes("hover")&&e.$el.addEventListener("pointerenter",P)}),{immediate:!0}),D((()=>{var e,o;(null==(o=null==(e=d.value)?void 0:e.$el)?void 0:o.removeEventListener)&&d.value.$el.removeEventListener("pointerenter",P)}));const k=$();function P(){var e,o;null==(o=null==(e=d.value)?void 0:e.$el)||o.focus()}s(_e,{contentRef:m,role:i((()=>e.role)),triggerId:R,isUsingKeyboard:w,onItemEnter:function(){},onItemLeave:function(){const e=c(m);F.value.includes("hover")&&(null==e||e.focus()),b.value=null}}),s("elDropdown",{instance:t,dropdownSize:k,handleClick:function(){_()},commandHandler:function(...e){o("command",...e)},trigger:u(e,"trigger"),hideOnClick:u(e,"hideOnClick")});return{t:a,ns:l,scrollbar:g,wrapStyle:y,dropdownTriggerKls:I,dropdownSize:k,triggerId:R,triggerKeys:h,currentTabId:b,handleCurrentTabIdChange:function(e){b.value=e},handlerMainButtonClick:e=>{o("click",e)},handleEntryFocus:function(e){w.value||(e.preventDefault(),e.stopImmediatePropagation())},handleClose:_,handleOpen:function(){var e;null==(e=v.value)||e.onOpen()},handleBeforeShowTooltip:function(){o("visible-change",!0)},handleShowTooltip:function(e){"keydown"===(null==e?void 0:e.type)&&m.value.focus()},handleBeforeHideTooltip:function(){o("visible-change",!1)},onFocusAfterTrapped:e=>{var o,n;e.preventDefault(),null==(n=null==(o=m.value)?void 0:o.focus)||n.call(o,{preventScroll:!0})},popperRef:v,contentRef:m,triggeringElementRef:d,referenceElementRef:f}}}),[["render",function(e,o,n,t,l,r){var a;const i=m("el-dropdown-collection"),s=m("el-roving-focus-group"),d=m("el-scrollbar"),u=m("el-only-child"),c=m("el-tooltip"),p=m("el-button"),f=m("arrow-down"),y=m("el-icon"),I=m("el-button-group");return g(),P("div",{class:L([e.ns.b(),e.ns.is("disabled",e.disabled)])},[h(c,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":"hover"===e.trigger?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":null==(a=e.referenceElementRef)?void 0:a.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":"hover"===e.trigger?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:e.teleported,pure:"",persistent:"",onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},K({content:w((()=>[h(d,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:w((()=>[h(s,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:w((()=>[h(i,null,{default:w((()=>[v(e.$slots,"dropdown")])),_:3})])),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])])),_:3},8,["wrap-style","view-class"])])),_:2},[e.splitButton?void 0:{name:"default",fn:w((()=>[h(u,{id:e.triggerId,ref:"triggeringElementRef",role:"button",tabindex:e.tabindex},{default:w((()=>[v(e.$slots,"default")])),_:3},8,["id","tabindex"])]))}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","teleported","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(g(),b(I,{key:0},{default:w((()=>[h(p,M({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:w((()=>[v(e.$slots,"default")])),_:3},16,["size","type","disabled","tabindex","onClick"]),h(p,M({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:w((()=>[h(y,{class:L(e.ns.e("icon"))},{default:w((()=>[h(f)])),_:1},8,["class"])])),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])])),_:3})):G("v-if",!0)],2)}],["__file","dropdown.vue"]]);const xe=l({name:"DropdownItemImpl",components:{ElIcon:_},props:ee,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:o}){const t=x("dropdown"),{role:l}=a(_e,void 0),{collectionItemRef:r}=a(oe,void 0),{collectionItemRef:s}=a(ve,void 0),{rovingFocusGroupItemRef:d,tabIndex:u,handleFocus:c,handleKeydown:p,handleMousedown:f}=a(ge,void 0),v=de(r,s,d),m=i((()=>"menu"===l.value?"menuitem":"navigation"===l.value?"link":"button")),g=Y((e=>{const{code:t}=e;if(t===n.enter||t===n.space)return e.preventDefault(),e.stopImmediatePropagation(),o("clickimpl",e),!0}),p);return{ns:t,itemRef:v,dataset:{[ne]:""},role:m,tabIndex:u,handleFocus:c,handleKeydown:g,handleMousedown:f}}}),Se=["aria-disabled","tabindex","role"];const Be=()=>{const e=a("elDropdown",{}),o=i((()=>null==e?void 0:e.dropdownSize));return{elDropdown:e,_elDropdownSize:o}};var De=t(l({name:"ElDropdownItem",components:{ElDropdownCollectionItem:te,ElRovingFocusItem:Re,ElDropdownItemImpl:t(xe,[["render",function(e,o,n,t,l,r){const a=m("el-icon");return g(),P(H,null,[e.divided?(g(),P("li",M({key:0,role:"separator",class:e.ns.bem("menu","item","divided")},e.$attrs),null,16)):G("v-if",!0),O("li",M({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:o[0]||(o[0]=o=>e.$emit("clickimpl",o)),onFocus:o[1]||(o[1]=(...o)=>e.handleFocus&&e.handleFocus(...o)),onKeydown:o[2]||(o[2]=A(((...o)=>e.handleKeydown&&e.handleKeydown(...o)),["self"])),onMousedown:o[3]||(o[3]=(...o)=>e.handleMousedown&&e.handleMousedown(...o)),onPointermove:o[4]||(o[4]=o=>e.$emit("pointermove",o)),onPointerleave:o[5]||(o[5]=o=>e.$emit("pointerleave",o))}),[e.icon?(g(),b(a,{key:0},{default:w((()=>[(g(),b(z(e.icon)))])),_:1})):G("v-if",!0),v(e.$slots,"default")],16,Se)],64)}],["__file","dropdown-item-impl.vue"]])},inheritAttrs:!1,props:ee,emits:["pointermove","pointerleave","click"],setup(e,{emit:o,attrs:n}){const{elDropdown:t}=Be(),l=T(),s=r(null),d=i((()=>{var e,o;return null!=(o=null==(e=c(s))?void 0:e.textContent)?o:""})),{onItemEnter:u,onItemLeave:p}=a(_e,void 0),f=Y((e=>(o("pointermove",e),e.defaultPrevented)),V((o=>{if(e.disabled)return void p(o);const n=o.currentTarget;n===document.activeElement||n.contains(document.activeElement)||(u(o),o.defaultPrevented||null==n||n.focus())}))),v=Y((e=>(o("pointerleave",e),e.defaultPrevented)),V((e=>{p(e)})));return{handleClick:Y((n=>{if(!e.disabled)return o("click",n),"keydown"!==n.type&&n.defaultPrevented}),(o=>{var n,r,a;e.disabled?o.stopImmediatePropagation():((null==(n=null==t?void 0:t.hideOnClick)?void 0:n.value)&&(null==(r=t.handleClick)||r.call(t)),null==(a=t.commandHandler)||a.call(t,e.command,l,o))})),handlePointerMove:f,handlePointerLeave:v,textContent:d,propsAndAttrs:i((()=>({...e,...n})))}}}),[["render",function(e,o,n,t,l,r){var a;const i=m("el-dropdown-item-impl"),s=m("el-roving-focus-item"),d=m("el-dropdown-collection-item");return g(),b(d,{disabled:e.disabled,"text-value":null!=(a=e.textValue)?a:e.textContent},{default:w((()=>[h(s,{focusable:!e.disabled},{default:w((()=>[h(i,M(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:w((()=>[v(e.$slots,"default")])),_:3},16,["onPointerleave","onPointermove","onClickimpl"])])),_:3},8,["focusable"])])),_:3},8,["disabled","text-value"])}],["__file","dropdown-item.vue"]]);const $e=l({name:"ElDropdownMenu",props:le,setup(e){const o=x("dropdown"),{_elDropdownSize:t}=Be(),l=t.value,{focusTrapRef:r,onKeydown:s}=a(j,void 0),{contentRef:d,role:u,triggerId:p}=a(_e,void 0),{collectionRef:f,getItems:v}=a(re,void 0),{rovingFocusGroupRef:m,rovingFocusGroupRootStyle:g,tabIndex:b,onBlur:w,onFocus:h,onMousedown:y}=a(me,void 0),{collectionRef:I}=a(fe,void 0),E=i((()=>[o.b("menu"),o.bm("menu",null==l?void 0:l.value)])),F=de(d,f,r,m,I),C=Y((o=>{var n;null==(n=e.onKeydown)||n.call(e,o)}),(e=>{const{currentTarget:o,code:t,target:l}=e;if(o.contains(l),n.tab===t&&e.stopImmediatePropagation(),e.preventDefault(),l!==c(d))return;if(!ae.includes(t))return;const r=v().filter((e=>!e.disabled)).map((e=>e.ref));ie.includes(t)&&r.reverse(),he(r)}));return{size:l,rovingFocusGroupRootStyle:g,tabIndex:b,dropdownKls:E,role:u,triggerId:p,dropdownListWrapperRef:F,handleKeydown:e=>{C(e),s(e)},onBlur:w,onFocus:h,onMousedown:y}}}),Pe=["role","aria-labelledby"];var Ke=t($e,[["render",function(e,o,n,t,l,r){return g(),P("ul",{ref:e.dropdownListWrapperRef,class:L(e.dropdownKls),style:N(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:o[0]||(o[0]=(...o)=>e.onBlur&&e.onBlur(...o)),onFocus:o[1]||(o[1]=(...o)=>e.onFocus&&e.onFocus(...o)),onKeydown:o[2]||(o[2]=A(((...o)=>e.handleKeydown&&e.handleKeydown(...o)),["self"])),onMousedown:o[3]||(o[3]=A(((...o)=>e.onMousedown&&e.onMousedown(...o)),["self"]))},[v(e.$slots,"default")],46,Pe)}],["__file","dropdown-menu.vue"]]);const Me=J(Te,{DropdownItem:De,DropdownMenu:Ke}),Le=U(De),Ge=U(Ke);export{Ge as E,Le as a,Me as b};
