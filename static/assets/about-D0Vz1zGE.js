import{d as t,r as e,y as s,o as a,c as l,e as r,w as o,f as n,a as c,W as u,X as i,F as p,R as d,z as m,t as h,p as b,m as x,_ as f}from"./index-DfJTpRkj.js";const g=t=>(b("data-v-b78b3bde"),t=t(),x(),t),v={class:"about-container p-6"},w={class:"flex items-center"},y=g((()=>n("span",null,"项目介绍",-1))),_=g((()=>n("div",{class:"project-info"},[n("h2",{class:"text-xl font-bold mb-4"},"## 网址"),n("ul",{class:"link-list space-y-3 list-disc pl-6"},[n("li",null,[m(" 官网&安装&文档： "),n("a",{href:"https://www.scope-sentry.top",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://www.scope-sentry.top ")]),n("li",null,[m(" Github： "),n("a",{href:"https://github.com/Autumn-27/ScopeSentry",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://github.com/Autumn-27/ScopeSentry ")]),n("li",null,[m(" 扫描端源码： "),n("a",{href:"https://github.com/Autumn-27/ScopeSentry-Scan",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://github.com/Autumn-27/ScopeSentry-Scan ")]),n("li",null,[m(" 插件市场： "),n("a",{href:"https://plugin.scope-sentry.top/",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://plugin.scope-sentry.top/ ")]),n("li",null,[m(" SecureFlow： "),n("a",{href:"https://plugin.scope-sentry.top/",target:"_blank",class:"text-blue-500 hover:text-blue-700 cursor-pointer"}," https://secureflow.scope-sentry.top ")])])],-1))),S={class:"flex items-center"},k=g((()=>n("span",null,"赞助与合作",-1))),j={class:"sponsor-content p-4"},A=g((()=>n("h3",{class:"text-2xl font-bold mb-4 text-center"},"支持这个项目",-1))),F=g((()=>n("p",{class:"text-gray-600 mb-8 text-center max-w-2xl mx-auto"}," 如果你觉得这个项目对你有帮助，可以通过以下方式支持作者的持续开发和维护 ",-1))),z={class:"sponsor-options grid grid-cols-1 md:grid-cols-2 gap-8"},q=g((()=>n("h4",{class:"font-bold text-lg text-center"},"赞助",-1))),C={class:"flex flex-col items-center p-4"},D={class:"qr-codes flex flex-col md:flex-row gap-8 justify-center items-center"},G=["src","alt"],I={class:"text-gray-600 text-lg"},L=g((()=>n("h4",{class:"font-bold text-lg text-center"},"合作",-1))),N={class:"flex flex-col items-center p-4"},O=g((()=>n("p",{class:"text-gray-600 mb-6 text-center break-all"},"如果有想法可以一起合作",-1))),Q=f(t({__name:"about",setup(t){const b=e([{name:"支付宝",img:"/assets/zfb--nwhu3QO.png"},{name:"微信",img:"/assets/wx-D1dLCFFN.jpg"}]);return(t,e)=>{const x=s("el-icon"),f=s("el-card"),g=s("el-button");return a(),l("div",v,[r(f,{class:"mb-6 transform hover:shadow-lg transition-all duration-300"},{header:o((()=>[n("div",w,[r(x,{class:"mr-2"},{default:o((()=>[r(c(u))])),_:1}),y])])),default:o((()=>[_])),_:1}),r(f,{class:"transform hover:shadow-lg transition-all duration-300"},{header:o((()=>[n("div",S,[r(x,{class:"mr-2"},{default:o((()=>[r(c(i))])),_:1}),k])])),default:o((()=>[n("div",j,[A,F,n("div",z,[r(f,{shadow:"hover",class:"sponsor-card"},{header:o((()=>[q])),default:o((()=>[n("div",C,[n("div",D,[(a(!0),l(p,null,d(b.value,(t=>(a(),l("div",{key:t.name,class:"qr-code-item flex flex-col items-center"},[n("img",{src:t.img,alt:`${t.name}收款码`,class:"w-64 h-64 object-cover mb-3 hover:scale-105 transition-transform duration-300"},null,8,G),n("span",I,h(t.name),1)])))),128))])])])),_:1}),r(f,{shadow:"hover",class:"sponsor-card"},{header:o((()=>[L])),default:o((()=>[n("div",N,[O,r(g,{type:"primary",size:"large",class:"w-full md:w-auto"},{default:o((()=>[m(" <EMAIL> ")])),_:1})])])),_:1})])])])),_:1})])}}}),[["__scopeId","data-v-b78b3bde"]]);export{Q as default};
