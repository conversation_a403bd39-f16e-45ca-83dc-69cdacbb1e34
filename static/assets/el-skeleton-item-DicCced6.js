import{r as a,H as e,O as t,Z as s,d as l,a7 as n,o,c as i,i as r,a as u,dB as p,j as c,n as d,aa as m,bh as v,F as f,R as k,a9 as y,e as g,aJ as b,bO as h,ah as w,ai as x}from"./index-3XfDPlIS.js";const S=s({animated:{type:Boolean,default:!1},count:{type:Number,default:1},rows:{type:Number,default:3},loading:{type:Boolean,default:!0},throttle:{type:Number}}),$=s({variant:{type:String,values:["circle","rect","h1","h3","text","caption","p","image","button"],default:"text"}}),_=l({name:"ElSkeletonItem"});var B=m(l({..._,props:$,setup(a){const e=n("skeleton");return(a,t)=>(o(),i("div",{class:d([u(e).e("item"),u(e).e(a.variant)])},["image"===a.variant?(o(),r(u(p),{key:0})):c("v-if",!0)],2))}}),[["__file","skeleton-item.vue"]]);const E=l({name:"ElSkeleton"});const N=w(m(l({...E,props:S,setup(s,{expose:l}){const p=s,m=n("skeleton"),w=((s,l=0)=>{if(0===l)return s;const n=a(!1);let o=0;const i=()=>{o&&clearTimeout(o),o=window.setTimeout((()=>{n.value=s.value}),l)};return e(i),t((()=>s.value),(a=>{a?i():n.value=a})),n})(v(p,"loading"),p.throttle);return l({uiLoading:w}),(a,e)=>u(w)?(o(),i("div",b({key:0,class:[u(m).b(),u(m).is("animated",a.animated)]},a.$attrs),[(o(!0),i(f,null,k(a.count,(e=>(o(),i(f,{key:e},[a.loading?y(a.$slots,"template",{key:e},(()=>[g(B,{class:d(u(m).is("first")),variant:"p"},null,8,["class"]),(o(!0),i(f,null,k(a.rows,(e=>(o(),r(B,{key:e,class:d([u(m).e("paragraph"),u(m).is("last",e===a.rows&&a.rows>1)]),variant:"p"},null,8,["class"])))),128))])):c("v-if",!0)],64)))),128))],16)):y(a.$slots,"default",h(b({key:1},a.$attrs)))}}),[["__file","skeleton.vue"]]),{SkeletonItem:B});x(B);export{N as E};
