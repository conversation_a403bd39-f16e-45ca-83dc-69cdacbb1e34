import{d as e,dC as t,H as l,r as a,s as o,e as i,z as s,F as n,A as r,o as m,i as u,w as d,a as p,B as c,j as h,J as f,l as b,K as j,M as g,_ as v}from"./index-3XfDPlIS.js";import{u as y}from"./useTable-BezX3TfM.js";import{E as x}from"./el-card-CuEws33_.js";import{E as _,a as S}from"./el-col-CN1tVfqh.js";import{E as w}from"./el-text-CLWE0mUm.js";import{_ as C}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as E}from"./useCrudSchemas-6tFKup3N.js";import{d as T}from"./index-BfB8wPHh.js";import{y as A}from"./index-BAb9yQka.js";import"./el-table-column-B5hg3WH6.js";import"./el-popper-DVoWBu_3.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-DcMbxLLg.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./tree-BfZhwLPs.js";import"./index-Dz8ZrwBc.js";function N(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!f(e)}const W=v(e({__name:"Subdomain",setup(e){const{t:f}=b(),{query:v}=t();l((()=>{z(),window.addEventListener("resize",z)}));const W=a(0),z=()=>{const e=window.innerHeight||document.documentElement.clientHeight;W.value=.8*e};a("");const I=o({});I.project=[v.id];const R=async e=>{Object.assign(I,e),K()},V=o([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:f("tableDemo.index"),type:"index",minWidth:"30"},{field:"host",label:f("subdomain.subdomainName"),minWidth:"200",formatter:(e,t,l)=>e.count?i(n,null,[i(w,null,N(l)?l:{default:()=>[l]}),i(w,{type:"info"},{default:()=>[s("("),e.count,s(")")]})]):i(w,null,N(l)?l:{default:()=>[l]}),slots:{header:()=>i("div",null,[i("span",null,[f("subdomain.subdomainName")]),i(r,{modelValue:L.value,"onUpdate:modelValue":e=>L.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>J("sub_host")},null)])}},{field:"type",label:f("subdomain.recordType"),minWidth:"200",columnKey:"type",filters:[{text:"A",value:"A"},{text:"NS",value:"NS"},{text:"CNAME",value:"CNAME"},{text:"PTR",value:"PTR"},{text:"TXT",value:"TXT"}]},{field:"value",label:f("subdomain.recordValue"),minWidth:"250",formatter:(e,t,l)=>{let a="";return l.forEach(((e,t)=>{a+=`${e}\r\n`})),a},slots:{header:()=>i("div",null,[i("span",null,[f("subdomain.recordValue")]),i(r,{modelValue:P.value,"onUpdate:modelValue":e=>P.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>J("sub_value")},null)])}},{field:"ip",label:"IP",minWidth:"150",formatter:(e,t,l)=>{let a="";return l.forEach(((e,t)=>{a+=`${e}\r\n`})),a},slots:{header:()=>i("div",null,[i("span",null,[s("IP")]),i(r,{modelValue:$.value,"onUpdate:modelValue":e=>$.value=e,placeholder:"Search",style:"width: 180px; margin-left: 10px;",size:"small",onChange:()=>J("sub_ip")},null)])}},{field:"time",label:f("asset.time"),minWidth:"200"}]),{allSchemas:k}=E(V),{tableRegister:H,tableState:M,tableMethods:U}=y({fetchDataApi:async()=>({list:(await T("",I,q)).data.list}),immediate:!0}),{loading:B,dataList:F}=M,{getList:K,getElTableExpose:O}=U;function D(){return{background:"var(--el-fill-color-light)"}}const L=a(""),P=a(""),$=a(""),q=o({}),J=async e=>{let t="";"sub_host"==e&&(t=L.value),"sub_value"==e&&(t=P.value),"sub_ip"==e&&(t=$.value),q[e]=t,K()},X=a([]),G=async()=>{j.confirm("Whether to delete?","Warning",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{const e=await O(),t=(null==e?void 0:e.getSelectionRows())||[];X.value=t.map((e=>e.id)),await A(X.value,"subdomain"),K()})).catch((()=>{g({type:"info",message:"Delete canceled"})}))};let Q=a(!1);const Y=async()=>{const e=await O(),t=(null==e?void 0:e.getSelectionRows())||[];X.value=t.map((e=>e.id)),0!=X.value.length?Q.value=!0:Q.value=!1};return(e,t)=>(m(),u(p(S),null,{default:d((()=>[i(p(_),null,{default:d((()=>[i(p(x),{style:{height:"min-content"}},{default:d((()=>[p(Q)?(m(),u(p(c),{key:0,onClick:G,type:"danger",size:"small"},{default:d((()=>[s("Dlete")])),_:1})):h("",!0),i(p(C),{columns:p(k).tableColumns,data:p(F),stripe:"","max-height":W.value,border:!0,loading:p(B),onSelectionChange:Y,rowKey:"id",resizable:!0,onRegister:p(H),onFilterChange:R,headerCellStyle:D,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["columns","data","max-height","loading","onRegister"])])),_:1})])),_:1})])),_:1}))}}),[["__scopeId","data-v-74d55aa1"]]);export{W as default};
