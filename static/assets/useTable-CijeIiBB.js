import{l as t,r as a,O as e,a as s,H as n,K as l,M as o,ai as c}from"./index-C6fb_XFi.js";const{t:i}=t(),u=t=>{const{immediate:u=!0}=t,m=a(!1),r=a(1),d=a(20),g=a(0),y=a([]);e((()=>r.value),(()=>{f.getList()})),e((()=>d.value),(()=>{1===s(r)||(r.value=1),f.getList()})),n((()=>{u&&f.getList()}));const v=a(),p=a(),w=async()=>{await c();const t=s(v);return t},f={getList:async()=>{m.value=!0;try{const a=await(null==t?void 0:t.fetchDataApi());a&&(y.value=a.list,g.value=a.total||0)}catch(a){}finally{m.value=!1}},setProps:async(t={})=>{const a=await w();null==a||a.setProps(t)},setColumn:async t=>{const a=await w();null==a||a.setColumn(t)},addColumn:async(t,a)=>{const e=await w();null==e||e.addColumn(t,a)},delColumn:async t=>{const a=await w();null==a||a.delColumn(t)},getElTableExpose:async()=>(await w(),s(p)),refresh:()=>{f.getList()},delList:async a=>{const{fetchDelApi:e}=t;e&&l.confirm(i("common.delMessage"),i("common.delWarning"),{confirmButtonText:i("common.delOk"),cancelButtonText:i("common.delCancel"),type:"warning"}).then((async()=>{if(await e()){o.success(i("common.delSuccess"));const t=(s(g)%s(d)===a||1===s(d))&&s(r)>1?s(r)-1:s(r);r.value=t,f.getList()}}))}};return{tableRegister:(t,a)=>{v.value=t,p.value=s(a)},tableMethods:f,tableState:{currentPage:r,pageSize:d,total:g,dataList:y,loading:m}}};export{u};
