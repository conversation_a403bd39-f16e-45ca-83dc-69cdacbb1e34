import{P as e,g as a,U as t}from"./PanelGroup-C3R5lPd3.js";import{d as l,r as s,s as o,v as r,x as n,y as d,o as m,c as u,e as i,w as p,a as c,f,t as v,i as g,z as _,j as h,A as y,B as b,F as x,l as j,_ as k}from"./index-C6fb_XFi.js";import{E as w,a as S}from"./el-col-Dl4_4Pn5.js";import{E as U}from"./el-card-B37ahJ8o.js";import{E as C}from"./el-progress-sY5OgffI.js";import{E as V}from"./el-text-BnUG9HvL.js";import"./el-tooltip-l0sNRNKZ.js";import{E as N}from"./el-popper-CeVwVUf9.js";import{E as A,a as E}from"./el-form-C2Y6uNCj.js";import{_ as F}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{E as I}from"./el-tag-C_oEQYGz.js";import{_ as T}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{g as z}from"./index-CBLGyxDn.js";import{g as M}from"./index-B40b3p-m.js";import{c as P}from"./index-DWlzJn9A.js";import"./el-skeleton-item-Cr_-rSiV.js";import"./CountTo.vue_vue_type_script_setup_true_lang-B0Gjq2kE.js";import"./index-CnCQNuY4.js";import"./castArray-DRqY4cIf.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";const B={class:"flex flex-col gap-2"},H=k(l({__name:"Analysis",setup(l){const{t:k}=j(),H=s(!0),D=o([{field:"nodeName",label:k("node.nodeName")},{field:"taskCount",label:k("node.taskCount"),formatter:(e,a,t)=>r(I,{round:!0,effect:"dark"},(()=>t))},{field:"nodeStatus",label:k("node.nodeStatus"),formatter:(e,a,t)=>r(I,{type:"1"===t?"success":"2"===t?"warning":"danger",effect:"dark"},(()=>k("1"==t?"node.statusRun":"2"==t?"node.statusStop":"node.statusError")))}]),L=o([{field:"name",label:k("task.taskName")},{field:"taskNum",label:k("task.taskCount"),formatter:(e,a,t)=>r(I,{round:!0,effect:"dark"},(()=>t))},{field:"progress",label:k("task.taskProgress"),formatter:(e,a,t)=>r(C,{percentage:t,type:"line",striped:!0,status:t<100?"":"success",stripedFlow:t<100})},{field:"creatTime",label:k("task.createTime")}]),O=o([{field:"nodeName",label:k("node.nodeName")},{field:"nodeUsageCpu",label:k("node.nodeUsageCpu"),formatter:(e,a,t)=>{let l=parseFloat(t);return l=parseFloat(l.toFixed(2)),r(C,{percentage:l,type:"dashboard",color:l<50?"#26a33f":l<=80?"#fe9900":"#df2800"})}},{field:"nodeUsageMemory",label:k("node.nodeUsageMemory"),formatter:(e,a,t)=>{let l=parseFloat(t);return l=parseFloat(l.toFixed(2)),r(C,{percentage:l,type:"dashboard",color:l<50?"#26a33f":l<80?"#fe9900":"#df2800"})}}]),R=o([{field:"name",label:k("common.name")},{field:"cversion",label:k("common.cversion")},{field:"lversion",label:k("common.lversion"),formatter:(e,a,t)=>{if(e.cversion!=e.lversion){ee.value=!0;const a=e.msg.split("\\n");let l="";return a.forEach((e=>{l+=`<div>${e}</div>`})),r(N,{placement:"top",content:l,rawContent:!0},[r(V,{type:"danger"},t)])}return r(V,t)}}]);let G=s([]);const J=s([]),Q=async()=>{try{const e=await z();e&&e.data&&Array.isArray(e.data.list)&&(J.value=e.data.list.map((e=>({nodeName:e.name,taskCount:e.running,nodeStatus:e.state,nodeUsageCpu:e.cpuNum,nodeUsageMemory:e.memNum}))),G.value=o(e.data.list.map((e=>({nodeName:e.name,nodeUsageCpu:e.cpuNum,nodeUsageMemory:e.memNum})))))}catch(e){}finally{H.value=!1}},W=s([]),X=async()=>{const e=await M("",1,10);W.value=o(e.data.list.map((e=>({name:e.name,taskNum:e.taskNum,progress:e.progress,creatTime:e.creatTime}))))},Y=s([]),$=async()=>{await Promise.all([Q(),X()]),H.value=!1};(async()=>{const e=await a();Y.value=o(e.data.list.map((e=>({name:e.name,cversion:e.cversion,lversion:e.lversion,msg:e.msg}))))})(),$();const q=setInterval($,1e4);n((()=>{clearInterval(q)}));const K=s(""),Z=async()=>{if(K.value){200==(await P(K.value)).code&&(localStorage.setItem("plugin_key",K.value),le.value=!1,te.value=!0)}},ee=s(!1),ae=s({server:"https://github.com/Autumn-27/ScopeSentry/archive/refs/heads/main.zip",scan:""}),te=s(!1),le=s(!1);async function se(){const e=localStorage.getItem("plugin_key");505==(await t(ae.value.server,ae.value.scan,e)).code&&localStorage.removeItem("plugin_key")}return(a,t)=>{const l=d("BaseButton");return m(),u(x,null,[i(e),i(c(S),{gutter:20,justify:"space-between"},{default:p((()=>[i(c(w),{xl:12,lg:12,md:24,sm:24,xs:24},{default:p((()=>[i(c(U),{shadow:"hover",class:"mb-20px"},{header:p((()=>[f("span",null,v(c(k)("dashboard.nodeInfo")),1)])),default:p((()=>[i(c(F),{columns:D,data:J.value,stripe:"",border:!1,height:250},null,8,["columns","data"])])),_:1})])),_:1}),i(c(w),{xl:12,lg:12,md:24,sm:24,xs:24},{default:p((()=>[i(c(U),{shadow:"hover",class:"mb-20px"},{header:p((()=>[f("span",null,v(c(k)("dashboard.taskInfo")),1)])),default:p((()=>[i(c(F),{columns:L,data:W.value,stripe:"",border:!1,height:250,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0}},null,8,["columns","data"])])),_:1})])),_:1}),i(c(w),{span:12},{default:p((()=>[i(c(U),{shadow:"hover",class:"mb-25px"},{header:p((()=>[f("div",null,[f("span",null,v(c(k)("node.nodeUsageStatus")),1)])])),default:p((()=>[i(c(F),{columns:O,data:c(G),highlightCurrentRow:!1,stripe:"",border:!1,height:600},null,8,["columns","data"])])),_:1})])),_:1}),i(c(w),{span:12},{default:p((()=>[i(c(U),{shadow:"hover",class:"mb-25px"},{header:p((()=>[i(c(S),null,{default:p((()=>[i(c(w),{span:12},{default:p((()=>[f("div",null,[f("span",null,v(c(k)("common.version")),1),ee.value?(m(),g(c(V),{key:0,type:"danger",size:"small",style:{position:"relative",left:"1rem"}},{default:p((()=>[_("*"+v(c(k)("common.updatemsg")),1)])),_:1})):h("",!0)])])),_:1})])),_:1})])),default:p((()=>[i(c(F),{columns:R,data:Y.value,stripe:"",border:!1,height:600},null,8,["columns","data"])])),_:1})])),_:1})])),_:1}),i(c(T),{modelValue:te.value,"onUpdate:modelValue":t[2]||(t[2]=e=>te.value=e),title:"Update",center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:430},{default:p((()=>[i(c(V),{type:"danger",size:"small",style:{position:"relative",left:"1rem"}},{default:p((()=>[_("*更新目前只支持docker容器搭建的程序，输入的url地址确保docker内可访问，节点最新版在github中releases的linux版本")])),_:1}),i(c(A),{model:ae.value,"label-width":"120px",class:"upload-form"},{default:p((()=>[i(c(E),{label:"server url"},{default:p((()=>[i(c(y),{modelValue:ae.value.server,"onUpdate:modelValue":t[0]||(t[0]=e=>ae.value.server=e),placeholder:"server url"},null,8,["modelValue"])])),_:1}),i(c(E),{label:"scan url"},{default:p((()=>[i(c(y),{modelValue:ae.value.scan,"onUpdate:modelValue":t[1]||(t[1]=e=>ae.value.scan=e),placeholder:"scan url(https://github.com/Autumn-27/ScopeSentry-Scan/releases/download/vx.x.x/ScopeSentry-Scan_linux_amd64_vx.x.x.zip)"},null,8,["modelValue"])])),_:1}),i(c(E),null,{default:p((()=>[i(c(b),{type:"primary",onClick:se},{default:p((()=>[_("Submit")])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),i(c(T),{modelValue:le.value,"onUpdate:modelValue":t[4]||(t[4]=e=>le.value=e),title:c(k)("plugin.key"),center:"",width:"30%",style:{"max-width":"400px",height:"200px"}},{default:p((()=>[f("div",B,[i(c(N),{class:"item",effect:"dark",content:c(k)("plugin.keyMsg"),placement:"top"},{default:p((()=>[i(c(y),{modelValue:K.value,"onUpdate:modelValue":t[3]||(t[3]=e=>K.value=e)},null,8,["modelValue"])])),_:1},8,["content"]),i(l,{onClick:Z,type:"primary",class:"w-full"},{default:p((()=>[_("确定")])),_:1})])])),_:1},8,["modelValue","title"])],64)}}}),[["__scopeId","data-v-e580b627"]]);export{H as default};
