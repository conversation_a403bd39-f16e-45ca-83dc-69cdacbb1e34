import{Z as e,$ as t,d as a,a7 as s,a8 as i,o as r,c as o,n as l,a as n,a9 as d,j as c,aH as v,aa as u,ah as f}from"./index-DfJTpRkj.js";const p=e({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:t(String),default:"solid"}}),y=a({name:"ElDivider"});const S=f(u(a({...y,props:p,setup(e){const t=e,a=s("divider"),u=i((()=>a.cssVar({"border-style":t.borderStyle})));return(e,t)=>(r(),o("div",{class:l([n(a).b(),n(a).m(e.direction)]),style:v(n(u)),role:"separator"},[e.$slots.default&&"vertical"!==e.direction?(r(),o("div",{key:0,class:l([n(a).e("text"),n(a).is(e.contentPosition)])},[d(e.$slots,"default")],2)):c("v-if",!0)],6))}}),[["__file","divider.vue"]]));export{S as E};
