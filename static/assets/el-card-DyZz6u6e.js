import{Z as a,$ as s,d as e,a7 as t,o,c as r,n as d,a as l,a9 as y,z as f,t as i,j as n,f as c,aH as h,aa as v,ah as p}from"./index-DfJTpRkj.js";const u=a({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:s([String,Object,Array]),default:""},bodyClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),b=e({name:"ElCard"});const S=p(v(e({...b,props:u,setup(a){const s=t("card");return(a,e)=>(o(),r("div",{class:d([l(s).b(),l(s).is(`${a.shadow}-shadow`)])},[a.$slots.header||a.header?(o(),r("div",{key:0,class:d(l(s).e("header"))},[y(a.$slots,"header",{},(()=>[f(i(a.header),1)]))],2)):n("v-if",!0),c("div",{class:d([l(s).e("body"),a.bodyClass]),style:h(a.bodyStyle)},[y(a.$slots,"default")],6),a.$slots.footer||a.footer?(o(),r("div",{key:1,class:d(l(s).e("footer"))},[y(a.$slots,"footer",{},(()=>[f(i(a.footer),1)]))],2)):n("v-if",!0)],2))}}),[["__file","card.vue"]]));export{S as E};
