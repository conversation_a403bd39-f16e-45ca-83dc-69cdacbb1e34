import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as a,dC as t,s as l,r as s,O as o,o as i,c as n,e as r,w as p,a as c,B as d,f as u,t as m,z as v,C as g,F as f,R as h,i as y,j as _,S as x,n as j,l as b,K as k,M as w,_ as S}from"./index-DfJTpRkj.js";import{a as C,E as V}from"./el-col-B4Ik8fnS.js";import{E as F,a as H}from"./el-table-column-7FjdLFwR.js";import"./el-checkbox-DU4wMKRd.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import{E as $}from"./el-tag-CbhrEnto.js";import{E}from"./el-text-vKNLRkxx.js";import{E as T}from"./el-divider-0NmzbuNU.js";import{E as A}from"./el-autocomplete-CyglTUOR.js";import{b as L,E as z,a as D}from"./el-dropdown-item-nnpzYk3y.js";import"./el-select-BkpcrSfo.js";/* empty css                */import{E as I}from"./el-tree-select-P6ZpyTcB.js";import{E as U}from"./el-switch-C5ZBDFmL.js";import{_ as O}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{u as R}from"./useIcon-CNpM61rT.js";import{_ as B}from"./exportData.vue_vue_type_script_setup_true_lang-CthEe30Y.js";import{y as K}from"./index-D4GvAO2k.js";import{_ as N}from"./AddTask.vue_vue_type_script_setup_true_lang-BD8eoln6.js";import"./el-card-DyZz6u6e.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-pagination-FJcT0ZDj.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./index-Co3LqSsp.js";import"./el-tab-pane-BijWf7kq.js";import"./el-form-DsaI0u2w.js";import"./el-radio-group-CTAZlJKV.js";import"./el-space-7M-SGVBd.js";/* empty css                          */import"./index-D1ADinPR.js";import"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./index-CyH6XROR.js";import"./useTable-CtyddZqf.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./index-KH6atv8j.js";import"./index-B0JD2UFG.js";import"./DetailTemplate-DU3RXrBH.js";import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";import"./index-jyMftxhc.js";const P={style:{float:"left"}},J={style:{float:"right",color:"var(--el-text-color-secondary)","font-size":"13px"}},W={class:"custom-dropdown"},q={key:0,class:"dropdown-item"},M={class:"label-text"},G={class:"label-text"},Q={class:"segment-control"},Z={class:"flex gap-2"},X={style:{color:"#888"}},Y={style:{"font-weight":"bold",color:"#333333"}},ee={style:{color:"#888"}},ae=S(a({__name:"Csearch",props:{getList:{type:Function},handleSearch:{type:Function},searchKeywordsData:{},index:{},getElTableExpose:{type:Function},handleFilterSearch:{type:Function},projectList:{},dynamicTags:{},handleClose:{type:Function},openAggregation:{type:Function},crudSchemas:{},statisticsHidden:{type:Boolean},changeStatisticsHidden:{type:Function},searchResultCount:{},activeSegment:{},setActiveSegment:{type:Function},getFilter:{type:Function}},emits:["update-column-visibility"],setup(a,{emit:S}){const{t:ae}=b(),{query:te}=t(),le=a,se=l([...le.searchKeywordsData]),oe={keyword:"task",example:'task=="test"',explain:ae("searchHelp.taskName")};se.push(oe),se.push({keyword:"tag",example:'tag=="test"',explain:"find tags"});const ie=[{operator:"=",meaning:ae("searchHelp.like"),value:'=""'},{operator:"!=",meaning:ae("searchHelp.notIn"),value:'!=""'},{operator:"==",meaning:ae("searchHelp.equal"),value:'==""'}],ne=[{operator:"&&",meaning:ae("searchHelp.and"),value:"&&",logic:"1"},{operator:"||",meaning:ae("searchHelp.or"),value:"||",logic:"1"},{operator:"()",meaning:ae("searchHelp.brackets"),value:"()",logic:"1"}],re=ie.concat(ne),pe=s(!1);(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${le.index}`)||"{}");le.crudSchemas.forEach((a=>{void 0!==e[a.field]&&(a.hidden=e[a.field])}))})(),o((()=>le.crudSchemas),(()=>{(()=>{const e=le.crudSchemas.reduce(((e,a)=>("select"!=a.field&&(e[a.field]=a.hidden),e)),{});localStorage.setItem(`columnConfig_${le.index}`,JSON.stringify(e))})()}),{deep:!0});const ce=()=>{pe.value=!0};function de(){return{background:"var(--el-fill-color-light)"}}const ue=s(""),me=R({icon:"iconoir:search"}),ve=R({icon:"tdesign:chat-bubble-help"}),ge=R({icon:"ri:arrow-drop-down-line"}),fe=R({icon:"ph:export-light"}),he=R({icon:"carbon:data-vis-1"}),ye=R({icon:"openmoji:delete"}),_e=R({icon:"carbon:task-complete"}),xe=s(!1),je=()=>{xe.value=!0},be=s([]),ke=async()=>{const e=await le.getElTableExpose(),a=(null==e?void 0:e.getSelectionRows())||[];return be.value=a.map((e=>e.id)),be.value},we=async()=>{k.confirm("Whether to delete?","Warning",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{await ke(),await K(be.value,le.index),le.getList()})).catch((()=>{w({type:"info",message:"Delete canceled"})}))},Se=s(""),Ce=s(!1),Ve=s(!1);let Fe=s(!0),He=s(!1),$e=s(!1);const Ee=(e,a)=>{if(Se.value=e,""==e&&(Fe.value=!0,$e.value=!1,He.value=!1),Fe.value){$e.value&&(e=e.replace(Se.value,"").trim());a(se.filter((a=>a.keyword.toLowerCase().includes(e.toLowerCase()))))}else if(He.value){const t=e.replace(Se.value,"").trim();a(ie.filter((e=>e.operator.includes(t))))}else if($e.value&&e.endsWith(" ")){const t=e.replace(ue.value,"").trim();a(ne.filter((e=>e.operator.includes(t))))}else a([])},Te=e=>{if(e.keyword){let a="";a=$e.value?Se.value+e.keyword:e.keyword,Se.value=a,ue.value=a,Ce.value=!0,Fe.value=!1,He.value=!0}else e.logic?(ue.value=`${Se.value}${e.value}`,Se.value=ue.value,Fe.value=!0):(ue.value=`${Se.value}${e.value}`,Se.value=ue.value,Ve.value=!0,He.value=!1,$e.value=!0)},Ae=s(!1),Le=s([]);o((()=>Le.value),(e=>{(async()=>{le.handleFilterSearch(ue.value,{project:Le.value})})()}));const ze=s(le.dynamicTags?[...le.dynamicTags]:[]),De=()=>{const e={};ze.value.forEach((a=>{const[t,l]=a.split("=");t in e?e[t].push(l):e[t]=[l]})),e.project=Le.value,le.handleFilterSearch(ue.value,e)};let Ie=te.task;void 0!==Ie&&""!==Ie&&ze.value.push(`task=${Ie}`);const Ue=JSON.parse(localStorage.getItem("assetActiveSegment")||"{}");Ue&&Ue.activeSegment&&le.setActiveSegment&&le.setActiveSegment(Ue.activeSegment,!1),De(),o((()=>le.dynamicTags),(e=>{e?(ze.value=[...e],void 0!==Ie&&""!==Ie&&ze.value.push(`task=${Ie}`)):ze.value=[],De()}),{immediate:!1});const Oe=S,Re=s(le.statisticsHidden),Be=()=>{location.reload()},Ke=R({icon:"icons8:insert-table"}),Ne=R({icon:"flowbite:grid-solid"});function Pe(e){le.setActiveSegment&&le.setActiveSegment(e,!0)}const Je=s(!1);let We=ae("task.addTask");const qe=()=>{Je.value=!1},Me=async()=>{await ke(),Je.value=!0};return(a,t)=>(i(),n(f,null,[r(c(e),null,{default:p((()=>[r(c(C),{class:"row-bg",gutter:20},{default:p((()=>[r(c(V),{span:6},{default:p((()=>[r(c(A),{modelValue:ue.value,"onUpdate:modelValue":t[0]||(t[0]=e=>ue.value=e),"fetch-suggestions":Ee,placeholder:c(ae)("form.input"),popperClass:"my-autocomplete",onSelect:Te,style:{width:"100%"}},{append:p((()=>[r(c(d),{onClick:ce,text:"",style:{display:"contents"},icon:c(ve)},null,8,["icon"])])),default:p((({item:e})=>[u("span",P,m(e.keyword||e.operator),1),u("span",J,m(e.explain||e.meaning),1)])),_:1},8,["modelValue","placeholder"])])),_:1}),r(c(V),{span:1.5},{default:p((()=>[r(c(d),{type:"primary",icon:c(me),onClick:t[1]||(t[1]=e=>a.$props.handleSearch(ue.value))},{default:p((()=>[v(m(c(ae)("form.input")),1)])),_:1},8,["icon"])])),_:1}),r(c(V),{span:1.5},{default:p((()=>[r(c(d),{type:"primary",onClick:je,icon:c(fe)},{default:p((()=>[v(m(c(ae)("asset.export")),1)])),_:1},8,["icon"])])),_:1}),r(c(V),{span:4},{default:p((()=>[r(c(I),{loading:Ae.value,modelValue:Le.value,"onUpdate:modelValue":t[2]||(t[2]=e=>Le.value=e),data:a.$props.projectList,placeholder:c(ae)("project.project"),multiple:"",filterable:"","show-checkbox":"","collapse-tags":"","max-collapse-tags":1},null,8,["loading","modelValue","data","placeholder"])])),_:1}),r(c(V),{span:1.5,xs:1.5,sm:1.5,md:1.5},{default:p((()=>[r(c(L),{trigger:"click"},{dropdown:p((()=>[r(c(z),null,{default:p((()=>[r(c(D),{icon:c(ye),onClick:we},{default:p((()=>[v(m(c(ae)("common.delete")),1)])),_:1},8,["icon"]),r(c(D),{icon:c(_e),onClick:Me},{default:p((()=>[v(m(c(ae)("task.addTask")),1)])),_:1},8,["icon"])])),_:1})])),default:p((()=>[r(c(d),{plain:"",class:"custom-button align-bottom"},{default:p((()=>[v(m(c(ae)("common.operation"))+" ",1),r(c(g),{class:"el-icon--right"},{default:p((()=>[r(c(ge))])),_:1})])),_:1})])),_:1})])),_:1}),r(c(V),{span:1,style:{display:"flex","align-items":"center"}},{default:p((()=>[r(c(L),null,{dropdown:p((()=>[r(c(z),null,{default:p((()=>[(i(!0),n(f,null,h(a.crudSchemas,((e,a)=>(i(),y(c(D),{key:a},{default:p((()=>["selection"!=e.field?(i(),n("div",q,[u("span",M,m(e.label),1),r(c(U),{size:"small",modelValue:e.hidden,"onUpdate:modelValue":a=>e.hidden=a,"active-value":!1,"inactive-value":!0,onChange:a=>(e=>{Oe("update-column-visibility",{field:e.field,hidden:e.hidden})})(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])):_("",!0)])),_:2},1024)))),128)),"asset"==a.$props.index?(i(),y(c(D),{key:0},{default:p((()=>[u("span",G,m(c(ae)("asset.Chart")),1),r(c(U),{size:"small",modelValue:Re.value,"onUpdate:modelValue":t[3]||(t[3]=e=>Re.value=e),"active-value":!1,"inactive-value":!0,onChange:t[4]||(t[4]=e=>a.changeStatisticsHidden(Re.value))},null,8,["modelValue"])])),_:1})):_("",!0),r(c(D),{divided:""},{default:p((()=>[r(c(d),{style:{width:"100%"},type:"primary",onClick:Be},{default:p((()=>[v("Save")])),_:1})])),_:1})])),_:1})])),default:p((()=>[u("div",W,[r(c(x),{icon:"ant-design:setting-outlined",class:"cursor-pointer"})])])),_:1})])),_:1}),"asset"==a.index?(i(),y(c(V),{key:0,span:2,style:{display:"flex","align-items":"center"}},{default:p((()=>[u("div",Q,[u("div",{class:j(["segment",{active:"tableSegment"===le.activeSegment}]),onClick:t[5]||(t[5]=e=>Pe("tableSegment"))},[r(c(g),null,{default:p((()=>[r(c(Ke))])),_:1})],2),u("div",{class:j(["segment",{active:"cardSegment"===le.activeSegment}]),onClick:t[6]||(t[6]=e=>Pe("cardSegment"))},[r(c(g),null,{default:p((()=>[r(c(Ne))])),_:1})],2)])])),_:1})):_("",!0),r(c(V),{span:2,xs:2,sm:2,md:2},{default:p((()=>["SensitiveResult"==a.index?(i(),y(c(d),{key:0,type:"success",onClick:a.$props.openAggregation,icon:c(he)},{default:p((()=>[v(m(c(ae)("project.aggregation")),1)])),_:1},8,["onClick","icon"])):_("",!0)])),_:1})])),_:1}),r(c(C),{style:{"margin-top":"10px"}},{default:p((()=>[r(c(V),{span:24},{default:p((()=>[u("div",Z,[u("span",X,m(c(ae)("asset.total")),1),u("span",Y,m(le.searchResultCount),1),u("span",ee,m(c(ae)("asset.result")),1),(i(!0),n(f,null,h(ze.value,(e=>(i(),y(c($),{key:e,closable:"","disable-transitions":!1,type:"info",size:"small",onClose:a=>function(e){e.includes("task=")&&(Ie=""),le.handleClose&&le.handleClose(e)}(e)},{default:p((()=>[v(m(e),1)])),_:2},1032,["onClose"])))),128))])])),_:1})])),_:1})])),_:1}),r(c(O),{modelValue:pe.value,"onUpdate:modelValue":t[7]||(t[7]=e=>pe.value=e),title:c(ae)("common.querysyntax"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[r(c(C),null,{default:p((()=>[r(c(V),null,{default:p((()=>[r(c(E),{tag:"b",size:"small"},{default:p((()=>[v(m(c(ae)("searchHelp.operator")),1)])),_:1}),r(c(T),{direction:"vertical"}),r(c(E),{size:"small",type:"danger"},{default:p((()=>[v(m(c(ae)("searchHelp.notice")),1)])),_:1})])),_:1}),r(c(V),{style:{"margin-top":"10px"}},{default:p((()=>[r(c(F),{headerCellStyle:de,data:c(re)},{default:p((()=>[r(c(H),{prop:"operator",label:c(ae)("searchHelp.operator"),width:"300"},null,8,["label"]),r(c(H),{prop:"meaning",label:c(ae)("searchHelp.meaning")},null,8,["label"])])),_:1},8,["data"])])),_:1}),r(c(V),{style:{"margin-top":"15px"}},{default:p((()=>[r(c(E),{tag:"b",size:"small"},{default:p((()=>[v(m(c(ae)("searchHelp.keywords")),1)])),_:1})])),_:1}),r(c(V),{style:{"margin-top":"10px"}},{default:p((()=>[r(c(F),{headerCellStyle:de,data:se},{default:p((()=>[r(c(H),{prop:"keyword",label:c(ae)("searchHelp.keywords")},null,8,["label"]),r(c(H),{prop:"example",label:c(ae)("searchHelp.example")},null,8,["label"]),r(c(H),{prop:"explain",label:c(ae)("searchHelp.explain")},null,8,["label"])])),_:1},8,["data"])])),_:1})])),_:1})])),_:1},8,["modelValue","title"]),r(c(O),{modelValue:xe.value,"onUpdate:modelValue":t[8]||(t[8]=e=>xe.value=e),title:c(ae)("asset.export"),center:"","max-height":"300",width:"70%",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[r(B,{index:a.$props.index,searchParams:ue.value,getFilter:a.$props.getFilter},null,8,["index","searchParams","getFilter"])])),_:1},8,["modelValue","title"]),r(c(O),{modelValue:Je.value,"onUpdate:modelValue":t[9]||(t[9]=e=>Je.value=e),title:c(We),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[r(N,{closeDialog:qe,create:!0,taskid:"",schedule:!1,getList:function(){},tp:a.$props.index+"Source","target-ids":be.value,getFilter:a.$props.getFilter,searchParams:ue.value},null,8,["getList","tp","target-ids","getFilter","searchParams"])])),_:1},8,["modelValue","title"])],64))}}),[["__scopeId","data-v-d75acf77"]]);export{ae as default};
