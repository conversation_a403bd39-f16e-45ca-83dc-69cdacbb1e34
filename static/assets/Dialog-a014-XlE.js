import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{_ as o}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{d as t,l,r as i,s as a,y as s,o as r,i as m,w as p,e as n,z as u,t as d,a as c,c as j,F as f,R as _,f as g}from"./index-DfJTpRkj.js";import{u as v,F as b}from"./useForm-BObJP2_c.js";import{u as y}from"./useValidator-qKFa4-ga.js";import{g as D}from"./index-sMv1lmRZ.js";import"./el-card-DyZz6u6e.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-D2BmgSQA.js";import"./refs-DAMUgizk.js";import"./el-form-DsaI0u2w.js";import"./castArray-CvwAI87l.js";import"./el-col-B4Ik8fnS.js";import"./el-tag-CbhrEnto.js";import"./el-checkbox-DU4wMKRd.js";import"./index-DE7jtbbk.js";import"./el-radio-group-CTAZlJKV.js";/* empty css                          */import"./el-input-number-DV6Zl9Iq.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./el-virtual-list-DQOsVxKt.js";import"./raf-zH43jzIi.js";import"./el-select-v2-D406kAkc.js";import"./el-switch-C5ZBDFmL.js";import"./el-autocomplete-CyglTUOR.js";import"./el-divider-0NmzbuNU.js";/* empty css                */import"./el-tree-select-P6ZpyTcB.js";import"./index-Co3LqSsp.js";import"./el-upload-BW2TOFr8.js";import"./el-progress-Wzr98AjO.js";import"./InputPassword-vnvlRugC.js";import"./style.css_vue_type_style_index_0_src_true_lang-CCQeJPcg.js";import"./JsonEditor.vue_vue_type_script_setup_true_lang-BI1SzYeb.js";import"./IconPicker-DBEypS2S.js";import"./el-tab-pane-BijWf7kq.js";import"./el-pagination-FJcT0ZDj.js";import"./isArrayLikeObject-DtpcG_un.js";import"./tsxHelper-DrslCeSo.js";/* empty css                        */import"./index-D1ADinPR.js";const x=t({__name:"Dialog",setup(t){const{required:x}=y(),{t:k}=l(),h=i(!1),P=i(!1),{formRegister:C,formMethods:V}=v(),{getElFormExpose:w}=V,F=a([{field:"field1",label:k("formDemo.input"),component:"Input",formItemProps:{rules:[x()]}},{field:"field2",label:k("formDemo.select"),component:"Select",optionApi:async()=>(await D()).data},{field:"field3",label:k("formDemo.radio"),component:"RadioGroup",componentProps:{options:[{label:"option-1",value:"1"},{label:"option-2",value:"2"}]}},{field:"field4",label:k("formDemo.checkbox"),component:"CheckboxGroup",value:[],componentProps:{options:[{label:"option-1",value:"1"},{label:"option-2",value:"2"}]}},{field:"field5",component:"DatePicker",label:k("formDemo.datePicker"),componentProps:{type:"date"}},{field:"field6",component:"TimeSelect",label:k("formDemo.timeSelect")}]),I=async()=>{const e=await w();null==e||e.validate((e=>{}))};return(t,l)=>{const i=s("BaseButton");return r(),m(c(e),{title:c(k)("dialogDemo.dialog"),message:c(k)("dialogDemo.dialogDes")},{default:p((()=>[n(i,{type:"primary",onClick:l[0]||(l[0]=e=>h.value=!h.value)},{default:p((()=>[u(d(c(k)("dialogDemo.open")),1)])),_:1}),n(i,{type:"primary",onClick:l[1]||(l[1]=e=>P.value=!P.value)},{default:p((()=>[u(d(c(k)("dialogDemo.combineWithForm")),1)])),_:1}),n(c(o),{modelValue:h.value,"onUpdate:modelValue":l[3]||(l[3]=e=>h.value=e),title:c(k)("dialogDemo.dialog")},{footer:p((()=>[n(i,{onClick:l[2]||(l[2]=e=>h.value=!1)},{default:p((()=>[u(d(c(k)("dialogDemo.close")),1)])),_:1})])),default:p((()=>[(r(),j(f,null,_(1e4,(e=>g("div",{key:e},d(e),1))),64))])),_:1},8,["modelValue","title"]),n(c(o),{modelValue:P.value,"onUpdate:modelValue":l[5]||(l[5]=e=>P.value=e),title:c(k)("dialogDemo.dialog")},{footer:p((()=>[n(i,{type:"primary",onClick:I},{default:p((()=>[u(d(c(k)("dialogDemo.submit")),1)])),_:1}),n(i,{onClick:l[4]||(l[4]=e=>P.value=!1)},{default:p((()=>[u(d(c(k)("dialogDemo.close")),1)])),_:1})])),default:p((()=>[n(c(b),{schema:F,onRegister:c(C)},null,8,["schema","onRegister"])])),_:1},8,["modelValue","title"])])),_:1},8,["title","message"])}}});export{x as default};
