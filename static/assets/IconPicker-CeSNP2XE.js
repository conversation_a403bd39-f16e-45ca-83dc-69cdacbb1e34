import{I as e}from"./IconPicker-CGJrUvM2.js";import{_ as t}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as o,l as s,r,o as a,i,w as p,e as l,a as n}from"./index-3XfDPlIS.js";import"./el-popper-DVoWBu_3.js";import"./el-tab-pane-xcqYouKU.js";import"./strings-Dm4Pnsdt.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./el-tag-DcMbxLLg.js";import"./index-tjM0-mlU.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";const m=o({__name:"IconPicker",setup(o){const{t:m}=s(),u=r("tdesign:book-open");return(o,s)=>(a(),i(n(t),{title:n(m)("router.iconPicker")},{default:p((()=>[l(n(e),{modelValue:u.value,"onUpdate:modelValue":s[0]||(s[0]=e=>u.value=e)},null,8,["modelValue"])])),_:1},8,["title"]))}});export{m as default};
