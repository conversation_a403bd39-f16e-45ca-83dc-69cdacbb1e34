import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as s,l as o,o as t,i,w as n,e as p,a}from"./index-3XfDPlIS.js";import{_ as c}from"./Infotip.vue_vue_type_script_setup_true_lang-DGvDVvj1.js";import"./el-card-CuEws33_.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-DVoWBu_3.js";import"./Highlight.vue_vue_type_script_lang-zuUE9qRJ.js";const m=s({__name:"Infotip",setup(s){const{t:m}=o(),r=e=>{e===m("iconDemo.accessAddress")&&window.open("https://iconify.design/")};return(s,o)=>(t(),i(a(e),{title:a(m)("infotipDemo.infotip"),message:a(m)("infotipDemo.infotipDes")},{default:n((()=>[p(a(c),{"show-index":!1,title:`${a(m)("iconDemo.recommendedUse")}${a(m)("iconDemo.iconify")}`,schema:[{label:a(m)("iconDemo.recommendeDes"),keys:["Iconify"]},{label:a(m)("iconDemo.accessAddress"),keys:[a(m)("iconDemo.accessAddress")]}],onClick:r},null,8,["title","schema"])])),_:1},8,["title","message"]))}});export{m as default};
