import{d as e,dE as a,r as l,H as t,o,i as s,w as u,e as i,a as n,A as d,B as m,z as r,t as v,l as f}from"./index-C6fb_XFi.js";import{a as c,E as p}from"./el-form-C2Y6uNCj.js";import{E as g,a as _}from"./el-col-Dl4_4Pn5.js";import{E as x}from"./el-switch-Bh7JeorW.js";import{E as b}from"./el-divider-Bw95UAdD.js";import{E as h}from"./el-text-BnUG9HvL.js";import{u as j}from"./index-CBLGyxDn.js";import{j as w,o as y,T as V}from"./index-CZoUTVkP.js";const C=e({__name:"Configuration",props:{closeDialog:{type:Function},getList:{type:Function},nodeConfForm:{}},setup(e){const C=[w(),y],{t:E}=f(),F=e,{nodeConfForm:M}=a(F),k=l({...M.value}),z=l(!1),U=l(!1),A=l(!1);t((()=>{"1"===k.value.state?(A.value=!0,U.value=!1):"2"===k.value.state?(A.value=!1,U.value=!1):"3"===k.value.state&&(A.value=!1,U.value=!0)}));const D=l(),L=k.value.name;return(e,a)=>(o(),s(n(p),{model:k.value,"label-width":"auto","status-icon":"",ref_key:"ruleFormRef",ref:D},{default:u((()=>[i(n(c),{label:n(E)("node.nodeName"),prop:"name"},{default:u((()=>[i(n(d),{modelValue:k.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>k.value.name=e)},null,8,["modelValue"])])),_:1},8,["label"]),i(n(c),{label:"Module Config"},{default:u((()=>[i(n(V),{modelValue:k.value.ModulesConfig,"onUpdate:modelValue":a[1]||(a[1]=e=>k.value.ModulesConfig=e),extensions:C,autofocus:!0,"indent-with-tab":!0,"tab-size":2,style:{height:"550px",width:"100%"}},null,8,["modelValue"])])),_:1}),i(n(c),{label:n(E)("common.state")},{default:u((()=>[i(n(x),{modelValue:A.value,"onUpdate:modelValue":a[2]||(a[2]=e=>A.value=e),"inline-prompt":"","active-text":n(E)("common.switchAction"),"inactive-text":n(E)("common.switchInactive"),disabled:U.value},null,8,["modelValue","active-text","inactive-text","disabled"])])),_:1},8,["label"]),i(n(_),null,{default:u((()=>[i(n(g),{span:16,offset:8},{default:u((()=>[i(n(c),null,{default:u((()=>[i(n(m),{type:"primary",onClick:a[3]||(a[3]=e=>(async e=>{z.value=!0,e&&await e.validate((async(e,a)=>{if(e){let e;e=await j(L,k.value.name,k.value.ModulesConfig,A.value),200===e.code&&(F.getList(),F.closeDialog()),z.value=!1}else z.value=!1}))})(D.value)),loading:z.value},{default:u((()=>[r(v(n(E)("task.save")),1)])),_:1},8,["loading"]),i(n(b),{direction:"vertical"}),i(n(h),{size:"small",type:"danger"},{default:u((()=>[r(v(n(E)("configuration.threadMsg")),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"]))}});export{C as _};
