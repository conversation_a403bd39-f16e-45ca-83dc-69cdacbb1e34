import{d as e,r as t,s as l,e as a,z as i,S as s,v as r,A as o,B as n,H as p,o as u,c as d,a as c,w as m,I as g,F as f,J as j,l as h,ai as v,_ as x}from"./index-C6fb_XFi.js";import{u as b}from"./useTable-CijeIiBB.js";import{E as y}from"./el-card-B37ahJ8o.js";import{E as _}from"./el-pagination-FWx5cl5J.js";import{E as S}from"./el-tag-C_oEQYGz.js";import"./el-select-vbM8Rxr1.js";import"./el-popper-CeVwVUf9.js";import{E as w,a as C}from"./el-col-Dl4_4Pn5.js";import{E}from"./el-text-BnUG9HvL.js";import{_ as k}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as z}from"./useCrudSchemas-CEXr0LRM.js";import{a as V,d as A,j as H}from"./index-BBupWySc.js";import L from"./Csearch-B51tl_vU.js";import"./index-BWEJ0epC.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./el-table-column-C9CkC7I1.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./tree-BfZhwLPs.js";import"./index-CnCQNuY4.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import"./el-divider-Bw95UAdD.js";import"./el-tree-select-D8i5b8X5.js";import"./index-w1A_Rrgh.js";/* empty css                */import"./el-switch-Bh7JeorW.js";import"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import"./useIcon-BxqaCND-.js";import"./exportData.vue_vue_type_script_setup_true_lang-C3cw41xZ.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-form-C2Y6uNCj.js";import"./el-radio-group-hI5DSxSU.js";import"./el-space-CdSK6Ce1.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import"./el-virtual-list-D7NvYvyu.js";import"./el-select-v2-CaMVABoW.js";import"./el-input-number-DVs4I2j5.js";import"./index-CBLGyxDn.js";import"./index-B40b3p-m.js";import"./DetailTemplate-Dao-XeZd.js";import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const R=x(e({__name:"DirScan",props:{projectList:{}},setup(e){const{t:x}=h(),R=[{keyword:"url",example:'url="http://example.com"',explain:x("searchHelp.url")},{keyword:"statuscode",example:'statuscode=="200"',explain:x("searchHelp.statuscode")},{keyword:"redirect",example:'redirect="https://example.com"',explain:x("searchHelp.redirect")},{keyword:"project",example:'project="Hackerone"',explain:x("searchHelp.project")},{keyword:"length",example:'length="1234"',explain:x("searchHelp.length")}],I=t(""),T=e=>{I.value=e,q()},D=l({}),O=l([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:x("tableDemo.index"),type:"index",minWidth:55},{field:"url",label:"URL",minWidth:200},{field:"status",label:x("dirScan.status"),columnKey:"status",minWidth:120,formatter:(e,t,l)=>{if(null==l)return a("div",null,[i("-")]);let r="";return r=l<300?"#2eb98a":"#ff5252",a(C,{gutter:1},{default:()=>[a(w,{span:1},{default:()=>[a(s,{icon:"clarity:circle-solid",color:r,size:10,style:"transform: translateY(8%)"},null)]}),a(w,{span:2},{default:()=>{return[a(E,null,(e=l,"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!j(e)?l:{default:()=>[l]}))];var e}})]})},filters:[{text:"200",value:200},{text:"201",value:201},{text:"204",value:204},{text:"301",value:301},{text:"302",value:302},{text:"304",value:304},{text:"400",value:400},{text:"401",value:401},{text:"403",value:403},{text:"404",value:404},{text:"500",value:500},{text:"502",value:502},{text:"503",value:503},{text:"504",value:504}]},{field:"length",label:"Length",minWidth:120,sortable:"custom"},{field:"msg",label:"Redirect",minWidth:200},{field:"tags",label:"TAG",fit:"true",formatter:(e,l,a)=>{null==a&&(a=[]),D[e.id]||(D[e.id]={inputVisible:!1,inputValue:"",inputRef:t(null)});const i=D[e.id],s=async()=>{i.inputValue&&(a.push(i.inputValue),V(e.id,U,i.inputValue)),i.inputVisible=!1,i.inputValue=""};return r(C,{},(()=>[...a.map((t=>r(w,{span:24,key:t},(()=>[r("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||oe("tags",t)})(e,t)},[r(S,{closable:!0,onClose:()=>(async t=>{const l=a.indexOf(t);l>-1&&a.splice(l,1),A(e.id,U,t)})(t)},(()=>t))])])))),r(w,{span:24},i.inputVisible?()=>r(o,{ref:i.inputRef,modelValue:i.inputValue,"onUpdate:modelValue":e=>i.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&s()},onBlur:s}):()=>r(n,{class:"button-new-tag",size:"small",onClick:()=>(i.inputVisible=!0,void v((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"}]);let U="DirScanResult";O.forEach((e=>{e.hidden=e.hidden??!1}));let W=t(!1);const P=({field:e,hidden:t})=>{const l=O.findIndex((t=>t.field===e));-1!==l&&(O[l].hidden=t),(()=>{const e=O.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=W.value,localStorage.setItem(`columnConfig_${U}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${U}`)||"{}");O.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),W.value=e.statisticsHidden})();const{allSchemas:F}=z(O),{tableRegister:N,tableState:$,tableMethods:B}=b({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=$,l=await H(I.value,e.value,t.value,te,ae);return{list:l.data.list,total:l.data.total}},immediate:!1}),{loading:J,dataList:K,total:M,currentPage:G,pageSize:Y}=$,{getList:q,getElTableExpose:X}=B;function Q(){return{background:"var(--el-fill-color-light)"}}Y.value=20,p((()=>{ee(),window.addEventListener("resize",ee)}));const Z=t(0),ee=()=>{const e=window.innerHeight||document.documentElement.clientHeight;Z.value=.7*e},te=l({}),le=async e=>{Object.assign(te,e),q()},ae=l({}),ie=async e=>{const t=e.prop,l=e.order;ae[t]=l,q()},se=(e,t)=>{Object.assign(te,t),I.value=e,q()},re=t([]),oe=(e,t)=>{const l=`${e}=${t}`;re.value=[...re.value,l]},ne=e=>{if(re.value){const[t,l]=e.split("=");t in te&&Array.isArray(te[t])&&(te[t]=te[t].filter((e=>e!==l)),0===te[t].length&&delete te[t]),re.value=re.value.filter((t=>t!==e))}},pe=()=>te;return(e,t)=>(u(),d(f,null,[a(L,{getList:c(q),handleSearch:T,searchKeywordsData:R,index:"DirScanResult",dynamicTags:re.value,handleClose:ne,getElTableExpose:c(X),handleFilterSearch:se,projectList:e.$props.projectList,crudSchemas:O,onUpdateColumnVisibility:P,searchResultCount:c(M),getFilter:pe},null,8,["getList","dynamicTags","getElTableExpose","projectList","crudSchemas","searchResultCount"]),a(c(C),null,{default:m((()=>[a(c(w),null,{default:m((()=>[a(c(y),null,{default:m((()=>[a(c(k),{pageSize:c(Y),"onUpdate:pageSize":t[0]||(t[0]=e=>g(Y)?Y.value=e:null),currentPage:c(G),"onUpdate:currentPage":t[1]||(t[1]=e=>g(G)?G.value=e:null),columns:c(F).tableColumns,data:c(K),stripe:"",border:!0,loading:c(J),resizable:!0,"max-height":Z.value,onRegister:c(N),onFilterChange:le,onSortChange:ie,headerCellStyle:Q,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!0,showAfter:0,popperOptions:{},popperClass:"test",placement:"top",hideAfter:0,disabled:!0},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","onRegister"])])),_:1})])),_:1}),a(c(w),{":span":24},{default:m((()=>[a(c(y),null,{default:m((()=>[a(c(_),{pageSize:c(Y),"onUpdate:pageSize":t[2]||(t[2]=e=>g(Y)?Y.value=e:null),currentPage:c(G),"onUpdate:currentPage":t[3]||(t[3]=e=>g(G)?G.value=e:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:c(M)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-743e2f8f"]]);export{R as default};
