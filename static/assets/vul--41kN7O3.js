import{d as e,r as t,s as l,e as a,L as i,v as o,A as s,B as n,G as r,z as u,F as p,H as d,y as m,o as c,c as v,a as g,w as f,I as h,t as x,f as b,J as j,l as y,Y as _,_ as w}from"./index-3XfDPlIS.js";import{u as S}from"./useTable-BezX3TfM.js";import{E as k}from"./el-card-CuEws33_.js";import{E as V}from"./el-pagination-DwzzZyu4.js";import{E as L}from"./el-tag-DcMbxLLg.js";import{E as R,a as C}from"./el-select-DH55-Cab.js";import"./el-popper-DVoWBu_3.js";import{E,a as z}from"./el-col-CN1tVfqh.js";import{E as H,a as U}from"./el-descriptions-item-DHQ8ug0J.js";import{E as T}from"./el-text-CLWE0mUm.js";import{_ as I}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{_ as A}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as W}from"./useCrudSchemas-6tFKup3N.js";import{r as D}from"./index-Dz8ZrwBc.js";import O from"./Csearch-CpC9XwHn.js";import{u as P,a as q,d as F}from"./index-BAb9yQka.js";import{_ as M}from"./Detail.vue_vue_type_script_setup_true_lang-Dj-GId_d.js";import{b as $}from"./index-Cyn7ZzlS.js";import"./index-tjM0-mlU.js";import"./strings-Dm4Pnsdt.js";import"./useInput-SkgDzq11.js";import"./debounce-Cb7r1Afr.js";import"./refs-CSSW5x_d.js";import"./el-table-column-B5hg3WH6.js";import"./el-checkbox-DjLAvZXr.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./raf-BoCEWvzN.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./castArray-uOT054sj.js";import"./index-DkclijAA.js";import"./tree-BfZhwLPs.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import"./el-divider-D9UCOo44.js";import"./el-autocomplete-DpYoUHkX.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./el-switch-C-DLgt5X.js";import"./useIcon-k-uSyz6l.js";import"./exportData.vue_vue_type_script_setup_true_lang--cnrLcU_.js";import"./el-tab-pane-xcqYouKU.js";import"./el-form-BY8piFS2.js";import"./el-radio-group-evFfsZkP.js";import"./el-space-BFgHxiAK.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import"./el-virtual-list-Drl4IGmp.js";import"./el-select-v2-CJw7ZO42.js";import"./index-lpN3i-fa.js";import"./index-BuRY9bVn.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-D4TGn7aE.js";import"./index-C7jW5IRr.js";const N={style:{whiteSpace:"pre-line",width:"500px"}},B={style:{whiteSpace:"pre-line"}};function K(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!j(e)}const J=w(e({__name:"vul",props:{projectList:{}},setup(e){const{t:j}=y(),w=[{keyword:"url",example:'url="http://example.com"',explain:j("searchHelp.url")},{keyword:"vulname",example:'vulname="nginxwebui-runcmd-rce"',explain:j("searchHelp.vulname")},{keyword:"level",example:'level="info"',explain:j("searchHelp.level")},{keyword:"matched",example:'matched="https://example.com"',explain:j("searchHelp.matched")},{keyword:"request",example:'request="cmd=whoami"',explain:j("searchHelp.vulRequest")},{keyword:"response",example:'response="root"',explain:j("searchHelp.response")},{keyword:"project",example:'project="Hackerone"',explain:j("searchHelp.project")}],J=t(""),G=e=>{J.value=e,pe()},Q=l({}),Y=l([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:j("tableDemo.index"),type:"index",minWidth:55},{field:"url",label:"URL",minWidth:100},{field:"vulnerability",label:"Vulnerability",minWidth:120},{field:"level",label:"Level",minWidth:100,columnKey:"level",formatter:(e,t,l)=>{if(null==l)return a("div",null,null);let o="",s="";return"critical"===l?(o="red",s=j("poc.critical")):"high"===l?(o="orange",s=j("poc.high")):"medium"===l?(o="yellow",s=j("poc.medium")):"low"===l?(o="blue",s=j("poc.low")):"info"===l?(o="green",s=j("poc.info")):"unknown"===l&&(o="gray",s=j("poc.unknown")),a(z,{gutter:20,style:"width: 80%"},{default:()=>[a(E,{span:1},{default:()=>[a(i,{icon:"clarity:circle-solid",color:o},null)]}),a(E,{span:5},{default:()=>[a(T,{type:"info"},K(s)?s:{default:()=>[s]})]})]})},filters:[{text:j("poc.critical"),value:"critical"},{text:j("poc.high"),value:"high"},{text:j("poc.medium"),value:"medium"},{text:j("poc.low"),value:"low"},{text:j("poc.info"),value:"info"},{text:j("poc.unknown"),value:"unknown"}]},{field:"matched",label:"Matched",minWidth:150},{field:"status",label:j("common.state"),minWidth:100,columnKey:"status",formatter:(e,t,l)=>{let i;null==e.status&&(e.status=1);const o=[{value:1,label:j("common.unprocessed")},{value:2,label:j("common.processing")},{value:3,label:j("common.ignored")},{value:4,label:j("common.suspected")},{value:5,label:j("common.confirmed")}];return a(C,{modelValue:e.status,"onUpdate:modelValue":async t=>{try{e.status=t,P(e.id,"vulnerability",t)}catch(l){}}},K(i=o.map((e=>a(R,{key:e.value,label:e.label,value:e.value},null))))?i:{default:()=>[i]})},filters:[{text:j("common.unprocessed"),value:1},{text:j("common.processing"),value:2},{text:j("common.ignored"),value:3},{text:j("common.suspected"),value:4},{text:j("common.confirmed"),value:5}]},{field:"tags",label:"TAG",fit:"true",formatter:(e,l,a)=>{null==a&&(a=[]),Q[e.id]||(Q[e.id]={inputVisible:!1,inputValue:"",inputRef:t(null)});const i=Q[e.id],r=async()=>{i.inputValue&&(a.push(i.inputValue),q(e.id,Z,i.inputValue)),i.inputVisible=!1,i.inputValue=""};return o(z,{},(()=>[...a.map((t=>o(E,{span:24,key:t},(()=>[o("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||we("tags",t)})(e,t)},[o(L,{closable:!0,onClose:()=>(async t=>{const l=a.indexOf(t);l>-1&&a.splice(l,1),F(e.id,Z,t)})(t)},(()=>t))])])))),o(E,{span:24},i.inputVisible?()=>o(s,{ref:i.inputRef,modelValue:i.inputValue,"onUpdate:modelValue":e=>i.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&r()},onBlur:r}):()=>o(n,{class:"button-new-tag",size:"small",onClick:()=>(i.inputVisible=!0,void _((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:j("asset.time"),minWidth:200},{field:"action",label:j("tableDemo.action"),formatter:(e,t,l)=>{let i;return a(p,null,[a(r,{type:"primary",onClick:()=>xe(e)},K(i=j("asset.detail"))?i:{default:()=>[i]}),a(r,{type:"success",onClick:()=>Le(e.vulnid)},{default:()=>[u("POC")]})])},minWidth:100}]);let Z="vulnerability";Y.forEach((e=>{e.hidden=e.hidden??!1}));let X=t(!1);const ee=({field:e,hidden:t})=>{const l=Y.findIndex((t=>t.field===e));-1!==l&&(Y[l].hidden=t),(()=>{const e=Y.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=X.value,localStorage.setItem(`columnConfig_${Z}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${Z}`)||"{}");Y.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),X.value=e.statisticsHidden})();const{allSchemas:te}=W(Y),{tableRegister:le,tableState:ae,tableMethods:ie}=S({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=ae,l=await((e,t,l,a)=>D.post({url:"/api/vul/data",data:{search:e,pageIndex:t,pageSize:l,filter:a}}))(J.value,e.value,t.value,be);return{list:l.data.list,total:l.data.total}},immediate:!1}),{loading:oe,dataList:se,total:ne,currentPage:re,pageSize:ue}=ae,{getList:pe,getElTableExpose:de}=ie;function me(){return{background:"var(--el-fill-color-light)"}}ue.value=20,d((()=>{ve(),window.addEventListener("resize",ve)}));const ce=t(0),ve=()=>{const e=window.innerHeight||document.documentElement.clientHeight;ce.value=.7*e},ge=l({URL:"",Vulnerability:"",Level:"",Matched:"",Time:"",Request:"",Response:""}),fe=t(""),he=t(!1),xe=e=>{const t=e.level;fe.value="";let l="";"critical"===t?(fe.value="red",l=j("poc.critical")):"high"===t?(fe.value="orange",l=j("poc.high")):"medium"===t?(fe.value="yellow",l=j("poc.medium")):"low"===t?(fe.value="blue",l=j("poc.low")):"info"===t?(fe.value="green",l=j("poc.info")):"unknown"===t&&(fe.value="gray",l=j("poc.unknown")),ge.Level=l,ge.Vulnerability=e.vulnerability,ge.Matched=e.matched,ge.Time=e.time,ge.URL=e.url,ge.Request=e.request,ge.Response=e.response,he.value=!0},be=l({}),je=async e=>{Object.assign(be,e),pe()},ye=(e,t)=>{Object.assign(be,t),J.value=e,pe()},_e=t([]),we=(e,t)=>{const l=`${e}=${t}`;_e.value=[..._e.value,l]},Se=e=>{if(_e.value){const[t,l]=e.split("=");t in be&&Array.isArray(be[t])&&(be[t]=be[t].filter((e=>e!==l)),0===be[t].length&&delete be[t]),_e.value=_e.value.filter((t=>t!==e))}};let ke=l({id:"",name:"",level:"",content:"",tags:[]});const Ve=t(!1),Le=async e=>{ke.id="",ke.name="",ke.level="",ke.content="",ke.tags=[];const t=await $(e);ke.id=t.data.data.id,ke.name=t.data.data.name,ke.level=t.data.data.level,ke.content=t.data.data.content,ke.tags=t.data.data.tags,Ve.value=!0},Re=()=>{Ve.value=!1},Ce=()=>be;return(e,t)=>{const l=m("ElScrollbar");return c(),v(p,null,[a(O,{getList:g(pe),handleSearch:G,searchKeywordsData:w,index:"vulnerability",getElTableExpose:g(de),handleFilterSearch:ye,projectList:e.$props.projectList,crudSchemas:Y,dynamicTags:_e.value,handleClose:Se,onUpdateColumnVisibility:ee,searchResultCount:g(ne),getFilter:Ce},null,8,["getList","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),a(g(z),null,{default:f((()=>[a(g(E),null,{default:f((()=>[a(g(k),null,{default:f((()=>[a(g(A),{pageSize:g(ue),"onUpdate:pageSize":t[0]||(t[0]=e=>h(ue)?ue.value=e:null),currentPage:g(re),"onUpdate:currentPage":t[1]||(t[1]=e=>h(re)?re.value=e:null),columns:g(te).tableColumns,data:g(se),stripe:"",border:!0,"max-height":ce.value,loading:g(oe),resizable:!0,onRegister:g(le),onFilterChange:je,headerCellStyle:me,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),a(g(E),{":span":24},{default:f((()=>[a(g(k),null,{default:f((()=>[a(g(V),{pageSize:g(ue),"onUpdate:pageSize":t[2]||(t[2]=e=>h(ue)?ue.value=e:null),currentPage:g(re),"onUpdate:currentPage":t[3]||(t[3]=e=>h(re)?re.value=e:null),"page-sizes":[20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:g(ne)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1}),a(g(I),{modelValue:he.value,"onUpdate:modelValue":t[4]||(t[4]=e=>he.value=e),title:g(j)("asset.detail"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},width:"70%","max-height":ce.value},{default:f((()=>[a(g(H),{border:!0,column:2},{default:f((()=>[a(g(U),{label:"URL"},{default:f((()=>[u(x(ge.URL),1)])),_:1}),a(g(U),{label:"Level",width:"100"},{default:f((()=>[a(g(i),{icon:"clarity:circle-solid",color:fe.value},null,8,["color"]),a(g(T),{type:"info"},{default:f((()=>[u(x(ge.Level),1)])),_:1})])),_:1}),a(g(U),{label:"Vulnerability"},{default:f((()=>[u(x(ge.Vulnerability),1)])),_:1}),a(g(U),{label:"Matched"},{default:f((()=>[u(x(ge.Matched),1)])),_:1}),a(g(U),{label:"Time",span:2},{default:f((()=>[u(x(ge.Time),1)])),_:1}),a(g(U),{label:"Request"},{default:f((()=>[a(l,{"max-height":ce.value,"max-width":"maxHeight"},{default:f((()=>[b("div",N,x(ge.Request),1)])),_:1},8,["max-height"])])),_:1}),a(g(U),{label:"Response"},{default:f((()=>[a(l,{"max-height":ce.value},{default:f((()=>[b("div",B,x(ge.Response),1)])),_:1},8,["max-height"])])),_:1})])),_:1})])),_:1},8,["modelValue","title","max-height"]),a(g(I),{modelValue:Ve.value,"onUpdate:modelValue":t[5]||(t[5]=e=>Ve.value=e),title:g(ke).id?e.$t("common.edit"):e.$t("common.new"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:800},{default:f((()=>[a(M,{closeDialog:Re,pocForm:g(ke),getList:g(pe)},null,8,["pocForm","getList"])])),_:1},8,["modelValue","title"])],64)}}}),[["__scopeId","data-v-2e23ae61"]]);export{J as default};
