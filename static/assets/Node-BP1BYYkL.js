import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,s as a,v as o,e as l,G as i,F as n,r as s,O as r,H as m,o as d,c as u,w as p,a as c,f,z as g,t as v,I as y,E as _,j,J as x,l as b}from"./index-C6fb_XFi.js";import{a as h,E as w}from"./el-col-Dl4_4Pn5.js";import{E as C}from"./el-tag-C_oEQYGz.js";import"./el-tooltip-l0sNRNKZ.js";import{E as k}from"./el-popper-CeVwVUf9.js";import{_ as S}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as N}from"./useTable-CijeIiBB.js";import{_ as E}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{_ as F}from"./Configuration.vue_vue_type_script_setup_true_lang-MweraegO.js";import{_ as H}from"./plugin.vue_vue_type_script_setup_true_lang-BBov9HvU.js";import{r as W,b as z,g as T,d as U}from"./index-CBLGyxDn.js";import"./el-card-B37ahJ8o.js";import"./el-table-column-C9CkC7I1.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./el-form-C2Y6uNCj.js";import"./el-switch-Bh7JeorW.js";import"./el-divider-Bw95UAdD.js";import"./el-text-BnUG9HvL.js";import"./index-CZoUTVkP.js";import"./useIcon-BxqaCND-.js";import"./index-DWlzJn9A.js";import"./index-CnCQNuY4.js";const V={class:"mb-10px"},I={style:{position:"relative",top:"12px"}},R={key:0};function A(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!x(e)}const D=t({__name:"Node",setup(t){const{t:x}=b(),D=a([{field:"selection",type:"selection",width:"55"},{field:"name",label:x("node.nodeName"),minWidth:20},{field:"maxTaskNum",label:x("configuration.maxTaskNum"),minWidth:10,formatter:(e,t,a)=>o(C,{type:"info"},(()=>a))},{field:"running",label:x("node.taskCount"),minWidth:10,formatter:(e,t,a)=>o(C,{round:!0,effect:"plain",hit:!0},(()=>a))},{field:"finished",label:x("node.finished"),minWidth:10,formatter:(e,t,a)=>o(C,{round:!0,effect:"plain",hit:!0},(()=>a))},{field:"cpuNum",label:x("node.nodeUsageCpu"),minWidth:20,formatter:(e,t,a)=>{let l=parseFloat(a);return l=parseFloat(l.toFixed(2)),o(C,{round:!0,effect:"plain",hit:!0,type:l<50?"":l<80?"warning":"danger"},(()=>l+"%"))}},{field:"memNum",label:x("node.nodeUsageMemory"),minWidth:20,formatter:(e,t,a)=>{let l=parseFloat(a);return l=parseFloat(l.toFixed(2)),o(C,{round:!0,effect:"plain",hit:!0,type:l<50?"":l<80?"warning":"danger"},(()=>l+"%"))}},{field:"state",label:x("node.nodeStatus"),minWidth:20,formatter:(e,t,a)=>o(C,{type:"1"===a?"success":"2"===a?"warning":"danger",effect:"light",hit:!0},(()=>x("1"===a?"node.statusRun":"2"===a?"node.statusStop":"node.statusError")))},{field:"updateTime",label:x("node.updateTime"),minWidth:20},{field:"action",label:x("tableDemo.action"),minWidth:30,formatter:(e,t,a)=>{let o,s,r,m;return l(n,null,[l(i,{type:"warning",size:"small",onClick:()=>ce(e.name)},A(o=x("node.plugin"))?o:{default:()=>[o]}),l(i,{type:"success",size:"small",onClick:()=>re(e)},A(s=x("node.log"))?s:{default:()=>[s]}),l(i,{type:"primary",size:"small",onClick:()=>Z(e)},A(r=x("common.config"))?r:{default:()=>[r]}),l(k,{content:x("node.restartMsg")},{default:()=>[l(i,{type:"danger",size:"small",onClick:()=>ee(e.name)},A(m=x("node.restart"))?m:{default:()=>[m]})]})])}}]),{tableRegister:L,tableState:M,tableMethods:O}=N({fetchDataApi:async()=>({list:(await T()).data.list})}),{loading:P,dataList:J,currentPage:B,pageSize:G}=M,{getList:K,getElTableExpose:X}=O;function Y(){return{background:"var(--el-fill-color-light)"}}const $=s(!1),q=()=>{$.value=!1},Q=a({name:"",maxTaskNum:"",state:"",ModulesConfig:""}),Z=async e=>{Q.name=e.name,Q.maxTaskNum=e.maxTaskNum,Q.ModulesConfig=e.modulesConfig,Q.state=e.state,$.value=!0},ee=async e=>{await W(e)},te=s(!1),ae=s([]),oe=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await(async()=>{const e=await X(),t=(null==e?void 0:e.getSelectionRows())||[];ae.value=t.map((e=>e.name)),te.value=!0;try{await U(ae.value),te.value=!1,K()}catch(a){te.value=!1,K()}})()},le=s(!1),ie=()=>{le.value=!1},ne=s(""),se=s(),re=async e=>{const t=await z(e.name);ne.value=t.logs,le.value=!0;const a="https:"===window.location.protocol?"wss://":"ws://",o=window.location.host,l=new WebSocket(a+o);l.onopen=()=>{setInterval((()=>{const t={node_name:e.name};l.send(JSON.stringify(t))}),3e3)},l.onmessage=e=>{ne.value+=e.data,se.value.setScrollTop(5e3)};const i=r(le,(e=>{e||(l.close(),i())}))};m((()=>{de(),window.addEventListener("resize",de)}));const me=s(0),de=()=>{const e=window.innerHeight||document.documentElement.clientHeight;me.value=.7*e},ue=s(""),pe=s(!1),ce=async e=>{ue.value=e,pe.value=!0},fe=()=>{pe.value=!1};return(t,a)=>(d(),u(n,null,[l(c(e),null,{default:p((()=>[l(c(h),null,{default:p((()=>[l(c(w),{style:{position:"relative",top:"16px"}},{default:p((()=>[f("div",V,[l(c(i),{type:"danger",loading:te.value,onClick:oe},{default:p((()=>[g(v(c(x)("common.delete")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),f("div",I,[l(c(S),{pageSize:c(G),"onUpdate:pageSize":a[0]||(a[0]=e=>y(G)?G.value=e:null),currentPage:c(B),"onUpdate:currentPage":a[1]||(a[1]=e=>y(B)?B.value=e:null),columns:D,data:c(J),stripe:"",border:!0,loading:c(P),resizable:!0,onRegister:c(L),headerCellStyle:Y,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","onRegister"])])])),_:1}),l(c(E),{modelValue:$.value,"onUpdate:modelValue":a[2]||(a[2]=e=>$.value=e),title:t.$t("common.config"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:me.value},{default:p((()=>[l(F,{closeDialog:q,nodeConfForm:Q,getList:c(K)},null,8,["nodeConfForm","getList"])])),_:1},8,["modelValue","title","maxHeight"]),l(c(E),{modelValue:le.value,"onUpdate:modelValue":a[3]||(a[3]=e=>le.value=e),title:c(x)("node.log"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:me.value},{footer:p((()=>[l(c(i),{onClick:ie},{default:p((()=>[g(v(c(x)("common.off")),1)])),_:1})])),default:p((()=>[l(c(_),{ref_key:"scrollbarRef",ref:se},{default:p((()=>[ne.value?(d(),u("pre",R,v(ne.value),1)):j("",!0)])),_:1},512)])),_:1},8,["modelValue","title","maxHeight"]),l(c(E),{modelValue:pe.value,"onUpdate:modelValue":a[4]||(a[4]=e=>pe.value=e),title:c(x)("node.plugin"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"},maxHeight:me.value},{default:p((()=>[l(H,{closeDialog:fe,name:ue.value},null,8,["name"])])),_:1},8,["modelValue","title","maxHeight"])],64))}});export{D as default};
