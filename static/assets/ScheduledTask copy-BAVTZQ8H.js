import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as t,l as a,r as l,s as o,e as s,L as i,G as n,F as r,H as d,o as u,i as p,w as m,a as c,z as f,t as g,A as v,B as y,f as _,I as h,J as b,M as j}from"./index-C6fb_XFi.js";import{E as x,a as w}from"./el-col-Dl4_4Pn5.js";import{E as k}from"./el-text-BnUG9HvL.js";import{a as V,E as S}from"./el-tab-pane-DDoZFwPS.js";import{E,a as N}from"./el-form-C2Y6uNCj.js";import{E as A}from"./el-input-number-DVs4I2j5.js";import"./el-tag-C_oEQYGz.js";import{E as C}from"./el-popper-CeVwVUf9.js";import"./el-virtual-list-D7NvYvyu.js";import{E as T}from"./el-select-v2-CaMVABoW.js";import{E as U}from"./el-checkbox-CvJzNe2E.js";import"./el-tooltip-l0sNRNKZ.js";import{E as z}from"./el-switch-Bh7JeorW.js";import{_ as W}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as I}from"./useTable-CijeIiBB.js";import{u as H}from"./useIcon-BxqaCND-.js";import{c as L,b as P,u as D}from"./index-B40b3p-m.js";import{_ as M}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{_ as R}from"./AddTask.vue_vue_type_script_setup_true_lang-BJlN6j_-.js";import{_ as F}from"./PageMonit.vue_vue_type_script_setup_true_lang-ePBLYKxC.js";import{a as O}from"./index-CBLGyxDn.js";import"./el-card-B37ahJ8o.js";import"./strings-BiUeKphX.js";import"./castArray-DRqY4cIf.js";import"./raf-DGOAeO92.js";import"./useInput-IB6tFdGu.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-table-column-C9CkC7I1.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./index-CnCQNuY4.js";import"./el-divider-Bw95UAdD.js";import"./el-radio-group-hI5DSxSU.js";import"./DetailTemplate-Dao-XeZd.js";/* empty css                */import"./index-DWlzJn9A.js";import"./index-fOwuVARc.js";const B={class:"mb-10px"},G={style:{position:"relative",top:"12px"}};function J(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!b(e)}const q=t({__name:"ScheduledTask copy",setup(t){const b=H({icon:"iconoir:search"}),{t:q}=a(),K=l(""),X=()=>{se()},Y=o([{field:"selection",type:"selection",width:"55"},{field:"name",label:q("task.taskName"),minWidth:30},{field:"cycle",label:q("task.taskCycle")+"(h)",minWidth:20},{field:"type",label:q("task.typeTask"),minWidth:20},{field:"lastTime",label:q("task.lastTime"),minWidth:40,formatter:(e,t,a)=>""==a?"-":a},{field:"nextTime",label:q("task.nextTime"),minWidth:40,formatter:(e,t,a)=>""==a||0==e.state?"-":a},{field:"state",label:q("common.state"),minWidth:20,formatter:(e,t,a)=>{if(null==a)return s("div",null,null);let l="",o="";return 1==a?(l="#2eb98a",o=q("common.on")):(l="red",o=q("common.statusStop")),s(w,{gutter:20},{default:()=>[s(x,{span:1},{default:()=>[s(i,{icon:"clarity:circle-solid",color:l},null)]}),s(x,{span:5},{default:()=>[s(k,{type:"info"},J(o)?o:{default:()=>[o]})]})]})}},{field:"action",label:q("tableDemo.action"),minWidth:40,formatter:(e,t,a)=>{let l,o,i;return e.type,s(r,null,["page_monitoring"===e.id?s(n,{type:"success",onClick:()=>Se(e)},J(l=q("common.edit"))?l:{default:()=>[l]}):s(r,null,[s(n,{type:"success",onClick:()=>ce(e)},J(o=q("common.edit"))?o:{default:()=>[o]}),s(n,{type:"danger",onClick:()=>ge(e)},J(i=q("common.delete"))?i:{default:()=>[i]})])])}}]),{tableRegister:Q,tableState:Z,tableMethods:$}=I({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=Z,a=await L(K.value,e.value,t.value);return{list:a.data.list,total:a.data.total}},immediate:!0}),{loading:ee,dataList:te,total:ae,currentPage:le,pageSize:oe}=Z;oe.value=20;const{getList:se,getElTableExpose:ie}=$;function ne(){return{background:"var(--el-fill-color-light)"}}const re=l(!1);let de=q("task.addTask");const ue=()=>{re.value=!1};let pe="",me=l(!0);const ce=async e=>{"Scan"==e.type?pe=e.id:xe.value=!0,de=q("common.edit")},fe=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await he()},ge=async e=>{window.confirm("Are you sure you want to delete the selected data?")&&await ye(e)},ve=l(!1),ye=async e=>{ve.value=!0;try{await P([e.id]);ve.value=!1,se()}catch(t){ve.value=!1,se()}},_e=l([]),he=async()=>{const e=await ie(),t=(null==e?void 0:e.getSelectionRows())||[];_e.value=t.map((e=>e.id)),ve.value=!0;try{await P(_e.value);ve.value=!1,se()}catch(a){ve.value=!1,se()}};d((()=>{je(),window.addEventListener("resize",je)}));const be=l(0),je=()=>{const e=window.innerHeight||document.documentElement.clientHeight;be.value=.75*e},xe=l(!1),we=l(!1),ke=l(!1),Ve=o({hour:24,allNode:!0,node:[],state:!0}),Se=async e=>{Ve.hour=e.cycle,Ve.allNode=e.allNode,Ve.node=e.node,Ve.state=e.state,we.value=!0},Ee=o([]),Ne=l(!1),Ae=l(!1),Ce=e=>{Ne.value=!1,e?(Ve.allNode=!0,Ve.node=[],Ee.forEach((e=>Ve.node.push(e.value)))):(Ve.allNode=!1,Ve.node=[])};return(async()=>{const e=await O();e.data.list.length>0?(Ae.value=!1,e.data.list.forEach((e=>{Ee.push({value:e,label:e})}))):(Ae.value=!0,j.warning(q("node.onlineNodeMsg")))})(),(t,a)=>(u(),p(c(e),null,{default:m((()=>[s(c(w),null,{default:m((()=>[s(c(x),{span:1},{default:m((()=>[s(c(k),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:m((()=>[f(g(c(q)("task.taskName"))+":",1)])),_:1})])),_:1}),s(c(x),{span:5},{default:m((()=>[s(c(v),{modelValue:K.value,"onUpdate:modelValue":a[0]||(a[0]=e=>K.value=e),placeholder:c(q)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),s(c(x),{span:5,style:{position:"relative",left:"16px"}},{default:m((()=>[s(c(y),{type:"primary",icon:c(b),style:{height:"100%"},onClick:X},{default:m((()=>[f("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),s(c(w),null,{default:m((()=>[s(c(x),{style:{position:"relative",top:"16px"}},{default:m((()=>[_("div",B,[s(c(n),{type:"danger",loading:ve.value,onClick:fe},{default:m((()=>[f(g(c(q)("task.delTask")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),_("div",G,[s(c(W),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:c(oe),"onUpdate:pageSize":a[1]||(a[1]=e=>h(oe)?oe.value=e:null),currentPage:c(le),"onUpdate:currentPage":a[2]||(a[2]=e=>h(le)?le.value=e:null),columns:Y,data:c(te),stripe:"",border:!0,loading:c(ee),"max-height":be.value,resizable:!0,pagination:{total:c(ae),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:c(Q),headerCellStyle:ne,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])]),s(c(M),{modelValue:re.value,"onUpdate:modelValue":a[3]||(a[3]=e=>re.value=e),title:c(de),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:m((()=>[s(R,{closeDialog:ue,getList:c(se),create:c(me),taskid:c(pe),schedule:!0},null,8,["getList","create","taskid"])])),_:1},8,["modelValue","title"]),s(c(M),{modelValue:we.value,"onUpdate:modelValue":a[9]||(a[9]=e=>we.value=e),title:c(q)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:m((()=>[s(c(V),{type:"card"},{default:m((()=>[s(c(S),{label:c(q)("router.configuration")},{default:m((()=>[s(c(E),{model:Ve,"label-width":"100px","status-icon":"",ref:"ruleFormRef"},{default:m((()=>[s(c(C),{content:c(q)("task.selectNodeMsg"),placement:"top"},{default:m((()=>[s(c(N),{label:c(q)("task.nodeSelect"),prop:"node"},{default:m((()=>[s(c(T),{modelValue:Ve.node,"onUpdate:modelValue":a[5]||(a[5]=e=>Ve.node=e),filterable:"",options:Ee,placeholder:"Please select node",style:{width:"80%"},multiple:"","tag-type":"success","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":7},{header:m((()=>[s(c(U),{modelValue:Ve.allNode,"onUpdate:modelValue":a[4]||(a[4]=e=>Ve.allNode=e),disabled:Ae.value,indeterminate:Ne.value,onChange:Ce},{default:m((()=>[f(" All ")])),_:1},8,["modelValue","disabled","indeterminate"])])),_:1},8,["modelValue","options"])])),_:1},8,["label"])])),_:1},8,["content"]),s(c(N),{label:c(q)("project.cycle"),prop:"type"},{default:m((()=>[s(c(A),{modelValue:Ve.hour,"onUpdate:modelValue":a[6]||(a[6]=e=>Ve.hour=e),min:1,"controls-position":"right",size:"small"},null,8,["modelValue"]),s(c(k),{style:{position:"relative",left:"16px"}},{default:m((()=>[f("Hour")])),_:1})])),_:1},8,["label"]),s(c(N),{label:c(q)("common.state")},{default:m((()=>[s(c(z),{modelValue:Ve.state,"onUpdate:modelValue":a[7]||(a[7]=e=>Ve.state=e),"inline-prompt":"","active-text":c(q)("common.switchAction"),"inactive-text":c(q)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),s(c(w),null,{default:m((()=>[s(c(x),{span:2,offset:8},{default:m((()=>[s(c(N),null,{default:m((()=>[s(c(y),{type:"primary",onClick:a[8]||(a[8]=e=>(async()=>{ke.value=!0,await D(Ve.hour,Ve.node,Ve.allNode,Ve.state),ke.value=!1,se()})()),loading:ke.value},{default:m((()=>[f(g(c(q)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["label"]),s(c(S),{label:c(q)("task.data")},{default:m((()=>[s(F)])),_:1},8,["label"])])),_:1})])),_:1},8,["modelValue","title"])])),_:1}))}});export{q as default};
