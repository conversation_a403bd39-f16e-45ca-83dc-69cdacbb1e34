import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{_ as o}from"./Dialog.vue_vue_type_style_index_0_lang-DjaYHddI.js";import{d as t,l,r as i,s,y as a,o as r,i as m,w as p,e as n,z as u,t as d,a as c,c as j,F as f,R as _,f as g}from"./index-C6fb_XFi.js";import{u as v,F as y}from"./useForm-RHT9mrs1.js";import{u as b}from"./useValidator-B7S-lU_d.js";import{g as D}from"./index-GgyIUJdx.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./refs-3HtnmaOD.js";import"./el-form-C2Y6uNCj.js";import"./castArray-DRqY4cIf.js";import"./el-col-Dl4_4Pn5.js";import"./el-tag-C_oEQYGz.js";import"./el-checkbox-CvJzNe2E.js";import"./index-BWEJ0epC.js";import"./el-radio-group-hI5DSxSU.js";/* empty css                          */import"./el-input-number-DVs4I2j5.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./debounce-BwgdhaaK.js";import"./el-virtual-list-D7NvYvyu.js";import"./raf-DGOAeO92.js";import"./el-select-v2-CaMVABoW.js";import"./el-switch-Bh7JeorW.js";import"./el-tree-select-D8i5b8X5.js";import"./index-w1A_Rrgh.js";import"./el-divider-Bw95UAdD.js";/* empty css                */import"./el-upload-DFauS7op.js";import"./el-progress-sY5OgffI.js";import"./InputPassword-ywconkEY.js";import"./style.css_vue_type_style_index_0_src_true_lang-DFrnfRdK.js";import"./JsonEditor.vue_vue_type_script_setup_true_lang-BhiHGTWK.js";import"./IconPicker-DMD4uMJR.js";import"./el-tab-pane-DDoZFwPS.js";import"./el-pagination-FWx5cl5J.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./tsxHelper-CeCzRM_x.js";import"./index-CnCQNuY4.js";const x=t({__name:"Dialog",setup(t){const{required:x}=b(),{t:k}=l(),h=i(!1),P=i(!1),{formRegister:C,formMethods:V}=v(),{getElFormExpose:w}=V,F=s([{field:"field1",label:k("formDemo.input"),component:"Input",formItemProps:{rules:[x()]}},{field:"field2",label:k("formDemo.select"),component:"Select",optionApi:async()=>(await D()).data},{field:"field3",label:k("formDemo.radio"),component:"RadioGroup",componentProps:{options:[{label:"option-1",value:"1"},{label:"option-2",value:"2"}]}},{field:"field4",label:k("formDemo.checkbox"),component:"CheckboxGroup",value:[],componentProps:{options:[{label:"option-1",value:"1"},{label:"option-2",value:"2"}]}},{field:"field5",component:"DatePicker",label:k("formDemo.datePicker"),componentProps:{type:"date"}},{field:"field6",component:"TimeSelect",label:k("formDemo.timeSelect")}]),I=async()=>{const e=await w();null==e||e.validate((e=>{}))};return(t,l)=>{const i=a("BaseButton");return r(),m(c(e),{title:c(k)("dialogDemo.dialog"),message:c(k)("dialogDemo.dialogDes")},{default:p((()=>[n(i,{type:"primary",onClick:l[0]||(l[0]=e=>h.value=!h.value)},{default:p((()=>[u(d(c(k)("dialogDemo.open")),1)])),_:1}),n(i,{type:"primary",onClick:l[1]||(l[1]=e=>P.value=!P.value)},{default:p((()=>[u(d(c(k)("dialogDemo.combineWithForm")),1)])),_:1}),n(c(o),{modelValue:h.value,"onUpdate:modelValue":l[3]||(l[3]=e=>h.value=e),title:c(k)("dialogDemo.dialog")},{footer:p((()=>[n(i,{onClick:l[2]||(l[2]=e=>h.value=!1)},{default:p((()=>[u(d(c(k)("dialogDemo.close")),1)])),_:1})])),default:p((()=>[(r(),j(f,null,_(1e4,(e=>g("div",{key:e},d(e),1))),64))])),_:1},8,["modelValue","title"]),n(c(o),{modelValue:P.value,"onUpdate:modelValue":l[5]||(l[5]=e=>P.value=e),title:c(k)("dialogDemo.dialog")},{footer:p((()=>[n(i,{type:"primary",onClick:I},{default:p((()=>[u(d(c(k)("dialogDemo.submit")),1)])),_:1}),n(i,{onClick:l[4]||(l[4]=e=>P.value=!1)},{default:p((()=>[u(d(c(k)("dialogDemo.close")),1)])),_:1})])),default:p((()=>[n(c(y),{schema:F,onRegister:c(C)},null,8,["schema","onRegister"])])),_:1},8,["modelValue","title"])])),_:1},8,["title","message"])}}});export{x as default};
