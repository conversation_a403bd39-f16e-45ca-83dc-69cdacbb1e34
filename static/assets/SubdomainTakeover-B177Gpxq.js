import{d as e,H as t,r as a,s as i,e as l,E as s,v as o,A as r,B as n,o as p,c as d,a as u,w as m,I as c,F as g,l as j,Y as f,_ as h}from"./index-DfJTpRkj.js";import{u as v}from"./useTable-CtyddZqf.js";import{E as b}from"./el-card-DyZz6u6e.js";import{E as x}from"./el-pagination-FJcT0ZDj.js";import{E as y}from"./el-tag-CbhrEnto.js";import"./el-select-BkpcrSfo.js";import"./el-popper-D2BmgSQA.js";import{E as _,a as S}from"./el-col-B4Ik8fnS.js";import{_ as w}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as k}from"./useCrudSchemas-Cz9y99Kk.js";import{a as E,d as C,x as V}from"./index-D4GvAO2k.js";import z from"./Csearch-C6xIjicy.js";import"./index-DE7jtbbk.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./el-table-column-7FjdLFwR.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./tree-BfZhwLPs.js";import"./index-D1ADinPR.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import"./el-text-vKNLRkxx.js";import"./el-divider-0NmzbuNU.js";import"./el-autocomplete-CyglTUOR.js";/* empty css                */import"./el-tree-select-P6ZpyTcB.js";import"./index-Co3LqSsp.js";import"./el-switch-C5ZBDFmL.js";import"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import"./useIcon-CNpM61rT.js";import"./exportData.vue_vue_type_script_setup_true_lang-CthEe30Y.js";import"./el-tab-pane-BijWf7kq.js";import"./el-form-DsaI0u2w.js";import"./el-radio-group-CTAZlJKV.js";import"./el-space-7M-SGVBd.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BD8eoln6.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./index-KH6atv8j.js";import"./index-B0JD2UFG.js";import"./DetailTemplate-DU3RXrBH.js";import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";import"./index-jyMftxhc.js";const T=h(e({__name:"SubdomainTakeover",props:{projectList:{}},setup(e){const{t:h}=j(),T=[{keyword:"domain",example:'domain="example.com"',explain:h("searchHelp.domain")},{keyword:"type",example:'type="github"',explain:h("searchHelp.subdomainType")},{keyword:"value",example:'value="exapmle.github.com"',explain:h("searchHelp.subdoaminValue")},{keyword:"response",example:'response="404 Not Found"',explain:h("searchHelp.body")},{keyword:"project",example:'project="Hackerone"',explain:h("searchHelp.project")}];t((()=>{H(),window.addEventListener("resize",H)}));const A=a(0),H=()=>{const e=window.innerHeight||document.documentElement.clientHeight;A.value=.7*e},L=a(""),I=e=>{L.value=e,q()},R=i({}),P=i([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:h("tableDemo.index"),type:"index",minWidth:"30"},{field:"host",label:"Domain",minWidth:"300"},{field:"value",label:h("subdomain.recordValue"),minWidth:"400"},{field:"type",label:"Type",minWidth:"200"},{field:"response",label:"Response",minWidth:"300",formatter:(e,t,a)=>l(s,{"max-height":"100"},{default:()=>[l("div",{style:"whiteSpace: 'pre-line'"},[a])]})},{field:"tags",label:"TAG",fit:"true",formatter:(e,t,i)=>{null==i&&(i=[]),R[e.id]||(R[e.id]={inputVisible:!1,inputValue:"",inputRef:a(null)});const l=R[e.id],s=async()=>{l.inputValue&&(i.push(l.inputValue),E(e.id,U,l.inputValue)),l.inputVisible=!1,l.inputValue=""};return o(S,{},(()=>[...i.map((t=>o(_,{span:24,key:t},(()=>[o("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||te("tags",t)})(e,t)},[o(y,{closable:!0,onClose:()=>(async t=>{const a=i.indexOf(t);a>-1&&i.splice(a,1),C(e.id,U,t)})(t)},(()=>t))])])))),o(_,{span:24},l.inputVisible?()=>o(r,{ref:l.inputRef,modelValue:l.inputValue,"onUpdate:modelValue":e=>l.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&s()},onBlur:s}):()=>o(n,{class:"button-new-tag",size:"small",onClick:()=>(l.inputVisible=!0,void f((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"}]);let U="SubdoaminTakerResult";P.forEach((e=>{e.hidden=e.hidden??!1}));let D=a(!1);const N=({field:e,hidden:t})=>{const a=P.findIndex((t=>t.field===e));-1!==a&&(P[a].hidden=t),(()=>{const e=P.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=D.value,localStorage.setItem(`columnConfig_${U}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${U}`)||"{}");P.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),D.value=e.statisticsHidden})();const{allSchemas:W}=k(P),{tableRegister:F,tableState:O,tableMethods:$}=v({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=O,a=await V(L.value,e.value,t.value,Z);return{list:a.data.list,total:a.data.total}},immediate:!1}),{loading:B,dataList:J,total:K,currentPage:M,pageSize:G}=O;G.value=20;const{getList:q,getElTableExpose:Q}=$;function Y(){return{background:"var(--el-fill-color-light)"}}const Z=i({}),X=(e,t)=>{Object.assign(Z,t),L.value=e,q()},ee=a([]),te=(e,t)=>{const a=`${e}=${t}`;ee.value=[...ee.value,a]},ae=e=>{if(ee.value){const[t,a]=e.split("=");t in Z&&Array.isArray(Z[t])&&(Z[t]=Z[t].filter((e=>e!==a)),0===Z[t].length&&delete Z[t]),ee.value=ee.value.filter((t=>t!==e))}},ie=()=>Z;return(e,t)=>(p(),d(g,null,[l(z,{getList:u(q),handleSearch:I,searchKeywordsData:T,index:u(U),getElTableExpose:u(Q),projectList:e.$props.projectList,handleFilterSearch:X,crudSchemas:P,dynamicTags:ee.value,handleClose:ae,onUpdateColumnVisibility:N,searchResultCount:u(K),getFilter:ie},null,8,["getList","index","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),l(u(S),null,{default:m((()=>[l(u(_),null,{default:m((()=>[l(u(b),{style:{height:"min-content"}},{default:m((()=>[l(u(w),{pageSize:u(G),"onUpdate:pageSize":t[0]||(t[0]=e=>c(G)?G.value=e:null),currentPage:u(M),"onUpdate:currentPage":t[1]||(t[1]=e=>c(M)?M.value=e:null),columns:u(W).tableColumns,data:u(J),stripe:"","max-height":A.value,border:!0,loading:u(B),resizable:!0,onRegister:u(F),headerCellStyle:Y,"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),l(u(_),{":span":24},{default:m((()=>[l(u(b),null,{default:m((()=>[l(u(x),{pageSize:u(G),"onUpdate:pageSize":t[2]||(t[2]=e=>c(G)?G.value=e:null),currentPage:u(M),"onUpdate:currentPage":t[3]||(t[3]=e=>c(M)?M.value=e:null),"page-sizes":[20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:u(K)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-87c8d9ed"]]);export{T as default};
