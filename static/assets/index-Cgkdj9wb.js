import{d as e,aq as i,a8 as n,r as o,o as t,i as a,aJ as d,a as l,j as s,dz as r,e as u,dr as f}from"./index-3XfDPlIS.js";import{E as c}from"./el-image-viewer-DYkjgdL9.js";const m=e({__name:"ImageViewer",props:{urlList:{type:Array,default:()=>[]},zIndex:i.number.def(200),initialIndex:i.number.def(0),infinite:i.bool.def(!0),hideOnClickModal:i.bool.def(!1),teleported:i.bool.def(!1),show:i.bool.def(!1)},setup(e){const i=e,r=n((()=>{const e={...i};return delete e.show,e})),u=o(i.show),f=()=>{u.value=!1};return(e,i)=>u.value?(t(),a(l(c),d({key:0},r.value,{onClose:f}),null,16)):s("",!0)}});let p=null;function h(e){if(!r)return;const{urlList:i,initialIndex:n=0,infinite:o=!0,hideOnClickModal:t=!1,teleported:a=!1,zIndex:d=2e3,show:l=!0}=e,s={},c=document.createElement("div");s.urlList=i,s.initialIndex=n,s.infinite=o,s.hideOnClickModal=t,s.teleported=a,s.zIndex=d,s.show=l,document.body.appendChild(c),p=u(m,s),f(p,c)}export{h as c};
