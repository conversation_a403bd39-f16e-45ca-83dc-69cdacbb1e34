import{d as e,dC as t,H as l,r as a,s,e as i,z as o,F as n,A as r,o as m,i as d,w as p,a as c,B as u,j as h,J as v,l as f,K as g,M as j,_ as y}from"./index-DfJTpRkj.js";import{u as b}from"./useTable-CtyddZqf.js";import{E as x}from"./el-card-DyZz6u6e.js";import{E as _,a as w}from"./el-col-B4Ik8fnS.js";import{E as S}from"./el-text-vKNLRkxx.js";import{_ as C}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as E}from"./useCrudSchemas-Cz9y99Kk.js";import{f as z}from"./index-Bz_w54LI.js";import{y as W}from"./index-D4GvAO2k.js";import"./el-table-column-7FjdLFwR.js";import"./el-popper-D2BmgSQA.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-CbhrEnto.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./tree-BfZhwLPs.js";import"./index-D1ADinPR.js";function I(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!v(e)}const V=y(e({__name:"Service",setup(e){const{t:v}=f(),{query:y}=t();l((()=>{k(),window.addEventListener("resize",k)}));const V=a(0),k=()=>{const e=window.innerHeight||document.documentElement.clientHeight;V.value=.8*e};a("");const A=s({});A.project=[y.id];const U=async e=>{Object.assign(A,e),K()},H=s([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:v("tableDemo.index"),type:"index",minWidth:"30"},{field:"service",label:v("asset.service"),minWidth:"100",formatter:(e,t,l)=>e.count?i(n,null,[i(S,null,I(l)?l:{default:()=>[l]}),i(S,{type:"info"},{default:()=>[o("("),e.count,o(")")]})]):i(S,null,I(l)?l:{default:()=>[l]}),slots:{header:()=>i("div",null,[i("span",null,[v("asset.service")]),i(r,{modelValue:P.value,"onUpdate:modelValue":e=>P.value=e,placeholder:"Search",style:"width: 80px; margin-left: 10px;",size:"small",onChange:()=>$("service_service")},null)])}},{field:"host",label:v("asset.domain"),minWidth:"200",slots:{header:()=>i("div",null,[i("span",null,[v("asset.domain")]),i(r,{modelValue:q.value,"onUpdate:modelValue":e=>q.value=e,placeholder:"Search",style:"width: 80px; margin-left: 10px;",size:"small",onChange:()=>$("service_domain")},null)])}},{field:"ip",label:"IP",minWidth:"250",slots:{header:()=>i("div",null,[i("span",null,[o("IP")]),i(r,{modelValue:J.value,"onUpdate:modelValue":e=>J.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>$("service_ip")},null)])}},{field:"port",label:v("asset.port"),minWidth:"250",slots:{header:()=>i("div",null,[i("span",null,[v("asset.port")]),i(r,{modelValue:N.value,"onUpdate:modelValue":e=>N.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>$("service_port")},null)])}},{field:"time",label:v("asset.time"),minWidth:"200"}]),{allSchemas:R}=E(H),{tableRegister:B,tableState:F,tableMethods:O}=b({fetchDataApi:async()=>({list:(await z("",A,G)).data.list}),immediate:!0}),{loading:T,dataList:D}=F,{getList:K,getElTableExpose:L}=O;function M(){return{background:"var(--el-fill-color-light)"}}const N=a(""),q=a(""),J=a(""),P=a(""),G=s({}),$=async e=>{let t="";"service_port"==e&&(t=N.value),"service_domain"==e&&(t=q.value),"service_ip"==e&&(t=J.value),"service_service"==e&&(t=P.value),G[e]=t,K()},Q=a([]),X=async()=>{g.confirm("Whether to delete?","Warning",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{const e=await L(),t=(null==e?void 0:e.getSelectionRows())||[];Q.value=t.map((e=>e.id)),await W(Q.value,"asset"),K()})).catch((()=>{j({type:"info",message:"Delete canceled"})}))};let Y=a(!1);const Z=async()=>{const e=await L(),t=(null==e?void 0:e.getSelectionRows())||[];Q.value=t.map((e=>e.id)),0!=Q.value.length?Y.value=!0:Y.value=!1};return(e,t)=>(m(),d(c(w),null,{default:p((()=>[i(c(_),null,{default:p((()=>[i(c(x),{style:{height:"min-content"}},{default:p((()=>[c(Y)?(m(),d(c(u),{key:0,onClick:X,type:"danger",size:"small"},{default:p((()=>[o("Dlete")])),_:1})):h("",!0),i(c(C),{columns:c(R).tableColumns,data:c(D),"max-height":V.value,border:!0,loading:c(T),onSelectionChange:Z,rowKey:"id",resizable:!0,onRegister:c(B),onFilterChange:U,headerCellStyle:M,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["columns","data","max-height","loading","onRegister"])])),_:1})])),_:1})])),_:1}))}}),[["__scopeId","data-v-f2d00836"]]);export{V as default};
