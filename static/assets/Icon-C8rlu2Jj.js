import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-B-a8-WPJ.js";import{d as o,l as n,y as i,o as s,c as t,e as c,a,w as l,f as p,z as u,F as m}from"./index-C6fb_XFi.js";import{_ as r}from"./Infotip.vue_vue_type_script_setup_true_lang-59YrZr7S.js";import{u as d}from"./useIcon-BxqaCND-.js";import"./el-card-B37ahJ8o.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-popper-CeVwVUf9.js";import"./Highlight.vue_vue_type_script_lang-9Vlpm4kA.js";const _={class:"flex justify-between"},f={class:"flex justify-between"},v={class:"flex justify-between"},y=o({__name:"Icon",setup(o){const{t:y}=n(),g=e=>{e===y("iconDemo.accessAddress")&&window.open("https://iconify.design/")},j=d({icon:"svg-icon:peoples"}),D=d({icon:"svg-icon:money"}),h=d({icon:"ep:aim"}),b=d({icon:"ep:alarm-clock"});return(o,n)=>{const d=i("Icon",!0),w=i("BaseButton");return s(),t(m,null,[c(a(r),{"show-index":!1,title:`${a(y)("iconDemo.recommendedUse")}${a(y)("iconDemo.iconify")}`,schema:[{label:a(y)("iconDemo.recommendeDes"),keys:["Iconify"]},{label:a(y)("iconDemo.accessAddress"),keys:[a(y)("iconDemo.accessAddress")]}],onClick:g},null,8,["title","schema"]),c(a(e),{title:a(y)("iconDemo.localIcon")},{default:l((()=>[p("div",_,[c(d,{icon:"svg-icon:peoples"}),c(d,{icon:"svg-icon:money"}),c(d,{icon:"svg-icon:message"}),c(d,{icon:"svg-icon:shopping"})])])),_:1},8,["title"]),c(a(e),{title:a(y)("iconDemo.iconify")},{default:l((()=>[p("div",f,[c(d,{icon:"ep:aim"}),c(d,{icon:"ep:alarm-clock"}),c(d,{icon:"ep:baseball"}),c(d,{icon:"ep:chat-line-round"})])])),_:1},8,["title"]),c(a(e),{title:"useIcon"},{default:l((()=>[p("div",v,[c(w,{icon:a(j)},{default:l((()=>[u("Button")])),_:1},8,["icon"]),c(w,{icon:a(D)},{default:l((()=>[u("Button")])),_:1},8,["icon"]),c(w,{icon:a(h)},{default:l((()=>[u("Button")])),_:1},8,["icon"]),c(w,{icon:a(b)},{default:l((()=>[u("Button")])),_:1},8,["icon"])])])),_:1})],64)}}});export{y as default};
