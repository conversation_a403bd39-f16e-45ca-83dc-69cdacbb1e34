import{Y as a,ca as e,d as s,aF as t,a6 as l,a7 as n,aN as i,o as p,i as r,w as u,a8 as c,n as m,a as o,aH as d,aI as y,a9 as g,ag as f}from"./index-C6fb_XFi.js";const x=a({type:{type:String,values:["primary","success","info","warning","danger",""],default:""},size:{type:String,values:e,default:""},truncated:{type:Boolean},lineClamp:{type:[String,Number]},tag:{type:String,default:"span"}}),v=s({name:"ElText"});const S=f(g(s({...v,props:x,setup(a){const e=a,s=t(),g=l("text"),f=n((()=>[g.b(),g.m(e.type),g.m(s.value),g.is("truncated",e.truncated),g.is("line-clamp",!i(e.lineClamp))]));return(a,e)=>(p(),r(y(a.tag),{class:m(o(f)),style:d({"-webkit-line-clamp":a.lineClamp})},{default:u((()=>[c(a.$slots,"default")])),_:3},8,["class","style"]))}}),[["__file","text.vue"]]));export{S as E};
