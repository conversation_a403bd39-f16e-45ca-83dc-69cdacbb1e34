import{Y as a,ca as s,d as e,aF as l,a6 as o,a7 as n,o as t,c,f as i,a8 as r,n as u,a as p,i as d,w as f,e as k,bd as m,ae as g,C as y,j as b,aH as v,h as C,a9 as h,ag as _}from"./index-C6fb_XFi.js";const B=a({type:{type:String,values:["primary","success","info","warning","danger"],default:"primary"},closable:Boolean,disableTransitions:<PERSON>olean,hit:Boolean,color:String,size:{type:String,values:s},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),E={close:a=>a instanceof MouseEvent,click:a=>a instanceof MouseEvent},S=e({name:"ElTag"});const T=_(h(e({...S,props:B,emits:E,setup(a,{emit:s}){const e=a,h=l(),_=o("tag"),B=n((()=>{const{type:a,hit:s,effect:l,closable:o,round:n}=e;return[_.b(),_.is("closable",o),_.m(a||"primary"),_.m(h.value),_.m(l),_.is("hit",s),_.is("round",n)]})),E=a=>{s("close",a)},S=a=>{s("click",a)};return(a,s)=>a.disableTransitions?(t(),c("span",{key:0,class:u(p(B)),style:v({backgroundColor:a.color}),onClick:S},[i("span",{class:u(p(_).e("content"))},[r(a.$slots,"default")],2),a.closable?(t(),d(p(y),{key:0,class:u(p(_).e("close")),onClick:g(E,["stop"])},{default:f((()=>[k(p(m))])),_:1},8,["class","onClick"])):b("v-if",!0)],6)):(t(),d(C,{key:1,name:`${p(_).namespace.value}-zoom-in-center`,appear:""},{default:f((()=>[i("span",{class:u(p(B)),style:v({backgroundColor:a.color}),onClick:S},[i("span",{class:u(p(_).e("content"))},[r(a.$slots,"default")],2),a.closable?(t(),d(p(y),{key:0,class:u(p(_).e("close")),onClick:g(E,["stop"])},{default:f((()=>[k(p(m))])),_:1},8,["class","onClick"])):b("v-if",!0)],6)])),_:3},8,["name"]))}}),[["__file","tag.vue"]]));export{T as E,B as t};
