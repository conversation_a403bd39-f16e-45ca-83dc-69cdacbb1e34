import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import{d as t,l as a,r as l,s as o,e as s,L as i,G as n,F as r,H as d,o as u,i as p,w as m,a as c,z as f,t as g,A as v,B as y,f as _,I as h,J as j,M as b}from"./index-DfJTpRkj.js";import{E as x,a as w}from"./el-col-B4Ik8fnS.js";import{E as k}from"./el-text-vKNLRkxx.js";import{a as S,E as V}from"./el-tab-pane-BijWf7kq.js";import{E,a as N}from"./el-form-DsaI0u2w.js";import{E as A}from"./el-input-number-DV6Zl9Iq.js";import"./el-tag-CbhrEnto.js";import{E as C}from"./el-popper-D2BmgSQA.js";import"./el-virtual-list-DQOsVxKt.js";import{E as T}from"./el-select-v2-D406kAkc.js";import{E as U}from"./el-checkbox-DU4wMKRd.js";import"./el-tooltip-l0sNRNKZ.js";import{E as z}from"./el-switch-C5ZBDFmL.js";import{_ as I}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as L}from"./useTable-CtyddZqf.js";import{u as P}from"./useIcon-CNpM61rT.js";import{e as W,c as H,u as M}from"./index-B0JD2UFG.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import{_ as F}from"./AddTask.vue_vue_type_script_setup_true_lang-BD8eoln6.js";import{_ as D}from"./PageMonit.vue_vue_type_script_setup_true_lang-GtPHHoMi.js";import{a as O}from"./index-KH6atv8j.js";import"./el-card-DyZz6u6e.js";import"./strings-CUyZ1T6U.js";import"./castArray-CvwAI87l.js";import"./raf-zH43jzIi.js";import"./useInput-BVvvvzTX.js";import"./index-DE7jtbbk.js";import"./debounce-CmAGCOy_.js";import"./el-table-column-7FjdLFwR.js";import"./isArrayLikeObject-DtpcG_un.js";import"./el-pagination-FJcT0ZDj.js";import"./el-select-BkpcrSfo.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./index-D1ADinPR.js";import"./el-divider-0NmzbuNU.js";import"./el-radio-group-CTAZlJKV.js";/* empty css                */import"./el-tree-select-P6ZpyTcB.js";import"./index-Co3LqSsp.js";import"./DetailTemplate-DU3RXrBH.js";import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";import"./index-jyMftxhc.js";const B={class:"mb-10px"},G={style:{position:"relative",top:"12px"}};function J(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!j(e)}const K=t({__name:"ScheduledTask copy",setup(t){const j=P({icon:"iconoir:search"}),{t:K}=a(),Q=l(""),Z=()=>{se()},$=o([{field:"selection",type:"selection",width:"55"},{field:"name",label:K("task.taskName"),minWidth:30},{field:"cycle",label:K("task.taskCycle")+"(h)",minWidth:20},{field:"type",label:K("task.typeTask"),minWidth:20},{field:"lastTime",label:K("task.lastTime"),minWidth:40,formatter:(e,t,a)=>""==a?"-":a},{field:"nextTime",label:K("task.nextTime"),minWidth:40,formatter:(e,t,a)=>""==a||0==e.state?"-":a},{field:"state",label:K("common.state"),minWidth:20,formatter:(e,t,a)=>{if(null==a)return s("div",null,null);let l="",o="";return 1==a?(l="#2eb98a",o=K("common.on")):(l="red",o=K("common.statusStop")),s(w,{gutter:20},{default:()=>[s(x,{span:1},{default:()=>[s(i,{icon:"clarity:circle-solid",color:l},null)]}),s(x,{span:5},{default:()=>[s(k,{type:"info"},J(o)?o:{default:()=>[o]})]})]})}},{field:"action",label:K("tableDemo.action"),minWidth:40,formatter:(e,t,a)=>{let l,o,i;return e.type,s(r,null,["page_monitoring"===e.id?s(n,{type:"success",onClick:()=>Ve(e)},J(l=K("common.edit"))?l:{default:()=>[l]}):s(r,null,[s(n,{type:"success",onClick:()=>ce(e)},J(o=K("common.edit"))?o:{default:()=>[o]}),s(n,{type:"danger",onClick:()=>ge(e)},J(i=K("common.delete"))?i:{default:()=>[i]})])])}}]),{tableRegister:q,tableState:X,tableMethods:Y}=L({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=X,a=await W(Q.value,e.value,t.value);return{list:a.data.list,total:a.data.total}},immediate:!0}),{loading:ee,dataList:te,total:ae,currentPage:le,pageSize:oe}=X;oe.value=20;const{getList:se,getElTableExpose:ie}=Y;function ne(){return{background:"var(--el-fill-color-light)"}}const re=l(!1);let de=K("task.addTask");const ue=()=>{re.value=!1};let pe="",me=l(!0);const ce=async e=>{"Scan"==e.type?pe=e.id:xe.value=!0,de=K("common.edit")},fe=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await he()},ge=async e=>{window.confirm("Are you sure you want to delete the selected data?")&&await ye(e)},ve=l(!1),ye=async e=>{ve.value=!0;try{await H([e.id]);ve.value=!1,se()}catch(t){ve.value=!1,se()}},_e=l([]),he=async()=>{const e=await ie(),t=(null==e?void 0:e.getSelectionRows())||[];_e.value=t.map((e=>e.id)),ve.value=!0;try{await H(_e.value);ve.value=!1,se()}catch(a){ve.value=!1,se()}};d((()=>{be(),window.addEventListener("resize",be)}));const je=l(0),be=()=>{const e=window.innerHeight||document.documentElement.clientHeight;je.value=.75*e},xe=l(!1),we=l(!1),ke=l(!1),Se=o({hour:24,allNode:!0,node:[],state:!0}),Ve=async e=>{Se.hour=e.cycle,Se.allNode=e.allNode,Se.node=e.node,Se.state=e.state,we.value=!0},Ee=o([]),Ne=l(!1),Ae=l(!1),Ce=e=>{Ne.value=!1,e?(Se.allNode=!0,Se.node=[],Ee.forEach((e=>Se.node.push(e.value)))):(Se.allNode=!1,Se.node=[])};return(async()=>{const e=await O();e.data.list.length>0?(Ae.value=!1,e.data.list.forEach((e=>{Ee.push({value:e,label:e})}))):(Ae.value=!0,b.warning(K("node.onlineNodeMsg")))})(),(t,a)=>(u(),p(c(e),null,{default:m((()=>[s(c(w),null,{default:m((()=>[s(c(x),{span:1},{default:m((()=>[s(c(k),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:m((()=>[f(g(c(K)("task.taskName"))+":",1)])),_:1})])),_:1}),s(c(x),{span:5},{default:m((()=>[s(c(v),{modelValue:Q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value=e),placeholder:c(K)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),s(c(x),{span:5,style:{position:"relative",left:"16px"}},{default:m((()=>[s(c(y),{type:"primary",icon:c(j),style:{height:"100%"},onClick:Z},{default:m((()=>[f("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),s(c(w),null,{default:m((()=>[s(c(x),{style:{position:"relative",top:"16px"}},{default:m((()=>[_("div",B,[s(c(n),{type:"danger",loading:ve.value,onClick:fe},{default:m((()=>[f(g(c(K)("task.delTask")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),_("div",G,[s(c(I),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:c(oe),"onUpdate:pageSize":a[1]||(a[1]=e=>h(oe)?oe.value=e:null),currentPage:c(le),"onUpdate:currentPage":a[2]||(a[2]=e=>h(le)?le.value=e:null),columns:$,data:c(te),stripe:"",border:!0,loading:c(ee),"max-height":je.value,resizable:!0,pagination:{total:c(ae),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:c(q),headerCellStyle:ne,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])]),s(c(R),{modelValue:re.value,"onUpdate:modelValue":a[3]||(a[3]=e=>re.value=e),title:c(de),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:m((()=>[s(F,{closeDialog:ue,getList:c(se),create:c(me),taskid:c(pe),schedule:!0},null,8,["getList","create","taskid"])])),_:1},8,["modelValue","title"]),s(c(R),{modelValue:we.value,"onUpdate:modelValue":a[9]||(a[9]=e=>we.value=e),title:c(K)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:m((()=>[s(c(S),{type:"card"},{default:m((()=>[s(c(V),{label:c(K)("router.configuration")},{default:m((()=>[s(c(E),{model:Se,"label-width":"100px","status-icon":"",ref:"ruleFormRef"},{default:m((()=>[s(c(C),{content:c(K)("task.selectNodeMsg"),placement:"top"},{default:m((()=>[s(c(N),{label:c(K)("task.nodeSelect"),prop:"node"},{default:m((()=>[s(c(T),{modelValue:Se.node,"onUpdate:modelValue":a[5]||(a[5]=e=>Se.node=e),filterable:"",options:Ee,placeholder:"Please select node",style:{width:"80%"},multiple:"","tag-type":"success","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":7},{header:m((()=>[s(c(U),{modelValue:Se.allNode,"onUpdate:modelValue":a[4]||(a[4]=e=>Se.allNode=e),disabled:Ae.value,indeterminate:Ne.value,onChange:Ce},{default:m((()=>[f(" All ")])),_:1},8,["modelValue","disabled","indeterminate"])])),_:1},8,["modelValue","options"])])),_:1},8,["label"])])),_:1},8,["content"]),s(c(N),{label:c(K)("project.cycle"),prop:"type"},{default:m((()=>[s(c(A),{modelValue:Se.hour,"onUpdate:modelValue":a[6]||(a[6]=e=>Se.hour=e),min:1,"controls-position":"right",size:"small"},null,8,["modelValue"]),s(c(k),{style:{position:"relative",left:"16px"}},{default:m((()=>[f("Hour")])),_:1})])),_:1},8,["label"]),s(c(N),{label:c(K)("common.state")},{default:m((()=>[s(c(z),{modelValue:Se.state,"onUpdate:modelValue":a[7]||(a[7]=e=>Se.state=e),"inline-prompt":"","active-text":c(K)("common.switchAction"),"inactive-text":c(K)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),s(c(w),null,{default:m((()=>[s(c(x),{span:2,offset:8},{default:m((()=>[s(c(N),null,{default:m((()=>[s(c(y),{type:"primary",onClick:a[8]||(a[8]=e=>(async()=>{ke.value=!0,await M(Se.hour,Se.node,Se.allNode,Se.state),ke.value=!1,se()})()),loading:ke.value},{default:m((()=>[f(g(c(K)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["label"]),s(c(V),{label:c(K)("task.data")},{default:m((()=>[s(D)])),_:1},8,["label"])])),_:1})])),_:1},8,["modelValue","title"])])),_:1}))}});export{K as default};
