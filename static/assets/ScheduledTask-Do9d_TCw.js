import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-KpS3kPsF.js";import{d as t,r as a,s as l,e as o,L as s,G as i,F as n,H as r,o as d,i as u,w as p,a as m,z as c,t as f,A as v,B as g,f as _,I as y,J as j,l as h,M as b}from"./index-3XfDPlIS.js";import{E as x,a as k}from"./el-col-CN1tVfqh.js";import{E as w}from"./el-text-CLWE0mUm.js";import{a as V,E as S}from"./el-tab-pane-xcqYouKU.js";import{E,a as A}from"./el-form-BY8piFS2.js";import{E as N}from"./el-input-number-CfcpPMpr.js";import"./el-tag-DcMbxLLg.js";import{E as C}from"./el-popper-DVoWBu_3.js";import"./el-virtual-list-Drl4IGmp.js";import{E as T}from"./el-select-v2-CJw7ZO42.js";import{E as U}from"./el-checkbox-DjLAvZXr.js";import"./el-tooltip-l0sNRNKZ.js";import{E as z}from"./el-switch-C-DLgt5X.js";import{_ as P}from"./Table.vue_vue_type_script_lang-B7lrRql4.js";import{u as D}from"./useTable-BezX3TfM.js";import{u as I}from"./useIcon-k-uSyz6l.js";import{c as L,e as W,u as H}from"./index-BuRY9bVn.js";import{_ as M}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{_ as R}from"./AddTask.vue_vue_type_script_setup_true_lang-BE38tpz6.js";import{_ as F}from"./AddProject.vue_vue_type_script_setup_true_lang-CRi0NtY7.js";import{_ as O}from"./PageMonit.vue_vue_type_script_setup_true_lang-CRgNw5qN.js";import{a as B}from"./index-lpN3i-fa.js";import"./el-card-CuEws33_.js";import"./strings-Dm4Pnsdt.js";import"./castArray-uOT054sj.js";import"./raf-BoCEWvzN.js";import"./useInput-SkgDzq11.js";import"./index-tjM0-mlU.js";import"./debounce-Cb7r1Afr.js";import"./el-table-column-B5hg3WH6.js";import"./isArrayLikeObject-fwCL_6Lc.js";import"./el-pagination-DwzzZyu4.js";import"./el-select-DH55-Cab.js";import"./el-image-viewer-DYkjgdL9.js";import"./el-empty-DqjrkPzw.js";import"./tsxHelper-C7SpLWNA.js";import"./el-dropdown-item-BMccEdtX.js";import"./refs-CSSW5x_d.js";import"./index-DkclijAA.js";import"./index-Dz8ZrwBc.js";import"./el-divider-D9UCOo44.js";import"./el-radio-group-evFfsZkP.js";/* empty css                */import"./el-tree-select-FGA9Rl0R.js";import"./index-6fa0VYPg.js";import"./DetailTemplate-joVmzVgM.js";import"./index-DcjWAoK6.js";import"./index-Cyn7ZzlS.js";import"./index-D4TGn7aE.js";const G={class:"mb-10px"},J={style:{position:"relative",top:"12px"}};function K(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!j(e)}const Q=t({__name:"ScheduledTask",setup(t){const j=I({icon:"iconoir:search"}),{t:Q}=h(),Z=a(""),$=()=>{ie()},q=l([{field:"selection",type:"selection",width:"55"},{field:"name",label:Q("task.taskName"),minWidth:30},{field:"cycle",label:Q("task.taskCycle"),minWidth:20},{field:"type",label:Q("task.typeTask"),minWidth:20},{field:"lastTime",label:Q("task.lastTime"),minWidth:40,formatter:(e,t,a)=>""==a?"-":a},{field:"nextTime",label:Q("task.nextTime"),minWidth:40,formatter:(e,t,a)=>""==a||0==e.state?"-":a},{field:"state",label:Q("common.state"),minWidth:20,formatter:(e,t,a)=>{if(null==a)return o("div",null,null);let l="",i="";return 1==a?(l="#2eb98a",i=Q("common.on")):(l="red",i=Q("common.statusStop")),o(k,{gutter:20},{default:()=>[o(x,{span:1},{default:()=>[o(s,{icon:"clarity:circle-solid",color:l},null)]}),o(x,{span:5},{default:()=>[o(w,{type:"info"},K(i)?i:{default:()=>[i]})]})]})}},{field:"action",label:Q("tableDemo.action"),minWidth:40,formatter:(e,t,a)=>{let l,s,r;return o(n,null,["page_monitoring"===e.id?o(i,{type:"success",onClick:()=>Ae(e)},K(l=Q("common.edit"))?l:{default:()=>[l]}):o(n,null,[o(i,{type:"success",onClick:()=>fe(e)},K(s=Q("common.edit"))?s:{default:()=>[s]}),o(i,{type:"danger",onClick:()=>ge(e)},K(r=Q("common.delete"))?r:{default:()=>[r]})])])}}]),{tableRegister:X,tableState:Y,tableMethods:ee}=D({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=Y,a=await W(Z.value,e.value,t.value);return{list:a.data.list,total:a.data.total}},immediate:!0}),{loading:te,dataList:ae,total:le,currentPage:oe,pageSize:se}=Y;se.value=20;const{getList:ie,getElTableExpose:ne}=ee;function re(){return{background:"var(--el-fill-color-light)"}}const de=a(!1);let ue=Q("task.addTask");const pe=()=>{de.value=!1};let me=a(""),ce=a(!0);const fe=async e=>{me.value=e.id,ue=Q("common.edit"),de.value=!0},ve=async()=>{window.confirm("Are you sure you want to delete the selected data?")&&await he()},ge=async e=>{window.confirm("Are you sure you want to delete the selected data?")&&await ye(e)},_e=a(!1),ye=async e=>{_e.value=!0;try{await L([e.id]);_e.value=!1,ie()}catch(t){_e.value=!1,ie()}},je=a([]),he=async()=>{const e=await ne(),t=(null==e?void 0:e.getSelectionRows())||[];je.value=t.map((e=>e.id)),_e.value=!0;try{await L(je.value);_e.value=!1,ie()}catch(a){_e.value=!1,ie()}};r((()=>{xe(),window.addEventListener("resize",xe)}));const be=a(0),xe=()=>{const e=window.innerHeight||document.documentElement.clientHeight;be.value=.75*e},ke=a(!1),we=()=>{ke.value=!1},Ve=a(!1),Se=a(!1),Ee=l({hour:24,allNode:!0,node:[],state:!0}),Ae=async e=>{Ee.hour=e.cycle,Ee.allNode=e.allNode,Ee.node=e.node,Ee.state=e.state,Ve.value=!0},Ne=l([]),Ce=a(!1),Te=a(!1),Ue=e=>{Ce.value=!1,e?(Ee.allNode=!0,Ee.node=[],Ne.forEach((e=>Ee.node.push(e.value)))):(Ee.allNode=!1,Ee.node=[])};(async()=>{const e=await B();e.data.list.length>0?(Te.value=!1,e.data.list.forEach((e=>{Ne.push({value:e,label:e})}))):(Te.value=!0,b.warning(Q("node.onlineNodeMsg")))})();const ze=async()=>{ue=Q("task.addScheduled"),ce.value=!0,de.value=!0};return(t,a)=>(d(),u(m(e),null,{default:p((()=>[o(m(k),null,{default:p((()=>[o(m(x),{span:1},{default:p((()=>[o(m(w),{class:"mx-1",style:{position:"relative",top:"8px"}},{default:p((()=>[c(f(m(Q)("task.taskName"))+":",1)])),_:1})])),_:1}),o(m(x),{span:5},{default:p((()=>[o(m(v),{modelValue:Z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Z.value=e),placeholder:m(Q)("common.inputText"),style:{height:"38px"}},null,8,["modelValue","placeholder"])])),_:1}),o(m(x),{span:5,style:{position:"relative",left:"16px"}},{default:p((()=>[o(m(g),{type:"primary",icon:m(j),style:{height:"100%"},onClick:$},{default:p((()=>[c("Search")])),_:1},8,["icon"])])),_:1})])),_:1}),o(m(k),null,{default:p((()=>[o(m(x),{style:{position:"relative",top:"16px"}},{default:p((()=>[_("div",G,[o(m(i),{type:"primary",onClick:ze},{default:p((()=>[c(f(m(Q)("task.addScheduled")),1)])),_:1}),o(m(i),{type:"danger",loading:_e.value,onClick:ve},{default:p((()=>[c(f(m(Q)("task.delTask")),1)])),_:1},8,["loading"])])])),_:1})])),_:1}),_("div",J,[o(m(P),{"tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!1,showAfter:0,popperOptions:{},popperClass:"test",placement:"bottom",hideAfter:0,disabled:!0},pageSize:m(se),"onUpdate:pageSize":a[1]||(a[1]=e=>y(se)?se.value=e:null),currentPage:m(oe),"onUpdate:currentPage":a[2]||(a[2]=e=>y(oe)?oe.value=e:null),columns:q,data:m(ae),stripe:"",border:!0,loading:m(te),"max-height":be.value,resizable:!0,pagination:{total:m(le),pageSizes:[20,30,50,100,200,500,1e3]},onRegister:m(X),headerCellStyle:re,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","loading","max-height","pagination","onRegister"])]),o(m(M),{modelValue:de.value,"onUpdate:modelValue":a[3]||(a[3]=e=>de.value=e),title:m(ue),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[o(R,{closeDialog:pe,getList:m(ie),create:m(ce),taskid:m(me),schedule:!0,tp:"scan","target-ids":[]},null,8,["getList","create","taskid"])])),_:1},8,["modelValue","title"]),o(m(M),{modelValue:ke.value,"onUpdate:modelValue":a[4]||(a[4]=e=>ke.value=e),title:m(Q)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[o(F,{closeDialog:we,projectid:m(""),getProjectData:m(ie),schedule:!1},null,8,["projectid","getProjectData"])])),_:1},8,["modelValue","title"]),o(m(M),{modelValue:Ve.value,"onUpdate:modelValue":a[10]||(a[10]=e=>Ve.value=e),title:m(Q)("common.edit"),center:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:p((()=>[o(m(V),{type:"card"},{default:p((()=>[o(m(S),{label:m(Q)("router.configuration")},{default:p((()=>[o(m(E),{model:Ee,"label-width":"100px","status-icon":"",ref:"ruleFormRef"},{default:p((()=>[o(m(C),{content:m(Q)("task.selectNodeMsg"),placement:"top"},{default:p((()=>[o(m(A),{label:m(Q)("task.nodeSelect"),prop:"node"},{default:p((()=>[o(m(T),{modelValue:Ee.node,"onUpdate:modelValue":a[6]||(a[6]=e=>Ee.node=e),filterable:"",options:Ne,placeholder:"Please select node",style:{width:"80%"},multiple:"","tag-type":"success","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":7},{header:p((()=>[o(m(U),{modelValue:Ee.allNode,"onUpdate:modelValue":a[5]||(a[5]=e=>Ee.allNode=e),disabled:Te.value,indeterminate:Ce.value,onChange:Ue},{default:p((()=>[c(" All ")])),_:1},8,["modelValue","disabled","indeterminate"])])),_:1},8,["modelValue","options"])])),_:1},8,["label"])])),_:1},8,["content"]),o(m(A),{label:m(Q)("project.cycle"),prop:"type"},{default:p((()=>[o(m(N),{modelValue:Ee.hour,"onUpdate:modelValue":a[7]||(a[7]=e=>Ee.hour=e),min:1,"controls-position":"right",size:"small"},null,8,["modelValue"]),o(m(w),{style:{position:"relative",left:"16px"}},{default:p((()=>[c("Hour")])),_:1})])),_:1},8,["label"]),o(m(A),{label:m(Q)("common.state")},{default:p((()=>[o(m(z),{modelValue:Ee.state,"onUpdate:modelValue":a[8]||(a[8]=e=>Ee.state=e),"inline-prompt":"","active-text":m(Q)("common.switchAction"),"inactive-text":m(Q)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),o(m(k),null,{default:p((()=>[o(m(x),{span:2,offset:8},{default:p((()=>[o(m(A),null,{default:p((()=>[o(m(g),{type:"primary",onClick:a[9]||(a[9]=e=>(async()=>{Se.value=!0,await H(Ee.hour,Ee.node,Ee.allNode,Ee.state),Se.value=!1,ie()})()),loading:Se.value},{default:p((()=>[c(f(m(Q)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["label"]),o(m(S),{label:m(Q)("task.data")},{default:p((()=>[o(O)])),_:1},8,["label"])])),_:1})])),_:1},8,["modelValue","title"])])),_:1}))}});export{Q as default};
