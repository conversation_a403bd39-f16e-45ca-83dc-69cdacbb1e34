import{d as t,s as e,v as n,S as r,o as a,c as i,e as o,a as p,I as l,w as c,F as s,l as d}from"./index-C6fb_XFi.js";import{_ as m}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{n as u}from"./index-B40b3p-m.js";import{u as f}from"./useTable-CijeIiBB.js";import{E as g}from"./el-tag-C_oEQYGz.js";import"./el-tooltip-l0sNRNKZ.js";import{E as v}from"./el-popper-CeVwVUf9.js";import{E as h}from"./el-card-B37ahJ8o.js";import{E as y}from"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";const S=t({__name:"ProgressInfo",props:{closeDialog:{type:Function},getProgressInfoID:{},getProgressInfotype:{},getProgressInforunnerid:{}},setup(t){const{t:S}=d(),b=t,w=e([{field:"target",label:S("task.taskTarget"),minWidth:40,formatter:(t,e,r)=>{const a=t.node&&""!==t.node?t.node:null;return a?n(v,{content:a,placement:"top",rawContent:!0},{default:()=>r}):r}},{field:"TargetHandler",label:S("scanTemplate.TargetHandler"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"SubdomainScan",label:S("scanTemplate.SubdomainScan"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"SubdomainSecurity",label:S("scanTemplate.SubdomainSecurity"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"PortScanPreparation",label:S("scanTemplate.PortScanPreparation"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"PortScan",label:S("scanTemplate.PortScan"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"PortFingerprint",label:S("scanTemplate.PortFingerprint"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"AssetMapping",label:S("scanTemplate.AssetMapping"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"AssetHandle",label:S("scanTemplate.AssetHandle"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"URLScan",label:S("scanTemplate.URLScan"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"WebCrawler",label:S("scanTemplate.WebCrawler"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"URLSecurity",label:S("scanTemplate.URLSecurity"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"DirScan",label:S("scanTemplate.DirScan"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"VulnerabilityScan",label:S("scanTemplate.VulnerabilityScan"),minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}},{field:"All",label:"All",minWidth:30,formatter:(t,e,a)=>{if(3==a.length)return n(r,{icon:"ph:prohibit"});if(""==a[0])return"-";let i="";return i+=`<div>Start:${a[0]}</div>`,i+=`<div>End:${a[1]}</div>`,""!=a[0]&&""!=a[1]?n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"success"},(()=>"Done")))):n(v,{content:i,placement:"top",rawContent:!0},(()=>n(g,{type:"primary"},(()=>"Running"))))}}]),{tableRegister:C,tableState:$,tableMethods:R}=f({fetchDataApi:async()=>{const t=await u(b.getProgressInfoID,T.value,W.value);return{total:t.data.total,list:t.data.list}},immediate:!1}),{loading:E,dataList:D,total:P,currentPage:T,pageSize:W}=$,{getList:j}=R;return j(),(t,e)=>(a(),i(s,null,[o(p(m),{pageSize:p(W),"onUpdate:pageSize":e[0]||(e[0]=t=>l(W)?W.value=t:null),currentPage:p(T),"onUpdate:currentPage":e[1]||(e[1]=t=>l(T)?T.value=t:null),onRegister:p(C),columns:w,data:p(D),rowKey:"_id",loading:p(E),resizable:!0,"max-height":"600","tooltip-options":{offset:1,showArrow:!1,effect:"dark",enterable:!0,showAfter:0,popperOptions:{},popperClass:"test",placement:"top",hideAfter:0,disabled:!0},style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","onRegister","columns","data","loading"]),o(p(h),null,{default:c((()=>[o(p(y),{pageSize:p(W),"onUpdate:pageSize":e[2]||(e[2]=t=>l(W)?W.value=t:null),currentPage:p(T),"onUpdate:currentPage":e[3]||(e[3]=t=>l(T)?T.value=t:null),"page-sizes":[10,20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:p(P)},null,8,["pageSize","currentPage","total"])])),_:1})],64))}});export{S as _};
