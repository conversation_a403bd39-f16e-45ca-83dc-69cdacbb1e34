import{d as e,r as t,s as l,e as a,z as i,S as s,v as r,A as o,B as n,H as p,o as u,c as d,a as m,w as c,I as f,F as g,J as j,l as v,Y as x,_ as h}from"./index-DfJTpRkj.js";import{u as y}from"./useTable-CtyddZqf.js";import{E as b}from"./el-card-DyZz6u6e.js";import{E as _}from"./el-pagination-FJcT0ZDj.js";import{E as S}from"./el-tag-CbhrEnto.js";import"./el-select-BkpcrSfo.js";import"./el-popper-D2BmgSQA.js";import{E as w,a as k}from"./el-col-B4Ik8fnS.js";import{E}from"./el-text-vKNLRkxx.js";import{E as C}from"./el-link-Bi4jWYBx.js";import{_ as L}from"./Table.vue_vue_type_script_lang-DQIxd2rv.js";import{u as z}from"./useCrudSchemas-Cz9y99Kk.js";import{a as U,d as H,w as R}from"./index-D4GvAO2k.js";import V from"./Csearch-C6xIjicy.js";import"./index-DE7jtbbk.js";import"./strings-CUyZ1T6U.js";import"./useInput-BVvvvzTX.js";import"./debounce-CmAGCOy_.js";import"./el-table-column-7FjdLFwR.js";import"./el-checkbox-DU4wMKRd.js";import"./isArrayLikeObject-DtpcG_un.js";import"./raf-zH43jzIi.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-image-viewer-C8GqWGTk.js";import"./el-empty-CuxEB3FC.js";import"./tsxHelper-DrslCeSo.js";import"./el-dropdown-item-nnpzYk3y.js";import"./castArray-CvwAI87l.js";import"./refs-DAMUgizk.js";import"./index-CyH6XROR.js";import"./tree-BfZhwLPs.js";import"./index-D1ADinPR.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-BmlnayZu.js";import"./el-divider-0NmzbuNU.js";import"./el-autocomplete-CyglTUOR.js";/* empty css                */import"./el-tree-select-P6ZpyTcB.js";import"./index-Co3LqSsp.js";import"./el-switch-C5ZBDFmL.js";import"./Dialog.vue_vue_type_style_index_0_lang-D9SXfiey.js";import"./useIcon-CNpM61rT.js";import"./exportData.vue_vue_type_script_setup_true_lang-CthEe30Y.js";import"./el-tab-pane-BijWf7kq.js";import"./el-form-DsaI0u2w.js";import"./el-radio-group-CTAZlJKV.js";import"./el-space-7M-SGVBd.js";/* empty css                          */import"./AddTask.vue_vue_type_script_setup_true_lang-BD8eoln6.js";import"./el-virtual-list-DQOsVxKt.js";import"./el-select-v2-D406kAkc.js";import"./index-KH6atv8j.js";import"./index-B0JD2UFG.js";import"./DetailTemplate-DU3RXrBH.js";import"./index-Dt8htmKv.js";import"./index-CCMFk4pF.js";import"./index-jyMftxhc.js";function T(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!j(e)}const A=h(e({__name:"URL",props:{projectList:{}},setup(e){const{t:j}=v(),h=[{keyword:"url",example:'url="http://example.com"',explain:j("searchHelp.url")},{keyword:"input",example:'input="example.com"',explain:j("searchHelp.inpur")},{keyword:"source",example:'source="exapmle.com/example.js"',explain:j("searchHelp.source")},{keyword:"type",example:'type="linkfinder"',explain:j("searchHelp.urlType")},{keyword:"project",example:'project="Hackerone"',explain:j("searchHelp.project")}],A=t(""),I=e=>{A.value=e,Q()},W=l({}),O=l([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:j("tableDemo.index"),type:"index",minWidth:55},{field:"url",label:"URL",minWidth:250,formatter:(e,t,l)=>a(C,{href:l,underline:!1,target:"_blank"},T(l)?l:{default:()=>[l]})},{field:"status",label:j("dirScan.status"),columnKey:"status",minWidth:120,formatter:(e,t,l)=>{if(null==l)return a("div",null,[i("-")]);let r="";return r=l<300?"#2eb98a":"#ff5252",a(k,{gutter:20},{default:()=>[a(w,{span:1},{default:()=>[a(s,{icon:"clarity:circle-solid",color:r,size:10,style:"transform: translateY(8%)"},null)]}),a(w,{span:2},{default:()=>[a(E,null,T(l)?l:{default:()=>[l]})]})]})},filters:[{text:"200",value:200},{text:"201",value:201},{text:"204",value:204},{text:"301",value:301},{text:"302",value:302},{text:"304",value:304},{text:"400",value:400},{text:"401",value:401},{text:"403",value:403},{text:"404",value:404},{text:"500",value:500},{text:"502",value:502},{text:"503",value:503},{text:"504",value:504}]},{field:"length",label:"Length",minWidth:120,sortable:"custom"},{field:"source",label:j("URL.source"),minWidth:100},{field:"type",label:j("URL.type"),minWidth:100},{field:"input",label:j("URL.input"),minWidth:200},{field:"tags",label:"TAG",fit:"true",formatter:(e,l,a)=>{null==a&&(a=[]),W[e.id]||(W[e.id]={inputVisible:!1,inputValue:"",inputRef:t(null)});const i=W[e.id],s=async()=>{i.inputValue&&(a.push(i.inputValue),U(e.id,P,i.inputValue)),i.inputVisible=!1,i.inputValue=""};return r(k,{},(()=>[...a.map((t=>r(w,{span:24,key:t},(()=>[r("div",{onClick:e=>((e,t)=>{e.target.classList.contains("el-tag__close")||ne("tags",t)})(e,t)},[r(S,{closable:!0,onClose:()=>(async t=>{const l=a.indexOf(t);l>-1&&a.splice(l,1),H(e.id,P,t)})(t)},(()=>t))])])))),r(w,{span:24},i.inputVisible?()=>r(o,{ref:i.inputRef,modelValue:i.inputValue,"onUpdate:modelValue":e=>i.inputValue=e,class:"w-20",size:"small",onKeyup:e=>{"Enter"===e.key&&s()},onBlur:s}):()=>r(n,{class:"button-new-tag",size:"small",onClick:()=>(i.inputVisible=!0,void x((()=>{})))},(()=>"+ New Tag")))]))},minWidth:"130"},{field:"time",label:j("asset.time"),minWidth:200}]);let P="UrlScan";O.forEach((e=>{e.hidden=e.hidden??!1}));let D=t(!1);const F=({field:e,hidden:t})=>{const l=O.findIndex((t=>t.field===e));-1!==l&&(O[l].hidden=t),(()=>{const e=O.reduce(((e,t)=>(e[t.field]=t.hidden,e)),{});e.statisticsHidden=D.value,localStorage.setItem(`columnConfig_${P}`,JSON.stringify(e))})()};(()=>{const e=JSON.parse(localStorage.getItem(`columnConfig_${P}`)||"{}");O.forEach((t=>{void 0!==e[t.field]&&"select"!=t.field&&(t.hidden=e[t.field])})),D.value=e.statisticsHidden})();const{allSchemas:N}=z(O),{tableRegister:$,tableState:B,tableMethods:J}=y({fetchDataApi:async()=>{const{currentPage:e,pageSize:t}=B,l=await R(A.value,e.value,t.value,ie,ee);return{list:l.data.list,total:l.data.total}},immediate:!1}),{loading:K,dataList:M,total:G,currentPage:Y,pageSize:q}=B,{getList:Q,getElTableExpose:Z}=J;function X(){return{background:"var(--el-fill-color-light)"}}q.value=20,p((()=>{ae(),window.addEventListener("resize",ae)}));const ee=l({}),te=async e=>{const t=e.prop,l=e.order;ee[t]=l,Q()},le=t(0),ae=()=>{const e=window.innerHeight||document.documentElement.clientHeight;le.value=.7*e},ie=l({}),se=(e,t)=>{Object.assign(ie,t),A.value=e,Q()},re=async e=>{Object.assign(ie,e),Q()},oe=t([]),ne=(e,t)=>{const l=`${e}=${t}`;oe.value=[...oe.value,l]},pe=e=>{if(oe.value){const[t,l]=e.split("=");t in ie&&Array.isArray(ie[t])&&(ie[t]=ie[t].filter((e=>e!==l)),0===ie[t].length&&delete ie[t]),oe.value=oe.value.filter((t=>t!==e))}},ue=()=>ie;return(e,t)=>(u(),d(g,null,[a(V,{getList:m(Q),handleSearch:I,searchKeywordsData:h,index:m(P),getElTableExpose:m(Z),projectList:e.$props.projectList,handleFilterSearch:se,crudSchemas:O,dynamicTags:oe.value,handleClose:pe,onUpdateColumnVisibility:F,searchResultCount:m(G),getFilter:ue},null,8,["getList","index","getElTableExpose","projectList","crudSchemas","dynamicTags","searchResultCount"]),a(m(k),null,{default:c((()=>[a(m(w),null,{default:c((()=>[a(m(b),null,{default:c((()=>[a(m(L),{pageSize:m(q),"onUpdate:pageSize":t[0]||(t[0]=e=>f(q)?q.value=e:null),currentPage:m(Y),"onUpdate:currentPage":t[1]||(t[1]=e=>f(Y)?Y.value=e:null),columns:m(N).tableColumns,data:m(M),"max-height":le.value,stripe:"",border:!0,loading:m(K),resizable:!0,onSortChange:te,onRegister:m($),onFilterChange:re,headerCellStyle:X,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["pageSize","currentPage","columns","data","max-height","loading","onRegister"])])),_:1})])),_:1}),a(m(w),{":span":24},{default:c((()=>[a(m(b),null,{default:c((()=>[a(m(_),{pageSize:m(q),"onUpdate:pageSize":t[2]||(t[2]=e=>f(q)?q.value=e:null),currentPage:m(Y),"onUpdate:currentPage":t[3]||(t[3]=e=>f(Y)?Y.value=e:null),"page-sizes":[20,50,100,200,500,1e3],layout:"total, sizes, prev, pager, next, jumper",total:m(G)},null,8,["pageSize","currentPage","total"])])),_:1})])),_:1})])),_:1})],64))}}),[["__scopeId","data-v-f46db613"]]);export{A as default};
