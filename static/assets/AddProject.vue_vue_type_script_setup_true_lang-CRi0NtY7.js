import{d as e,s as a,a8 as l,r as t,H as o,o as s,c as d,e as u,w as n,a as r,A as i,z as p,t as c,i as m,j as g,B as f,af as h,F as v,R as _,l as b,M as j,f as k}from"./index-3XfDPlIS.js";import{E as x}from"./el-checkbox-DjLAvZXr.js";import{E as V}from"./el-divider-D9UCOo44.js";import{a as y,E as w}from"./el-form-BY8piFS2.js";import{a as T,E as N}from"./el-col-CN1tVfqh.js";import{E}from"./el-switch-C-DLgt5X.js";import"./el-tooltip-l0sNRNKZ.js";import{E as U}from"./el-popper-DVoWBu_3.js";import{E as P,b as A}from"./el-radio-group-evFfsZkP.js";import"./el-tag-DcMbxLLg.js";import"./el-virtual-list-Drl4IGmp.js";import{E as D}from"./el-select-v2-CJw7ZO42.js";import{E as M}from"./el-input-number-CfcpPMpr.js";import{E as q}from"./el-text-CLWE0mUm.js";import{a as z,E as C}from"./el-select-DH55-Cab.js";import{b as F,u as S,c as R}from"./index-D4TGn7aE.js";import{a as I}from"./index-lpN3i-fa.js";import{_ as L}from"./Dialog.vue_vue_type_style_index_0_lang-DUWYs8XO.js";import{h as B}from"./index-BuRY9bVn.js";import H from"./DetailTemplate-joVmzVgM.js";const G={key:0},J={style:{float:"left"}},O=e({__name:"AddProject",props:{closeDialog:{type:Function},projectid:{},getProjectData:{type:Function},schedule:{type:Boolean}},setup(e){const{t:O}=b();let K=a({name:"",tag:"",logo:"",target:"",ignore:"",scheduledTasks:!1,hour:24,node:[],allNode:!1,duplicates:"None",template:""});const Q=e,W=l((()=>{const e={name:[{required:!0,message:O("project.msgProject"),trigger:"blur"}],tag:[{required:!0,message:O("project.msgProjectTag"),trigger:"blur"}],target:[{required:!0,message:O("project.msgProjectScope"),trigger:"blur"}],node:[{required:!1,message:O("task.nodeMsg"),trigger:"blur"}],template:[{required:!0,message:"Please select template",trigger:"blur"}]};return K.scheduledTasks&&(e.node=[{required:!0,message:O("task.nodeMsg"),trigger:"blur"}]),e})),X=t(!1),Y=t(),Z=t(!1),$=t(!1),ee=t(!1),ae=t(!1),le=a([]),te=e=>{ee.value=!1,e?(K.node=[],le.forEach((e=>K.node.push(e.value)))):K.node=[]},oe=a([]),se=async()=>{oe.length=0;const e=await B("",1,1e3);e.data.list.length>0&&e.data.list.forEach((e=>{oe.push({value:e.id,label:e.name})}))};o((()=>{(async()=>{if(""!=Q.projectid){$.value=!0;const e=await R(Q.projectid);K.name=e.data.name,K.tag=e.data.tag,K.target=e.data.target,K.logo=e.data.logo,K.scheduledTasks=e.data.scheduledTasks,K.hour=e.data.hour,K.allNode=e.data.allNode,K.node=e.data.node,K.duplicates=e.data.duplicates,K.ignore=e.data.ignore,K.template=e.data.template,$.value=!1}})(),(async()=>{const e=await I();e.data.list.length>0?(ae.value=!1,e.data.list.forEach((e=>{le.push({value:e,label:e})}))):(ae.value=!0,j.warning(O("node.onlineNodeMsg")))})(),se()}));const de=t("");let ue="";const ne=t(!1),re=async e=>{de.value=e,""!=e&&(ue=O("task.editTemplate")),ne.value=!0},ie=()=>{ne.value=!1};return(e,a)=>(s(),d(v,null,[u(r(w),{model:r(K),"label-width":"auto",rules:W.value,"status-icon":"",ref_key:"ruleFormRef",ref:Y,loading:$.value},{default:n((()=>[u(r(y),{label:r(O)("project.projectName"),prop:"name"},{default:n((()=>[u(r(i),{modelValue:r(K).name,"onUpdate:modelValue":a[0]||(a[0]=e=>r(K).name=e),placeholder:r(O)("project.msgProject")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),u(r(y),{label:"TAG",prop:"tag"},{default:n((()=>[u(r(i),{modelValue:r(K).tag,"onUpdate:modelValue":a[1]||(a[1]=e=>r(K).tag=e),placeholder:r(O)("project.msgProjectTag")},null,8,["modelValue","placeholder"])])),_:1}),u(r(y),{label:r(O)("project.projectScope"),prop:"target"},{default:n((()=>[u(r(i),{modelValue:r(K).target,"onUpdate:modelValue":a[2]||(a[2]=e=>r(K).target=e),placeholder:r(O)("task.msgTarget"),type:"textarea",autosize:{minRows:6,maxRows:15}},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),u(r(y),{label:r(O)("task.ignore"),prop:"ignore"},{default:n((()=>[u(r(i),{modelValue:r(K).ignore,"onUpdate:modelValue":a[3]||(a[3]=e=>r(K).ignore=e),placeholder:r(O)("task.ignoreMsg"),type:"textarea",rows:"10"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),u(r(y),{label:"Logo",prop:"logo"},{default:n((()=>[u(r(i),{modelValue:r(K).logo,"onUpdate:modelValue":a[4]||(a[4]=e=>r(K).logo=e),placeholder:"http(s)://xxxxx.xx"},null,8,["modelValue"])])),_:1}),u(r(V),{"content-position":"center",style:{}},{default:n((()=>[p(c(r(O)("project.scheduledTasks")),1)])),_:1}),u(r(T),null,{default:n((()=>[u(r(N),{span:6},{default:n((()=>[u(r(y),{label:r(O)("project.scheduledTasks")},{default:n((()=>[u(r(U),{effect:"dark",content:r(O)("project.msgScheduledTasks"),placement:"top"},{default:n((()=>[u(r(E),{modelValue:r(K).scheduledTasks,"onUpdate:modelValue":a[5]||(a[5]=e=>r(K).scheduledTasks=e),"inline-prompt":"","active-text":r(O)("common.switchAction"),"inactive-text":r(O)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["content"])])),_:1},8,["label"])])),_:1}),r(K).scheduledTasks?(s(),m(r(N),{key:0,span:12},{default:n((()=>[u(r(y),{label:r(O)("project.cycle"),prop:"type"},{default:n((()=>[u(r(M),{modelValue:r(K).hour,"onUpdate:modelValue":a[6]||(a[6]=e=>r(K).hour=e),min:1,"controls-position":"right",size:"small"},null,8,["modelValue"]),u(r(q),{style:{position:"relative",left:"16px"}},{default:n((()=>[p("Hour")])),_:1})])),_:1},8,["label"])])),_:1})):g("",!0)])),_:1}),u(r(T),null,{default:n((()=>[u(r(N),null,{default:n((()=>[u(r(y),{label:r(O)("configuration.runNowOne")},{default:n((()=>[u(r(E),{modelValue:Z.value,"onUpdate:modelValue":a[7]||(a[7]=e=>Z.value=e),"inline-prompt":"","active-text":r(O)("common.switchAction"),"inactive-text":r(O)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),r(K).scheduledTasks||Z.value?(s(),d("div",G,[u(r(T),null,{default:n((()=>[u(r(N),{span:12},{default:n((()=>[u(r(y),{label:r(O)("task.nodeSelect"),prop:"node"},{default:n((()=>[u(r(D),{modelValue:r(K).node,"onUpdate:modelValue":a[8]||(a[8]=e=>r(K).node=e),filterable:"",options:le,placeholder:"Please select node",style:{width:"80%"},multiple:"","tag-type":"success","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":7},{header:n((()=>[u(r(x),{disabled:ae.value,indeterminate:ee.value,onChange:te},{default:n((()=>[p(" All ")])),_:1},8,["disabled","indeterminate"])])),_:1},8,["modelValue","options"])])),_:1},8,["label"])])),_:1}),u(r(N),{span:12},{default:n((()=>[u(r(y),{label:r(O)("task.autoNode")},{default:n((()=>[u(r(U),{effect:"dark",content:r(O)("task.selectNodeMsg"),placement:"top"},{default:n((()=>[u(r(E),{modelValue:r(K).allNode,"onUpdate:modelValue":a[9]||(a[9]=e=>r(K).allNode=e),"inline-prompt":"","active-text":r(O)("common.switchAction"),"inactive-text":r(O)("common.switchInactive")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["content"])])),_:1},8,["label"])])),_:1})])),_:1}),u(r(V),{"content-position":"center",style:{width:"60%",left:"20%"}},{default:n((()=>[p(c(r(O)("task.duplicates")),1)])),_:1}),u(r(T),null,{default:n((()=>[u(r(N),{span:24},{default:n((()=>[u(r(y),{label:r(O)("task.duplicates"),prop:"type"},{default:n((()=>[u(r(P),{modelValue:r(K).duplicates,"onUpdate:modelValue":a[10]||(a[10]=e=>r(K).duplicates=e)},{default:n((()=>[u(r(A),{label:"None",name:"duplicates",checked:!0,value:"None"}),u(r(U),{effect:"dark",content:r(O)("task.duplicatesMsg"),placement:"top"},{default:n((()=>[u(r(A),{label:r(O)("task.duplicatesSubdomain"),name:"duplicates",value:"subdomain"},null,8,["label"])])),_:1},8,["content"])])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),u(r(V),{"content-position":"center",style:{width:"60%",left:"20%"}},{default:n((()=>[p(c(r(O)("router.scanTemplate")),1)])),_:1}),u(r(y),{label:r(O)("router.scanTemplate"),prop:"template"},{default:n((()=>[u(r(z),{modelValue:r(K).template,"onUpdate:modelValue":a[12]||(a[12]=e=>r(K).template=e),placeholder:"Please select template",style:{width:"30%"}},{footer:n((()=>[u(r(f),{type:"success",size:"small",plain:"",style:{"margin-left":"15px"},onClick:a[11]||(a[11]=h((e=>re("")),["stop"]))},{default:n((()=>[p(c(r(O)("common.new")),1)])),_:1})])),default:n((()=>[(s(!0),d(v,null,_(oe,(e=>(s(),m(r(C),{key:e.value,label:e.label,value:e.value},{default:n((()=>[u(r(T),null,{default:n((()=>[u(r(N),{span:16},{default:n((()=>[k("span",J,c(e.label),1)])),_:2},1024),u(r(N),{span:8},{default:n((()=>[u(r(f),{type:"primary",size:"small",style:{"margin-left":"15px"},onClick:h((a=>re(e.value)),["stop"])},{default:n((()=>[p(c(r(O)("common.edit")),1)])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])):g("",!0),u(r(V)),u(r(T),null,{default:n((()=>[u(r(N),{span:2,offset:12},{default:n((()=>[u(r(y),null,{default:n((()=>[u(r(f),{type:"primary",onClick:a[13]||(a[13]=e=>(async e=>{X.value=!0,e&&(await e.validate((async(e,a)=>{e?""==Q.projectid?(200===(await F(Z.value,K.name,K.tag,K.target,K.logo,K.scheduledTasks,K.hour,K.allNode,K.node,K.duplicates,K.ignore,K.template)).code&&Q.closeDialog(),X.value=!1):(200===(await S(Z.value,Q.projectid,K.name,K.tag,K.target,K.logo,K.scheduledTasks,K.hour,K.allNode,K.node,K.duplicates,K.ignore,K.template)).code&&Q.closeDialog(),X.value=!1):X.value=!1})),Q.getProjectData(1,50))})(Y.value)),loading:X.value},{default:n((()=>[p(c(r(O)("task.save")),1)])),_:1},8,["loading"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules","loading"]),u(r(L),{modelValue:ne.value,"onUpdate:modelValue":a[14]||(a[14]=e=>ne.value=e),title:r(ue),center:"",fullscreen:"",style:{"border-radius":"15px","box-shadow":"5px 5px 10px rgba(0, 0, 0, 0.3)"}},{default:n((()=>[u(H,{closeDialog:ie,getList:se,id:de.value},null,8,["id"])])),_:1},8,["modelValue","title"])],64))}});export{O as _};
