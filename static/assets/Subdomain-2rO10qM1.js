import{d as e,dC as t,H as l,r as a,s as o,e as i,z as s,F as n,A as r,o as m,i as u,w as d,a as p,B as c,j as h,J as f,l as b,K as g,M as j,_ as v}from"./index-C6fb_XFi.js";import{u as y}from"./useTable-CijeIiBB.js";import{E as x}from"./el-card-B37ahJ8o.js";import{E as _,a as S}from"./el-col-Dl4_4Pn5.js";import{E as w}from"./el-text-BnUG9HvL.js";import{_ as C}from"./Table.vue_vue_type_script_lang-7Pp5E_zy.js";import{u as E}from"./useCrudSchemas-CEXr0LRM.js";import{d as T}from"./index-DKxEKp57.js";import{t as A}from"./index-BBupWySc.js";import"./el-table-column-C9CkC7I1.js";import"./el-popper-CeVwVUf9.js";import"./index-BWEJ0epC.js";import"./debounce-BwgdhaaK.js";import"./el-checkbox-CvJzNe2E.js";import"./isArrayLikeObject-zBQ5eq7G.js";import"./raf-DGOAeO92.js";import"./el-tooltip-l0sNRNKZ.js";import"./el-tag-C_oEQYGz.js";import"./el-pagination-FWx5cl5J.js";import"./el-select-vbM8Rxr1.js";import"./strings-BiUeKphX.js";import"./useInput-IB6tFdGu.js";import"./el-image-viewer-DrhdpOg4.js";import"./el-empty-jJjDxScx.js";import"./tsxHelper-CeCzRM_x.js";import"./el-dropdown-item-DpH7Woj3.js";import"./castArray-DRqY4cIf.js";import"./refs-3HtnmaOD.js";import"./index-ghAu5K8t.js";import"./tree-BfZhwLPs.js";import"./index-CnCQNuY4.js";function W(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!f(e)}const N=v(e({__name:"Subdomain",setup(e){const{t:f}=b(),{query:v}=t();l((()=>{R(),window.addEventListener("resize",R)}));const N=a(0),R=()=>{const e=window.innerHeight||document.documentElement.clientHeight;N.value=.8*e};a("");const V=o({});V.project=[v.id];const k=async e=>{Object.assign(V,e),O()},z=o([{field:"selection",type:"selection",minWidth:"55"},{field:"index",label:f("tableDemo.index"),type:"index",minWidth:"30"},{field:"host",label:f("subdomain.subdomainName"),minWidth:"200",formatter:(e,t,l)=>e.count?i(n,null,[i(w,null,W(l)?l:{default:()=>[l]}),i(w,{type:"info"},{default:()=>[s("("),e.count,s(")")]})]):i(w,null,W(l)?l:{default:()=>[l]}),slots:{header:()=>i("div",null,[i("span",null,[f("subdomain.subdomainName")]),i(r,{modelValue:L.value,"onUpdate:modelValue":e=>L.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>$("sub_host")},null)])}},{field:"type",label:f("subdomain.recordType"),minWidth:"200",columnKey:"type",filters:[{text:"A",value:"A"},{text:"NS",value:"NS"},{text:"CNAME",value:"CNAME"},{text:"PTR",value:"PTR"},{text:"TXT",value:"TXT"}]},{field:"value",label:f("subdomain.recordValue"),minWidth:"250",formatter:(e,t,l)=>{let a="";return l.forEach(((e,t)=>{a+=`${e}\r\n`})),a},slots:{header:()=>i("div",null,[i("span",null,[f("subdomain.recordValue")]),i(r,{modelValue:P.value,"onUpdate:modelValue":e=>P.value=e,placeholder:"Search",style:"width: 200px; margin-left: 10px;",size:"small",onChange:()=>$("sub_value")},null)])}},{field:"ip",label:"IP",minWidth:"150",formatter:(e,t,l)=>{let a="";return l.forEach(((e,t)=>{a+=`${e}\r\n`})),a},slots:{header:()=>i("div",null,[i("span",null,[s("IP")]),i(r,{modelValue:X.value,"onUpdate:modelValue":e=>X.value=e,placeholder:"Search",style:"width: 180px; margin-left: 10px;",size:"small",onChange:()=>$("sub_ip")},null)])}},{field:"time",label:f("asset.time"),minWidth:"200"}]),{allSchemas:I}=E(z),{tableRegister:H,tableState:U,tableMethods:D}=y({fetchDataApi:async()=>({list:(await T("",V,J)).data.list}),immediate:!0}),{loading:F,dataList:M}=U,{getList:O,getElTableExpose:B}=D;function K(){return{background:"var(--el-fill-color-light)"}}const L=a(""),P=a(""),X=a(""),J=o({}),$=async e=>{let t="";"sub_host"==e&&(t=L.value),"sub_value"==e&&(t=P.value),"sub_ip"==e&&(t=X.value),J[e]=t,O()},q=a([]),Y=async()=>{g.confirm("Whether to delete?","Warning",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{const e=await B(),t=(null==e?void 0:e.getSelectionRows())||[];q.value=t.map((e=>e.id)),await A(q.value,"subdomain"),O()})).catch((()=>{j({type:"info",message:"Delete canceled"})}))};let G=a(!1);const Q=async()=>{const e=await B(),t=(null==e?void 0:e.getSelectionRows())||[];q.value=t.map((e=>e.id)),0!=q.value.length?G.value=!0:G.value=!1};return(e,t)=>(m(),u(p(S),null,{default:d((()=>[i(p(_),null,{default:d((()=>[i(p(x),{style:{height:"min-content"}},{default:d((()=>[p(G)?(m(),u(p(c),{key:0,onClick:Y,type:"danger",size:"small"},{default:d((()=>[s("Dlete")])),_:1})):h("",!0),i(p(C),{columns:p(I).tableColumns,data:p(M),stripe:"","max-height":N.value,border:!0,loading:p(F),onSelectionChange:Q,rowKey:"id",resizable:!0,onRegister:p(H),onFilterChange:k,headerCellStyle:K,style:{fontFamily:"-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji"}},null,8,["columns","data","max-height","loading","onRegister"])])),_:1})])),_:1})])),_:1}))}}),[["__scopeId","data-v-74d55aa1"]]);export{N as default};
