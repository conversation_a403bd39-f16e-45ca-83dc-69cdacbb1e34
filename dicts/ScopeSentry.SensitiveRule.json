[{"_id": {"$oid": "664b4407efaa5cfd9a79a8ae"}, "name": "JSON Web Token", "regular": "ey[A-Za-z0-9_-]*\\.[A-Za-z0-9_-]*\\.[A-Za-z0-9_\\-\\/.+=]*", "color": "green", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8af"}, "name": "Swagger UI", "regular": "(swagger-ui.html)|(\\\"swagger\\\":)|(Swagger UI)|(swaggerUi)|(swaggerVersion)", "color": "red", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b0"}, "name": "Ueditor", "regular": "ueditor\\.(config|all)\\.js", "color": "green", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b1"}, "name": "Java Deserialization", "regular": "javax\\.faces\\.ViewState", "color": "yellow", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b2"}, "name": "URL As A Value", "regular": "=(https?)(://|%3a%2f%2f)", "color": "cyan", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b3"}, "name": "Upload Form", "regular": "type=\\\"file\\\"", "color": "yellow", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b4"}, "name": "Email", "regular": "[\\w.%+-]+@[A-Za-z0-9-]+\\.[A-Za-z]{2,}", "color": "yellow", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b5"}, "name": "Chinese IDCard", "regular": "'[^0-9]((\\d{8}(0\\d|10|11|12)([0-2]\\d|30|31)\\d{3}$)|(\\d{6}(18|19|20)\\d{2}(0[1-9]|10|11|12)([0-2]\\d|30|31)\\d{3}(\\d|X|x)))[^0-9]'", "color": "orange", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b6"}, "name": "Chinese Mobile Number", "regular": "'[^\\w]((?:(?:\\+|00)86)?1(?:(?:3[\\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\\d])|(?:9[189]))\\d{8})[^\\w]'", "color": "orange", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b7"}, "name": "Internal IP Address", "regular": "'[^0-9]((127\\.0\\.0\\.1)|(10\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|(172\\.((1[6-9])|(2\\d)|(3[01]))\\.\\d{1,3}\\.\\d{1,3})|(192\\.168\\.\\d{1,3}\\.\\d{1,3}))'", "color": "cyan", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b8"}, "name": "MAC Address", "regular": "(^([a-fA-F0-9]{2}(:[a-fA-F0-9]{2}){5})|[^a-zA-Z0-9]([a-fA-F0-9]{2}(:[a-fA-F0-9]{2}){5}))", "color": "green", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8b9"}, "name": "Chinese Bank Card ID", "regular": "'[^0-9]([1-9]\\d{12,18})[^0-9]'", "color": "orange", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ba"}, "name": "Cloud Key", "regular": "(accesskeyid)|(accesskeysecret)|(LTAI[a-z0-9]{12,20})", "color": "yellow", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8bb"}, "name": "Windows File/Dir Path", "regular": "'[^\\w](([a-zA-Z]:\\\\(?:\\w+\\\\?)*)|([a-zA-Z]:\\\\(?:\\w+\\\\)*\\w+\\.\\w+))'", "color": "green", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8bc"}, "name": "Password Field", "regular": "(|'|\")([p](ass|wd|asswd|assword))(|'|\")(:|=)( |)('|\")(.*?)('|\")(|,)", "color": "yellow", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8bd"}, "name": "Username Field", "regular": "(|'|\")((u(ser|sername))|(account))(|'|\")(:|=)", "color": "green", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8be"}, "name": "WeCom Key", "regular": "[c|C]or[p|P]id|[c|C]orp[s|S]ecret", "color": "green", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8bf"}, "name": "JDBC Connection", "regular": "jdbc:[a-z:]+:\\/\\/[a-z0-9\\.\\-_:;=\\/@?,&]+", "color": "yellow", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c0"}, "name": "Authorization Header", "regular": "(basic [a-z0-9=:_\\+\\/-]{5,100})|(bearer [a-z0-9_.=:_\\+\\/-]{5,100})", "color": "yellow", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c1"}, "name": "Github Access Token", "regular": "[a-z0-9_-]*:[a-z0-9_\\-]+@github\\.com", "color": "green", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c2"}, "name": "Sensitive Field", "regular": "((key)|(secret)|(token)|(config)|(auth)|(access)|(admin))(|'|\")(:|=)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c4"}, "name": "Source Map", "regular": "\\.js\\.map", "color": "null", "state": true}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c5"}, "name": "HTML Notes", "regular": "(<!--[\\s\\S]*?-->)", "color": "green", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c6"}, "name": "C<PERSON> <PERSON><PERSON><PERSON>", "regular": "(\\+\\{.*?\\}\\[[a-zA-Z]\\]\\+\".*?\\.js\")", "color": "green", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c7"}, "name": "URL Schemes", "regular": "(?![http]|[https])(([-A-Za-z0-9]{1,20})://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|])", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c8"}, "name": "Potential cryptographic private key", "regular": "(\\.pem['\"])", "color": "green", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8c9"}, "name": "google_api", "regular": "(<PERSON>za[0-9A-Za-z-_]{35})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ca"}, "name": "firebase", "regular": "(AAAA[A-Za-z0-9_-]{7}:[A-Za-z0-9_-]{140})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8cb"}, "name": "authorization_api", "regular": "(api[key|_key|\\s+]+[a-zA-Z0-9_\\-]{5,100})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8cc"}, "name": "Log file", "regular": "(\\.log['\"])", "color": "green", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8cd"}, "name": "Potential cryptographic key bundle", "regular": "(\\.pkcs12['\"])", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ce"}, "name": "Potential cryptographic key bundle", "regular": "(\\.p12['\"])", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8cf"}, "name": "Potential cryptographic key bundle", "regular": "(\\.pfx['\"])", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d0"}, "name": "Pidgin OTR private key", "regular": "(otr\\.private_key)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d1"}, "name": "File", "regular": "(\\.((asc)|(ovpn)|(cscfg)|(rdp)|(mdf)|(sdf)|(sqlite)|(sqlite3)|(bek)|(tpm)|(fve)|(jks)|(psafe3)|(agilekeychain)|(keychain)|(pcap)|(gnucash)|(kwallet)|(tblk)|(dayone)|(exports)|(functions)|(extra)|(proftpdpasswd))['\"])", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d2"}, "name": "Ruby On Rails secret token configuration file", "regular": "(secret_token\\.rb)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d3"}, "name": "Carrierwave configuration file", "regular": "(carrierwave\\.rb)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d4"}, "name": "Potential Ruby On Rails database configuration file", "regular": "(database\\.yml)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d5"}, "name": "OmniAuth configuration file", "regular": "(omniauth\\.rb)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d6"}, "name": "Django configuration file", "regular": "(settings\\.py)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d7"}, "name": "Jenkins publish over SSH plugin file", "regular": "(jenkins.plugins.publish_over_ssh\\.BapSshPublisherPlugin.xml)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d8"}, "name": "Potential Jenkins credentials file", "regular": "(credentials\\.xml)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8d9"}, "name": "Potential MediaWiki configuration file", "regular": "LocalSettings\\.php", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8da"}, "name": "Sequel Pro MySQL database manager bookmark file", "regular": "Favorites\\.plist", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8db"}, "name": "Little Snitch firewall configuration file", "regular": "(configuration\\.user\\.xpl)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8dc"}, "name": "Potential jrnl journal file", "regular": "(journal\\.txt)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8dd"}, "name": "Chef Knife configuration file", "regular": "(knife\\.rb)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8de"}, "name": "Robomongo MongoDB manager configuration file", "regular": "(robomongo\\.json)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8df"}, "name": "FileZilla FTP configuration file", "regular": "(filezilla\\.xml)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8e0"}, "name": "FileZilla FTP recent servers file", "regular": "(recentservers\\.xml)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8e1"}, "name": "Ventrilo server configuration file", "regular": "(ventrilo_srv\\.ini)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8e2"}, "name": "Terraform variable config file", "regular": "(terraform\\.tfvars)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8e7"}, "name": "SSH configuration file", "regular": "(\\.ssh_config)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8e8"}, "name": "Shell command history file", "regular": "\\.?(bash_|zsh_|sh_|z)history", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8e9"}, "name": "MySQL client command history file", "regular": "\\.mysql_history", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ea"}, "name": "PostgreSQL client command history file", "regular": "(\\.?psql_history)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8eb"}, "name": "PostgreSQL password file", "regular": "(\\.?pgpass)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ec"}, "name": "Ruby IRB console history file", "regular": "(\\.?irb_history)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ed"}, "name": "Pidgin chat client account configuration file", "regular": "(\\.?purple/accounts\\\\.xml)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ee"}, "name": "DBeaver SQL database manager configuration file", "regular": "(\\.?dbeaver-data-sources.xml)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ef"}, "name": "Mutt e-mail client configuration file", "regular": "(\\.?muttrc)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f0"}, "name": "S3cmd configuration file", "regular": "(\\.?s3cfg)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f1"}, "name": "AWS CLI credentials file", "regular": "(\\.?aws/credentials)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f2"}, "name": "SFTP connection configuration file", "regular": "(sftp-config(\\.json)?)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f3"}, "name": "T command-line Twitter client configuration file", "regular": "(\\.?trc)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f4"}, "name": "Shell configuration file", "regular": "(\\.?(bash|zsh|csh)rc)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f5"}, "name": "Shell profile configuration file", "regular": "(\\.?(bash_|zsh_)profile)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f6"}, "name": "Shell command alias configuration file", "regular": "\\.?(bash_|zsh_)aliases", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f7"}, "name": "PHP configuration file", "regular": "(config(\\.inc)?\\.php)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f8"}, "name": "GNOME Keyring database file", "regular": "(key(store|ring))", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8f9"}, "name": "KeePass password manager database file", "regular": "(kdbx?)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8fa"}, "name": "SQL dump file", "regular": "(sql(dump)?)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8fb"}, "name": "Apache htpasswd file", "regular": "(\\.?htpasswd)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8fc"}, "name": "Configuration file for auto-login process", "regular": "(\\.|_)netrc", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8fd"}, "name": "Rubygems credentials file", "regular": "(\\.?gem/credentials)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8fe"}, "name": "Tugboat DigitalOcean management tool configuration", "regular": "(\\.?tugboat)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a8ff"}, "name": "DigitalOcean doctl command-line client configuration file", "regular": "(doctl/config.yaml)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a900"}, "name": "git-credential-store helper credentials file", "regular": "(\\.?git-credentials)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a901"}, "name": "GitHub Hub command-line client configuration file", "regular": "config/hub", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a902"}, "name": "Git configuration file", "regular": "(\\.?gitconfig)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a903"}, "name": "Chef private key", "regular": "(\\.?chef/(.*)\\\\.pem)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a904"}, "name": "Potential Linux shadow file", "regular": "(etc/shadow)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a905"}, "name": "Potential Linux passwd file", "regular": "(etc/passwd)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a906"}, "name": "Docker configuration file", "regular": "(\\.?dockercfg)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a907"}, "name": "NPM configuration file", "regular": "(\\.?npmrc)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a908"}, "name": "Environment configuration file", "regular": "(\\.?env)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a909"}, "name": "AWS Access Key ID Value", "regular": "((A3T[A-Z0-9]|AKIA|AGPA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a90a"}, "name": "ak sk", "regular": "(((access_key|access_token|admin_pass|admin_user|algolia_admin_key|algolia_api_key|alias_pass|alicloud_access_key|amazon_secret_access_key|amazonaws|ansible_vault_password|aos_key|api_key|api_key_secret|api_key_sid|api_secret|api.googlemaps AIza|apidocs|apikey|apiSecret|app_debug|app_id|app_key|app_log_level|app_secret|appkey|appkeysecret|application_key|appsecret|appspot|auth_token|authorizationToken|authsecret|aws_access|aws_access_key_id|aws_bucket|aws_key|aws_secret|aws_secret_key|aws_token|AWSSecretKey|b2_app_key|bashrc password|bintray_apikey|bintray_gpg_password|bintray_key|bintraykey|bluemix_api_key|bluemix_pass|browserstack_access_key|bucket_password|bucketeer_aws_access_key_id|bucketeer_aws_secret_access_key|built_branch_deploy_key|bx_password|cache_driver|cache_s3_secret_key|cattle_access_key|cattle_secret_key|certificate_password|ci_deploy_password|client_secret|client_zpk_secret_key|clojars_password|cloud_api_key|cloud_watch_aws_access_key|cloudant_password|cloudflare_api_key|cloudflare_auth_key|cloudinary_api_secret|cloudinary_name|codecov_token|config|conn.login|connectionstring|consumer_key|consumer_secret|credentials|cypress_record_key|database_password|database_schema_test|datadog_api_key|datadog_app_key|db_password|db_server|db_username|dbpasswd|dbpassword|dbuser|deploy_password|digitalocean_ssh_key_body|digitalocean_ssh_key_ids|docker_hub_password|docker_key|docker_pass|docker_passwd|docker_password|dockerhub_password|dockerhubpassword|dot-files|dotfiles|droplet_travis_password|dynamoaccesskeyid|dynamosecretaccesskey|elastica_host|elastica_port|elasticsearch_password|encryption_key|encryption_password|env.heroku_api_key|env.sonatype_password|eureka.awssecretkey)[a-z0-9_ .\\-,]{0,25})(=|>|:=|\\|\\|:|<=|=>|:).{0,5}['\\\"]([0-9a-zA-Z\\-_=]{8,64}))\\b`", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a90b"}, "name": "AWS Access Key ID", "regular": "(?:'||\")?(A3T[A-Z0-9]{16}|AKIA[A-Z0-9]{16}|AGPA[A-Z0-9]{16}|AIDA[A-Z0-9]{16}|AROA[A-Z0-9]{16}|AIPA[A-Z0-9]{16}|ANPA[A-Z0-9]{16}|ANVA[A-Z0-9]{16}|ASIA[A-Z0-9]{16})(?:'||\")?", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a90c"}, "name": "AWS Account ID", "regular": "((\"|'|`)?((?i)aws)?_?((?i)account)_?((?i)id)?(\"|'|`)?\\s{0,50}(:|=>|=)\\s{0,50}(\"|'|`)?[0-9]{4}-?[0-9]{4}-?[0-9]{4}(\"|'|`)?)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a90d"}, "name": "Artifactory API Token", "regular": "((?:\\s|=|:|\"|^)AKC[a-zA-Z0-9]{10,})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a90e"}, "name": "Artifactory Password", "regular": "((?:\\s|=|:|\"|^)AP[\\dABCDEF][a-zA-Z0-9]{8,})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a90f"}, "name": "Authorization Basic", "regular": "basic [a-zA-Z0-9_\\-:\\.=]+", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a910"}, "name": "Authorization Authorization Bearer", "regular": "(bearer [a-zA-Z0-9_\\\\-\\\\.=]+)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a911"}, "name": "AWS Client ID", "regular": "((A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a912"}, "name": "AWS MWS Key", "regular": "(amzn\\.mws\\.[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a913"}, "name": "AWS MWS Key", "regular": "(amzn\\.mws\\.[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a914"}, "name": "AWS Secret Key", "regular": "((?i)aws(.{0,20})?(?-i)['\\\"][0-9a-zA-Z\\/+]{40}['\"])", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a915"}, "name": "Base64", "regular": "((eyJ|YTo|Tzo|PD[89]|aHR0cHM6L|aHR0cDo|rO0)[a-zA-Z0-9+/]+={0,2})", "color": "null", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a916"}, "name": "Basic Auth Credentials", "regular": "(?<=:\\/\\/)[a-zA-Z0-9]+:[a-zA-Z0-9]+@[a-zA-Z0-9]+\\.[a-zA-Z]+", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a917"}, "name": "Cloudinary Basic Auth", "regular": "(cloudinary:\\/\\/[0-9]{15}:[0-9A-Za-z]+@[a-z]+)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a918"}, "name": "Facebook Access Token", "regular": "(EAACEdEose0cBA[0-9A-Za-z]+)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a919"}, "name": "Facebook Client ID", "regular": "((?i)(facebook|fb)(.{0,20})?['\\\"][0-9]{13,17})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a91a"}, "name": "Facebook Oauth", "regular": "([f|F][a|A][c|C][e|E][b|B][o|O][o|O][k|K].*['|\\\"][0-9a-f]{32}['|\\\"])", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a91b"}, "name": "Facebook Secret Key", "regular": "((?i)(facebook|fb)(.{0,20})?(?-i)['\\\"][0-9a-f]{32})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a91c"}, "name": "<PERSON><PERSON><PERSON>", "regular": "((?i)github(.{0,20})?(?-i)['\\\"][0-9a-zA-Z]{35,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a91d"}, "name": "Google API Key", "regular": "(AIza[0-9A-Za-z\\\\-_]{35})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a91e"}, "name": "Google Cloud Platform API Key", "regular": "((?i)(google|gcp|youtube|drive|yt)(.{0,20})?['\\\"][AIza[0-9a-z\\\\-_]{35}]['\\\"])", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a91f"}, "name": "Google Oauth", "regular": "([0-9]+-[0-9A-Za-z_]{32}\\.apps\\.googleusercontent\\.com)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a920"}, "name": "Heroku API Key", "regular": "([h|H][e|E][r|R][o|O][k|K][u|U].{0,30}[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a921"}, "name": "LinkedIn Secret Key", "regular": "((?i)linkedin(.{0,20})?['\\\"][0-9a-z]{16}['\\\"])", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a922"}, "name": "Mailchamp API Key", "regular": "[0-9a-f]{32}-us[0-9]{1,2}", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a923"}, "name": "Mailgun API Key", "regular": "(key-[0-9a-zA-Z]{32})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a924"}, "name": "Picatic API Key", "regular": "(sk_live_[0-9a-z]{32})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a925"}, "name": "<PERSON><PERSON><PERSON>", "regular": "(xox[baprs]-([0-9a-zA-Z]{10,48})?)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a926"}, "name": "Slack Webhook", "regular": "(https://hooks.slack.com/services/T[a-zA-Z0-9_]{8}/B[a-zA-Z0-9_]{8}/[a-zA-Z0-9_]{24})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a927"}, "name": "Stripe API Key", "regular": "((?:r|s)k_live_[0-9a-zA-Z]{24})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a928"}, "name": "Square Access Token", "regular": "(sqOatp-[0-9A-Za-z\\\\-_]{22})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a929"}, "name": "Square Oauth Secret", "regular": "(sq0csp-[ 0-9A-Za-z\\\\-_]{43})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a92a"}, "name": "Twilio API Key", "regular": "(SK[0-9a-fA-F]{32})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a92b"}, "name": "Twitter Oauth", "regular": "([t|T][w|W][i|I][t|T][t|T][e|E][r|R].{0,30}['\\\"\\\\s][0-9a-zA-Z]{35,44}['\\\"\\\\s])", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a92c"}, "name": "Twitter Secret Key", "regular": "(?i)twitter(.{0,20})?['\\\"][0-9a-z]{35,44}", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a92d"}, "name": "google_captcha", "regular": "(6L[0-9A-Za-z-_]{38}|^6[0-9a-zA-Z_-]{39})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a92e"}, "name": "google_oauth", "regular": "(ya29\\.[0-9A-Za-z\\-_]+)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a92f"}, "name": "amazon_aws_access_key_id", "regular": "A[SK]IA[0-9A-Z]{16}", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a930"}, "name": "amazon_aws_url", "regular": "s3\\.amazonaws.com[/]+|[a-zA-Z0-9_-]*\\.s3\\.amazonaws.com", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a931"}, "name": "authorization_api", "regular": "(api[key|\\s*]+[a-zA-Z0-9_\\-]+)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a932"}, "name": "twilio_account_sid", "regular": "(AC[a-zA-Z0-9_\\-]{32})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a933"}, "name": "twilio_app_sid", "regular": "AP[a-zA-Z0-9_\\-]{32}", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a934"}, "name": "paypal_braintree_access_token", "regular": "(access_token\\$production\\$[0-9a-z]{16}\\$[0-9a-f]{32})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a935"}, "name": "square_oauth_secret", "regular": "(sq0csp-[ 0-9A-Za-z\\-_]{43}|sq0[a-z]{3}-[0-9A-Za-z\\-_]{22,43})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a936"}, "name": "square_access_token", "regular": "(sqOatp-[0-9A-Za-z\\-_]{22}|EAAA[a-zA-Z0-9]{60})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a937"}, "name": "rsa_private_key", "regular": "(-----BEGIN RSA PRIVATE KEY-----)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a938"}, "name": "ssh_dsa_private_key", "regular": "(-----BEGIN DSA PRIVATE KEY-----)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a939"}, "name": "ssh_dc_private_key", "regular": "(-----BEGIN EC PRIVATE KEY-----)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a93a"}, "name": "pgp_private_block", "regular": "(-----B<PERSON>IN PGP PRIVATE KEY BLOCK-----)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a93b"}, "name": "json_web_token", "regular": "eyJ[A-Za-z0-9_-]*\\.[A-Za-z0-9_-]*\\.[A-Za-z0-9_\\-\\/.+=]*", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a93c"}, "name": "Google Cloud", "regular": "(GOOG[\\w\\W]{10,30})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a93d"}, "name": "Microsoft Azure", "regular": "(AZ[A-Za-z0-9]{34,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a93e"}, "name": "腾讯云", "regular": "(AKID[A-Za-z0-9]{13,20})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a93f"}, "name": "亚马逊云", "regular": "(AKIA[A-Za-z0-9]{16})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a940"}, "name": "IBM Cloud", "regular": "(IBM[A-Za-z0-9]{10,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a941"}, "name": "Oracle Cloud", "regular": "(OCID[A-Za-z0-9]{10,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a942"}, "name": "阿里云", "regular": "(LTAI[A-Za-z0-9]{12,20})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a943"}, "name": "华为云", "regular": "(AK[\\w\\W]{10,62})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a944"}, "name": "百度云", "regular": "(AK[A-Za-z0-9]{10,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a945"}, "name": "京东云", "regular": "(AK[A-Za-z0-9]{10,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a946"}, "name": "UCloud", "regular": "(UC[A-Za-z0-9]{10,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a947"}, "name": "青云", "regular": "(QY[A-Za-z0-9]{10,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a948"}, "name": "金山云", "regular": "(KS3[A-Za-z0-9]{10,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a949"}, "name": "联通云", "regular": "(LTC[A-Za-z0-9]{10,60})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a94a"}, "name": "移动云", "regular": "(YD[A-Za-z0-9]{10,60})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a94b"}, "name": "电信云", "regular": "(CTC[A-Za-z0-9]{10,60})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a94c"}, "name": "一云通", "regular": "(YYT[A-Za-z0-9]{10,60})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a94d"}, "name": "用友云", "regular": "(YY[A-Za-z0-9]{10,40})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a94e"}, "name": "南大通用云", "regular": "CI[A-Za-z0-9]{10,40}", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a94f"}, "name": "G-Core Labs", "regular": "(gcore[A-Za-z0-9]{10,30})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a950"}, "name": "MailChimp API Key", "regular": "([0-9a-f]{32}-us[0-9]{12})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a951"}, "name": "Outlook team", "regular": "((https://outlook\\.office.com/webhook/[0-9a-f-]{36}@))", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a952"}, "name": "<PERSON><PERSON>", "regular": "(?i)sauce.{0,50}(\"|'|`)?[0-9a-f-]{36}(\"|'|`)?", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a953"}, "name": "SonarQube Docs API Key", "regular": "((?i)sonar.{0,50}(\"|'|`)?[0-9a-f]{40}(\"|'|`)?)", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a954"}, "name": "HockeyApp", "regular": "(?i)hockey.{0,50}(\"|'|`)?[0-9a-f]{32}(\"|'|`)?", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a956"}, "name": "NuGet API Key", "regular": "(oy2[a-z0-9]{43})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a957"}, "name": "StackHawk API Key", "regular": "(hawk\\.[0-9A-Za-z\\-_]{20}\\.[0-9A-Za-z\\-_]{20})", "color": "red", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a958"}, "name": "Heroku config file", "regular": "(heroku\\.json)", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a959"}, "name": "jwt_token", "regular": "eyJ[A-Za-z0-9_\\/+-]{10,}={0,2}\\.[A-Za-z0-9_\\/+\\-]{15,}={0,2}\\\\.[A-Za-z0-9_\\/+\\-]{10,}={0,2}", "color": "yellow", "state": false}, {"_id": {"$oid": "664b4407efaa5cfd9a79a95a"}, "name": "INFO-KEY", "regular": "(access_key|access_token|admin_pass|admin_user|algolia_admin_key|algolia_api_key|alias_pass|alicloud_access_key|amazon_secret_access_key|amazonaws|ansible_vault_password|aos_key|api_key|api_key_secret|api_key_sid|api_secret|api.googlemaps AIza|apidocs|apikey|apiSecret|app_debug|app_id|app_key|app_log_level|app_secret|appkey|appkeysecret|application_key|appsecret|appspot|auth_token|authorizationToken|authsecret|aws_access|aws_access_key_id|aws_bucket|aws_key|aws_secret|aws_secret_key|aws_token|AWSSecretKey|b2_app_key|bashrc password|bintray_apikey|bintray_gpg_password|bintray_key|bintraykey|bluemix_api_key|bluemix_pass|browserstack_access_key|bucket_password|bucketeer_aws_access_key_id|bucketeer_aws_secret_access_key|built_branch_deploy_key|bx_password|cache_driver|cache_s3_secret_key|cattle_access_key|cattle_secret_key|certificate_password|ci_deploy_password|client_secret|client_zpk_secret_key|clojars_password|cloud_api_key|cloud_watch_aws_access_key|cloudant_password|cloudflare_api_key|cloudflare_auth_key|cloudinary_api_secret|cloudinary_name|codecov_token|conn.login|connectionstring|consumer_key|consumer_secret|credentials|cypress_record_key|database_password|database_schema_test|datadog_api_key|datadog_app_key|db_password|db_server|db_username|dbpasswd|dbpassword|dbuser|deploy_password|digitalocean_ssh_key_body|digitalocean_ssh_key_ids|docker_hub_password|docker_key|docker_pass|docker_passwd|docker_password|dockerhub_password|dockerhubpassword|dot-files|dotfiles|droplet_travis_password|dynamoaccesskeyid|dynamosecretaccesskey|elastica_host|elastica_port|elasticsearch_password|encryption_key|encryption_password|env.heroku_api_key|env.sonatype_password|eureka.awssecretkey)", "color": "yellow", "state": false}, {"_id": {"$oid": "669683ab793467d11cf992b1"}, "name": "Druid", "regular": "(Druid Stat Index)", "color": "red", "state": true}, {"_id": {"$oid": "66968474793467d11cf992b7"}, "name": "Route<PERSON>", "regular": "(\\$router\\.push)", "color": "red", "state": true}, {"_id": {"$oid": "669684d5793467d11cf992bc"}, "name": "AccessKey", "regular": "((?i)((access_key|access_token|admin_pass|admin_user|algolia_admin_key|algolia_api_key|alias_pass|alicloud_access_key|amazon_secret_access_key|amazonaws|ansible_vault_password|aos_key|api_key|api_key_secret|api_key_sid|api_secret|api.googlemaps AIza|apidocs|apikey|apiSecret|app_debug|app_id|app_key|app_log_level|app_secret|appkey|appkeysecret|application_key|appsecret|appspot|auth_token|authorizationToken|authsecret|aws_access|aws_access_key_id|aws_bucket|aws_key|aws_secret|aws_secret_key|aws_token|AWSSecretKey|b2_app_key|bashrc password|bintray_apikey|bintray_gpg_password|bintray_key|bintraykey|bluemix_api_key|bluemix_pass|browserstack_access_key|bucket_password|bucketeer_aws_access_key_id|bucketeer_aws_secret_access_key|built_branch_deploy_key|bx_password|cache_driver|cache_s3_secret_key|cattle_access_key|cattle_secret_key|certificate_password|ci_deploy_password|client_secret|client_zpk_secret_key|clojars_password|cloud_api_key|cloud_watch_aws_access_key|cloudant_password|cloudflare_api_key|cloudflare_auth_key|cloudinary_api_secret|cloudinary_name|codecov_token|config|conn.login|connectionstring|consumer_key|consumer_secret|credentials|cypress_record_key|database_password|database_schema_test|datadog_api_key|datadog_app_key|db_password|db_server|db_username|dbpasswd|dbpassword|dbuser|deploy_password|digitalocean_ssh_key_body|digitalocean_ssh_key_ids|docker_hub_password|docker_key|docker_pass|docker_passwd|docker_password|dockerhub_password|dockerhubpassword|dot-files|dotfiles|droplet_travis_password|dynamoaccesskeyid|dynamosecretaccesskey|elastica_host|elastica_port|elasticsearch_password|encryption_key|encryption_password|env.heroku_api_key|env.sonatype_password|eureka.awssecretkey)[a-z0-9_ .\\-,]{0,25})(=|>|:=|\\|\\|:|<=|=>|:).{0,5}['\\\"\\ ]([0-9a-zA-Z\\-_=]{8,64})['\\\"\\ ])", "color": "red", "state": true}, {"_id": {"$oid": "669684f0793467d11cf992bf"}, "name": "AccessKey2", "regular": "(['\\\"\\ ](GOOG[\\w\\W]{10,30})['\\\"\\ ]|(['\\\"\\ ]AZ[A-Za-z0-9]{34,40}['\\\"\\ ])|(['\\\"\\ ]AKID[A-Za-z0-9]{13,20}['\\\"\\ ])|(['\\\"\\ ]AKIA[A-Za-z0-9]{16}['\\\"\\ ])|(['\\\"\\ ][a-zA-Z0-9]{8}(-[a-zA-Z0-9]{4}){3}-[a-zA-Z0-9]{12}['\\\"\\ ])|(['\\\"\\ ]OCID[A-Za-z0-9]{10,40}['\\\"\\ ])|(['\\\"\\ ]LTAI[A-Za-z0-9]{12,20}['\\\"\\ ])|(['\\\"\\ ][A-Z0-9]{20}$['\\\"\\ ])|(['\\\"\\ ]JDC_[A-Z0-9]{28,32}['\\\"\\ ])|(['\\\"\\ ]AK[A-Za-z0-9]{10,40}['\\\"\\ ])|(['\\\"\\ ]UC[A-Za-z0-9]{10,40}['\\\"\\ ])|(['\\\"\\ ]QY[A-Za-z0-9]{10,40}['\\\"\\ ])|(['\\\"\\ ]AKLT[a-zA-Z0-9-_]{16,28}['\\\"\\ ])|(['\\\"\\ ]LTC[A-Za-z0-9]{10,60}['\\\"\\ ])|(['\\\"\\ ]YD[A-Za-z0-9]{10,60}['\\\"\\ ])|(['\\\"\\ ]CTC[A-Za-z0-9]{10,60}['\\\"\\ ])|(['\\\"\\ ]YYT[A-Za-z0-9]{10,60}['\\\"\\ ])|(['\\\"\\ ]YY[A-Za-z0-9]{10,40}['\\\"\\ ])|(['\\\"\\ ]CI[A-Za-z0-9]{10,40}['\\\"\\ ])|(['\\\"\\ ]gcore[A-Za-z0-9]{10,30}['\\\"\\ ]))", "color": "red", "state": true}, {"_id": {"$oid": "66968503793467d11cf992c2"}, "name": "敏感信息", "regular": "((?i)((access_key|appsecret|app_secret|access_token|password|secretkey|accesskey|accesskeyid|accesskeysecret|secret_key|pwd|test_user|admin_pass|admin_user|algolia_admin_key|algolia_api_key|alias_pass|alicloud_access_key|amazon_secret_access_key|amazonaws|ansible_vault_password|aos_key|api_key|api_key_secret|api_key_sid|api_secret|api.googlemaps AIza|apidocs|apikey|apiSecret|app_debug|app_id|app_key|app_log_level|app_secret|appkey|appkeysecret|application_key|appsecret|appspot|auth_token|authorizationToken|authsecret|aws_access|aws_access_key_id|aws_bucket|aws_key|aws_secret|aws_secret_key|aws_token|AWSSecretKey|b2_app_key|bashrc password|bintray_apikey|bintray_gpg_password|bintray_key|bintraykey|bluemix_api_key|bluemix_pass|browserstack_access_key|bucket_password|bucketeer_aws_access_key_id|bucketeer_aws_secret_access_key|built_branch_deploy_key|bx_password|cache_driver|cache_s3_secret_key|cattle_access_key|cattle_secret_key|certificate_password|ci_deploy_password|client_secret|client_zpk_secret_key|clojars_password|cloud_api_key|cloud_watch_aws_access_key|cloudant_password|cloudflare_api_key|cloudflare_auth_key|cloudinary_api_secret|cloudinary_name|codecov_token|config|conn.login|connectionstring|consumer_key|consumer_secret|credentials|cypress_record_key|database_password|database_schema_test|datadog_api_key|datadog_app_key|db_password|db_server|db_username|dbpasswd|dbpassword|dbuser|deploy_password|digitalocean_ssh_key_body|digitalocean_ssh_key_ids|docker_hub_password|docker_key|docker_pass|docker_passwd|docker_password|dockerhub_password|dockerhubpassword|dot-files|dotfiles|droplet_travis_password|dynamoaccesskeyid|dynamosecretaccesskey|elastica_host|elastica_port|elasticsearch_password|encryption_key|encryption_password|env.heroku_api_key|env.sonatype_password|eureka.awssecretkey)[a-z0-9_.]{0,25})(=|>|:=|:|<=|=>|:).{0,5}['\\\"\\ ]([0-9a-zA-Z-_=]{12,64})['\\\"\\ ])", "color": "red", "state": true}]